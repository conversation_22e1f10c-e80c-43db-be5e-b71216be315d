package com.jd.oa.joywork.detail.data;

import static com.jd.oa.joywork.detail.data.TaskDataUtils.SUCCESS;
import static com.jd.oa.joywork.detail.data.TaskDataUtils.toMap;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkEx;
import com.jd.oa.joywork.JoyWorkNetConstant;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.RiskEnum;
import com.jd.oa.joywork.detail.data.db.UpdateReporter;
import com.jd.oa.joywork.detail.data.entity.CommonEntity;
import com.jd.oa.joywork.detail.data.entity.DelMemberSend;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.data.entity.UpdateMemberSend;
import com.jd.oa.joywork.detail.ui.TaskDetailActivity;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TaskDetailWebservice {
    private static final String NO_AUTHORITY = "1104007";

    public static class TaskCallback extends SimpleRequestCallback<String> {
        @Override
        public void onFailure(HttpException exception, String info) {
            DialogUtils.removeLoadDialog(AppBase.getTopActivity());
            ToastUtils.showToast(JoyWorkEx.INSTANCE.filterErrorMsg(info));
        }

        @Override
        public void onSuccess(ResponseInfo<String> info) {
            super.onSuccess(info);
            Gson gson = new GsonBuilder().serializeNulls().create();
            final CommonEntity common = gson.fromJson(info.result, CommonEntity.class);
            Handler handler = new Handler(Looper.getMainLooper());
            if (common != null && !SUCCESS.equals(common.getErrorCode())) {
                DialogUtils.removeLoadDialog(AppBase.getTopActivity());
                ToastUtils.showToast(JoyWorkEx.INSTANCE.filterErrorMsg(common.getErrorMsg()));
                hasError = true;
                if (NO_AUTHORITY.equals(common.getErrorCode())) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            Activity activity = AppBase.getTopActivity();
                            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                                return;
                            }
                            if (activity instanceof TaskDetailActivity) {
                                activity.finish();
                            }
                        }
                    });
                }
            } else {//这里临时增加一个刷新UI的方式，以后可以改为其他
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        Activity activity = AppBase.getTopActivity();
                        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                            return;
                        }
                        if (activity instanceof TaskDetailActivity) {
                            ((TaskDetailActivity) activity).updateWebView();
                        }
                    }
                });
            }
        }

        public boolean hasError;
    }

    static void getTaskDetail(String taskId, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        HttpManager.post(null, param, callback, "work.task.getTaskDetailWithExt.v2");
    }

    static void findTaskPraises(String taskId, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("limit", 8);
        param.put("offset", 0);
        HttpManager.post(null, param, callback, "work.task.findTaskPraises");
    }

    public static void deleteTask(String taskId, String actionSource, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("type", 2);
        appendActionSource(param, actionSource);
        HttpManager.post(null, param, callback, "work.task.deleteTask.v2");
    }


    public static void getProjectList(int pageSize, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        int[] a = {1, 2};
        param.put("permissions", a);
//        param.put("taskId", taskId);
        param.put("returnGroup", true);
        param.put("offset", pageSize);
        param.put("limit", 999);
        HttpManager.post(null, param, callback, JoyWorkNetConstant.PROJECT_LIST);

    }

    //    taskAddProject  待办关联项目待办分组
    public static void taskAddProject(String taskId, String projectId, String groupId, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("projectId", projectId);
        param.put("groupId", groupId);
        HttpManager.post(null, param, callback, "work.task.taskAddProject.v2");

    }


    //    taskAddProject  待办取消关联项目待办分组
    public static void taskDeleteProject(String taskId, String projectId, String actionSource, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("projectId", projectId);
//        param.put("groupId", groupId);
        appendActionSource(param, actionSource);
        HttpManager.post(null, param, callback, "work.task.taskDeleteProject.v2");

    }


    static void revertTask(String taskId, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("type", 2);
        HttpManager.post(null, param, callback, "work.task.revertTask.v2");
    }

    static void copyTask(UpdateMemberSend send, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
//        Map<String, Object> param = new HashMap<>();
//        param.put("taskId", taskId);
//        param.put("members", send);
        HttpManager.post(null, toMap(send), callback, "work.task.copyTask.v2");
    }

    static void delTaskMembers(DelMemberSend delMemberSend, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        HttpManager.post(null, toMap(delMemberSend), callback, "work.task.delTaskMembers.v2");
    }

    static void taskRemindTime(String taskId, String remind, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("remindTime", remind);
        HttpManager.post(null, param, callback, "work.task.taskRemindTime.v2");
    }

    static void taskDuplicate(String taskId, DuplicateEnum duplicate, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("cycle", duplicate.getValue());
        HttpManager.post(null, param, callback, "work.task.taskSetCycle.v2");
    }

    static void updateTaskMembers(UpdateMemberSend updateMemberSend, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        HttpManager.post(null, toMap(updateMemberSend), callback, "work.task.updateTaskMembers.v2");
    }

    public static void updateTaskMembers2(UpdateMemberSend updateMemberSend, TaskCallback callback) {
        HttpManager.post(null, toMap(updateMemberSend), callback, "work.task.updateTaskMembers.v2");
    }

    static void updateTitle(String taskId, String title, String actionSource, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("title", title);
        postTaskUpdate(taskId, param, actionSource, callback);
    }


    static void updateTarget(String taskId, String goalId, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("goalId", goalId);
        param.put("type", "1");
        HttpManager.post(null, param, callback, JoyWorkNetConstant.TASK_LINK_TARGET);
    }

    static void updateKr(String taskId, String krId, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("goalId", krId);
        param.put("type", "2");
        HttpManager.post(null, param, callback, JoyWorkNetConstant.TASK_LINK_TARGET);
    }

    static void delTarget(String taskId, String goalId, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("goalId", goalId);
        param.put("type", "1");
        HttpManager.post(null, param, callback, JoyWorkNetConstant.TASK_DEL_TARGET);
    }

    static void delKr(String taskId, String krId, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("goalId", krId);
        param.put("type", "2");
        HttpManager.post(null, param, callback, JoyWorkNetConstant.TASK_DEL_TARGET);
    }

    static void updateEndTime(String taskId, long startTime, long endTime, String actionSource, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        postTaskUpdate(taskId, param, actionSource, callback);
    }

    static void updateEndTimeCycleAlert(String taskId, long startTime, long endTime, Integer cycle, String remindStr, String actionSource, TaskDetailWebservice.TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        param.put("cycle", cycle == null ? DuplicateEnum.NO.getValue() : cycle);
        param.put("remindStr", remindStr);
        postTaskUpdate(taskId, param, actionSource, callback);
    }

    public static void updateRisk(String taskId, RiskEnum riskEnum, String content, String actionSource, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("riskStatus", riskEnum.getCode() + "");
        if (content != null) {
            param.put("riskContent", content);
        }
        postTaskUpdate(taskId, param, actionSource, callback);
    }

    public static void updatePriority(String taskId, int joyWorkLevel, String actionSource, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("priorityType", joyWorkLevel);
        postTaskUpdate(taskId, param, actionSource, callback);
    }

    public static void updateRemark(String taskId, String remark, String actionSource, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("remark", remark);
        postTaskUpdate(taskId, param, actionSource, callback);
    }

    static void addJoySpace(String taskId, List<Documents> documentsList, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("documents", documentsList);
        HttpManager.post(null, param, callback, "work.task.addJoySpace.v2");
    }

    static void deleteJoySpace(String taskId, String documentId, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("documentId", documentId);
        HttpManager.post(null, param, callback, "work.task.deleteJoySpace.v2");
    }

    static void saveTaskResources(String taskId, Resources resources, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("resource", resources);
        HttpManager.post(null, param, callback, "work.task.saveTaskResources");
    }

    static void delTaskResources(String taskId, List<String> resourcesList, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("resourceIds", resourcesList);
        HttpManager.post(null, param, callback, "work.task.delTaskResources");
    }

    static void cancelTaskPraise(String taskId, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        HttpManager.post(null, param, callback, "work.task.cancelTaskPraise");
    }

    static void taskPraise(String taskId, TaskCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        HttpManager.post(null, param, callback, "work.task.taskPraise");
    }

    static void taskUpdateStatus(String taskId, int taskStatus, String content, boolean force, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        Map<String, Object> param = new HashMap<>();
        param.put("taskId", taskId);
        param.put("taskStatus", taskStatus);
        if (ObjExKt.isLegalString(content)) {
            param.put("content", content);
        }
        param.put("force", force);
        HttpManager.post(null, param, callback, "work.task.taskUpdateStatus.v2");
    }

    public static void postTaskUpdate(String taskId, Map<String, Object> param, String actionSource, TaskCallback callback) {
        UpdateReporter.INSTANCE.reportUpdate();
        param.put("taskId", taskId);
        param.put("aggMessage", true);
        appendActionSource(param, actionSource);
        HttpManager.post(null, param, callback, "work.task.taskUpdate.v2");
    }

    //配合服务端透传deepLink中的参数回服务端
    private static void appendActionSource(Map<String, Object> param, String actionSource) {
        try {
            if (actionSource != null) {
                if (param == null) {
                    param = new HashMap<>();
                }
                Map<String, Object> map = new Gson().fromJson(actionSource,
                        new TypeToken<Map<String, Object>>() {
                        }.getType());
                param.put("actionSource", map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}


