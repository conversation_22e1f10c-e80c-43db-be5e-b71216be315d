package com.jd.oa.joywork.urge

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.isChief
import com.jd.oa.joywork.joinLegalString
import com.jd.oa.utils.argb
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import kotlinx.android.synthetic.main.jdme_joywork_urge_list_item.view.*


class UrgeListAdapter(
    val context: Context,
    val owners: List<JoyWorkUser>,
    val selectedCallback: ((JoyWorkUser) -> Boolean)? = null,
    val selectChangeListener: ((JoyWorkUser) -> Unit)? = null
) : RecyclerView.Adapter<UrgeListAdapter.VH>() {

    private val click = View.OnClickListener {
        val user = it.tag as JoyWorkUser
        selectChangeListener?.invoke(user)
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        fun bind(joyWorkUser: JoyWorkUser) {
            itemView.name.text = joyWorkUser.realName ?: ""
            itemView.department.text =
                listOf(joyWorkUser.deptInfo?.deptName, joyWorkUser.titleName).joinLegalString()
            JoyWorkViewItem.avatar(itemView.mAvatarView, joyWorkUser.headImg)
            handleSelCb(selectedCallback?.invoke(joyWorkUser) ?: false)
            itemView.tag = joyWorkUser
            itemView.setOnClickListener(<EMAIL>)
            if (joyWorkUser.chief.isChief()) {
                itemView.hat.visible()
            } else {
                itemView.hat.gone()
            }
        }

        private fun handleSelCb(sel: Boolean) {
            if (sel) {
                itemView.mSelCb.setText(R.string.icon_padding_checkcircle)
                itemView.mSelCb.argb("#FE3B30")
            } else {
                itemView.mSelCb.setText(R.string.icon_prompt_circle)
                itemView.mSelCb.argb("#8F959E")
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        val view =
            (context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).inflate(
                R.layout.jdme_joywork_urge_list_item,
                parent,
                false
            )
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        holder.bind(owners[position])
    }

    override fun getItemCount(): Int {
        return owners.size
    }
}