package com.jd.oa.joywork.shortcut

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.EntranceType
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkDetailParam
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.JoyWorkMsgCenter
import com.jd.oa.joywork.MsgCenterCallback2
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.data.db.UpdateReporter
import com.jd.oa.joywork.detail.ui.JoyWorkTaskDetailContract
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.team.TeamDetailActivity
import com.jd.oa.joywork.team.create.TeamCreateActivity
import com.jd.oa.joywork.team.kpi.WeeklySummaryActivity
import com.jd.oa.joywork.utils.JoyWorkNetConfig
import com.jd.oa.model.WorkInfo
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.model.service.JoyWorkService
import com.jd.oa.model.service.contract.startActivityForResult
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel
import com.jd.oa.utils.JDMAUtils

class JoyWorkServiceImpl : JoyWorkService {
    /**
     * 从聊天界面可以长按消息、点击加号创建待办
     * @param content: 聊天内容。如果是长按消息，此处表示消息内容；否则该字段为 Null
     * @param info: 聊天信息。里面可读取到是单聊还是群聊
     * @param task: AI 识别待办后，咚咚携带过来的关于消息识别后的参数。2022年11月23日 目前没有用
     * @param isExtend: 是点击加号还是长按消息。true 表示点击加号，false 表示长按消息
     */
    override fun goShortcutCreate(
        context: Activity,
        content: String?,
        info: String?,
        atUsers: String?,
        isExtend: Boolean
    ) {
        JoyWorkShortcutCreator.showShortcutDialog(context, content, info, isExtend)
    }

    override fun goCompleteCreate(
        context: Activity,
        content: String?,
        info: String?,
        atUsers: String?,
        isExtend: Boolean,
        label: MEChattingLabel
    ) {
        JoyWorkShortcutCreator.goCompleteCreate(context, content, info, atUsers, isExtend, label)
    }

    override fun jdmaClick(type: Int) {
        when (type) {
            JoyWorkService.TYPE_EX -> JoyWorkConstant.JOYWORK_DD_EX
            JoyWorkService.TYPE_ITEM -> JoyWorkConstant.JOYWORK_DD_ITEM
            else -> null
        }?.apply {
            JDMAUtils.onEventClick(this, this)
        }
    }

    override fun filterMsg(msg: String?, defMsg: String): String {
        return JoyWorkEx.filterErrorMsg(msg, defMsg)
    }

    override fun getRiskEntranceType(): String {
        return EntranceType.RISK.code
    }

    override fun getHandleEntranceType(): String {
        return EntranceType.MY_HANDLE.code
    }

    override fun getAssignEntranceType(): String {
        return EntranceType.MY_ASSIGN.code
    }

    override fun getCoorEntranceType(): String {
        return EntranceType.MY_COOPERATE.code
    }

    override fun getProjectListEntranceType(): String {
        return EntranceType.PROJECT_LIST.code
    }

    override fun openProjectDetail(context: Context, proId: String, title: String, isTab: Boolean) {
        val intent = TeamDetailActivity.inflateIntent(
            context = context,
            title = title,
            id = proId,
            isTab = isTab,
            type = JoyWorkProjectList.TYPE_NORMAL,
            iconUrl = null,
        )
        context.startActivity(intent)
    }

    override fun createProject(activity: Activity) {
        activity.startActivity(Intent(activity, TeamCreateActivity::class.java))
    }

    override fun saveTab(id: String?, context: Context) {
        if (id.isLegalString()) {
            JoyWorkPreference(context).put(JoyWorkPreference.KV_ENTITY_WORK_BENCH_TAB, id!!)
        }
    }

    override fun getTab(context: Context): String? {
        val tab = JoyWorkPreference(context).get(JoyWorkPreference.KV_ENTITY_WORK_BENCH_TAB)
        if (tab.isLegalString()) {
            return tab
        } else {
            return null
        }
    }

    override fun openSelDir(context: Context, title: String, gid: String) {
        WeeklySummaryActivity.openSelDir(context, title, gid)
    }

    override fun getJoyWorkImAppId(): String {
        return JoyWorkNetConfig.getJoyWorkIMAppId()
    }

    override fun workbenchCreate(activity: Activity) {
        val tmp = ShortcutDialogTmpData()
        tmp.fromDD = false
        val self = JoyWorkUser.getSelf()
        self.setToChief()
        tmp.owners = mutableListOf(self)
        JoyWorkShortcutDialog(
            activity,
            JoyWorkShortcutDraft.useDraftWhenHad(),
            tmpData = tmp
        ).show()
    }

    override fun updateTaskStatus(
        context: Context,
        workInfo: WorkInfo?,
        callback: IServiceCallback<Void>?
    ) {
        val work = JoyWork.fromWorkInfo(workInfo);
        JoyWorkViewItem.finishAction(work, context, object :
            JoyWorkUpdateCallback {
            override fun result(success: Boolean, errorMsg: String) {
                callback?.onResult(success, error = errorMsg)
            }

            override fun onStart() {
            }

        })
    }

    override fun openTaskDetailForResult(
        fragmentActivity: FragmentActivity,
        taskId: String,
        continuous: Boolean,
        from: String?,
        callback: IServiceCallback<WorkInfo>?
    ) {
        val listener = if (continuous) {
            val listener = object : MsgCenterCallback2<WorkInfo>() {
                override fun onInvoke(t: WorkInfo) {
                    callback?.onResult(true, t)
                }
            }
            JoyWorkMsgCenter.registerDetailUpdate(listener)
            listener
        } else {
            UpdateReporter.workInfo = null
            null
        }
        val detailParam = JoyWorkDetailParam(null, taskId = taskId, null).apply {
            this.from = from
        }
        val resultCallback = object : IServiceCallback<DetailReturnParcel?> {
            override fun onResult(
                success: Boolean,
                t: DetailReturnParcel?,
                error: String?
            ) {
                if (listener != null) {
                    JoyWorkMsgCenter.unRegisterDetailUpdate(listener)
                } else {
                    if (t != null && t.update && UpdateReporter.workInfo != null) {
                        callback?.onResult(true, UpdateReporter.workInfo)
                    } else {
                        callback?.onResult(false)
                    }
                }
                UpdateReporter.workInfo = null
            }
        }
        startActivityForResult(
            fragmentActivity,
            JoyWorkTaskDetailContract(detailParam, resultCallback)
        )
    }
}