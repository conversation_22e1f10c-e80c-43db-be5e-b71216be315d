package com.jd.oa.joywork.ui

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.forEachChild
import com.jd.oa.utils.isVisible

class AverageLinearLayout(context: Context, attributeSet: AttributeSet) :
    LinearLayout(context, attributeSet) {
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val totalW = measuredWidth - dividerPadding * (childCount - 1)
        val w = Math.min(totalW / childCount, CommonUtils.dp2px(96f))
        forEachChild {
            if (it.isVisible() && it.measuredWidth != w) {
                it.measure(MeasureSpec.makeMeasureSpec(w, MeasureSpec.EXACTLY), heightMeasureSpec)
            }
        }
    }
}