package com.jd.oa.joywork.detail.data.entity.custom;

import org.json.JSONObject;

//    {
//        "detailId": "361208658673111040",
//            "sort": 105000000,
//            "content": "{\"background\":\"#00C166\",\"content\":\"正常\",\"color\":\"#FFFFFF\"}",
//            "status": 1
//    },
public class CustomFieldItem {
    // 每一个自定义选项
    public String detailId;
    public Integer sort;
    public Integer status;
    public String content; // 该选项的一些配置

    private ItemContent itemContent;

    public ItemContent getItemContent() {
        if (itemContent == null) {
            itemContent = new ItemContent();
            try {
                JSONObject object = new JSONObject(content);
                itemContent.background = object.getString("background");
                itemContent.color = object.getString("color");
                itemContent.content = object.getString("content");
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return itemContent;
    }
}
