package com.jd.oa.joywork.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.util.ArrayMap
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.ViewModelProviders
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.chenenyu.router.annotation.Route
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkNum
import com.jd.oa.joywork.dialog.biz.JoyWorkFilterDialog
import com.jd.oa.joywork.notification.MyNotificationCenterFragment
import com.jd.oa.joywork.notification.NotifyRepo
import com.jd.oa.joywork.self.JoyWorkSelfFragment
import com.jd.oa.joywork.self.SelfBaseItf
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.joywork.team.TeamMainFragment
import com.jd.oa.joywork.team.kpi.GoalListActivity
import com.jd.oa.joywork.team.kpi.GoalListPlaceHolderFragment
import com.jd.oa.joywork.utils.getParamsKey
import com.jd.oa.joywork.view.BubbleView
import com.jd.oa.listener.Refreshable
import com.jd.oa.router.DeepLink
import com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME
import com.jd.oa.theme.manager.ThemeApi
import com.jd.oa.theme.view.JoyWorkTheme
import com.jd.oa.utils.*
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.*

/**
 * Joywork 列表页框架。含页头及 我的待办，团队待办 tabs。
 * 我的待办：在 self 包下
 * 团队待办：在 team 包下
 * <AUTHOR>
 */
//@Route(DeepLink.JOY_WORK_LIST_NEW, DeepLink.JOY_WORK_LIST)
class JoyWorkFragment : BaseFragment(), Refreshable, View.OnClickListener {
    private lateinit var mAdapter: PagerAdapter
    private lateinit var mViewPager: ViewPager
    private lateinit var mTabContainer: LinearLayout
    private var mInitPos = 2
    private var mLastPos: Int = -1
    private val filterMap = ArrayMap<Int, JoyWorkFilterDialog.Item>()
    private val tabItems = ArrayList<JoyWorkTabItem<Pair<String, Int>, Fragment>>()
    private val onTabClick = View.OnClickListener {
        val index = it.tag as Int
        val frg = tabItems[index].fragment
        if (frg is GoalListPlaceHolderFragment) {
            val intent = GoalListActivity.inflateIntent(
                requireActivity(),
                string(R.string.joywork_my_goal),
                false
            )
            requireActivity().startActivity(intent)
        } else {
            mViewPager.currentItem = it.tag as Int
        }
    }

    private lateinit var mViewModel: JoyWorkViewModel

    // 所有的 fragment
    private var mStrategy: JoyWorkMainStrategy = JoyWorkMainNormalStrategy

    private val pageChangeListener = object : ViewPager.SimpleOnPageChangeListener() {
        override fun onPageSelected(position: Int) {
            tabItems[position].clickId?.apply {
                JDMAUtils.onEventClick(this, this)
            }
            if (mLastPos != position) {
                if (mLastPos != -1) {
                    val tv =
                        mTabContainer.getChildAt(mLastPos).findViewById<TextView>(R.id.tab_title)
                    tv.argb("#666666")
                    mTabContainer.getChildAt(mLastPos).findViewById<View>(R.id.indicator)
                        .invisible()
                    filterMap.remove(mLastPos)
                    (tabItems[mLastPos].fragment as? SelfBaseItf)?.onPageUnselected()
                }
                (tabItems[mViewPager.currentItem].fragment as? SelfBaseItf)?.onPageSelected()
                mLastPos = position

                val tv2 = mTabContainer.getChildAt(mLastPos).findViewById<TextView>(R.id.tab_title)
                tv2.argb("#333333")
                mTabContainer.getChildAt(mLastPos).findViewById<View>(R.id.indicator)
                    .visible()
            }
        }
    };

    private val refreshNumRunnable = Runnable { mViewModel.refreshNum() }

    private val finishReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            mHandler.postDelayed(refreshNumRunnable, JoyWorkConstant.REFRESH_INTERVAL)
        }
    }

    private val mThemeDataChangeObserver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            mStrategy.onThemeDataChange(activity?.findViewById(R.id.ll_title_container))
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        kotlin.runCatching {
            (requireActivity() as? FunctionActivity)?.setBarHide()
        }
        mViewModel = ViewModelProviders.of(requireActivity()).get(JoyWorkViewModel::class.java)
        val action = EntranceType.valueByCode(
            arguments.getParamsKey(
                "action",
                dv = EntranceType.MY_HANDLE.code
            )
        )
        mViewModel.setUI(action)
        if (action == EntranceType.RISK) {
            mInitPos = 0
        } else if (action == EntranceType.PROJECT_LIST) {
            mInitPos = 3
        }
        val result = kotlin.runCatching {
            val jsonObject = JSONObject(requireArguments().getString("mparam") as String)
            mStrategy =
                if (jsonObject.get("isTab") == "1") JoyWorkMainTabStrategy else JoyWorkMainNormalStrategy
        }
        if (result.isFailure) {
            mStrategy = JoyWorkMainNormalStrategy
        }
        initTabs()
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(
            finishReceiver,
            IntentFilter(ProjectConstant.FINISH_ACTION)
        )
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(
            mThemeDataChangeObserver,
            IntentFilter(ACTION_CHANGE_THEME)
        )

        val view: View = inflater.inflate(R.layout.jdme_joywork_fragment, container, false)
        mViewPager = view.findViewById<View>(R.id.viewpager) as ViewPager
        mTabContainer = view.findViewById<LinearLayout>(R.id.self_tabs_ll).also {
            tabItems.forEachIndexed { index, joyWorkTabItem ->
                val tabView = requireContext().inflater.inflate(
                    R.layout.joywork_tablayout_item,
                    it,
                    false
                )
                val title = tabView.findViewById<TextView>(R.id.tab_title)
                title.text = joyWorkTabItem.content.first
                if (index == mInitPos) {
                    tabView.findViewById<View>(R.id.indicator).visible()
                } else {
                    tabView.findViewById<View>(R.id.indicator).invisible()
                }
                val bubble = tabView.findViewById<BubbleView>(R.id.bubbleText)
                bubble.gone()
                tabView.tag = index
                tabView.setOnClickListener(onTabClick)

                val paddingView = tabView.findViewById<View>(R.id.paddingView)
                if (index == tabItems.size - 1) {
                    paddingView.gone()
                } else {
                    paddingView.visible()
                }
                it.addView(tabView)
            }
        }

        mAdapter = SunAppFragmentAdapter(childFragmentManager, tabItems)
        mViewPager.offscreenPageLimit = tabItems.size
        mViewPager.adapter = mAdapter
        mViewPager.addOnPageChangeListener(pageChangeListener)
        mViewPager.currentItem = mInitPos
        mLastPos = mInitPos

        mStrategy.handleTitle(view.findViewById(R.id.ll_title_container), requireActivity())
        initObservers()
        getNums()
        getNotificationNum(true)
        mViewModel.numRefreshFlow.observe(requireActivity()) {
            if (it) {
                scope.launch {
                    mViewModel.post(Repo.getNums().data ?: JoyWorkNum.getDefault())
                }
            }
        }
        // 接听内部 frg 发出的 toast 申请
        mViewModel.toastLiveData.observe(requireActivity()) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }

        return view
    }

    private fun initObservers() {
        mViewModel.post(JoyWorkNum.getDefault())
        mViewModel.updateNotificationLiveData.observe(requireActivity()) {
            getNotificationNum(it)
        }
        mViewModel.notificationNumLiveData.observe(requireActivity()) {
            val ll = mTabContainer.getChildAt(0) as ViewGroup
            if ((it ?: 0) > 0) {
                ll.findViewById<TextView>(R.id.bubbleText).visible()
                ll.findViewById<TextView>(R.id.countView).text = it.clamp99()
            } else {
                ll.findViewById<TextView>(R.id.bubbleText).gone()
            }
        }
    }

    private val scope = MainScope()
    private fun getNums() {
        scope.launch {
            mViewModel.post(Repo.getNums().data ?: JoyWorkNum.getDefault())
        }
    }

    private fun getNotificationNum(onlyUpdateNum: Boolean) {
        scope.launch {
            val nums = NotifyRepo.getNotificationNum()
            nums.ifSuccessSilence {
                if (mViewModel.mNumUpdated
                    && !onlyUpdateNum
                    && !Objects.equals(it?.nums, mViewModel.notificationNumLiveData.value)
                ) {
                    mViewModel.postNotificationRefreshList(true)
                }
                mViewModel.postNotificationNum(it?.nums ?: 0)
            }
        }
    }

    private fun initTabs() {
        tabItems.add(
            JoyWorkTabItem(
                Pair(string(R.string.joywork_notification_center), 0),
                null,
                MyNotificationCenterFragment()
            )
        )
        tabItems.add(
            JoyWorkTabItem(
                Pair(string(R.string.joywork_my_goal), 0),
                null,
                GoalListPlaceHolderFragment()
            )
        )
        tabItems.add(
            JoyWorkTabItem(
                Pair(string(R.string.joywork_tabbar_title_my), 0),
                JoyWorkConstant.JOYWORK_SELF,
                JoyWorkSelfFragment.getInstance(mStrategy == JoyWorkMainTabStrategy)
            )
        )
        tabItems.add(
            JoyWorkTabItem(
                Pair(string(R.string.joywork_tabbar_title_team), 0),
                JoyWorkConstant.JOYWORK_TEAM,
                TeamMainFragment.getInstance(mStrategy == JoyWorkMainTabStrategy)
            )
        )
    }

    // tabbar 打开当前界面时会调用该方法
    override fun refresh() {
        tabItems.forEach {
            (it.fragment as? JoyWorkSelfFragment)?.onShowAtTabbar()
        }
    }

    override fun onDestroyView() {
        scope.cancel()
        if (mStrategy is JoyWorkMainNormalStrategy) {
            // 待办界面返回时，提示刷新工作台待办卡片
            LocalBroadcastManager.getInstance(requireActivity())
                .sendBroadcast(Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK))
        }
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(finishReceiver)
        LocalBroadcastManager.getInstance(requireContext())
            .unregisterReceiver(mThemeDataChangeObserver)
        super.onDestroyView()
    }

    override fun onResume() {
        super.onResume()
        if (mStrategy is JoyWorkMainTabStrategy) {
            val data = JoyWorkTheme.currentTheme
            if (data.isGlobal.isTrue() && data.isDarkTheme) {
                ThemeApi.checkAndSetDarkTheme(activity)
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(activity)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        val a = activity ?: return
        if (mStrategy is JoyWorkMainTabStrategy) {
            if (StatusBarConfig.enableImmersive()) {
                QMUIStatusBarHelper.setStatusBarLightMode(a)
            }
        }
    }
}

class SunAppFragmentAdapter(
    fm: FragmentManager,
    private val tabItems: ArrayList<JoyWorkTabItem<Pair<String, Int>, Fragment>>
) : FragmentPagerAdapter(fm) {
    override fun getItem(position: Int): Fragment {
        return tabItems[position].fragment
    }

    override fun getCount(): Int {
        return tabItems.size
    }
}