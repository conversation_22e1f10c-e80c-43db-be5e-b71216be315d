package com.jd.oa.joywork.team.view;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;

public class HRecyclerView extends RecyclerView {

    public final ArrayList<View> brotherView = new ArrayList<>();

    private Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
        }
    };

    public int totalDx = 0;

    private static final int INVALID_POINTER = -1;
    private int mScrollPointerId = INVALID_POINTER;

    private int mTouchSlop;

    public HRecyclerView(@NonNull Context context) {
        super(context);
        init();
    }

    public HRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public HRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        final ViewConfiguration vc = ViewConfiguration.get(getContext());
        mTouchSlop = vc.getScaledTouchSlop();
    }

    private int downX = 0;
    private int downY = 0;
    private float mUpY = Float.MAX_VALUE;
    private boolean dragH = false;

    @Override
    public boolean dispatchTouchEvent(MotionEvent e) {
        switch (e.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                mUpY = Float.MAX_VALUE;
                downY = (int) (e.getY() + 0.5f);
                break;
            }
            case MotionEvent.ACTION_UP: {
                mUpY = e.getY();
                break;
            }
        }
        return super.dispatchTouchEvent(e);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        switch (e.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                dragH = false;
                downX = (int) (e.getX() + 0.5f);
                downY = (int) (e.getY() + 0.5f);
                mScrollPointerId = e.getPointerId(0);
                super.onInterceptTouchEvent(e);
                return false;
            }
            case MotionEvent.ACTION_MOVE: {
                final int index = e.findPointerIndex(mScrollPointerId);
                if (index < 0) {
                    return super.onInterceptTouchEvent(e);
                }
                final int x = (int) (e.getX(index) + 0.5f);
                final int y = (int) (e.getY(index) + 0.5f);
                final int dx = x - downX;
                final int dy = y - downY;
                // 水平滑动，保证不拦截
                if (dragH || (Math.abs(dx) > mTouchSlop && Math.abs(dx) > Math.abs(dy))) {
                    dragH = true;
                    super.onInterceptTouchEvent(e);
                    return false;
                }
            }
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                dragH = false;
                break;
        }
        return super.onInterceptTouchEvent(e);
    }

    public boolean isUp() {
        Log.e("TAG", "isUp: " + dragH + ", " + mUpY + ", " + downY + ", " + mTouchSlop);
        return !dragH && mUpY != Float.MAX_VALUE && mUpY < downY && Math.abs(mUpY - downY) > mTouchSlop;
    }

    public void syncChildren(View triggerChild, final int dx) {
        totalDx = dx;
        handler.post(new Runnable() {
            @Override
            public void run() {
                int childCount = getChildCount();
                for (int i = 0; i < childCount; i++) {
                    View child = getChildAt(i);
                    // 可以换成自定义属性
                    if (child instanceof ItemContainer && !((ItemContainer) child).fixPos()) {
                        child.setScrollX(dx);
                    }
                    for (View view : brotherView) {
                        view.setScrollX(dx);
                    }
                }
            }
        });
    }
}
