package com.jd.oa.joywork.ui

import com.jd.oa.joywork.CoroutineResult
import com.jd.oa.joywork.bean.JoyWorkNum
import com.jd.oa.joywork.repo.JoyWorkRepo
import kotlinx.coroutines.suspendCancellableCoroutine

object Repo {
    suspend fun getNums(): CoroutineResult<JoyWorkNum> {
        return suspendCancellableCoroutine { c ->
            JoyWorkRepo.getNums { num ->
                c.resumeWith(
                    Result.success(
                        CoroutineResult<JoyWorkNum>().success(
                            num ?: JoyWorkNum()
                        )
                    )
                )
            }
        }
    }
}