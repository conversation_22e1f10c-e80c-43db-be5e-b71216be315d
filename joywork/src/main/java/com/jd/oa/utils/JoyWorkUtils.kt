package com.jd.oa.utils

import android.app.Activity
import android.view.View
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.abilities.utils.MELogUtil

private const val LOG_TAG = "joywork"

object JoyWorkUtils {

    fun joyWorkLog(info: String) {
        MELogUtil.localV(LOG_TAG, info)
        MELogUtil.onlineV(LOG_TAG, info)
    }

    fun <T : View> RecyclerView.ViewHolder.bindView(@IdRes id: Int): Lazy<T> = lazy {
        itemView.findViewById(id)
    }

    fun <T : View> Fragment.bindView(@IdRes id: Int): Lazy<T> = lazy {
        view!!.findViewById(id)
    }

    fun <T : View> Activity.bindView(@IdRes id: Int): Lazy<T> = lazy {
        findViewById(id)
    }
}