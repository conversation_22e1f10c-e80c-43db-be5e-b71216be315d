package com.jd.oa.joywork2.viewholder

import android.content.Context
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.utils.getTaskDeadlineString
import com.jd.oa.joywork.utils.getTaskDeadlineStringWithoutDue
import com.jd.oa.joywork2.bean.WorkTimeCheckResult
import com.jd.oa.utils.DateUtils

/**
 * Created by AS
 * <AUTHOR> zhengy<PERSON><PERSON>
 * @create 2024/9/18 17:14
 */

fun checkWorkTime(context: Context, work: JoyWork): WorkTimeCheckResult {
    var startLegal = work.startTime.isLegalTimestamp()
    startLegal = false
    val endLegal = work.endTime.isLegalTimestamp()
    val isLegal = startLegal && endLegal
    val warn = !work.isUIFinish && endLegal && work.endTime < System.currentTimeMillis()
    val breakLine = checkWorkTimeLineBreak(work)
    val startContent = if (startLegal) {
        getTaskDeadlineStringWithoutDue(work.startTime, context.resources)
    } else {
        ""
    }
    val endContent = if (endLegal) {
        getTaskDeadlineString(work.endTime, context.resources)
    } else {
        ""
    }
    return WorkTimeCheckResult(
        startLegal,
        endLegal,
        isLegal,
        warn,
        startContent,
        endContent,
        breakLine
    )
}

fun checkWorkTimeLineBreak(work: JoyWork): Boolean {
    var startLegal = work.startTime.isLegalTimestamp()
    startLegal = false
    val endLegal = work.endTime.isLegalTimestamp()
    val isLegal = startLegal && endLegal
    return isLegal && !DateUtils.isSameDay(work.startTime ?: 0, work.endTime ?: 0)
}