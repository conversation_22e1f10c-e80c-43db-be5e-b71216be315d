package com.jd.oa.joywork2.bean;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.joywork.bean.JoyWork;

import java.util.List;

public class JoyWorkGroup2 {
    private int total;

    @SerializedName("regionType")
    private String blockType;
    private Integer pageSize;
    private Integer page;
    private List<JoyWork> tasks;
    private Integer unPlanCount = 0;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getBlockType() {
        return blockType;
    }

    public void setBlockType(String blockType) {
        this.blockType = blockType;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public List<JoyWork> getTasks() {
        return tasks;
    }

    public void setTasks(List<JoyWork> tasks) {
        this.tasks = tasks;
    }

    public Integer getUnPlanCount() {
        return unPlanCount;
    }

    public void setUnPlanCount(Integer unPlanCount) {
        this.unPlanCount = unPlanCount;
    }


    @Override
    public String toString() {
        return "JoyWorkGroup{" +
                "total=" + total +
                ", blockType=" + blockType +
                ", pageSize=" + pageSize +
                ", page=" + page +
                ", tasks=" + tasks +
                ", unPlanCount=" + unPlanCount +
                '}';
    }
}
