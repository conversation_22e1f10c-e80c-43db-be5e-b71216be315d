package com.jd.oa.joywork2.list.calendar.view

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.LinearLayout
import android.widget.LinearLayout.LayoutParams
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.adapter.TagFloatMenuAdapter
import com.jd.oa.joywork.bean.Tag
import com.jd.oa.joywork2.view.WorkFloatMenu.autoAdjustArrowPos
import com.jd.oa.ui.recycler.MyDividerItem
import com.jd.oa.utils.ColorUtils
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.LocaleUtils

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/25
 * @Description:
 */
class HorizontalLabelLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    private val labels = mutableListOf<Tag>() // 保存所有 label 的内容
    private val labelViews = mutableListOf<TextView>() // 保存所有 label 对应的视图
    private val labelMore by lazy { createPlusMore() }

    fun setLabels(newLabels: List<Tag>) {
        labels.clear()
        labels.addAll(newLabels)
        removeAllViews()
        labelViews.clear()

        if (labels.isEmpty()) return

        for (label in labels) {
            val labelView = createLabelView(label)
            labelViews.add(labelView)
            addView(labelView)
        }
        addView(labelMore)
    }

    private fun createLabelView(tag: Tag): TextView {
        return TextView(context).apply {
            setLabelTextViewParam(tag, false)
        }
    }

    private fun createPlusMore(remainingCount: Int = 0): TextView {
        return TextView(context).apply {
            setLabelMoreText(remainingCount)
            background =
                DrawableEx.roundSolidRect(Color.parseColor("#FFF0F1F2"), CommonUtils.dp2FloatPx(4))
            layoutParams = LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            isVisible = false
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
            setTextColor(Color.parseColor("#FF6B7280"))
            setPadding(
                DensityUtil.dp2px(context, 4f), DensityUtil.dp2px(context, 1f),
                DensityUtil.dp2px(context, 4f), DensityUtil.dp2px(context, 1f)
            )
        }
    }

    private fun showTagsMenu(
        context: Context,
        anchorView: View,
        items: MutableList<Tag>
    ) {
        // 共用待办视图类型 PopWindow 卡片
        val contentView: View =
            LayoutInflater.from(context).inflate(R.layout.float_pop_menu, null)
        val recyclerView = contentView.findViewById<RecyclerView>(R.id.recycle)
        PopupWindow(anchorView.context).apply {
            this.contentView = contentView
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            isTouchable = true
            isFocusable = true
            isOutsideTouchable = true
            showAsDropDown(anchorView, 0, 0)
        }
        recyclerView.apply {
            addItemDecoration(MyDividerItem(context))
            layoutManager = LinearLayoutManager(context)
            adapter = TagFloatMenuAdapter(context, items)
        }
        contentView.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                autoAdjustArrowPos(contentView, anchorView)
                contentView.viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        })
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val totalWidth = MeasureSpec.getSize(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)

        var usedWidth = 0
        var maxHeight = 0
        var remainingCount = 0

        // 测量子 View，计算总宽高
        for (i in labelViews.indices) {
            val labelView = labelViews[i]
            measureChild(labelView, widthMeasureSpec, heightMeasureSpec)
            val childWidth = labelView.measuredWidth + labelView.marginStart + labelView.marginEnd
            val childHeight = labelView.measuredHeight

            if (usedWidth + childWidth <= totalWidth) {
                usedWidth += childWidth
                maxHeight = maxOf(maxHeight, childHeight)
                labelView.isVisible = true
            } else {
                remainingCount = labelViews.size - i
                break
            }
        }

        // 如果有剩余未展示的标签，测量 "+N" 视图
        if (remainingCount > 0) {
            measureChild(labelMore, widthMeasureSpec, heightMeasureSpec)
            if (usedWidth + labelMore.measuredWidth > totalWidth) {
                // 调整最后一个可见标签为 "+(N+1)"
                labelViews[labelViews.size - remainingCount - 1].isVisible = false
                remainingCount += 1
            }
            labelMore.apply {
                setLabelMoreText(remainingCount)
                isVisible = true
                setOnClickListener {
                    showTagsMenu(
                        context,
                        this,
                        labels.subList(labels.size - remainingCount, labels.size)
                    )
                }
            }
            maxHeight = maxOf(maxHeight, labelMore.measuredHeight)
        }

        val measuredHeight =
            if (heightMode == MeasureSpec.UNSPECIFIED) maxHeight else MeasureSpec.getSize(
                heightMeasureSpec
            )
        setMeasuredDimension(totalWidth, measuredHeight)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        var usedWidth = 0
        val parentHeight = bottom - top
        val allLabelView = if (labelMore.isVisible) labelViews.plus(labelMore) else labelViews
        // 布局子标签
        for (i in allLabelView.indices) {
            val labelView = allLabelView[i]
            if (!labelView.isVisible) continue

            val childWidth = labelView.measuredWidth
            val childHeight = labelView.measuredHeight

            val childLeft = usedWidth + labelView.marginStart
            // 居中对齐
            val childTop = (parentHeight - childHeight) / 2
            labelView.layout(childLeft, childTop, childLeft + childWidth, childTop + childHeight)
            usedWidth += childWidth + labelView.marginEnd
        }
    }
}

/**
 * 统一处理标签 TextView 样式
 */
internal fun TextView.setLabelTextViewParam(tag: Tag, visible: Boolean) {
    includeFontPadding = true
    text =
        if ("zh_CN" == LocaleUtils.getUserSetLocaleStr(context)) tag.text else tag.enText
    setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
    setTextColor(Color.parseColor(tag.color))
    maxLines = 1
    setPadding(
        DensityUtil.dp2px(context, 4f), DensityUtil.dp2px(context, 1f),
        DensityUtil.dp2px(context, 4f), DensityUtil.dp2px(context, 1f)
    )
    isVisible = visible
    background = DrawableEx.roundSolidRect(
        ColorUtils.addTransparencyToColor(Color.parseColor(tag.color), 0.8f),
        CommonUtils.dp2FloatPx(4)
    )
    val lp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
    lp.rightMargin = DensityUtil.dp2px(context, 8f)
    layoutParams = lp
}

private fun TextView.setLabelMoreText(remainCount: Int) {
    text = "+".plus(remainCount)
}
