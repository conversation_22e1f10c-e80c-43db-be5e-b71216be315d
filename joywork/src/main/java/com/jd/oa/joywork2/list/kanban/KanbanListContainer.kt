package com.jd.oa.joywork2.list.kanban

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.graphics.drawable.PaintDrawable
import android.graphics.drawable.ShapeDrawable.ShaderFactory
import android.graphics.drawable.shapes.RectShape
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue.COMPLEX_UNIT_DIP
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.R
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.view.red
import com.jd.oa.joywork2.list.ListContainerInheritedVM
import com.jd.oa.joywork2.list.WorkListBaseFragment
import com.jd.oa.joywork2.list.WorkListContainerBaseFragment
import com.jd.oa.joywork2.list.calendar.CalendarListContainer.Companion.SUPPORT_SCROLL
import com.jd.oa.joywork2.list.kanban.group.EmptyGroupListener
import com.jd.oa.joywork2.list.kanban.group.newGroup
import com.jd.oa.joywork2.list.kanban.group.showGroupAction
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.JoyWorkViewModel
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.utils.ColorEx
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import java.io.Serializable

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/23 15:38
 */

abstract class KanbanListContainer : WorkListContainerBaseFragment() {

    private val stateContainer by bindView<SwipeRefreshLayout>(R.id.refresh)
    private val emptyView by bindView<View>(R.id.empty_layout)
    private val failView by bindView<View>(R.id.fail_layout)
    protected val tabGroup by bindView<TabLayout>(R.id.tab_groups)
    private val flAddGroup by bindView<FrameLayout>(R.id.fl_add_group)
    private val viewPager by bindView<ViewPager2>(R.id.viewPager)
    private var adapter: KanbanViewPagerAdapter? = null

    protected val conditionViewModel: ConditionViewModel by activityViewModels()
    private val joyWorkViewModel: JoyWorkViewModel by activityViewModels()
    private val containerViewModel: KanbanListContainerVM by viewModels()
    protected val listContainerInheritedVM: ListContainerInheritedVM<String> by viewModels()


    protected val currentField by lazy {
        {
            conditionViewModel.curCondition?.grouper?.fieldEnum
        }
    }

    abstract val isTeamList: Boolean

    override fun onCreateViewSub(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        val root = inflater.inflate(R.layout.jdme_joywork_kanban_list_container, container, false)
        return root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        stateContainer.red(requireActivity())
        flAddGroup.background = background()
        flAddGroup.setOnClickListener(this)
        emptyView.setOnClickListener(this)
        failView.setOnClickListener(this)
        configView()
        initObservers()
        containerViewModel.queryGroups(
            conditionViewModel.curCondition,
            conditionViewModel.mMenuItemFactory?.invoke(),
            isTeamList
        )
        tabGroup.tabRippleColor = null
    }


    open fun configView() {
        if (currentField.invoke() == ConditionGrouper.Field.CUSTOM) {
            showAddGroup()
        } else {
            hideAddGroup()
        }
        listContainerInheritedVM.movable = isCustomSort()
    }

    fun showAddGroup() {
        val layoutParams = tabGroup.layoutParams as LinearLayout.LayoutParams
        flAddGroup.visible()
        layoutParams.rightMargin = DisplayUtils.dip2px(-20f)
        tabGroup.layoutParams = layoutParams
    }

    fun hideAddGroup() {
        val layoutParams = tabGroup.layoutParams as LinearLayout.LayoutParams
        flAddGroup.gone()
        layoutParams.rightMargin = 0
        tabGroup.layoutParams = layoutParams
    }

    fun background(): Drawable {
        val sf: ShaderFactory = object : ShaderFactory() {
            override fun resize(width: Int, height: Int): Shader {
                val y = height.toFloat() / 2
                return LinearGradient(
                    0f, y, width.toFloat(), y,
                    intArrayOf(Color.parseColor("#33FFFFFF"), Color.WHITE),
                    floatArrayOf(0f, 0.4f), Shader.TileMode.MIRROR
                )
            }
        }
        return PaintDrawable().apply {
            shape = RectShape()
            shaderFactory = sf
        }

    }

    override fun initObservers() {
        super.initObservers()
        containerViewModel.refreshState.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            stateContainer.isRefreshing = it
            if (it) {
                emptyView.gone()
                failView.gone()
            }
        }
        containerViewModel.loadGroupsResult.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            if (it.first) {
                if (it.third.isLegalList()) {
                    stateContainer.gone()
                    initTabs(it.third!!)
                } else {
                    failView.gone()
                    emptyView.visible()
                }
            } else {
                emptyView.gone()
                failView.visible()
            }
        }

        conditionViewModel.mConditionValueLiveData.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            if (it !is ConditionState.Condition) return@observe
            configView()
            if (it.grouper.fieldEnum == containerViewModel.currentGrouper) return@observe
            containerViewModel.queryGroups(
                conditionViewModel.curCondition,
                conditionViewModel.mMenuItemFactory?.invoke(),
                isTeamList
            )
        }

        listContainerInheritedVM.detailReturn.observe(viewLifecycleOwner) {
            if (it == null || !it) return@observe
            childFragmentManager.fragments.forEach { fragment ->
                if (fragment is WorkListBaseFragment<*, *>) {
                    fragment.loadData(refresh = true, keepSize = true)
                }
            }
        }
    }

    private fun initTabs(groups: List<Group>) {
        context?.runCatching {
            tabGroup.tabMode = TabLayout.MODE_SCROLLABLE
            tabGroup.setSelectedTabIndicatorHeight(0)
            viewPager.isUserInputEnabled = SUPPORT_SCROLL
            viewPager.offscreenPageLimit = 1
            adapter = KanbanViewPagerAdapter(
                groups.toMutableList(),
                getProjectId(),
                getIsArchiveProject(),
                this@KanbanListContainer
            )
            viewPager.adapter = adapter
            val mediator = TabLayoutMediator(tabGroup, viewPager, tabConfigurationStrategy)
            mediator.attach()
        }
    }

    private val tabConfigurationStrategy = object : TabConfigurationStrategy {
        override fun onConfigureTab(tab: TabLayout.Tab, position: Int) {
            val groups = adapter?.groups ?: return
            runCatching {
                val group = groups[position]
                val tabView = TextView(requireContext()).apply {
                    setTextSize(COMPLEX_UNIT_DIP, 12f)
                    text = if (group.title.isLegalString()) group.title else getDefaultGroupTitle()
                    maxLines = 1
                    maxEms = 10
                    ellipsize = TextUtils.TruncateAt.END
                    gravity = Gravity.CENTER_HORIZONTAL
                    setBackgroundResource(R.drawable.joywork2_status_group_sel)
                    setTextColor(
                        ColorEx.addItem(
                            ColorEx.Item().apply {
                                selected = true
                                color =
                                    ContextCompat.getColor(context, R.color.color_F63218)
                            },
                            ColorEx.Item().apply {
                                selected = false
                                color =
                                    ContextCompat.getColor(context, R.color.color_1B1B1B)
                            },
                        )
                    )
                    setPadding(
                        DisplayUtils.dip2px(12f),
                        DisplayUtils.dip2px(8f),
                        DisplayUtils.dip2px(12f),
                        DisplayUtils.dip2px(8f)
                    )
                }
                tab.setCustomView(tabView)
                tab.tag = group
                if (hasPermission(ProjectPermissionEnum.GROUP) && isCustomGrouper()) {
                    tab.view.setOnLongClickListener {
                        showGroupAction(
                            it.context,
                            getProjectId(),
                            tab.tag as Group,
                            groups,
                            this@KanbanListContainer::onGroupActionSelected,
                            groupActionListener
                        )
                        false
                    }
                }
            }
        }
    }


    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.empty_layout, R.id.fail_layout -> {
                containerViewModel.queryGroups(
                    conditionViewModel.curCondition,
                    conditionViewModel.mMenuItemFactory?.invoke(),
                    isTeamList
                )
            }

            R.id.fl_add_group -> {
                adapter?.groups?.run {
                    val last = get(size - 1)
                    newGroup(v.context, getProjectId(), false, last, groupActionListener)
                }
            }
        }
    }

    final override fun onClickAdd(view: View) {
        super.onClickAdd(view)
        val groups = if (isCustomGrouper()) {
            adapter?.groups
        } else if (joyWorkViewModel.isProjectList) {
            joyWorkViewModel.detailLiveData.value?.groups?.map {
                Group().apply {
                    groupId = it.groupId
                    projectId = it.projectId
                    title = it.title
                    type = it.type
                }
            }?.toMutableList()
        } else {
            null
        }
        runCatching {
            onCreateGroup(view, groups?.getOrNull(viewPager.currentItem), groups)
        }
    }

    abstract fun onCreateGroup(view: View, group: Group?, groups: List<Group>?)


    private val groupActionListener = object : EmptyGroupListener() {

        override fun onSortedGroups(groups: List<Group>) {
            super.onSortedGroups(groups)
            initTabs(groups)
        }

        override fun delGroup(group: Group, isHard: Boolean) {
            super.delGroup(group, isHard)
            val index = getIndexByGroup(group.groupId)
            val currentIndex = viewPager.currentItem
            val newIndex = if (currentIndex < index) {
                currentIndex
            } else if (currentIndex == index) {
                if (index == (adapter?.itemCount ?: 0) - 1) {
                    index - 1
                } else {
                    index
                }
            } else {
                currentIndex - 1
            }
            val newGroups = adapter?.groups ?: mutableListOf()
            newGroups.removeAt(index)
            initTabs(newGroups)
            viewPager.post {
                viewPager.setCurrentItem(newIndex, false)
            }
        }

        override fun afterCreateSuccess(isBefore: Boolean, anchor: Group, newGroup: Group) {
            super.afterCreateSuccess(isBefore, anchor, newGroup)
            val anchorIndex = getIndexByGroup(anchor.groupId)
            if (anchorIndex != -1) {
                val addIndex = if (isBefore) anchorIndex else anchorIndex + 1
                val newGroups = adapter?.groups ?: mutableListOf()
                newGroups.add(addIndex, newGroup)
                initTabs(newGroups)
                viewPager.post {
                    viewPager.setCurrentItem(addIndex, false)
                }
            }
        }

        override fun afterRenameSuccess(group: Group, newName: String) {
            super.afterRenameSuccess(group, newName)
            group.title = newName
            val tabStrip = tabGroup.getChildAt(0) as LinearLayout
            var tab: TabLayout.Tab? = null
            for (i in 0 until tabStrip.childCount) {
                val tabIndex = tabGroup.getTabAt(i)
                val g = tabIndex?.tag as? Group
                if (g?.groupId == group.groupId) {
                    tab = tabIndex
                    break
                }
            }
            tab?.run {
                val textView = customView as? TextView
                textView?.text = newName
                textView?.post {
                    textView.requestLayout()
                }
            }
        }

        private fun getIndexByGroup(groupId: String): Int {
            val tabStrip = tabGroup.getChildAt(0) as LinearLayout
            for (i in 0 until tabStrip.childCount) {
                val tab = tabGroup.getTabAt(i)
                val group = tab?.tag as? Group
                if (group?.groupId == groupId) {
                    return i
                }
            }
            return -1
        }

    }

    fun isCustomSort(): Boolean = conditionViewModel.curCondition?.sorter?.canDraggable() == true

    fun isCustomGrouper(): Boolean = currentField.invoke() == ConditionGrouper.Field.CUSTOM

    abstract fun getDefaultGroupTitle(): String

    abstract fun hasPermission(permissionEnum: ProjectPermissionEnum): Boolean

    abstract fun onGroupActionSelected(action: JoyWorkAction)


    @SuppressLint("NotifyDataSetChanged")
    class KanbanViewPagerAdapter(
        val groups: MutableList<Group>,
        val projectId: String,
        val isArchiveProject: Boolean,
        fragment: Fragment
    ) :
        FragmentStateAdapter(fragment) {

        override fun getItemCount(): Int = groups.size

        override fun createFragment(position: Int): Fragment {
            val group = groups[position]
            val fragment = KanbanListFragment()
            fragment.arguments = Bundle().apply {
                putString("regionType", group.groupId)
                putString("projectId", projectId)
                putSerializable("groups", groups as Serializable)
                putBoolean("isArchiveProject", isArchiveProject)
            }
            return fragment
        }

        fun getItem(position: Int): Group {
            return groups[position]
        }
    }
}