package com.jd.oa.joywork2.menu

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.FrameLayout


class SimpleDrawerLayout(context: Context, attributeSet: AttributeSet) :
    FrameLayout(context, attributeSet) {
    private val mAnimatorDuration = 200L

    private val mScrimColor = Color.parseColor("#80000000")
    private var mOpenOffsetAnimator: ValueAnimator? = null
    private var mCloseOffsetAnimator: ValueAnimator? = null

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {

        for (i in 0 until childCount) {
            val child = getChildAt(i)
            if (i == 0) {
                val lp = child.layoutParams as? MarginLayoutParams
                val topM = lp?.topMargin ?: 0
                val childWidth = child.measuredWidth
                val childHeight = child.measuredHeight
                val childLeft = -childWidth
                child.layout(
                    childLeft, topM, childLeft + childWidth,
                    topM + childHeight
                );
            } else {
                child.layout(0, 0, 0, 0)
            }
        }
    }

    fun openDrawer(view: View) {
        mCloseOffsetAnimator?.cancel()
        if (mOpenOffsetAnimator != null) {
            return
        }
        mOpenOffsetAnimator = ValueAnimator.ofFloat(0F, 1F)
        mOpenOffsetAnimator?.addUpdateListener { valueA ->
            val curValue = valueA.animatedValue as Float
            updateUI(view, curValue)
        }
        mOpenOffsetAnimator?.addListener(object : AnimatorListenerAdapter() {

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                dispatchOnDrawerOpened()
                mOpenOffsetAnimator = null
            }
        })
        mOpenOffsetAnimator?.duration = mAnimatorDuration
        mOpenOffsetAnimator?.start()
    }

    fun closeDrawer(view: View) {
        mOpenOffsetAnimator?.cancel()
        if (mCloseOffsetAnimator != null) {
            return
        }
        mCloseOffsetAnimator = ValueAnimator.ofFloat(1F, 0F)
        mCloseOffsetAnimator?.addUpdateListener { valueA ->
            val curValue = valueA.animatedValue as Float
            updateUI(view, curValue)
        }
        mCloseOffsetAnimator?.addListener(object : AnimatorListenerAdapter() {

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                dispatchOnDrawerClosed()
                mCloseOffsetAnimator = null
            }
        })
        mCloseOffsetAnimator?.duration = mAnimatorDuration
        mCloseOffsetAnimator?.start()
    }

    private fun updateUI(view: View, offset: Float) {
        view.translationX = offset * view.width

        val baseAlpha = mScrimColor and -0x1000000 ushr 24
        val imag = (baseAlpha * offset).toInt()
        val color = imag shl 24 or (mScrimColor and 0xffffff)
        setBackgroundColor(color)
    }

    override fun generateDefaultLayoutParams(): FrameLayout.LayoutParams {
        return LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    override fun generateLayoutParams(p: ViewGroup.LayoutParams?): ViewGroup.LayoutParams {
        return when (p) {
            is LayoutParams -> LayoutParams(p)
            is MarginLayoutParams -> LayoutParams(p)
            else -> LayoutParams(p)
        }
    }

    override fun checkLayoutParams(p: ViewGroup.LayoutParams?): Boolean {
        return p is LayoutParams && super.checkLayoutParams(p)
    }

    override fun generateLayoutParams(attrs: AttributeSet?): FrameLayout.LayoutParams {
        return LayoutParams(context, attrs)
    }

    class LayoutParams : FrameLayout.LayoutParams {
        constructor(c: Context, attrs: AttributeSet) : super(c, attrs)
        constructor(source: LayoutParams) : super(source)
        constructor(width: Int, height: Int) : super(width, height)
        constructor(source: ViewGroup.LayoutParams) : super(source)
        constructor(source: MarginLayoutParams) : super(source)
    }

    private var mListeners: ArrayList<DrawerListener>? = null

    fun addDrawerListener(listener: DrawerListener) {
        if (mListeners == null) {
            mListeners = ArrayList()
        }
        mListeners?.add(listener)
    }

    fun removeDrawerListener(listener: DrawerListener) {
        mListeners?.remove(listener)
    }

    fun dispatchOnDrawerClosed() {
        mListeners?.forEach { it.onDrawerClosed() }
    }

    fun dispatchOnDrawerOpened() {
        mListeners?.forEach { it.onDrawerOpened() }
    }

    interface DrawerListener {

        /**
         * Called when a drawer has settled in a completely open state.
         * The drawer is interactive at this point.
         *
         * @param drawerView Drawer view that is now open
         */
        fun onDrawerOpened()

        /**
         * Called when a drawer has settled in a completely closed state.
         *
         * @param drawerView Drawer view that is now closed
         */
        fun onDrawerClosed()

    }

}