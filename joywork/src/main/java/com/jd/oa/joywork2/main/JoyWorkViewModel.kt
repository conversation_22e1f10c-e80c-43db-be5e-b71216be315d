package com.jd.oa.joywork2.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.ProjectDetail
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainStrategy
import com.jd.oa.joywork.team.AbsRepoCallback
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.ProjectErrorCode
import com.jd.oa.joywork2.list.CustomFieldsRepo

class JoyWorkViewModel : ViewModel() {

    companion object {
        //自定义扩展列字段
        var customFields = mutableListOf<CustomFieldGroup>()
    }

    init {
        loadExtFields()
    }

    // 通过 ViewModel 向底层共享数据
    var mainStrategy: JoyWorkMainStrategy = JoyWorkMainNormalStrategy


    private fun loadExtFields() {
        CustomFieldsRepo.getProjectSysAllExtList { success, customFieldGroupOuter, _ ->
            if (success && customFieldGroupOuter != null) {
                customFields.clear()
                customFields.addAll(customFieldGroupOuter.customFields)
            }
        }
    }

    private val _detailLiveData: MutableLiveData<JoyWorkProjectList.ListDTO?> = MutableLiveData()
    val detailLiveData: LiveData<JoyWorkProjectList.ListDTO?> = _detailLiveData
    fun updateListDTO(listDto: JoyWorkProjectList.ListDTO) {
        _detailLiveData.value = listDto
    }

    fun getProjectDetail(projectId: String) {
        ProjectRepo.getProjectDetail(projectId, object : AbsRepoCallback<ProjectDetail>() {
            override fun result(
                t: ProjectDetail?,
                errorMsg: String?,
                success: Boolean,
                errorCode: Int?
            ) {
                if (success && t != null) {
                    updateListDTO(JoyWorkProjectList.ListDTO().apply {
                        title = t.title
                        permissions = t.permissions
                        groups = t.groups
                        this.projectId = t.projectId
                        status = t.status
                        desc = t.desc
                        type = _detailLiveData.value?.type
                        this.icon = _detailLiveData.value?.icon
                    })
                } else {
                    if (ProjectErrorCode.values()
                            .firstOrNull { it.code == "$errorCode" } != null
                    ) {
                        requestError.postValue(Pair(true, errorMsg))
                    } else {
                        requestError.postValue(Pair(false, errorMsg))
                    }
                }
            }
        })
    }

    //是否是清单页面（团队待办）
    var isProjectList = false

    fun init(isProjectList: Boolean) {
        this.isProjectList = isProjectList
    }

    //<finish, error>
    val requestError = MutableLiveData<Pair<Boolean, String?>>()


}

