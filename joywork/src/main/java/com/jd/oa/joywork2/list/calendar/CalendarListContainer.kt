package com.jd.oa.joywork2.list.calendar

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.haibin.calendarview.Calendar
import com.haibin.calendarview.CalendarLayout
import com.haibin.calendarview.CalendarView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDraft
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork2.list.ListContainerInheritedVM
import com.jd.oa.joywork2.list.WorkListBaseFragment
import com.jd.oa.joywork2.list.WorkListContainerBaseFragment
import com.jd.oa.joywork2.list.calendar.view.CalendarHandleLayout
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.invisible
import com.jd.oa.utils.visible

/**
 * 日历列表
 */
abstract class CalendarListContainer : WorkListContainerBaseFragment() {

    companion object {
        const val SUPPORT_SCROLL = true
    }

    val containerViewModel: CalendarListContainerVM by viewModels()
    protected val listContainerInheritedVM: ListContainerInheritedVM<Calendar> by viewModels()
    var startDay = 1
    protected val sp by lazy {
        JoyWorkPreference(requireActivity())
    }

    val viewPager by bindView<ViewPager2>(R.id.viewPager)

    private val calendarLayout by bindView<CalendarLayout>(R.id.calendar_container)
    val calendarView by bindView<CalendarView>(R.id.calendar_view)
    private val handleIndicator by bindView<ImageView>(R.id.handle_indicator)

    val tvYearMonth by bindView<TextView>(R.id.tv_ym)
    private val weeArea by bindView<View>(R.id.week_area)
    val tvWeek by bindView<TextView>(R.id.tv_week)
    private val monthArrow by bindView<TextView>(R.id.arrow_month)

    val helper = CalendarHelper()

    //监听日历设置变化
    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            intent?.runCatching {
                val params = intent.getStringExtra("params")
                val action = JsonUtils.getKeyValue(params, "action")
                if ("settingUpdated" == action) {
                    val flutterPrefs: SharedPreferences? =
                        context?.getSharedPreferences(
                            "jdme_${PreferenceManager.UserInfo.getTimlineAppID()}_${PreferenceManager.UserInfo.getUserName()}_FlutterSharedPreferences",
                            Context.MODE_PRIVATE
                        )
                    startDay =
                        flutterPrefs?.getInt("flutter.calendarSet_joyday_start_of_week", 1) ?: 1
                    setWeekFirstDay()
                }
            }
        }
    }

    private val adapter: CalendarViewPagerAdapter by lazy {
        CalendarViewPagerAdapter(helper, getProjectId(), getIsArchiveProject(), this)
    }

    override fun onCreateViewSub(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val root = inflater.inflate(R.layout.jdme_joywork_calendar_container, container, false)
//        runCatching {
//            val filter = IntentFilter()
//            filter.addAction("sendAction")
//            LocalBroadcastManager.getInstance(requireContext()).registerReceiver(receiver, filter)
//        }
        return root
    }

    override fun onDestroyView() {
        super.onDestroyView()
//        runCatching {
//            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(receiver)
//        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val yearArea = view.findViewById<View>(R.id.ym_area)
        yearArea.setOnClickListener(this)
        val calendarHandle = view.findViewById<CalendarHandleLayout>(R.id.calendar_handle)
        calendarHandle.setOnClickListener(this)
        calendarHandle.calendarHandleTouchListener =
            object : CalendarHandleLayout.CalendarHandleTouchListener {
                override fun onMoveSomeDistance(up: Boolean) {
                    if (up && calendarLayout.isExpand) {
                        yearArea?.run {
                            onClick(yearArea)
                        }
                    }
                    if (!up && !calendarLayout.isExpand) {
                        yearArea?.run {
                            onClick(yearArea)
                        }
                    }
                }
            }

        view.findViewById<View>(R.id.last_week).setOnClickListener(this)
        view.findViewById<View>(R.id.next_week).setOnClickListener(this)
        tvYearMonth.text = helper.currentMonth()
        val flutterPrefs: SharedPreferences? =
            context?.getSharedPreferences(
                "jdme_${PreferenceManager.UserInfo.getTimlineAppID()}_${PreferenceManager.UserInfo.getUserName()}_FlutterSharedPreferences",
                Context.MODE_PRIVATE
            )
//        startDay = flutterPrefs?.getInt("flutter.calendarSet_joyday_start_of_week", 1) ?: 1
        setWeekFirstDay()
        tvWeek.text = helper.currentWeek(helper.today, startDay == 1)
        listContainerInheritedVM.movable = false
        containerViewModel.loadHolidays(view.context)
        handleIndicator.setImageResource(R.drawable.joywork_calendar_expand)
        viewPager.isUserInputEnabled = SUPPORT_SCROLL
        viewPager.offscreenPageLimit = 1
        viewPager.adapter = adapter
        viewPager.setCurrentItem(helper.getMiddleValue(), false)
        viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {

            var previousState: Int = -1

            //是否是用户滑动切换页面
            var userScrollChange = false

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                val calendar = helper.getTargetCalendar(position)
                if (userScrollChange) {
                    calendarView.scrollToCalendar(calendar.year, calendar.month, calendar.day)
                }

            }

            override fun onPageScrollStateChanged(state: Int) {
                if (previousState == ViewPager2.SCROLL_STATE_DRAGGING &&
                    state == ViewPager2.SCROLL_STATE_SETTLING
                ) {
                    userScrollChange = true
                } else if (previousState == ViewPager2.SCROLL_STATE_SETTLING &&
                    state == ViewPager2.SCROLL_STATE_IDLE
                ) {
                    userScrollChange = false
                }
                previousState = state
            }
        })
        calendarView.setOnCalendarSelectListener(object : CalendarView.OnCalendarSelectListener {
            override fun onCalendarOutOfRange(calendar: Calendar?) {
            }

            override fun onCalendarSelect(
                calendar: Calendar?,
                isClick: Boolean
            ) {
                calendar?.run {
                    viewPager.setCurrentItem(helper.getTargetPosition(this), SUPPORT_SCROLL)
                    tvYearMonth.text = helper.currentMonth(this)
                    tvWeek.text = helper.currentWeek(this, startDay == 1)
                    context?.run {
                        containerViewModel.loadHolidays(this)
                    }
                    mAddView?.takeIf {
                        showAdd()
                    }?.run {
                        if (calendar < helper.today && isEnabled) {
                            this.isEnabled = false
                            findViewById<View>(R.id.add_foreground).visible()
                        } else if (calendar >= helper.today && !isEnabled) {
                            this.isEnabled = true
                            findViewById<View>(R.id.add_foreground).gone()
                        }
                    }
                }
            }
        })
        calendarView.setOnViewChangeListener { isMonthView ->
            if (isMonthView) {
                handleIndicator.setImageResource(R.drawable.joywork_calendar_expand)
                weeArea.invisible()
            } else {
                handleIndicator.setImageResource(R.drawable.joywork_calendar_close)
                weeArea.visible()
            }
            sp.put(JoyWorkPreference.KV_ENTITY_CALENDAR_VIEW_WEEK, !isMonthView)
        }

        val showWeek = sp.get(JoyWorkPreference.KV_ENTITY_CALENDAR_VIEW_WEEK)
        if (showWeek) {
            calendarLayout.shrink(0)
            monthArrow.rotation = 180f
        }
        initObservers()
    }

    override fun initObservers() {
        super.initObservers()
        containerViewModel.holidaysLD.observe(viewLifecycleOwner) {
            if (it == null || it.isEmpty()) return@observe
            calendarView.addSchemeDate(it)
        }
        listContainerInheritedVM.detailReturn.observe(viewLifecycleOwner) {
            if (it == null || !it) return@observe
            childFragmentManager.fragments.forEach { fragment ->
                if (fragment is WorkListBaseFragment<*, *>) {
                    fragment.loadData(refresh = true, keepSize = true)
                }
            }
        }
    }

    override fun onClick(view: View?) {
        view?.runCatching {
            when (id) {
                R.id.calendar_handle, R.id.ym_area -> {
                    val rotation = if (calendarLayout.isExpand) {
                        calendarLayout.shrink()
                        180f
                    } else {
                        calendarLayout.expand()
                        0f
                    }
                    monthArrow.animate()
                        .setDuration(200)
                        .rotation(rotation)
                        .start()
                }

                R.id.last_week -> {
                    val calendar = helper.jumpWeek(calendarView.selectedCalendar, false)
                    calendarView.scrollToCalendar(calendar.year, calendar.month, calendar.day)
                }

                R.id.next_week -> {
                    val calendar = helper.jumpWeek(calendarView.selectedCalendar, true)
                    calendarView.scrollToCalendar(calendar.year, calendar.month, calendar.day)
                }

                else -> {

                }
            }
        }
    }

    class CalendarViewPagerAdapter(
        val helper: CalendarHelper,
        val projectId: String,
        val isArchiveProject: Boolean,
        fragment: Fragment
    ) :
        FragmentStateAdapter(fragment) {

        override fun getItemCount(): Int = helper.getMaxValue()

        override fun createFragment(position: Int): Fragment {
            val fragment = CalendarListFragment()
            fragment.arguments = Bundle().apply {
                putSerializable("date", helper.getTargetCalendar(position))
                putString("projectId", projectId)
                putBoolean("isArchiveProject", isArchiveProject)
            }
            return fragment
        }
    }

    final override fun onClickAdd(view: View) {
        val draft = JoyWorkShortcutDraft.newDraft()
        val selectedCalendar = calendarView.selectedCalendar
        draft.endTime = helper.createWorkOnDay(selectedCalendar)
        onClickAddOnDay(view, selectedCalendar)
    }

    abstract fun onClickAddOnDay(view: View, calendar: Calendar)

    abstract val isProject: Boolean

    private fun setWeekFirstDay() {
        if (startDay == 1)
            calendarView.setWeekStarWithMon()
        else
            calendarView.setWeekStarWithSun()
    }
}


