package com.jd.oa.joywork2.view

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.FrameLayout
import android.widget.PopupWindow
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.adapter.FloatMenuAdapter
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.utils.DisplayUtils


object WorkFloatMenu {

    @JvmStatic
    fun showViewTypeMenu(
        activity: Activity,
        curShowType: TodoShowType,
        anchorView: View,
        items: List<TodoShowType>,
        menuItem: MenuItem?,
        condition: ConditionState.Condition?,
        onItemClickListener: (TodoShowType) -> Unit
    ) {
        val contentView: View =
            LayoutInflater.from(activity).inflate(R.layout.float_pop_menu, null)
        val recyclerView = contentView.findViewById<RecyclerView>(R.id.recycle)
        val popupWindow = PopupWindow(anchorView.context)
        popupWindow.contentView = contentView
        // 如果不设置PopupWindow的背景，有些版本就会出现一个问题：无论是点击外部区域还是Back键都无法dismiss弹框
        popupWindow.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // setOutsideTouchable设置生效的前提是setTouchable(true)和setFocusable(false)
        popupWindow.isTouchable = true
        popupWindow.isFocusable = true
        popupWindow.isOutsideTouchable = true
        popupWindow.showAsDropDown(anchorView, 0, 0)
        recyclerView.layoutManager = LinearLayoutManager(activity)
        val newItems = if ((menuItem == MenuItem.MyAssign || menuItem == MenuItem.MyCooperation
                    || menuItem is MenuItem.Custom)
            && condition?.status == TaskStatusEnum.FINISH) {
            items.filterNot { it.value == TodoShowType.KANBAN.value }
        } else items
        recyclerView.adapter = FloatMenuAdapter(activity, curShowType, newItems) {
            popupWindow.dismiss()
            onItemClickListener.invoke(it)
        }
        contentView.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                autoAdjustArrowPos(contentView, anchorView)
                contentView.viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        })
    }


    fun autoAdjustArrowPos(contentView: View, anchorView: View) {
        val arrow = contentView.findViewById<View>(R.id.arrow_up)
        val width: Int = DisplayUtils.dip2px(20f)
        val pos = IntArray(2)
        contentView.getLocationOnScreen(pos)
        val popLeftPos = pos[0]
        anchorView.getLocationOnScreen(pos)
        val anchorLeftPos = pos[0]
        val arrowLeftMargin = anchorLeftPos - popLeftPos + anchorView.width / 2 - width / 2
        val downArrowParams = arrow.layoutParams as FrameLayout.LayoutParams
        downArrowParams.leftMargin = arrowLeftMargin
        arrow.layoutParams = downArrowParams
    }

}