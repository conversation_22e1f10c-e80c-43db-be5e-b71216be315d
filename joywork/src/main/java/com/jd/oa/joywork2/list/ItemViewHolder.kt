package com.jd.oa.joywork2.list

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.team.view.DividerLinearLayout
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

abstract class ItemViewHolder(
    val view: View,
    private val mClick: (JoyWork) -> Unit,
) : RecyclerView.ViewHolder(
    view
), Exchangeable {

    override var exchangeable = true

    companion object {
        private var lastClickTime: Long = 0
    }

    val itemClick = View.OnClickListener {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime > 1000) {
            val task = it.getTag(R.id.jdme_tag_id) as JoyWork
            mClick(task)
            lastClickTime = currentTime
        }
    }

}


class TableItemViewHolder(
    private val context: Context,
    parent: ViewGroup,
    private val mCeilProcessors: List<CeilProcessor>,
    private val mAdapter: RecyclerView.Adapter<*>,
    mClick: (JoyWork) -> Unit,
) : ItemViewHolder(
    context.inflater.inflate(
        R.layout.joywork2_list_item,
        parent,
        false
    ),
    mClick
), Exchangeable {

    override var exchangeable = true

    private val itemContainer by lazy { itemView.findViewById<DividerLinearLayout>(R.id.item_container) }
    private val cover by lazy { itemView.findViewById<View>(R.id.cover) }

    fun bind(joyWork: JoyWork, data: ArrayList<Any>) {
//        if (data.indexOf(joyWork) == 0 || data.indexAtWithNull(data.indexOf(joyWork) - 1)
//                ?.isJoyWork() == true
//        ) {
//            itemContainer.setShowTop(false)
//        } else {
//            itemContainer.setShowTop(true)
//        }

        itemView.setTag(R.id.jdme_tag_id, joyWork)
        itemView.setOnClickListener(itemClick)

        if (showCover(joyWork)) {
            cover.visible()
        } else {
            cover.gone()
        }

        mCeilProcessors.forEach {
            it.createItem(itemContainer)
            it.bindItem(context, itemContainer, joyWork, this, mAdapter)
        }
    }

    private fun showCover(joyWork: JoyWork): Boolean {
        return joyWork.isDeleted
    }
}