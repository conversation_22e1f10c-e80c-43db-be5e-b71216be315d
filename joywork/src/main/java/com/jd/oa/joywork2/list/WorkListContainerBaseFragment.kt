package com.jd.oa.joywork2.list

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork2.main.JoyWorkViewModel
import com.jd.oa.joywork2.menu.JoyWork2MenuViewModel
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.clickEvent

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/16 14:28
 */
abstract class WorkListContainerBaseFragment : BaseFragment() {

    val mMainViewModel: JoyWorkViewModel by activityViewModels()
    private val mMenuViewModel: JoyWork2MenuViewModel by activityViewModels()

    protected var mAddView: RelativeLayout? = null

    private var addView: RelativeLayout? = null
    private val mAddClick = View.OnClickListener {
        onClickAdd(it)
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.INCOMPLETE_CLICK1
            )
        }
    }

    abstract fun onCreateViewSub(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View?

    final override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val containerRoot: FrameLayout? =
            inflater.inflate(
                R.layout.joywork_work_list_container_frg,
                container,
                false
            ) as? FrameLayout
        val content = onCreateViewSub(inflater, containerRoot, savedInstanceState)
        containerRoot?.addView(content)
        if (showAdd()) {
            val addRoot = inflater.inflate(R.layout.joywork2_fragment_mine, container, false)
            containerRoot?.addView(addRoot)
            addView = addRoot.findViewById<RelativeLayout?>(R.id.add).apply {
                mAddView = this
                setOnClickListener(mAddClick)
                val lp = (layoutParams as? ViewGroup.MarginLayoutParams)
                    ?: ViewGroup.MarginLayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                val strategy = mMainViewModel.mainStrategy
                lp.apply {
                    rightMargin =
                        requireActivity().resources.getDimensionPixelSize(strategy.addViewRightMargin)
                    bottomMargin =
                        requireActivity().resources.getDimensionPixelSize(strategy.addViewBottomMargin)
                }
                layoutParams = lp
            }
        }

        return containerRoot
    }

    open fun showAdd(): Boolean = true

    open fun onClickAdd(view: View) {
    }

    open fun initObservers() {
        mMenuViewModel.mSelectMenuItemLiveData.observe(viewLifecycleOwner) {
            addView?.isVisible = it.canCreateTask
        }
    }

    abstract fun getProjectId(): String

    abstract fun getTaskListType(): TaskListTypeEnum

    abstract fun getIsArchiveProject(): Boolean
}