package com.jd.oa.joywork2.list.kanban

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork2.list.WorkListBaseFragment
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.bean.ConditionFilter
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.joywork2.main.bean.ConditionSorter
import com.jd.oa.joywork2.menu.MenuItem

/**
 * 看板列表
 */
class KanbanListFragment : WorkListBaseFragment<String, KanbanListVM>() {

    override val viewModel: KanbanListVM by viewModels()

    override val menuItemFactory: (() -> MenuItem)? by lazy {
        conditionViewModel.mMenuItemFactory
    }

    override val conditionFactory: () -> ConditionState.Condition by lazy {
        {
            conditionViewModel.curCondition ?: ConditionState.Condition(
                filter = ConditionFilter.getDefaultInstance(),
                status = TaskStatusEnum.UN_FINISH,
                sorter = ConditionSorter(ConditionSorter.Field.END_TIME, ConditionSorter.Order.ASC),
                grouper = ConditionGrouper(ConditionGrouper.Field.CUSTOM),
                listColumns = null,
                operator = null,
                isDefault = true
            )
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        regionType = arguments?.getString("regionType") ?: ""
        projectId = arguments?.getString("projectId") ?: ""
        groups = arguments?.getSerializable("groups") as? List<Group>
        keyData = regionType
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        conditionViewModel.curCondition?.grouper?.fieldEnum?.run {
            viewModel.grouperField = this
        }
        conditionViewModel.mConditionValueLiveData.observe(viewLifecycleOwner) { conditionState ->
            if (conditionState == null) return@observe
            if (conditionState !is ConditionState.Condition) return@observe
            viewModel.grouperField = conditionState.grouper.fieldEnum
            loadData(true)
        }
    }

    override fun keyDateEquals(other: String): Boolean = other == keyData

}


