package com.jd.oa.joywork2.list.kanban

import android.os.Bundle
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commit
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.ProjectDelete
import com.jd.oa.joywork.ProjectNewAfter
import com.jd.oa.joywork.ProjectNewBefore
import com.jd.oa.joywork.ProjectRename
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.string
import com.jd.oa.utils.visible

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/23 16:59
 */
class TeamKanbanListContainer : KanbanListContainer() {

    companion object {
        const val TAG = "TeamKanbanListContainer"

        fun loadList(
            fragmentManager: FragmentManager,
            layoutId: Int,
            projectId: String,
            forceReplace: Boolean = false,
            isArchiveProject: Boolean = false
        ) {
            if (fragmentManager.findFragmentByTag(TAG) == null || forceReplace) {
                fragmentManager.commit(true) {
                    setReorderingAllowed(true)
                    replace(
                        layoutId,
                        TeamKanbanListContainer().also {
                            it.arguments = Bundle().apply {
                                putString("projectId", projectId)
                                putBoolean("isArchiveProject", isArchiveProject)
                            }
                        },
                        TAG
                    )
                }
            }
        }
    }

    override val isTeamList: Boolean = true

    override fun onCreateGroup(view: View, group: Group?, groups: List<Group>?) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.isProject = true
        tmpData.taskListTypeEnum = getTaskListType()
        tmpData.selectGroupId = group?.groupId
        val dialogO =
            JoyWorkShortcutCreator.getShortcutDialog(
                requireActivity(),
                tmpData = tmpData,
                showGroup = true,
                projectId = getProjectId(),
                groups = groups,
                conditionViewModel.viewIdFactory?.invoke(),
                conditionViewModel.viewTypeFactory?.invoke()
            )
        dialogO.showSnackBar = true
        dialogO.successCallback = { result, value, dialog ->
            dialog?.dismiss()
            listContainerInheritedVM.onCreateWorkSuccess(value.groupId, result)
        }
        dialogO.show()
    }

    override fun initObservers() {
        super.initObservers()
        mMainViewModel.detailLiveData.observe(viewLifecycleOwner) {
            configView()
        }
    }

    override fun configView() {
        mAddView?.runCatching {
            if (hasPermission(ProjectPermissionEnum.TASK)) {
                visible()
            } else {
                gone()
            }
        }

        if (!hasPermission(ProjectPermissionEnum.GROUP)
            || currentField.invoke() != ConditionGrouper.Field.CUSTOM
        ) {
            hideAddGroup()
        } else {
            showAddGroup()
        }
        listContainerInheritedVM.movable =
            hasPermission(ProjectPermissionEnum.TASK) && isCustomSort()
    }

    override fun getDefaultGroupTitle(): String {
        return context?.string(R.string.joywork_task_link_untitled_group) ?: ""
    }

    override fun getProjectId(): String = arguments?.getString("projectId") ?: ""

    override fun getTaskListType(): TaskListTypeEnum = TaskListTypeEnum.PROJECT

    override fun getIsArchiveProject(): Boolean = arguments?.getBoolean("isArchiveProject") ?: false

    override fun hasPermission(permissionEnum: ProjectPermissionEnum): Boolean {
        val permissions = mMainViewModel.detailLiveData.value?.permissions ?: return false
        return permissions.contains(permissionEnum.code)
    }

    override fun onGroupActionSelected(action: JoyWorkAction) {
        when (action) {
            is ProjectRename -> {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_MORE_RENAME
                )
            }

            is ProjectNewAfter -> {
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_INSERTGROUPRIGHT
                    )
                }
            }

            is ProjectNewBefore -> {

                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_INSERTGROUPLEFT
                    )
                }
            }

            is ProjectDelete -> {

                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_MORE_DELETE
                    )
                }
            }
        }
    }
}