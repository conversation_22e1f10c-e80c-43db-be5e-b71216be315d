package com.jd.oa.joywork2.main


import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.chenenyu.router.annotation.Route
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.EntranceType
import com.jd.oa.joywork.JoyWorkCommonConstant
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.joywork.team.TeamMainFragment
import com.jd.oa.joywork.utils.getParamsKey
import com.jd.oa.joywork2.list.JoyWork2GroupFragment
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.list.calendar.SelfCalendarListContainer
import com.jd.oa.joywork2.list.kanban.SelfKanbanListContainer
import com.jd.oa.joywork2.menu.JoyWork2MenuViewModel
import com.jd.oa.joywork2.menu.MenuFragment
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.joywork2.menu.MenuRepo
import com.jd.oa.listener.Refreshable
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME
import com.jd.oa.theme.manager.ThemeApi
import com.jd.oa.theme.view.JoyWorkTheme
import com.jd.oa.utils.JoyWorkUtils.joyWorkLog
import com.jd.oa.utils.StatusBarConfig
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.safeLaunch
import com.jd.oa.utils.visible
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

/**
 * 将整个界面分为不同的 fragment
 * 侧边栏的菜单统一使用 MenuItem 表示，内部会将我处理的、我指派的、我关注的、我的清单等几个固定项单独实现
 */
@Route(DeepLink.JOY_WORK_LIST_NEW, DeepLink.JOY_WORK_LIST)
class JoyWorkFragment : BaseFragment(), Refreshable, View.OnClickListener {

    companion object {
        const val ACTION_PROJECT_LIST = "projectList"
    }

    private var mMenuContainer: ViewGroup? = null

    private val mMenuViewModel: JoyWork2MenuViewModel by activityViewModels()
    private val mTabViewModel: Tab2ViewModel by activityViewModels()
    private val mMainViewModel: JoyWorkViewModel by activityViewModels()
    private val conditionViewModel: ConditionViewModel by activityViewModels()


    private var mStrategy: JoyWorkMainStrategy = JoyWorkMainNormalStrategy

    private val finishReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            lifecycleScope.safeLaunch {
                delay(1000)
            }
        }
    }

    private val mThemeDataChangeObserver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            mStrategy.onThemeDataChange(activity?.findViewById(R.id.ll_title_container))
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        kotlin.runCatching {
            (requireActivity() as? FunctionActivity)?.setBarHide()
        }
        return inflater.inflate(R.layout.joywork2_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initTitle(view)
        initObserver()
        initTabsAndCondition()
        initViewModels()
        showProjectList()
    }

    private fun initViewModels() {
        mMenuViewModel.mSelectMenuItemLiveData.observe(viewLifecycleOwner) {
            if (it == MenuItem.MyProjectList) { // 我的清单单独写，使用原来的界面
                if (childFragmentManager.findFragmentByTag(TeamMainFragment.FRG_TAG) != null) {
                    return@observe
                }
                childFragmentManager.commit(true) {
                    setReorderingAllowed(true)
                    replace(
                        R.id.list_container,
                        TeamMainFragment.getInstance(mStrategy == JoyWorkMainTabStrategy),
                        TeamMainFragment.FRG_TAG
                    )
                }
            } else {
                loadViewByType(it)
            }
        }
        lifecycleScope.launch {
            mMenuViewModel.updateSelectMenuItem(parseRouter())
        }
        mTabViewModel.mToggleMenuLiveData.observe(viewLifecycleOwner) {
            val a = activity ?: return@observe
            val fragmentManager = a.supportFragmentManager
            ensureMenuContainer()
            val group = mMenuContainer
                ?: return@observe // Unable to find the ViewGroup for loading the fragment.
            group.visible()
            if (fragmentManager.findFragmentByTag(MenuFragment.FRG_TAG) != null) {
                return@observe
            }
            fragmentManager.commit(true) {
                setReorderingAllowed(true)
                replace(group.id, MenuFragment(), MenuFragment.FRG_TAG)
            }
            fragmentManager.commit(true) {
                setReorderingAllowed(true)
                replace(
                    group.id,
                    MenuFragment(),
                    MenuFragment.FRG_TAG
                )
            }
        }

        conditionViewModel.showTypeLiveData.observe(viewLifecycleOwner) {
            if (it == null) {
                return@observe
            }
            val menuItem = mMenuViewModel.mSelectMenuItemLiveData.value
            menuItem?.run {
                loadViewByType(this)
            }
        }

    }

    private fun loadViewByType(menuItem: MenuItem) {
        val showType = conditionViewModel.showTypeLiveData.value
        when (showType?.second) {
            TodoShowType.TABLE, TodoShowType.CARD -> {
                loadJoyWorkBaseFragment(menuItem)
            }

            TodoShowType.CALENDAR -> {
                loadCalendarFragment(menuItem)
            }

            TodoShowType.KANBAN -> {
                loadKanbanFragment(menuItem)
            }

            else -> {
                loadJoyWorkBaseFragment(menuItem)
            }
        }
    }

    private fun loadJoyWorkBaseFragment(menuItem: MenuItem) {
        // 此处需要根据不同的菜单项使用不同的实例
        // 为啥不用同一实例？因为菜单项不同，Fragment 内部的逻辑可能不同（特别是网络请求）
        // 如果用同一个，就需要在此处更新 Fragment 内部的不同状态，太麻烦
        val tag = "${JoyWork2GroupFragment.FRG_TAG}#${menuItem.getViewId()}"
        if (childFragmentManager.findFragmentByTag(tag) != null) {
            return
        }
        childFragmentManager.commit(true) {
            setReorderingAllowed(true)
            replace(
                R.id.list_container,
                JoyWork2GroupFragment(),
                tag
            )
        }
    }

    private fun loadCalendarFragment(menuItem: MenuItem) {
        val tag = "${SelfCalendarListContainer.TAG}#${menuItem.getViewId()}"
        if (childFragmentManager.findFragmentByTag(tag) != null) {
            return
        }
        childFragmentManager.commit(true) {
            setReorderingAllowed(true)
            replace(
                R.id.list_container,
                SelfCalendarListContainer(),
                tag
            )
        }
    }

    private fun loadKanbanFragment(menuItem: MenuItem) {
        val tag = "${SelfKanbanListContainer.TAG}#${menuItem.getViewId()}"
        if (childFragmentManager.findFragmentByTag(tag) != null) {
            return
        }
        childFragmentManager.commit(true) {
            setReorderingAllowed(true)
            replace(
                R.id.list_container,
                SelfKanbanListContainer(),
                tag
            )
        }
    }

    private suspend fun parseRouter(): MenuItem {
        val action = EntranceType.valueByCode(
            arguments.getParamsKey(
                "action",
                dv = EntranceType.MY_HANDLE.code
            )
        )
        return when (action) {
            EntranceType.MY_HANDLE -> {
                clickEvent(JoyWorkConstant.MOBILE_EVENT_WORKBENCH_TASK_MY_MYEXECUTION)
                MenuItem.MyHandle
            }

            EntranceType.MY_COOPERATE -> {
                clickEvent(JoyWorkConstant.MOBILE_EVENT_WORKBENCH_TASK_MY_MYFOLLOW)
                MenuItem.MyCooperation
            }

            EntranceType.MY_ASSIGN -> MenuItem.MyAssign

            else -> {
                val viewId = listOf(
                    PreferenceManager.UserInfo.getUserId(),
                    PreferenceManager.UserInfo.getTeamId(),
                    action.code
                ).joinToString(separator = "_\$_")
                val menuItemList = withContext(Dispatchers.Default) {
                    runCatching {
                        MenuRepo.getMyHandle()
                    }.getOrNull()
                }
                val menuBean = menuItemList?.firstOrNull { it.id == viewId } ?: return MenuItem.MyHandle
                MenuItem.ExpandMenuItem(menuBean)
            }
        }
    }

    private fun initTitle(view: View) {
        val result = kotlin.runCatching {
            val jsonObject = JSONObject(requireArguments().getString("mparam") as String)
            mStrategy =
                if (jsonObject.get("isTab") == "1") JoyWorkMainTabStrategy else JoyWorkMainNormalStrategy
        }
        if (result.isFailure) {
            mStrategy = JoyWorkMainNormalStrategy
        }
        mStrategy.handleTitle(view.findViewById(R.id.ll_title_container), requireActivity())
        mMainViewModel.mainStrategy = mStrategy
        mMainViewModel.init(false)
    }

    //加载tab和条件布局
    private fun initTabsAndCondition() {
        if (childFragmentManager.findFragmentByTag(Tab2Fragment.FRG_TAG) == null) {
            childFragmentManager.commit(true) {
                setReorderingAllowed(true)
                replace(R.id.tabs_container, Tab2Fragment(), Tab2Fragment.FRG_TAG)
            }
        }
        JoyConditionFragment.tryLoadConditionView(childFragmentManager, R.id.condition_container)
    }

    private fun initObserver() {
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(
            finishReceiver,
            IntentFilter(ProjectConstant.FINISH_ACTION)
        )
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(
            finishReceiver,
            IntentFilter(ProjectConstant.UNFINISH_ACTION)
        )
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(
            mThemeDataChangeObserver,
            IntentFilter(ACTION_CHANGE_THEME)
        )
    }

    private fun showProjectList() {
        kotlin.runCatching {
            if (arguments?.containsKey("mparam") == true) {
                val mparam = arguments?.getString("mparam")
                val jsonObject = JSONObject(mparam)
                val action = jsonObject.optString("action")
                if (TextUtils.equals(action, ACTION_PROJECT_LIST)) {
                    lifecycleScope.safeLaunch {
                        delay(500)
                        clickEvent(JoyWorkConstant.MOBILE_EVENT_WORKBENCH_TASK_LIST_CHECKMORE)
                        mTabViewModel.openMenu()
                    }
                }
            }
        }
    }

    override fun refresh() {

    }

    override fun onDestroyView() {
        if (mStrategy is JoyWorkMainNormalStrategy) {
            // 任务界面返回时，提示刷新工作台任务卡片
            LocalBroadcastManager.getInstance(requireActivity())
                .sendBroadcast(Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK))
        }
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(finishReceiver)
        LocalBroadcastManager.getInstance(requireContext())
            .unregisterReceiver(mThemeDataChangeObserver)
        super.onDestroyView()
    }

    override fun onResume() {
        super.onResume()
        if (mStrategy is JoyWorkMainTabStrategy) {
            val data = JoyWorkTheme.currentTheme
            if (data.isGlobal.isTrue() && data.isDarkTheme) {
                ThemeApi.checkAndSetDarkTheme(activity)
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(activity)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        val a = activity ?: return
        if (mStrategy is JoyWorkMainTabStrategy) {
            if (StatusBarConfig.enableImmersive()) {
                QMUIStatusBarHelper.setStatusBarLightMode(a)
            }
        }
    }

    private fun ensureMenuContainer() {
        if (mMenuContainer != null) {
            return
        }
        val a = activity
        // joywork_drawer_menu_container
        val containerTemp = a?.findViewById<View>(R.id.joywork_drawer_menu_container)
        if (containerTemp is ViewGroup) {
            mMenuContainer = containerTemp
            return
        }
        val decorView = a?.window?.decorView as? ViewGroup
        if (decorView == null) {
            joyWorkLog("can not get container View")
            return
        }
        val container = FrameLayout(a)
        container.id = R.id.joywork_drawer_menu_container
        container.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        decorView.addView(container)
        mMenuContainer = container
    }
}