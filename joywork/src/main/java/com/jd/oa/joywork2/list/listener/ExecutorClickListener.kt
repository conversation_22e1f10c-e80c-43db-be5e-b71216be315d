package com.jd.oa.joywork2.list.listener

import android.app.Activity
import android.content.Intent
import android.view.View
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.canMarkOtherFinish
import com.jd.oa.joywork.detail.canMarkOtherUnFinish
import com.jd.oa.joywork.detail.canTransfer
import com.jd.oa.joywork.detail.canUpdateChief
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.Owner
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.detail.fromMembers
import com.jd.oa.joywork.detail.fromOwner
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.executor.ExecutorStateListActivity
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.inflateIntent
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.permission_addable
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.permission_deletable
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.permission_ex_chief
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.permission_finishable
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.permission_transfer
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.permission_unfinishable
import com.jd.oa.joywork.executor.ExecutorStateListActivity.Companion.permission_urgeable
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.ToastUtils

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/13 00:55
 */
class ExecutorClickListener(
    val work: JoyWork,
    val position: Int,
    val callback: (Int, JoyWork) -> Unit
) : View.OnClickListener {

    override fun onClick(view: View?) {
        if (view == null) return
        if (!work.owners.isLegalList()) {
            jumpToSelectorContact(view)
        } else {
            jumpToExecutorList(view)
        }
    }

    private fun jumpToSelectorContact(view: View?) {
        if (view == null) return
        val selected = ArrayList<MemberEntityJd>()
        work.owners?.forEach {
            val jd = MemberEntityJd()
            jd.fromUser(it)
            selected.add(jd)
        }
        ExecutorUtils.selectExecutors(
            activity = view.context as Activity,
            selected = selected,
            sessionId = null,
            needSetOwner = false,
            onFailure = {
            },
            onSuccess = { bean ->
                if (bean == null) {
                    return@selectExecutors
                }
                val members: MutableList<Members> = ArrayList()
                for (memberEntityJd in bean) {
                    val member = Members()
                    member.fromDD(memberEntityJd!!)
                    members.add(member)
                }
                JoyWorkRepo.addRelation(
                    members,
                    taskId = work.taskId,
                    actionSource = null,
                    onSuccess = { ms ->
                        if (ms.isLegalList()) {
                            ms.forEach {
                                val user = JoyWorkUser()
                                user.fromMembers(it)
                                work.owners.add(user)
                            }
                        }
                        callback(position, work)
                    },
                    onFailure = { msg ->
                        ToastUtils.showToast(view.context, msg)
                    }
                )
            }
        )
    }

    private fun jumpToExecutorList(view: View) {
        val i = Intent(view.context, ExecutorStateListActivity::class.java)
        var p = 0

        // 根据 work 对象检查并设置权限
        val permissions = listOf(
            work.isRemindable to permission_urgeable,
            work.canTransfer() to permission_addable,
            work.canTransfer() to permission_deletable,
            work.canMarkOtherFinish() to permission_finishable,
            work.canMarkOtherUnFinish() to permission_unfinishable,
            work.canTransfer() to permission_transfer,
            work.canUpdateChief() to permission_ex_chief
        )

        permissions.forEach { (condition, permission) ->
            if (condition) p = p or permission
        }

        // 将 work.owners 转换为 Owner 对象的列表
        val list = work.owners.map { owner ->
            val o = Owner()
            o.fromJoyWorkUser(owner)
            o
        }

        // 填充 Intent 并启动活动
        inflateIntent(i, work.taskId, list, p) { owners ->
            work.owners?.clear()
            if (owners.isLegalList()) {
                work.owners?.addAll(owners.map { owner ->
                    val user = JoyWorkUser()
                    user.fromOwner(owner)
                    user
                })
            }
            callback(position, work)
        }

        view.context.startActivity(i)
    }

    interface OnExecutorChangedListener {
        fun onExecutorLineChanged(position: Int, work: JoyWork)
    }
}