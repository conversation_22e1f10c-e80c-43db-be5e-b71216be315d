package com.jd.oa.joywork2.input

import com.jd.oa.business.joyspace.JoySpacePage
import com.jd.oa.business.joyspace.queryPageInfo
import com.jd.oa.joywork.utils.HyperLinkHelper
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/16 23:34
 */
class CopyOpMonitor(val scope: CoroutineScope) {

    fun onTextChanged(
        s: CharSequence?,
        start: Int,
        before: Int,
        count: Int,
        callback: (JoySpacePage) -> Unit
    ) {
        //before == 0表示新增
        //count > https://joyspace.jd.com
        when {
            count > 20 && before == 0 -> {
                scope.safeLaunch {
                    val links = withContext(Dispatchers.Default) {
                        val content = s?.toString()?.substring(start, start + count)
                        HyperLinkHelper.findLinks(content ?: "")
                    }
                    if (links.isNotEmpty()) {
                        val result = queryPageInfo(links[0], "joywork")
                        if (result.success && result.data?.valid == 1) {
                            callback.invoke(result.data!!)
                        }
                    }
                }
            }
        }
    }
}