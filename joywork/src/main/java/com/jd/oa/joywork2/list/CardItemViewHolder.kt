package com.jd.oa.joywork2.list

import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.utils.allChildrenGone
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

class CardItemViewHolder(
    private val context: Context,
    parent: ViewGroup,
    private val mCeilProcessors: List<CeilProcessor>,
    private val mAdapter: RecyclerView.Adapter<*>,
    mClick: (JoyWork) -> Unit,
) : ItemViewHolder(
    context.inflater.inflate(
        R.layout.joywork2_list_card_item,
        parent,
        false
    ),
    mClick
) {

    fun bind(joyWork: JoyWork, data: ArrayList<Any>, roundBg: Boolean) {
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        itemView.setOnClickListener(itemClick)
        mCeilProcessors.forEach {
            it.onBindItemAsCard(context, itemView, joyWork, this, mAdapter)
        }
        val secondLineLayout = itemView.findViewById<LinearLayout>(R.id.second_line)
        if (secondLineLayout.allChildrenGone(false)) {
            secondLineLayout.gone()
        } else {
            secondLineLayout.visible()
        }
        val thirdLine = itemView.findViewById<LinearLayout>(R.id.third_line)
        if (thirdLine.allChildrenGone(false)) {
            thirdLine.gone()
        } else {
            thirdLine.visible()
        }
        val itemRoot = itemView.findViewById<View>(R.id.card_item_root)
        if (roundBg) {
            itemRoot.setBackgroundResource(R.drawable.joywork_bottom_round_8)
        } else {
            itemRoot.setBackgroundColor(Color.WHITE)
        }
    }

}