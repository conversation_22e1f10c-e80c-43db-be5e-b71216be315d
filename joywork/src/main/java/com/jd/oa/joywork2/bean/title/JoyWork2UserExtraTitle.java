package com.jd.oa.joywork2.bean.title;

import androidx.annotation.NonNull;

import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper;

public class JoyWork2UserExtraTitle extends JoyWork2ExtraTitle {
    public JoyWorkUser user;

    public JoyWork2UserExtraTitle(String title, JoyWorkUser user, Object extra) {
        super(title, extra);
        this.user = user;
    }

    @NonNull
    @Override
    public String toString() {
        return "title = " + title + ", extra = "
                + extraObj;
    }

    public JoyWorkCustomGrouper.Group getExtraGroup() {
        if (extraObj instanceof JoyWorkCustomGrouper.Group) {
            return (JoyWorkCustomGrouper.Group) extraObj;
        } else {
            return null;
        }
    }
}
