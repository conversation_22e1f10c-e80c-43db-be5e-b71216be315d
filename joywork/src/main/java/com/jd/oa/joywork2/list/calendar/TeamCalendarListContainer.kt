package com.jd.oa.joywork2.list.calendar

import android.os.Bundle
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import com.haibin.calendarview.Calendar
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/16 19:17
 */
class TeamCalendarListContainer : CalendarListContainer() {

    companion object {
        const val TAG = "TeamCalendarListContainer"

        fun loadList(
            fragmentManager: FragmentManager,
            layoutId: Int,
            projectId: String,
            forceReplace: Boolean = false,
            isArchiveProject: Boolean = false
        ) {
            if (fragmentManager.findFragmentByTag(TAG) == null || forceReplace) {
                fragmentManager.commit(true) {
                    setReorderingAllowed(true)
                    replace(
                        layoutId,
                        TeamCalendarListContainer().also {
                            it.arguments = Bundle().apply {
                                putString("projectId", projectId)
                                putBoolean("isArchiveProject", isArchiveProject)
                            }
                        },
                        TAG
                    )
                }
            }
        }
    }

    private val conditionViewModel: ConditionViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        configAddView()
    }

    override fun initObservers() {
        super.initObservers()
        mMainViewModel.detailLiveData.observe(viewLifecycleOwner) {
            configAddView()
        }
    }

    private fun configAddView() {
        mAddView?.runCatching {
            val permissions = mMainViewModel.detailLiveData.value?.permissions ?: emptyList()
            if (permissions.contains(ProjectPermissionEnum.TASK.code)) {
                visible()
            } else {
                gone()
            }
        }
    }

    override fun onClickAddOnDay(view: View, calendar: Calendar) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.isProject = true
        tmpData.taskListTypeEnum = getTaskListType()
        val groups = getGroupConfig()
        val dialogO =
            JoyWorkShortcutCreator.getShortcutDialog(
                requireActivity(),
                tmpData = tmpData,
                showGroup = true,
                projectId = getProjectId(),
                groups = groups,
                conditionViewModel.viewIdFactory?.invoke(),
                conditionViewModel.viewTypeFactory?.invoke()
            )
        dialogO.showSnackBar = true
        dialogO.successCallback = { result, value, dialog ->
            dialog?.dismiss()
            listContainerInheritedVM.onCreateWorkSuccess(CalendarHelper.timeInMillisToCalendar(value.endTime), result)
        }
        dialogO.show()
    }

    override fun getProjectId(): String = arguments?.getString("projectId") ?: ""

    override fun getTaskListType(): TaskListTypeEnum = TaskListTypeEnum.PROJECT

    private fun getGroupConfig(): List<Group> {
        val groups = mMainViewModel.detailLiveData.value?.groups ?: ArrayList()
        val dg = groups.map {
            Group().apply {
                groupId = it.groupId
                projectId = it.projectId
                title = it.title
                type = it.type
            }
        }
        return dg
    }

    override val isProject: Boolean
        get() = true

    override fun getIsArchiveProject(): Boolean = arguments?.getBoolean("isArchiveProject") ?: false
}