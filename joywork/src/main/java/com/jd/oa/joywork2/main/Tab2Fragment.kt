package com.jd.oa.joywork2.main

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.chenenyu.router.Router
import com.google.android.material.tabs.TabLayout
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork2.menu.JoyWork2MenuViewModel
import com.jd.oa.joywork2.menu.MenuBean
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.joywork2.view.WorkFloatMenu
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.Constants.KEY_JOY_WORK_OA
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.bindClick
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.grayResourceSwitchEnable
import com.jd.oa.utils.safeLaunch
import com.jd.oa.utils.visible

internal class Tab2Fragment : BaseFragment() {
    companion object {
        const val FRG_TAG = "Tab2Fragment"
    }

    private val mMenuViewModel: JoyWork2MenuViewModel by activityViewModels()
    private val mTab2ViewModel: Tab2ViewModel by activityViewModels()
    private val conditionViewModel: ConditionViewModel by activityViewModels()

    private val mTabLayout: TabLayout by bindView(R.id.mTabs)
    private val mShowCondition: TextView by bindView(R.id.mShowCondition)
    private val mListViewType: TextView by bindView(R.id.view_type)


    // lazy or no lazy, it's a problem
    private val mSelfTabItems by lazy {
        mutableListOf(
            MenuItem.MyHandle,
            MenuItem.MyApproval,
            MenuItem.MyCooperation,
            MenuItem.MyAssign
        )
    }

    private var mFromCallback = false

    private val mTabObserver = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tabP: TabLayout.Tab?) {
            val tabView = tabP?.customView ?: return
            val textView = tabView.findViewById<View>(R.id.mTabText)
            textView.isSelected = true
            val menuItem = tabView.tag as? MenuItem ?: return
            // 注意此处会有一个循环操作，直接将 mFromCallback = true 会有问题
            // 初始化时先执行到 mSelectMenuItemLiveData 的观察者，由它初始化 tab，然后执行到此方法
            // 如果将 mFromCallback = true，updateSelectMenuItem() 内部因为是相同的 menuItem 因此不会触发观察者
            // 导致 mFromCallback = true 不会变成 false
            // 因此第一次切换 menu 时 tab 就不会发生变化
            mFromCallback = mMenuViewModel.updateSelectMenuItem(menuItem)

            val clickId = when (menuItem) {
                MenuItem.MyHandle -> {
                    JoyWorkConstant.JOYWORK_MYEXECUTION
                }

                MenuItem.MyApproval -> {
                    JoyWorkConstant.JOYWORK_MYAPPROVAL
                }

                MenuItem.MyCooperation -> {
                    JoyWorkConstant.JOYWORK_MYFOLLOWED
                }

                MenuItem.MyAssign -> {
                    JoyWorkConstant.JOYWORK_MYCREATION
                }

                else -> null
            }
            clickId?.run {
                clickEvent {
                    ClickEventParam(eventId = clickId)
                }
            }
        }

        override fun onTabUnselected(tabP: TabLayout.Tab?) {
            val tabView = tabP?.customView ?: return
            val textView = tabView.findViewById<View>(R.id.mTabText)
            textView.isSelected = false
        }

        override fun onTabReselected(tab: TabLayout.Tab?) {

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.joywork2_tabs_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!KEY_JOY_WORK_OA.grayResourceSwitchEnable(default = "1")) {
            mSelfTabItems.remove(MenuItem.MyApproval)
        }
        initViewModels(view)
        mTabLayout.addOnTabSelectedListener(mTabObserver)
        mShowCondition.setOnClickListener {
            conditionViewModel.toggleConditionContainer()
            mShowCondition.isSelected = conditionViewModel.conditionContainerShow.value == true
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_LISTMANAGEMENT
                )
            }
        }
        view.bindClick<View>(R.id.mToggleMenu) {
            mTab2ViewModel.openMenu()
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_NAV_TAB
                )
            }
        }

        mListViewType.setOnClickListener {
            WorkFloatMenu.showViewTypeMenu(
                requireActivity(),
                conditionViewModel.showType,
                mListViewType,
                conditionViewModel.showTypeItems(),
                conditionViewModel.mMenuItemFactory?.invoke(),
                conditionViewModel.curCondition,
            ) {
                conditionViewModel.updateListViewType(it)
            }
        }

    }

    private fun initViewModels(view: View) {
        mMenuViewModel.mSelectMenuItemLiveData.observe(viewLifecycleOwner) {
            if (it.getViewType() == MenuBean.ViewType.SYS_VIEW.code) {
                mListViewType.gone()
            } else {
                mListViewType.visible()
            }
            lifecycleScope.safeLaunch {
                if (it?.needCondition() != true) {
                    mTab2ViewModel.showOrHideConditionIcon(false)
                    conditionViewModel.setCondition(ConditionState.Nothing, false)
                } else {
                    mTab2ViewModel.showOrHideConditionIcon(true)
                    conditionViewModel.initCondition({
                        it.getViewId()
                    }, {
                        it.getViewType()
                    })
                }
            }
            if (mFromCallback) {
                mFromCallback = false
                return@observe
            }
            when {
                mSelfTabItems.contains(it) -> {
                    // 待我处理、待我审批、我指派的、我关注的
                    val i =
                        mSelfTabItems.indexOfFirst { item -> item.getViewId() == it.getViewId() }
                    if (mTabLayout.tabCount == mSelfTabItems.size) {
                        selectTab(i)
                    } else {
                        initTabs(false, view.context, mSelfTabItems, i.coerceAtLeast(0))
                    }
                }

                else -> { // 自定义视图， 我的清单
                    initTabs(true, view.context, listOf(it))
                }
            }
        }
        conditionViewModel.init(false) {
            mMenuViewModel.mSelectMenuItemLiveData.value!!
        }

        mTab2ViewModel.mConditionShowOrHideLiveData.observe(viewLifecycleOwner) {
            if (it) {
                mShowCondition.visible()
            } else {
                mShowCondition.gone()
            }
//          mShowCondition.isSelected = it
        }
        conditionViewModel.showTypeLiveData.observe(viewLifecycleOwner) {
            if (it == null) {
                return@observe
            }
            mListViewType.setText(it.second.resId)
        }
    }

    private fun initTabs(needIcon: Boolean, context: Context, items: List<MenuItem>, p: Int = 0) {
        mTabLayout.removeAllTabs()
        mTabLayout.clearOnTabSelectedListeners()
        items.forEach {
            val tab = mTabLayout.newTab()
            val view = LayoutInflater.from(context).inflate(R.layout.joywork2_tabs_item, null)
            view.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            tab.customView = view
            view.tag = it
            val text = view.findViewById<TextView>(R.id.mTabText)
            text.text = it.getTitle(context)
            val clear = view.findViewById<View>(R.id.mClear)
            if (!needIcon) {
                clear.gone()
            } else {
                clear.visible()
                clear.setOnClickListener {
                    mMenuViewModel.updateSelectMenuItem(MenuItem.MyHandle)
                }
            }
            // 拦截「待我审批」Tab组件 TabLayout 里的点击事件
            if (it is MenuItem.MyApproval) {
                tab.view.isClickable = false
                tab.customView?.setOnClickListener {
                    Router.build(DeepLink.APPROVAL_PAGE).go(requireActivity())
                }
            }
            mTabLayout.addTab(tab, false)
        }
        selectTab(p)
        mTabLayout.addOnTabSelectedListener(mTabObserver)
    }

    private fun selectTab(position: Int) {
        mTabLayout.selectTab(mTabLayout.getTabAt(position), true)
    }
}
