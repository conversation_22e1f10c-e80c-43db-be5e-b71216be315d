package com.jd.oa.joywork2.list.listener

import android.view.View
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork2.list.IWorkListHost

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/13 00:55
 */
class LastCommentClickListener(
    val position: Int,
    val work: JoyWork,
    private val workListHost: IWorkListHost
) : IViewClickListener {
    override val host: IWorkListHost
        get() = workListHost

    override fun onClick(view: View?) {
        work.latestComment?.let {
            gotoDetail(work, true)
        }
    }
}