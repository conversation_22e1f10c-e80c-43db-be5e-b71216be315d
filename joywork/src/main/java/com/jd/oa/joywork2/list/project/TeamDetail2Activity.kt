package com.jd.oa.joywork2.list.project

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.FrameLayout
import androidx.activity.viewModels
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.TeamDetailActivity
import com.jd.oa.joywork2.backend.JoyWorkSource
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.JDMAUtils
import org.json.JSONObject
import java.util.Objects

/**
 * 团队待办列表
 * at 24.07.11 从TeamDetailActivity复制过来，实现清单逻辑
 * 1.condition部分复用
 * 2.列表复用JoyWork2groupFragment
 * 3.
 */
@Route(DeepLink.JOY_WORK_PROJECT_DETAIL, DeepLink.JOYWORK_PROJECT_DEFAULT)
class TeamDetail2Activity : BaseActivity() {

    companion object {
        fun inflateIntent(
            context: Context,
            title: String = "",
            id: String,
            type: Int,
            iconUrl: String?,
            isTab: Boolean,
            isArchiveProject: Boolean
        ): Intent {
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_TEAM_ITEM_CLICK,
                JoyWorkConstant.JOYWORK_TEAM_ITEM_CLICK
            )
            val bundle = Intent(context, TeamDetail2Activity::class.java)
            bundle.putExtra("title", title)
            bundle.putExtra("projectId", id)
            bundle.putExtra("isTab", isTab)
            bundle.putExtra("type", type)
            bundle.putExtra("iconUrl", iconUrl ?: "")
            bundle.putExtra("isArchiveProject", isArchiveProject)
            return bundle
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_function)
        if (Objects.equals(getId(), JoyWorkSource.HR.bizCode)) { // 京东人事特殊处理
            val newIntent = Intent(this, TeamDetailActivity::class.java)
            newIntent.putExtras(intent)
            startActivity(newIntent)
            finish()
            return
        }
        hideAction()
        val frameLayout = findViewById<FrameLayout>(R.id.me_fragment_content)
        supportFragmentManager.beginTransaction().replace(
            frameLayout.id,
            TeamDetail2Fragment.getInstance(
                isTab(),
                getProjectTitle(),
                getId(),
                getType(),
                getIconUrl(),
                getIsArchiveProject()
            ),
            getId()
        ).commit()
    }

    private fun getIconUrl(): String? {
        if (intent.hasExtra("iconUrl")) {
            return intent.getStringExtra("iconUrl")
        }
        return ""
    }

    private fun getIsArchiveProject(): Boolean {
        if (intent.hasExtra("isArchiveProject")) {
            return intent.getBooleanExtra("isArchiveProject", false)
        }
        return false
    }

    private fun getType(): Int {
        val deeplink = intent.getStringExtra("raw_uri")
        if (deeplink.isLegalString() && deeplink!!.startsWith(DeepLink.JOYWORK_PROJECT_DEFAULT)) {
            return JoyWorkProjectList.TYPE_OTHER
        }
        return intent.getIntExtra("type", JoyWorkProjectList.TYPE_NORMAL);
    }

    private fun isTab(): Boolean {
        return intent.getBooleanExtra("isTab", false)
    }

    private fun getId(): String {
        // mparam={"projectId":"352391515286536192","title":"ABCD"}
        // {"projectId":"352391515286536192","title":"ABCD"}
        var ret = ""
        if (intent.hasExtra("mparam")) {// 从 deeplink 来
            val mparam = intent.getStringExtra("mparam") ?: ""
            kotlin.runCatching {
                ret = JSONObject(mparam).getString("projectId")
            }
        }
        if (!ret.isLegalString()) {
            ret = intent.getStringExtra("projectId") ?: ""
        }
        return ret
    }

    private fun getProjectTitle(): String {
        // mparam={"projectId":"352391515286536192","title":"ABCD"}
        // {"projectId":"352391515286536192","title":"ABCD"}
        var ret = ""
        if (intent.hasExtra("mparam")) {// 从 deeplink 来
            val mparam = intent.getStringExtra("mparam") ?: ""
            kotlin.runCatching {
                ret = JSONObject(mparam).getString("title")
            }
        }
        if (!ret.isLegalString()) {
            ret = intent.getStringExtra("title") ?: ""
        }
        return ret
    }
}