package com.jd.oa.joywork2.list

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.CallSuper
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkDetailParam
import com.jd.oa.joywork.JoyWorkDialog
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.JoyWorkLevel
import com.jd.oa.joywork.JoyWorkMediator
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.DialogManager
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.joywork.detail.canTransfer
import com.jd.oa.joywork.detail.canUpdateDeadline
import com.jd.oa.joywork.detail.canUpdateMark
import com.jd.oa.joywork.detail.canUpdatePriority
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail.Project
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.UpdateMemberSend
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem
import com.jd.oa.joywork.detail.data.entity.custom.EmptyCustomFieldItem
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.detail.viewmodel.TaskDetailBindingAdapter
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalMap
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.notifyRiskUpdateWhenEndTimeUpdate
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork.sortOwners
import com.jd.oa.joywork.utils.getTimeStringWithoutSuffix
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork2.backend.TimestampType
import com.jd.oa.joywork2.backend.iconId
import com.jd.oa.joywork2.backend.toJoyWorkSource
import com.jd.oa.joywork2.bean.WorkTimeCheckResult
import com.jd.oa.joywork2.list.calendar.view.HorizontalLabelLayout
import com.jd.oa.joywork2.list.listener.ExecutorClickListener
import com.jd.oa.joywork2.list.listener.MoreButtonClickListener
import com.jd.oa.joywork2.viewholder.checkWorkTime
import com.jd.oa.joywork2.viewholder.checkWorkTimeLineBreak
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.NClick
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.click
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.color
import com.jd.oa.utils.gone
import com.jd.oa.utils.invisible
import com.jd.oa.utils.visible
import java.util.Objects


fun interface CeilProcessorFactory {
    fun create(): List<CeilProcessor>
}

fun enableView(b: Boolean, view: View) {
    if (b) {
        view.setBackgroundColor(Color.TRANSPARENT)
    } else {
        view.setBackgroundColor(Color.parseColor("#F0F3F3"))
    }
    view.isEnabled = b
}

// 用于处理各列
abstract class CeilProcessor {
    private lateinit var mCeilRootView: View

    protected fun <T : View> bindView(@IdRes id: Int) = lazy {
        mCeilRootView.findViewById<T>(id)
    }

    abstract fun getTitleString(context: Context): String
    abstract fun isMain(): Boolean // main 与普通列的区别在于：main 列比较宽

    fun bindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        ensureRootView(parent)
        onBindItem(context, parent, work, viewHolder, adapter)
    }


    fun createItem(parent: ViewGroup) {
        if (!::mCeilRootView.isInitialized) {
            mCeilRootView = onCreateItem(parent)
        }
    }

    private fun ensureRootView(parent: ViewGroup) {
        if (!::mCeilRootView.isInitialized) {
            createItem(parent)
        }
    }

    abstract fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    )

    abstract fun onCreateItem(parent: ViewGroup): View

    @CallSuper
    open fun onBindItemAsCard(
        context: Context,
        itemView: View,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        mCeilRootView = itemView
    }
}

enum class CeilType {
    START_TIME,
    END_TIME,
    PRIORITY,
    EXECUTORS,
    EXT_STATE, //待办状态
    EXT_PROGRESS, //待办进度
}

interface CeilChangedListener {
    fun onCeilChanged(ceilType: CeilType, work: JoyWork)
}

// 标题
class TitleCeilProcessor(
    private val listener: TitleCeilProcessorListener,
    private val operateListener: MoreButtonClickListener.MoreButtonCeilProcessorListener,
    private val ceilProcessorNum: Int,
) : CeilProcessor() {

    interface TitleCeilProcessorListener {
        fun onFinishStatusChange(joyWork: JoyWork, adapterPosition: Int)
        fun needIndentation(): Boolean
    }

    private val title: TextView by bindView(R.id.title)
    private val risk by bindView<TextView>(R.id.risk)

    private val cb by bindView<TextView>(R.id.cb_task)
    private val cb2 by bindView<ImageView>(R.id.cb_task2)
    private val cbContainer by bindView<View>(R.id.cb_task_container)
    private val titleContainer by bindView<View>(R.id.ll_title_container)
    private val indentation by bindView<View>(R.id.indentation)
    private val moreButton by bindView<IconFontView>(R.id.more_button_icon)

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_project_col_name)
    }

    override fun isMain(): Boolean = true

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_title, parent, true)
        return parent.findViewById(R.id.ll_title_container)
    }

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        if (listener.needIndentation()) {
            indentation.visible()
        } else {
            indentation.gone()
        }
        if (isMain()) {
            titleContainer.apply {
                val w: Int = if (ceilProcessorNum > 1) {
                    CommonUtils.getScreentWidth(context) * 4 / 5
                } else {
                    CommonUtils.getScreentWidth(context)
                }
                this.layoutParams.width = w
            }
        }
        JoyWorkViewItem.title(work, title)
        work.sortOwners()
        JoyWorkViewItem.risk(work, risk)

        cb.setTag(R.id.jdme_tag_id, work)
        cbContainer.setTag(R.id.jdme_tag_id, work)
        JoyWorkViewItem.checkboxNew(
            work,
            cb,
            cb2,
            cbContainer
        ) { work: JoyWork, cbView: View ->
            JDMAUtils.onEventClick(
                JoyWorkConstant.INCOMPLETE_FINISH,
                JoyWorkConstant.INCOMPLETE_FINISH
            )
            JoyWorkViewItem.finishAction(work, context, object : JoyWorkUpdateCallback {
                override fun result(success: Boolean, errorMsg: String) {
                    work.isFinishing = false
                    if (success) {
                        try {
                            val tmp = cbView.tag as JoyWork
                            listener.onFinishStatusChange(tmp, viewHolder.adapterPosition)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            result(false, JoyWorkEx.filterErrorMsg(""))
                        }
                    } else {
                        adapter.notifyItemChanged(viewHolder.adapterPosition)
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }

                override fun onStart() {
                    work.isFinishing = true
                    adapter.notifyItemChanged(viewHolder.adapterPosition)
                }
            })
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_COMPLETE_MYSELF
                )
            }
        }
    }

    override fun onBindItemAsCard(
        context: Context, itemView: View, work: JoyWork, viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        super.onBindItemAsCard(context, itemView, work, viewHolder, adapter)
        JoyWorkViewItem.title(work, title)
        cb.setTag(R.id.jdme_tag_id, work)
        cbContainer.setTag(R.id.jdme_tag_id, work)
        JoyWorkViewItem.checkboxNew(
            work,
            cb,
            cb2,
            cbContainer
        ) { work: JoyWork, cbView: View ->
            JDMAUtils.onEventClick(
                JoyWorkConstant.INCOMPLETE_FINISH,
                JoyWorkConstant.INCOMPLETE_FINISH
            )
            JoyWorkViewItem.finishAction(work, context, object : JoyWorkUpdateCallback {
                override fun result(success: Boolean, errorMsg: String) {
                    work.isFinishing = false
                    if (success) {
                        try {
                            val tmp = cbView.tag as JoyWork
                            listener.onFinishStatusChange(tmp, viewHolder.adapterPosition)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            result(false, JoyWorkEx.filterErrorMsg(""))
                        }
                    } else {
                        adapter.notifyItemChanged(viewHolder.adapterPosition)
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }

                override fun onStart() {
                    work.isFinishing = true
                    adapter.notifyItemChanged(viewHolder.adapterPosition)
                }
            })
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_COMPLETE_MYSELF
                )
            }
        }
        moreButton.setOnClickListener(
            MoreButtonClickListener(
                work,
                viewHolder.itemView.context,
                moreButton,
                viewHolder,
                operateListener
            )
        )
    }

}

// 执行人
class ExecutorsCeilProcessor(
    private val listener: ExecutorsCeilProcessorListener,
    private val activity: Activity,
    private val showType: TodoShowType
) : CeilProcessor() {
    interface ExecutorsCeilProcessorListener : CeilChangedListener

    private val ownerContainer by bindView<View>(R.id.owner_container)
    private val mAvatarView: JoyWorkAvatarView by bindView(R.id.mAvatarView)
    private val mAvatarViewThird: JoyWorkAvatarView by bindView(R.id.mAvatarView_third)

    private val ownerClickListener = View.OnClickListener {
        if (it.tag == null) {
            return@OnClickListener
        }
        val joywork = it.tag as JoyWork
        val selected = ArrayList<MemberEntityJd>()
        joywork.owners?.forEach {
            val jd = MemberEntityJd()
            jd.fromUser(it)
            selected.add(jd)
        }
        enableView(false, it)
        ExecutorUtils.selectExecutors(
            activity = activity,
            selected = selected,
            sessionId = null,
            needSetOwner = false,
            onFailure = {
                enableView(true, it)
            },
            onSuccess = { bean ->
                if (bean == null) {
                    enableView(true, it)
                    return@selectExecutors
                }
                val memberSend = UpdateMemberSend()
                memberSend.taskId = joywork.taskId
                val members: MutableList<Members> = ArrayList()
                for (memberEntityJd in bean) {
                    val member = Members()
                    member.fromDD(memberEntityJd!!)
                    members.add(member)
                }
                memberSend.members = members
                JoyWorkRepo.addRelation(
                    members,
                    taskId = joywork.taskId,
                    actionSource = null,
                    onSuccess = { ms ->
                        enableView(true, it)
                        listener.onCeilChanged(CeilType.EXECUTORS, joywork)
                    },
                    onFailure = { msg ->
                        ToastUtils.showToast(it.context, msg)
                        enableView(true, it)
                    }
                )
            }
        )
        val clickId = when (showType) {
            TodoShowType.CARD -> {
                JoyWorkConstant.MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_ASSIGNEES
            }

            TodoShowType.TABLE -> {
                JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_ASSIGNEES
            }

            else -> {
                JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_ASSIGNEES
            }
        }
        clickEvent {
            ClickEventParam(
                eventId = clickId
            )
        }
    }

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_more_owner)
    }

    override fun isMain() = false

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        joyWork: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        joyWork.sortOwners()
        JoyWorkViewItem.avatarView(joyWork, mAvatarView, null) { joywork ->
            if (!joywork.owners.isLegalList() || joywork.owners.size >= 2) {
                ""
            } else {
                joywork.owners.firstOrNull()?.realName ?: ""
            }
        }
        if (joyWork.canTransfer()) {
            ownerContainer.tag = joyWork
            ownerContainer.setOnClickListener(ExecutorClickListener(
                joyWork,
                viewHolder.bindingAdapterPosition
            ) { _, work ->
                listener.onCeilChanged(CeilType.EXECUTORS, work)
            })
        } else {
            ownerContainer.tag = null
            ownerContainer.isEnabled = false
            ownerContainer.isClickable = false
        }
    }

    override fun onBindItemAsCard(
        context: Context,
        itemView: View,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        super.onBindItemAsCard(context, itemView, work, viewHolder, adapter)
        work.sortOwners()
        val lineBreak = checkWorkTimeLineBreak(work)
        val avatarView = if (lineBreak) {
            mAvatarView.gone()
            mAvatarViewThird.visible()
            mAvatarViewThird
        } else {
            mAvatarViewThird.gone()
            mAvatarView.visible()
            mAvatarView
        }
        JoyWorkViewItem.avatarView(work, avatarView, null) { joywork ->
            if (!joywork.owners.isLegalList() || joywork.owners.size >= 2) {
                ""
            } else {
                joywork.owners.firstOrNull()?.realName ?: ""
            }
        }
        if (work.canTransfer()) {
            avatarView.tag = work
            avatarView.setOnClickListener(
                ExecutorClickListener(
                    work,
                    viewHolder.bindingAdapterPosition
                ) { _, w ->
                    listener.onCeilChanged(CeilType.EXECUTORS, w)
                }
            )
        } else {
            avatarView.tag = null
            avatarView.isEnabled = false
            avatarView.isClickable = false
        }
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_executors, parent, true)
        return parent.findViewById(R.id.owner_container)
    }
}

// 截止时间
class DeadlineCeilProcessor(
    private val listener: DeadlineCeilProcessorListener,
    private val showType: TodoShowType
) :
    CeilProcessor() {
    interface DeadlineCeilProcessorListener : CeilChangedListener {
        fun deadlineTimestampType(joyWork: JoyWork): TimestampType
    }

    private val deadline by bindView<TextView>(R.id.deadline)
    private val startTime by bindView<TextView>(R.id.start_time)
    private val hyphen by bindView<TextView>(R.id.hyphen)
    private val dupIcon by bindView<IconFontView>(R.id.dupIcon)
    private val deadlineDupContainer by bindView<ViewGroup>(R.id.deadline_dup)
    private val deadlineVH by bindView<View>(R.id.mark_value_vh)
    private val deadlineContainer by bindView<View>(R.id.deadline_container)

    private val deadlineListener = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        if (!joywork.canUpdateDeadline()) {
            return@OnClickListener
        }
        enableView(false, view)
        val detail = createValue(joywork)
        detail.type = Value.TYPE_DEADLINE
        detail.replaceAlertType(AlertType.createFromString(joywork.remindStr))
        detail.duplicateEnum = DuplicateEnum.getByValue(joywork.cycle)
        ShortcutManager(null, view.context).selectTime(detail) {
            if (it && !detail.isClean) {
                // 取消
                enableView(true, view)
                return@selectTime
            }
            val params = HashMap<String, Any>()
            if (!Objects.equals(
                    detail.startTime,
                    joywork.startTime
                )
            ) {
                params["startTime"] =
                    if (detail.startTime.isLegalTimestamp()) detail.startTime else -1
            }
            if (!Objects.equals(detail.endTime, joywork.endTime)) {
                notifyRiskUpdateWhenEndTimeUpdate(view.context, detail.endTime, joywork.endTime)
                params["endTime"] =
                    if (detail.endTime.isLegalTimestamp()) detail.endTime else -1
            }
            if (detail.mAlertType.isEmpty()) {
                detail.mAlertType.add(AlertType.NO)
            }
            if (!AlertType.equals(
                    AlertType.createFromString(joywork.remindStr),
                    detail.mAlertType
                )
            ) {
                params["remindStr"] = AlertType.valueToString(detail.mAlertType)
            }
            val c = (detail.duplicateEnum ?: DuplicateEnum.NO).value
            if (!Objects.equals(c, joywork.cycle)) {
                params["cycle"] = c
            }
            if (!params.isLegalMap()) {
                enableView(true, view)
                return@selectTime
            }
            TaskDetailWebservice.postTaskUpdate(
                joywork.taskId,
                params,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            // 刷新整个界面
                            listener.onCeilChanged(CeilType.END_TIME, joywork)
                        }
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }
                })
        }
        val clickId = when (showType) {
            TodoShowType.CARD -> {
                JoyWorkConstant.MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_DDL
            }

            TodoShowType.TABLE -> {
                JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_DDL
            }

            else -> {
                JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_DDL
            }
        }
        clickEvent {
            ClickEventParam(
                eventId = clickId
            )
        }
    }

    private val startClickListener = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        if (!joywork.canUpdateDeadline()) {
            return@OnClickListener
        }
        enableView(false, view)
        val value = createValue(joywork)
        value.type = Value.TYPE_START_TIME
        value.replaceAlertType(AlertType.createFromString(joywork.remindStr))
        value.duplicateEnum = DuplicateEnum.getByValue(joywork.cycle)
        ShortcutManager(null, view.context).selectTime(value) {
            if (it && !value.isClean) {
                // 取消
                enableView(true, view)
                return@selectTime
            }
            val params = HashMap<String, Any>()
            if (!Objects.equals(
                    value.startTime,
                    joywork.startTime
                )
            ) {
                params["startTime"] =
                    if (value.startTime.isLegalTimestamp()) value.startTime else -1
            }
            if (!Objects.equals(
                    value.endTime,
                    joywork.endTime
                )
            ) {
                notifyRiskUpdateWhenEndTimeUpdate(view.context, value.endTime, joywork.endTime)
                params["endTime"] =
                    if (value.endTime.isLegalTimestamp()) value.endTime else -1
            }
            if (value.mAlertType.isEmpty()) {
                value.mAlertType.add(AlertType.NO)
            }
            if (!AlertType.equals(
                    AlertType.createFromString(joywork.remindStr),
                    value.mAlertType
                )
            ) {
                params["remindStr"] = AlertType.valueToString(value.mAlertType)
            }
            val c = (value.duplicateEnum ?: DuplicateEnum.NO).value
            if (!Objects.equals(c, joywork.cycle)) {
                params["cycle"] = c
            }
            if (!params.isLegalMap()) {
                enableView(true, view)
                return@selectTime
            }
            TaskDetailWebservice.postTaskUpdate(
                joywork.taskId,
                params,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            listener.onCeilChanged(CeilType.START_TIME, joywork)
                        }
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }
                })
        }
    }

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_shortcut_deadline_tips)
    }

    override fun isMain() = false
    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        when (listener.deadlineTimestampType(work)) {
            TimestampType.FINISH_TIME -> {
                JoyWorkViewItem.finishTime(work, deadline, deadlineVH)
            }

            else -> {
                JoyWorkViewItem.deadline(work, deadline, deadlineVH)
            }
        }
        JoyWorkViewItem.duplicateIcon(work, dupIcon)
        JoyWorkViewItem.deadlineDupView(work, deadlineDupContainer, deadlineVH)
        deadlineContainer.tag = work
        deadlineContainer.click(deadlineListener)
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_deadline, parent, true)
        return parent.findViewById(R.id.deadline_container)
    }

    override fun onBindItemAsCard(
        context: Context,
        itemView: View,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        super.onBindItemAsCard(context, itemView, work, viewHolder, adapter)
        val checkResult = checkWorkTime(context, work)
        checkStart(work, checkResult)
        checkHyphen(work, checkResult)
        checkEnd(work, checkResult)
    }

    private fun checkStart(work: JoyWork, checkResult: WorkTimeCheckResult) {
        if (checkResult.showStart) {
            startTime.visible()
            if (checkResult.warn)
                startTime.setTextColor(deadline.context.color(R.color.joywork_red))
            else
                startTime.setTextColor(Color.parseColor("#8F959E"))
            startTime.text = checkResult.startContent
            startTime.tag = work
            startTime.click(startClickListener)
        } else {
            startTime.gone()
        }
    }

    private fun checkHyphen(work: JoyWork, checkResult: WorkTimeCheckResult) {
        if (checkResult.showHyphen) {
            hyphen.visible()
        } else {
            hyphen.gone()
        }
        if (checkResult.warn)
            hyphen.setTextColor(deadline.context.color(R.color.joywork_red))
        else
            hyphen.setTextColor(Color.parseColor("#8F959E"))
    }

    private fun checkEnd(work: JoyWork, checkResult: WorkTimeCheckResult) {
        if (checkResult.showEnd) {
            deadline.visible()
            if (checkResult.warn)
                deadline.setTextColor(deadline.context.color(R.color.joywork_red))
            else
                deadline.setTextColor(Color.parseColor("#8F959E"))
            deadline.text = checkResult.endContent
            deadline.tag = work
            deadline.click(deadlineListener)
        } else {
            deadline.gone()
        }
    }
}

fun createValue(joywork: JoyWork): Value {
    val value = Value()
    var startTime = joywork.startTime
    if (!startTime.isLegalTimestamp()) {
        // 没有开始时间，需要指定默认值
        val endTime = joywork.endTime
        val startTimeAnchor =
            if (endTime.isLegalTimestamp() && endTime < System.currentTimeMillis()) {
                // 有截止时间且已截止，取截止时间前一天早 9:00
                Math.max(0, endTime - 24 * 60 * 60 * 1000L)
            } else {
                // 取当前天早上 9：00
                System.currentTimeMillis()
            }
        startTime = DateUtils.getSpecialHour(startTimeAnchor, 9)
    }
    value.startTime = startTime
    val endTime = if (joywork.endTime.isLegalTimestamp()) {
        joywork.endTime
    } else {
        DateUtils.getSpecialHour(System.currentTimeMillis() + 24 * 60 * 60 * 1000L, 16)
    }
    value.endTime = endTime
    return value
}

// 开始时间
class StartTimeCeilProcessor(private val listener: StartTimeCeilProcessorListener) :
    CeilProcessor() {
    interface StartTimeCeilProcessorListener : CeilChangedListener

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_update_start)
    }

    override fun isMain() = false

    private val start by bindView<TextView>(R.id.start_text)
    private val startVH by bindView<View>(R.id.mark_value_vh)
    private val startContainer by bindView<View>(R.id.start_rooter)

    private val startClickListener = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        if (!joywork.canUpdateDeadline()) {
            return@OnClickListener
        }
        enableView(false, view)
        val value = createValue(joywork)
        value.type = Value.TYPE_START_TIME
        value.replaceAlertType(AlertType.createFromString(joywork.remindStr))
        value.duplicateEnum = DuplicateEnum.getByValue(joywork.cycle)
        ShortcutManager(null, view.context).selectTime(value) {
            if (it && !value.isClean) {
                // 取消
                enableView(true, view)
                return@selectTime
            }
            val params = HashMap<String, Any>()
            if (!Objects.equals(
                    value.startTime,
                    joywork.startTime
                )
            ) {
                params["startTime"] =
                    if (value.startTime.isLegalTimestamp()) value.startTime else -1
            }
            if (!Objects.equals(
                    value.endTime,
                    joywork.endTime
                )
            ) {
                notifyRiskUpdateWhenEndTimeUpdate(view.context, value.endTime, joywork.endTime)
                params["endTime"] =
                    if (value.endTime.isLegalTimestamp()) value.endTime else -1
            }
            if (value.mAlertType.isEmpty()) {
                value.mAlertType.add(AlertType.NO)
            }
            if (!AlertType.equals(
                    AlertType.createFromString(joywork.remindStr),
                    value.mAlertType
                )
            ) {
                params["remindStr"] = AlertType.valueToString(value.mAlertType)
            }
            val c = (value.duplicateEnum ?: DuplicateEnum.NO).value
            if (!Objects.equals(c, joywork.cycle)) {
                params["cycle"] = c
            }
            if (!params.isLegalMap()) {
                enableView(true, view)
                return@selectTime
            }
            TaskDetailWebservice.postTaskUpdate(
                joywork.taskId,
                params,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            listener.onCeilChanged(CeilType.START_TIME, joywork)
                        }
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }
                })
        }
    }

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        JoyWorkViewItem.startTimeWithoutSuffix(work, start, startVH)
        startContainer.tag = work
        startContainer.click(startClickListener)
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_start_time, parent, true)
        return parent.findViewById(R.id.start_rooter)
    }
}

// 待办来源
class SourceCeilProcessor(private val listener: SourceCeilProcessorListener) :
    CeilProcessor() {
    interface SourceCeilProcessorListener

    private val icon by bindView<TextView>(R.id.mIcon)
    private val sourceName by bindView<TextView>(R.id.name)
    private val rootContainer by bindView<View>(R.id.source_rooter)
    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork2_grouper_source)
    }

    private val click = View.OnClickListener {
//        val work = it.getTag(R.id.jdme_tag_id) as JoyWork
//        if (work.groupTitle.isLegalString()) {
//            TaskDetailBindingAdapter.jumpToSource(
//                it.context,
//                work.mobileContent,
//                work.bizCode,
//                work.sourceName
//            )
//        }
    }

    override fun isMain() = false
    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        val name = work.sourceName
        if (name.isLegalString()) {
            sourceName.visible()
            sourceName.text = name
            val source = work.bizCode.toJoyWorkSource()
            if (source == null) {
                icon.setText(R.string.icon_general_control)
            } else {
                icon.setText(source.iconId())
            }
            icon.visible()
        } else {
            sourceName.gone()
            icon.gone()
        }
        rootContainer.setTag(R.id.jdme_tag_id, work)
        rootContainer.setOnClickListener(click)
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_source, parent, true)
        return parent.findViewById(R.id.source_rooter)
    }
}

/**
 * 一级来源
 */
class PrimarySourceCeilProcessor(val listener: PrimarySourceCeilProcessorListener) :
    CeilProcessor() {
    interface PrimarySourceCeilProcessorListener

    private val icon by bindView<TextView>(R.id.mIcon)
    private val sourceName by bindView<TextView>(R.id.name)
    private val rootContainer by bindView<View>(R.id.source_rooter)

    private val click = View.OnClickListener {
        val work = it.getTag(R.id.jdme_tag_id) as JoyWork
        val name = work.tripartiteName ?: work.sourceName
        if (name.isLegalString()) {
            TaskDetailBindingAdapter.jumpToSource(
                it.context,
                work.mobileContent,
                work.bizCode,
                name
            )
        }
    }

    override fun getTitleString(context: Context) =
        context.getString(R.string.joywork2_grouper_primary_source)

    override fun isMain(): Boolean = false

    override fun onCreateItem(parent: ViewGroup): View {
        val view = parent.context.layoutInflater.inflate(
            R.layout.joywork2_list_item_source_primary,
            parent,
            false
        )
        parent.addView(view)
        return view
    }

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>,
    ) {
        val name =
            if (!TextUtils.isEmpty(work.tripartiteName)) work.tripartiteName else work.sourceName
        if (name.isLegalString()) {
            sourceName.visible()
            sourceName.text = name
            val source = work.bizCode.toJoyWorkSource()
            if (source == null) {
                icon.setText(R.string.icon_general_control)
            } else {
                icon.setText(source.iconId())
            }
            icon.visible()
        } else {
            sourceName.gone()
            icon.gone()
        }
        rootContainer.setTag(R.id.jdme_tag_id, work)
        rootContainer.setOnClickListener(click)
    }
}

/**
 * 二级来源
 */
class SecondarySourceCeilProcessor(
    val listener: SecondarySourceCeilProcessorListener
) : CeilProcessor() {
    interface SecondarySourceCeilProcessorListener

    private val icon by bindView<TextView>(R.id.mIcon)
    private val sourceName by bindView<TextView>(R.id.name)
    private val rootContainer by bindView<View>(R.id.source_rooter)

    private val click = View.OnClickListener {
        val work = it.getTag(R.id.jdme_tag_id) as JoyWork
        val name = work.subTripartiteName
        if (name.isLegalString()) {
            TaskDetailBindingAdapter.jumpToSource(
                it.context,
                if (!TextUtils.isEmpty(work.subMobileContent)) work.subMobileContent else work.mobileContent,
                work.bizCode,
                name
            )
        }
    }

    override fun getTitleString(context: Context) =
        context.getString(R.string.joywork2_grouper_secondary_source)

    override fun isMain(): Boolean = false

    override fun onCreateItem(parent: ViewGroup): View {
        val view = parent.context.layoutInflater.inflate(
            R.layout.joywork2_list_item_source_secondary,
            parent,
            false
        )
        parent.addView(view)
        return view
    }

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>,
    ) {
        val name = work.subTripartiteName
        if (name.isLegalString()) {
            sourceName.visible()
            sourceName.text = name
            val source = work.bizCode.toJoyWorkSource()
            if (source == null) {
                icon.setText(R.string.icon_general_control)
            } else {
                icon.setText(source.iconId())
            }
            icon.visible()
        } else {
            sourceName.gone()
            icon.gone()
        }
        rootContainer.setTag(R.id.jdme_tag_id, work)
        rootContainer.setOnClickListener(click)
    }
}

class LatestCommentCeilProcessor(
    val listener: LatestCommentCeilProcessorListener
) : CeilProcessor() {
    interface LatestCommentCeilProcessorListener {
        val fragment: Fragment
    }

    private val root by bindView<View>(R.id.root)
    private val commentDate by bindView<TextView>(R.id.comment_date)
    private val commentDateThird by bindView<TextView>(R.id.comment_date_third)

    private val click = View.OnClickListener { view ->
        val task = view.getTag(R.id.jdme_tag_id) as JoyWork
        task.latestComment?.let {
            JoyWorkMediator.goDetail(
                listener.fragment,
                JoyWorkDetailParam(
                    taskId = task.taskId,
                    projectId = task.projectId,
                    taskName = task.title
                ).apply {
                    from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                    obj = DetailReturnParcel()
                    commentId = task.latestComment?.commentId
                    reqCode = 10
                })
        }
    }

    override fun getTitleString(context: Context) =
        context.getString(R.string.joywork2_grouper_latest_comment)

    override fun isMain(): Boolean = false

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(
            R.layout.joywork2_list_item_latest_comment,
            parent,
            true
        )
        return parent.findViewById(R.id.root)
    }

    @SuppressLint("SimpleDateFormat")
    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>,
    ) {
        val comment = work.latestComment
        if (comment != null && comment.updateTime != null && comment.updateTime != 0L) {
            commentDate.visible()
            commentDate.text = getTimeStringWithoutSuffix(comment.updateTime, context.resources)
        } else {
            commentDate.invisible()
        }
        root.setTag(R.id.jdme_tag_id, work)
        root.setOnClickListener(click)
    }

    override fun onBindItemAsCard(
        context: Context,
        itemView: View,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        super.onBindItemAsCard(context, itemView, work, viewHolder, adapter)
        val comment = work.latestComment
        val lineBreak = checkWorkTimeLineBreak(work)
        val commentView = if (lineBreak) {
            commentDate.gone()
            commentDateThird.visible()
            commentDateThird
        } else {
            commentDateThird.gone()
            commentDate.visible()
            commentDate
        }
        if (comment != null && comment.updateTime != null && comment.updateTime != 0L) {
            commentView.visible()
        } else {
            commentView.gone()
        }
        commentView.setTag(R.id.jdme_tag_id, work)
        commentView.setOnClickListener(click)
    }
}

typealias ProjectAddCallback = (Project?, isDel: Boolean) -> Unit

// 所属清单
class ProjectCeilProcessor(private val listener: ProjectCeilProcessorListener) :
    CeilProcessor() {

    companion object {
        fun addProjects(
            p: Project,
            taskId: String,
            context: Context,
            success: (String, Project) -> Unit
        ) {
            TaskDetailWebservice.taskAddProject(
                taskId,
                p.projectId,
                p.groupId,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onFailure(exception: HttpException?, info: String?) {
                        Toast.makeText(
                            context,
                            JoyWorkEx.filterErrorMsg(info),
                            Toast.LENGTH_SHORT
                        ).show()
                    }

                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        if (hasError) {
                            onFailure(null, "")
                        } else {
                            success(taskId, p)
                        }
                    }
                })
        }
    }


    interface ProjectCeilProcessorListener {
        fun afterProjectDeleted(joyWork: JoyWork)

        fun afterProjectAdded(taskId: String, project: Project)
        fun gotoSelectProject(
            joyWork: JoyWork,
            callback: ProjectAddCallback
        )
        fun gotoDialogSelectProject(
            joyWork: JoyWork,
            callback: ProjectAddCallback
        )
    }

    private val mProjectsView by bindView<TextView>(R.id.mProjectsView)
    private val mContainer by bindView<View>(R.id.project_rooter)

    private val projectClickListener = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        if (NClick.isFastDoubleClick()) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        projectClick(joywork, view.context) { p, isDel ->
            if (p == null) {
                return@projectClick
            }
            if (isDel) {
                TaskDetailWebservice.taskDeleteProject(
                    joywork.taskId,
                    p.projectId,
                    null,
                    object : TaskDetailWebservice.TaskCallback() {
                        override fun onFailure(exception: HttpException?, info: String?) {
                            Toast.makeText(
                                view.context,
                                JoyWorkEx.filterErrorMsg(info),
                                Toast.LENGTH_SHORT
                            ).show()
                        }

                        override fun onSuccess(info: ResponseInfo<String>?) {
                            super.onSuccess(info)
                            if (hasError) {
                                onFailure(null, "")
                            } else {
                                joywork.projects?.removeAll {
                                    Objects.equals(it.projectId, p.projectId)
                                }
                                listener.afterProjectDeleted(joywork)
                            }
                        }
                    })
            } else {
                addProjects(p, joywork.taskId, view.context) { taskId, p ->
                    listener.afterProjectAdded(taskId, p)
                }
            }
        }
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_CHECKLIST
            )
        }
    }

    private fun projectClick(
        joyWork: JoyWork,
        context: Context,
        callback: ProjectAddCallback
    ) {
        if (joyWork.projects.isLegalList()) {
            val dialog = JoyWorkDialog.showTaskProjectsDialog(context, joyWork.projects, {
                listener.gotoDialogSelectProject(joyWork, callback)
            }) {
                callback.invoke(it, true)
            }
            dialog.setOnDismissListener {
                callback.invoke(null, false)
            }
        } else {
            listener.gotoDialogSelectProject(joyWork, callback)
        }
    }

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_self_order)
    }

    override fun isMain() = false

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        JoyWorkViewItem.projectNames(work, mProjectsView)
        mContainer.tag = work
        mContainer.click(projectClickListener)
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_project, parent, true)
        return parent.findViewById(R.id.project_rooter)
    }
}


class PriorityCeilProcessor(private val listener: PriorityCeilProcessorListener) :
    CeilProcessor() {
    interface PriorityCeilProcessorListener : CeilChangedListener

    private val priorityClick = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        if (!joywork.canUpdatePriority()) {
            return@OnClickListener
        }
        enableView(false, view)
        JDMAUtils.clickEvent("", JoyWorkConstant.TEAM_LIST_SET_REMINDER, null)
        val dialog = DialogManager.showLevelDialog(view.context, joywork.priorityType) {
            TaskDetailWebservice.updatePriority(
                joywork.taskId,
                it,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }

                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            // 刷新整个界面
                            joywork.priorityType = it
                            listener.onCeilChanged(CeilType.PRIORITY, joywork)
                        }
                    }
                })
        }
        dialog.setOnDismissListener {
            enableView(true, view)
        }
        dialog.show()
    }

    private val rootContainer by bindView<View>(R.id.rooter)
    private val priority by bindView<TextView>(R.id.priority)
    private val priorityPlaceHolder by bindView<View>(R.id.priority_value_vh)
    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_update_priority)
    }

    override fun isMain() = false
    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        JoyWorkViewItem.priority(work, priority, priorityPlaceHolder)
        rootContainer.tag = work
        rootContainer.setOnClickListener(priorityClick)
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_priority, parent, true)
        return parent.findViewById(R.id.rooter)
    }

    override fun onBindItemAsCard(
        context: Context,
        itemView: View,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        super.onBindItemAsCard(context, itemView, work, viewHolder, adapter)
        (priorityPlaceHolder.background as? GradientDrawable)?.run {
            val level = JoyWorkLevel.NO.getLevel(work.priorityType ?: JoyWorkLevel.NO.value)
            setColor(level.getCardTypeBgColor())
        }
    }

}

//待办状态和进度共用
abstract class ExtMultiSelectCeilProcessor(
    private val customFieldGroup: CustomFieldGroup,
    private val ceilProcessorListener: ExtValueChangedProcessorListener,
    private val ceilType: CeilType
) :
    CeilProcessor() {

    interface ExtValueChangedProcessorListener : CeilChangedListener

    private val exRoot by bindView<View>(R.id.ex_root)
    private val value by bindView<TextView>(R.id.mark_value)
    private val valueVH by bindView<View>(R.id.mark_value_vh)
    private val valueContainer by bindView<View>(R.id.mark_container)

    override fun isMain(): Boolean = false


    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        val columnId = customFieldGroup.columnId
        val fields = work.safeCustomFields()
        var fieldItem: CustomFieldItem = EmptyCustomFieldItem.getInstance()
        if (fields.isNotEmpty()) {
            val detailIds = fields[columnId]
            val detailId = detailIds?.firstOrNull()
            customFieldGroup.details?.firstOrNull { fi ->
                if (detailId != null && fi.detailId == detailId) {
                    fieldItem = fi
                    true
                } else {
                    false
                }
            }
        }

        if (work.canUpdateMark()) {
            exRoot.click {
                enableView(false, it)
                val curItem = fieldItem
                DialogManager.showMarkDialog(
                    it.context,
                    curItem,
                    customFieldGroup,
                    work.taskId
                ) { isDel, item2 ->
                    enableView(true, it)
                    if (item2 != null) {
                        val detailIds = fields[columnId]
                        val list = if (isDel) {
                            emptyList()
                        } else {
                            if (item2 == EmptyCustomFieldItem.getInstance()) {
                                emptyList()
                            } else {
                                if (detailIds.isNullOrEmpty()) {
                                    mutableListOf(item2.detailId)
                                } else {
                                    detailIds[0] = item2.detailId
                                    detailIds
                                }
                            }
                        }
                        fields[columnId] = list
                        ceilProcessorListener.onCeilChanged(ceilType, work)
                    }
                }
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_STATUS
                    )
                }
            }
        } else {
            exRoot.isEnabled = false
            exRoot.isClickable = false
        }

        kotlin.runCatching {
            if (fieldItem == EmptyCustomFieldItem.getInstance()) {
                valueContainer.background =
                    DrawableEx.roundSolidRect(Color.TRANSPARENT, CommonUtils.dp2FloatPx(4))
            } else {
                valueContainer.background = DrawableEx.roundSolidRect(
                    Color.parseColor(fieldItem.itemContent.background),
                    CommonUtils.dp2FloatPx(4)
                )
            }
        }

        if (fieldItem == EmptyCustomFieldItem.getInstance()) {
            valueVH.visible()
            value.gone()
        } else {
            valueVH.gone()
            value.visible()
            // 鬼知道后台会返回啥样的数据，先 catch 为敬
            kotlin.runCatching {
                value.text = fieldItem.itemContent.content
            }
            kotlin.runCatching {
                value.setTextColor(Color.parseColor(fieldItem.itemContent.color))
            }
        }
    }

    override fun onCreateItem(parent: ViewGroup): View {
        val view = parent.context.layoutInflater.inflate(
            R.layout.jdme_joywork_project_item_ex,
            parent,
            false
        )
        parent.addView(view)
        return view
    }

}


class ExtStatusCeilProcessor(
    customFieldGroup: CustomFieldGroup,
    valueChangedProcessorListener: ExtValueChangedProcessorListener
) : ExtMultiSelectCeilProcessor(
    customFieldGroup,
    valueChangedProcessorListener,
    ceilType = CeilType.EXT_STATE
) {
    override fun getTitleString(context: Context): String =
        context.getString(R.string.joywork_project_setting_filter_status)
}

class ExtProgressCeilProcessor(
    customFieldGroup: CustomFieldGroup,
    valueChangedProcessorListener: ExtValueChangedProcessorListener
) : ExtMultiSelectCeilProcessor(
    customFieldGroup,
    valueChangedProcessorListener,
    ceilType = CeilType.EXT_PROGRESS
) {
    override fun getTitleString(context: Context): String =
        context.getString(R.string.joywork_project_todo_progress)
}

class LabelCeilProcessor: CeilProcessor() {
    private val labelContainer by bindView<HorizontalLabelLayout>(R.id.label_container)

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_project_label)
    }

    override fun isMain(): Boolean = false

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
    }

    override fun onCreateItem(parent: ViewGroup): View {
        return parent.findViewById(R.id.root)
    }

    override fun onBindItemAsCard(
        context: Context,
        itemView: View,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        super.onBindItemAsCard(context, itemView, work, viewHolder, adapter)
        JoyWorkViewItem.loadLabel(work, labelContainer)
    }
}

class CreateTimeCeilProcessor: CeilProcessor() {
    private val create by bindView<TextView>(R.id.create_text)
    private val createVH by bindView<View>(R.id.mark_value_vh)

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_project_create_time)
    }

    override fun isMain(): Boolean = false

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        JoyWorkViewItem.createTimeWithoutSuffix(work, create, createVH)
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_create_time, parent, true)
        return parent.findViewById(R.id.create_rooter)
    }
}

class CreatorCeilProcessor: CeilProcessor() {
    private val mAvatarView: JoyWorkAvatarView by bindView(R.id.creator_avatar)

    override fun getTitleString(context: Context): String {
        return context.getString(R.string.joywork_project_creator)
    }

    override fun isMain(): Boolean = false

    override fun onBindItem(
        context: Context,
        parent: ViewGroup,
        work: JoyWork,
        viewHolder: ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ) {
        JoyWorkViewItem.creatorAvatarView(work, mAvatarView, null) {
            it.assigner.realName ?: ""
        }
    }

    override fun onCreateItem(parent: ViewGroup): View {
        parent.context.layoutInflater.inflate(R.layout.joywork2_list_item_creator, parent, true)
        return parent.findViewById(R.id.creator_container)
    }

}
