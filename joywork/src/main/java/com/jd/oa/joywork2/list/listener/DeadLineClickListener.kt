package com.jd.oa.joywork2.list.listener

import android.view.View
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.canUpdateDeadline
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.isLegalMap
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.notifyRiskUpdateWhenEndTimeUpdate
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork2.list.createValue
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import java.util.Objects

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/13 00:43
 */
class DeadLineClickListener(
    val position: Int,
    val onDeadLineChangedListener: OnDeadLineChangedListener
) : View.OnClickListener {
    override fun onClick(view: View?) {
        if (view == null || view.tag == null) {
            return
        }
        val work = view.tag as JoyWork
        if (!work.canUpdateDeadline()) {
            return
        }
        val detail = createValue(work)
        detail.type = Value.TYPE_DEADLINE
        detail.replaceAlertType(AlertType.createFromString(work.remindStr))
        detail.duplicateEnum = DuplicateEnum.getByValue(work.cycle)
        ShortcutManager(null, view.context).selectTime(detail) {
            if (it && !detail.isClean) {
                return@selectTime
            }
            val params = HashMap<String, Any>()
            if (!Objects.equals(
                    detail.startTime,
                    work.startTime
                )
            ) {
                params["startTime"] =
                    if (detail.startTime.isLegalTimestamp()) detail.startTime else -1
            }
            if (!Objects.equals(detail.endTime, work.endTime)) {
                notifyRiskUpdateWhenEndTimeUpdate(view.context, detail.endTime, work.endTime)
                params["endTime"] =
                    if (detail.endTime.isLegalTimestamp()) detail.endTime else -1
            }
            if (detail.mAlertType.isEmpty()) {
                detail.mAlertType.add(AlertType.NO)
            }
            if (!AlertType.equals(
                    AlertType.createFromString(work.remindStr),
                    detail.mAlertType
                )
            ) {
                params["remindStr"] = AlertType.valueToString(detail.mAlertType)
            }
            val c = (detail.duplicateEnum ?: DuplicateEnum.NO).value
            if (!Objects.equals(c, work.cycle)) {
                params["cycle"] = c
            }
            if (!params.isLegalMap()) {
                return@selectTime
            }
            TaskDetailWebservice.postTaskUpdate(
                work.taskId,
                params,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        if (!hasError) {
                            // 刷新整个界面
                            if (detail.startTime.isLegalTimestamp()){
                                work.startTime = detail.startTime
                            } else {
                                work.startTime = -1
                            }
                            if (detail.endTime.isLegalTimestamp()){
                                work.endTime = detail.endTime
                            } else {
                                work.endTime = -1
                            }
                            onDeadLineChangedListener.onDeadLineChanged(
                                position,
                                work
                            )
                        }
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                    }
                })
        }
    }

    interface OnDeadLineChangedListener {
        fun onDeadLineChanged(position: Int, work: JoyWork)
    }
}