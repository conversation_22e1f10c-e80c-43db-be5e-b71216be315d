package com.jd.oa.joywork2.main.bean;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.StringRes;

import com.jd.oa.joywork.R;

import java.util.HashMap;
import java.util.Objects;

public class ConditionGrouper {
    public String field; // 分组字段
    public boolean disable;
    public ConditionGrouper prev;

    protected ConditionGrouper() {

    }

    public ConditionGrouper(Field field) {
        this(field, false);
    }

    public ConditionGrouper(Field field, boolean disable) {
        this.field = field.value;
        this.disable = disable;
    }

    public HashMap<String, String> toMap() {
        HashMap<String, String> map = new HashMap<>();
        if (TextUtils.isEmpty(field)) {
            return map;
        }
        map.put("field", field);
        return map;
    }

    public String getText(Context context) {
        for (Field value : Field.values()) {
            if (Objects.equals(value.value, field)) {
                return context.getString(value.textId);
            }
        }
        return "";
    }

    // 根据 field 返回对应的 Field 枚举
    public Field getFieldEnum() {
        for (Field fieldEnum : Field.values()) {
            if (Objects.equals(fieldEnum.value, field)) {
                return fieldEnum;
            }
        }
        return Field.END_TIME;
    }

    public enum Field {
//        NO("NO_GROUP", R.string.joywork2_grouper_no), // 无分组
        CUSTOM("CUSTOM_GROUP", R.string.joywork_sort_custom_group), // 自定义分组
        END_TIME("END_TIME", R.string.joywork_project_setting_sort_time), // 截止时间
        START_TIME("START_TIME", R.string.joywork_update_start), // 开始时间
        FINISH_TIME("FINISH_TIME",R.string.joywork_update_finish),// 完成时间
        EXECUTOR("EXECUTOR", R.string.joywork_project_setting_sort_owner), //  执行人
        EXT_STATUS("EXT_STATUS",R.string.joywork_project_setting_filter_status),// 待办状态
        PROJECT("PROJECT", R.string.joywork_self_order), // 所属清单
        BIZ_CODE("BIZ_CODE", R.string.joywork2_grouper_source), // 待办来源
        SOURCE("SOURCE", R.string.joywork2_grouper_primary_source), //  一级分类
        SUB_SOURCE("SUB_SOURCE", R.string.joywork2_grouper_secondary_source); //  二级分类


        public final String value;
        public final int textId;

        Field(String value, @StringRes int textId) {
            this.value = value;
            this.textId = textId;
        }
    }
}
