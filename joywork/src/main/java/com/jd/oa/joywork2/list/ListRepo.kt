package com.jd.oa.joywork2.list

import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.self.base.SelfListRepo
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.ResultWithoutGroup
import com.jd.oa.joywork2.backend.JoyWorkExceptionWithMessage
import com.jd.oa.joywork2.backend.JoyWorkHttpException
import com.jd.oa.joywork2.backend.ParseResponse
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper
import com.jd.oa.joywork2.bean.JoyWorkWrapper2
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.main.fillHashMap
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.joywork2.menu.fillHashMap
import com.jd.oa.network.SimpleReqCallbackAdapter
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

interface ListRepo {
    // 返回 Any 是因为不同接口返回的数据结构不同
    suspend fun list(condition: ConditionState, item: MenuItem, dynamicLimitSize: Int = ProjectRepo.PAGE_LIMIT, regionType: String? = null): Any

    suspend fun loadMore(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        regionType: String
    ): Any
}

object GetGroupedTimeList : ListRepo {
    override suspend fun list(condition: ConditionState, item: MenuItem, dynamicLimitSize: Int, regionType: String?) =
        suspendCancellableCoroutine<JoyWorkWrapper2> { coroutine ->
            val params = HashMap<String, Any>()
            item.fillHashMap(params)
            (condition as? ConditionState.Condition)?.fillHashMap(params)
            params["offset"] = 0
            params["limit"] = dynamicLimitSize
            if(regionType.isLegalString()){
                params["groupKey"] = regionType!!
            }
            val headers = HashMap<String, String>()
            networkInternal(params, headers, coroutine)
        }

    override suspend fun loadMore(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        regionType: String
    ) = suspendCancellableCoroutine<JoyWorkWrapper2> { coroutine ->
        val params = HashMap<String, Any>()
        item.fillHashMap(params)
        (condition as? ConditionState.Condition)?.fillHashMap(params)
        params["offset"] = offset
        params["limit"] = ProjectRepo.PAGE_LIMIT
        params["groupKey"] = regionType
        val headers = HashMap<String, String>()
        networkInternal(params, headers, coroutine)
    }

    private fun networkInternal(
        params: HashMap<String, Any>,
        headers: HashMap<String, String>,
        coroutine: CancellableContinuation<JoyWorkWrapper2>
    ) {
        HttpManager.color().post(
            params,
            headers,
            "joywork.getGroupedTimeList",
            repoCallback<JoyWorkWrapper2>(coroutine)
        )
    }
}

object GetCustomGroupList : ListRepo {
    override suspend fun list(condition: ConditionState, item: MenuItem, dynamicLimitSize: Int, regionType: String?) =
        suspendCancellableCoroutine<JoyWorkCustomGrouper> { coroutine ->
            val params = HashMap<String, Any>()
            item.fillHashMap(params)
            (condition as? ConditionState.Condition)?.fillHashMap(params)
            params["offset"] = 0
            params["limit"] = dynamicLimitSize
            params["need"] = 50
            if(regionType.isLegalString()){
                params["groupId"] = regionType!!
            }
            val headers = HashMap<String, String>()
//            HttpManager.post(null, params, callback, "joywork.getCustomGroupList")
            networkInternal(params, headers, coroutine)
        }

    override suspend fun loadMore(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        regionType: String
    ) = suspendCancellableCoroutine<JoyWorkCustomGrouper> { coroutine ->

        val params = HashMap<String, Any>()
        item.fillHashMap(params)
        (condition as? ConditionState.Condition)?.fillHashMap(params)
        params["offset"] = offset
        params["limit"] = ProjectRepo.PAGE_LIMIT
        params["groupId"] = regionType
        val headers = HashMap<String, String>()
        networkInternal(params, headers, coroutine)
    }

    private fun networkInternal(
        params: HashMap<String, Any>,
        headers: HashMap<String, String>,
        coroutine: CancellableContinuation<JoyWorkCustomGrouper>
    ) {
        HttpManager.color().post(
            params,
            headers,
            "joywork.getCustomGroupList",
            repoCallback<JoyWorkCustomGrouper>(coroutine)
        )
    }
}

object FindMyTasksRepo : ListRepo {
    override suspend fun list(condition: ConditionState, item: MenuItem, dynamicLimitSize: Int, regionType: String?) =
        suspendCancellableCoroutine<JoyWorkWrapper> { coroutine ->
            networkInternal(0, item, coroutine)
        }

    override suspend fun loadMore(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        regionType: String
    ) = suspendCancellableCoroutine<JoyWorkWrapper>
    { coroutine ->
        networkInternal(offset, item, coroutine)
    }

    private fun networkInternal(
        offset: Int,
        item: MenuItem,
        coroutine: CancellableContinuation<JoyWorkWrapper>,
    ) {

        val callback = object : JoyWorkVMCallback() {
            override fun onError(msg: String) {
                coroutine.resumeWithException(
                    JoyWorkExceptionWithMessage(
                        JoyWorkEx.filterErrorMsg(
                            msg
                        )
                    )
                )
            }

            override fun call(wrapper: JoyWorkWrapper, rawData: String) {
                coroutine.resumeWith(Result.success(wrapper))
            }
        }
        val role = when (item) {
            MenuItem.MyHandle -> TaskUserRole.OWNER
            MenuItem.MyCooperation -> TaskUserRole.EXECUTOR
            MenuItem.MyAssign -> TaskUserRole.ASSIGN
            else -> {
                callback.onError("")
                return
            }
        }
        SelfListRepo.listFinishTasks(role, offset, callback)
    }
}

object TileListRepo : ListRepo {
    override suspend fun list(condition: ConditionState, item: MenuItem, dynamicLimitSize: Int, regionType: String?) =
        suspendCancellableCoroutine<ResultWithoutGroup> { coroutine ->
            networkInternal(condition, item, 0, coroutine)
        }

    override suspend fun loadMore(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        regionType: String
    ) = suspendCancellableCoroutine<ResultWithoutGroup>
    { coroutine ->
        networkInternal(condition, item, offset, coroutine)
    }

    private fun networkInternal(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        coroutine: CancellableContinuation<ResultWithoutGroup>,
    ) {
        val callback = object : SimpleRequestCallback<String>() {
            override fun onFailure(exception: HttpException?, info: String?) {
                coroutine.resumeWithException(
                    JoyWorkExceptionWithMessage(
                        JoyWorkEx.filterErrorMsg(
                            info
                        )
                    )
                )
            }

            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val result: String? = info?.result
                if (result == null) {
                    onFailure(null, "")
                    return
                }
                val response =
                    ParseResponse.parse<ResultWithoutGroup>(
                        result,
                        ResultWithoutGroup::class.java
                    )
                if (response.isSuccessful) {
                    val map = response.data
                    coroutine.resumeWith(Result.success(map))
                } else {
                    onFailure(null, response.errorMessage)
                }
            }
        }
        val params = HashMap<String, Any>()
        item.fillHashMap(params)
        (condition as? ConditionState.Condition)?.fillHashMap(params)
        params["offset"] = offset
        params["limit"] = ProjectRepo.PAGE_LIMIT
        val headers = HashMap<String, String>()
        HttpManager.color().post(
            params,
            headers,
            "joywork.getTileList",
            repoCallback<ResultWithoutGroup>(coroutine)
        )
    }
}

inline fun <reified T> repoCallback(coroutine: CancellableContinuation<T>): SimpleRequestCallback<String> =
    object : SimpleRequestCallback<String>() {

        override fun onFailure(exception: HttpException?, info: String?) {
            super.onFailure(exception, info)
            onFailureInner(null, info)
        }

        private fun onFailureInner(code: String?, message: String?) {
            coroutine.resumeWithException(
                JoyWorkHttpException(
                    JoyWorkEx.filterErrorMsg(
                        message
                    ),
                    code ?: "-1"
                )
            )
        }

        override fun onSuccess(info: ResponseInfo<String>?) {
            super.onSuccess(info)
            val result: String? = info?.result
            if (result == null) {
                onFailureInner(null, "")
                return
            }
            val response =
                ParseResponse.parse<T>(
                    result,
                    T::class.java
                )
            if (response.isSuccessful) {
                val map = response.data
                coroutine.resumeWith(Result.success(map))
            } else {
                onFailureInner(response.errorCode, response.errorMessage)
            }
        }

    }

object CustomFieldsRepo {
    //获取所有扩展列数据
    fun getProjectSysAllExtList(callback: (Boolean, CustomFieldGroupOuter?, String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["jdmeAppId"] = HttpManager.getConfig().userInfo.appId
        HttpManager.color().post(
            params,
            null, "joywork.getProjectSysAllExtList",
            SimpleReqCallbackAdapter<CustomFieldGroupOuter>(object :
                AbsReqCallback<CustomFieldGroupOuter>(CustomFieldGroupOuter::class.java) {
                override fun onSuccess(
                    jsonObject: CustomFieldGroupOuter?,
                    tArray: MutableList<CustomFieldGroupOuter>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.invoke(true, this, null)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, null, JoyWorkEx.filterErrorMsg(errorMsg))
                }
            })
        )
    }
}