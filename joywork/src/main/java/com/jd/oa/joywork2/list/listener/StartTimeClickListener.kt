package com.jd.oa.joywork2.list.listener

import android.view.View
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.canUpdateDeadline
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.isLegalMap
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.notifyRiskUpdateWhenEndTimeUpdate
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork2.list.createValue
import com.jd.oa.joywork2.list.enableView
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import java.util.Objects

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/10/8 14:44
 */
class StartTimeClickListener(
    val position: Int,
    val work: JoyWork,
    val onStartTimeChangedListener: OnStartTimeChangedListener?
) : View.OnClickListener {


    override fun onClick(view: View?) {
        if (view == null) return
        if (!work.canUpdateDeadline()) {
            return
        }
        enableView(false, view)
        val value = createValue(work)
        value.type = Value.TYPE_START_TIME
        value.replaceAlertType(AlertType.createFromString(work.remindStr))
        value.duplicateEnum = DuplicateEnum.getByValue(work.cycle)
        ShortcutManager(null, view.context).selectTime(value) {
            if (it && !value.isClean) {
                // 取消
                enableView(true, view)
                return@selectTime
            }
            val params = HashMap<String, Any>()
            if (!Objects.equals(
                    value.startTime,
                    work.startTime
                )
            ) {
                params["startTime"] =
                    if (value.startTime.isLegalTimestamp()) value.startTime else -1
            }
            if (!Objects.equals(
                    value.endTime,
                    work.endTime
                ) && value.endTime.isLegalTimestamp()
            ) {
                notifyRiskUpdateWhenEndTimeUpdate(view.context, value.endTime, work.endTime)
                params["endTime"] =
                    if (value.endTime.isLegalTimestamp()) value.endTime else -1
            }
            if (value.mAlertType.isEmpty()) {
                value.mAlertType.add(AlertType.NO)
            }
            if (!AlertType.equals(
                    AlertType.createFromString(work.remindStr),
                    value.mAlertType
                )
            ) {
                params["remindStr"] = AlertType.valueToString(value.mAlertType)
            }
            val c = (value.duplicateEnum ?: DuplicateEnum.NO).value
            if (!Objects.equals(c, work.cycle)) {
                params["cycle"] = c
            }
            if (!params.isLegalMap()) {
                enableView(true, view)
                return@selectTime
            }
            TaskDetailWebservice.postTaskUpdate(
                work.taskId,
                params,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            if (value.startTime.isLegalTimestamp())
                                work.startTime = value.startTime
                            if (value.endTime.isLegalTimestamp())
                                work.endTime = value.endTime
                            onStartTimeChangedListener?.onStartTimeChanged(position, work)
                        }
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }
                })
        }
    }

    interface OnStartTimeChangedListener {
        fun onStartTimeChanged(position: Int, work: JoyWork)
    }
}