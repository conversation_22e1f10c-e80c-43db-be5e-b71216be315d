package com.jd.oa.joywork2.viewholder

import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork2.list.calendar.view.HorizontalLabelLayout
import com.jd.oa.ui.IconFontView

class WorkItemViewHolder(
    itemView: View
) : RecyclerView.ViewHolder(
    itemView
) {

    //优先级
    val priorityPlaceHolder: View by lazy {
        itemView.findViewById(R.id.priority_value_vh)
    }

    val root: View by lazy {
        itemView.findViewById(R.id.root)
    }

    //标题
    val title: TextView by lazy {
        itemView.findViewById(R.id.title)
    }

    //checkBox
    val cb: TextView by lazy {
        itemView.findViewById(R.id.cb_task)
    }


    val cb2: ImageView by lazy {
        itemView.findViewById(R.id.cb_task2)
    }

    val cbContainer: View by lazy {
        itemView.findViewById(R.id.cb_task_container)
    }

    //第二行
    val secondLine: LinearLayout by lazy {
        itemView.findViewById(R.id.second_line)
    }

    //开始时间
    val startTime: TextView by lazy {
        itemView.findViewById(R.id.start_time)
    }

    //截止时间
    val deadline: TextView by lazy {
        itemView.findViewById(R.id.deadline)
    }


    val hyphen: TextView by lazy {
        itemView.findViewById(R.id.hyphen)
    }


    //头像
    val mAvatarView: JoyWorkAvatarView by lazy {
        itemView.findViewById(R.id.mAvatarView)
    }

    //评论
    val commentDate: TextView by lazy {
        itemView.findViewById(R.id.comment_date)
    }


    //第三行
    val thirdLine: LinearLayout by lazy {
        itemView.findViewById(R.id.third_line)
    }


    //头像
    val mAvatarViewThird: JoyWorkAvatarView by lazy {
        itemView.findViewById(R.id.mAvatarView_third)
    }

    //评论
    val commentDateThird: TextView by lazy {
        itemView.findViewById(R.id.comment_date_third)
    }

    // 标签
    val labelContainer: HorizontalLabelLayout by lazy {
        itemView.findViewById(R.id.label_container)
    }

    // 操作按钮
    val operateButtonContainer: LinearLayout by lazy {
        itemView.findViewById(R.id.operate_button_container)
    }

    // 更多按钮
    val moreButton: IconFontView by lazy {
        itemView.findViewById(R.id.more_button_icon)
    }
}