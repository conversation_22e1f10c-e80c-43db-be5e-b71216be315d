package com.jd.oa.joywork2.main

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.R
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.main.condition.CalendarConditionView
import com.jd.oa.joywork2.main.condition.FilterConditionView
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

class JoyConditionFragment : BaseFragment() {

    companion object {

        @JvmStatic
        fun tryLoadConditionView(
            fragmentManager: FragmentManager,
            layoutId: Int,
            type: Int = -1,
            isProjectList: Boolean = false
        ) {
            val tag = "JoyConditionFragment"
            if (fragmentManager.findFragmentByTag(tag) == null) {
                fragmentManager.commit(true) {
                    setReorderingAllowed(true)
                    replace(
                        layoutId,
                        JoyConditionFragment().also {
                            it.arguments = Bundle().apply {
                                putInt("type", type)
                                putBoolean("isProjectList", isProjectList)
                            }
                        },
                        tag
                    )
                }
            }
        }
    }

    //是否是清单页面
    var isProjectList: Boolean = false

    private val conditionViewModel: ConditionViewModel by activityViewModels()

    private val conditionRoot: ViewGroup by bindView(R.id.condition_root)

    private var filterConditionView: FilterConditionView? = null
    private var calendarConditionView: CalendarConditionView? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val type = arguments?.getInt("type", -1) ?: -1
        isProjectList = arguments?.getBoolean("isProjectList", false) ?: false
        filterConditionView = FilterConditionView(type, this, conditionViewModel, isProjectList)
        calendarConditionView = CalendarConditionView(this, conditionViewModel, isProjectList)
        return inflater.inflate(R.layout.joywork2_condition_fragment, container, false)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        filterConditionView?.onViewCreated(view)
        calendarConditionView?.onViewCreated(view)
        if (isProjectList) {
            conditionRoot.visible()
        }
        initObservers()
    }


    private fun initObservers() {
        conditionViewModel.conditionContainerShow.observe(viewLifecycleOwner) {
            if (it) conditionRoot.visible() else conditionRoot.gone()
        }
        conditionViewModel.showTypeLiveData.observe(viewLifecycleOwner) {
            if (it == null) {
                return@observe
            }
            if (it.second == TodoShowType.CALENDAR) {
                calendarConditionView?.show()
                filterConditionView?.hide()
            } else {
                calendarConditionView?.hide()
                filterConditionView?.show()
            }
        }
        conditionViewModel.calendarTaskStateLD.observe(viewLifecycleOwner) {
            if (it == null) {
                return@observe
            }
            calendarConditionView?.updateState(it)
        }
    }
}