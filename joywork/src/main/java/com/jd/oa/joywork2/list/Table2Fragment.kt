package com.jd.oa.joywork2.list

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.R
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.joywork.view.red
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.gone
import com.jd.oa.utils.vertical

abstract class Table2Fragment : BaseFragment() {

    protected val mRv: HRecyclerView by bindView(R.id.rv)
    protected val titleContainer: FrameLayout by bindView(R.id.title_container)
    protected val contentContainer: FrameLayout by bindView(R.id.content_container)
    protected val mRefresh: SwipeRefreshLayout by bindView(R.id.refresh)
    protected val mTitleGroup: ViewGroup by bindView(R.id.title_container_child)

    abstract fun onCreateCeilProcessorFactory(): CeilProcessorFactory

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.joywork_table_frg, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val processors = onCreateCeilProcessorFactory().create()
        processors.forEach {
            addTitleItem(it.getTitleString(view.context), it.isMain(), processors.size)
        }

        mRv.vertical()
        mRv.brotherView.add(view.findViewById(R.id.title_container))
        mRefresh.red(requireActivity())
        onSetupView(mRv, mRefresh)
    }

    protected fun addTitleItem(titleStr: String, isMain: Boolean, processorNum: Int) {
        val titleView = layoutInflater.inflate(R.layout.joywork_table_frg_title, mTitleGroup, false)

        val name = titleView.findViewById<TextView>(R.id.name)
        if (isMain) {
            val w = if (processorNum > 1) {
                CommonUtils.getScreentWidth(mTitleGroup.context) * 4 / 5
            } else {
                CommonUtils.getScreentWidth(mTitleGroup.context)
            }
            titleView.findViewById<View>(R.id.mRoot).layoutParams.width = w
        }
        name.text = titleStr

        val divider = titleView.findViewById<View>(R.id.mDivider)
        divider.gone()
        mTitleGroup.addView(titleView)
    }

    open fun refreshTitleItems(factory: CeilProcessorFactory) {
        if (context == null) return
        mTitleGroup.removeAllViews()
        val processors = factory.create()
        processors.forEach {
            addTitleItem(it.getTitleString(requireContext()), it.isMain(), processors.size)
        }
    }

    abstract fun onSetupView(rv: HRecyclerView, refreshLayout: SwipeRefreshLayout)
}