package com.jd.oa.joywork2.main.condition

import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.Effect
import com.jd.oa.joywork2.main.JoyConditionFragment
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.bindClick
import com.jd.oa.utils.gone
import com.jd.oa.utils.invisible
import com.jd.oa.utils.visible

/**
 * Created by AS
 * <AUTHOR> zhen<PERSON><PERSON><PERSON>
 * @create 2024/9/10 11:20
 * type
 * @see JoyWorkProjectList.TYPE_NORMAL
 */
class FilterConditionView(
    val type: Int,
    fragment: JoyConditionFragment,
    conditionViewModel: ConditionViewModel,
    isProjectList: Boolean = false,
) : ConditionView(fragment, conditionViewModel, isProjectList) {

    private var mStatusContainer: View? = null
    private var mSorterContainer: View? = null
    private var mGrouperContainer: View? = null

    private val reset by fragment.bindView<LinearLayout>(R.id.reset)

    private val mConditionStatusText by fragment.bindView<TextView>(R.id.mConditionStatusText)
    private val mConditionSorterText by fragment.bindView<TextView>(R.id.mConditionSorterText)
    private val mConditionGrouperText by fragment.bindView<TextView>(R.id.mConditionGrouperText)
    override val conditionView: View by fragment.bindView<TextView>(R.id.filter_condition)

    override fun onViewCreated(view: View) {
        mStatusContainer = view.bindClick(R.id.mStatusContainer) {
            conditionViewModel.showStatusDialog(it.context, it)
        }
        mGrouperContainer = view.bindClick(R.id.mGrouperContainer) {
            conditionViewModel.showGrouperDialog(it.context, it)
        }
        mSorterContainer = view.bindClick(R.id.mSortContainer) {
            conditionViewModel.showSorterDialog(it.context, it)
        }
        reset.setOnClickListener {
            conditionViewModel.resetCondition()
        }

        //清单页面要根据type隐藏排序和分组
        if (isProjectList && !isNormal()) {
            mSorterContainer.gone()
            mGrouperContainer.gone()
        }
        showConditionByState(conditionViewModel.defaultCondition())
        initObservers()
    }

    private fun showConditionByState(state: ConditionState.Condition) {
        val context = fragment.context ?: return
        if (state.grouper.disable) {
            mGrouperContainer?.isEnabled = false
            mConditionGrouperText.setText(R.string.joywork2_grouper_title)
        } else {
            mGrouperContainer?.isEnabled = true
            mConditionGrouperText.text =
                context.getString(
                    R.string.joywork2_grouper_text,
                    state.grouper.getText(context)
                )
        }
        if (state.sorter.disable) {
            mSorterContainer?.isEnabled = false
            mConditionSorterText.setText(R.string.joywork_project_setting_sort)
        } else {
            mSorterContainer?.isEnabled = true
            mConditionSorterText.text =
                context.getString(R.string.joywork2_sorter_text, state.sorter.getText(context))
        }
        mConditionStatusText.setText(state.status.getStringId())
    }

    private fun initObservers() {
        conditionViewModel.mConditionValueLiveData.observe(fragment) {
            if (it !is ConditionState.Condition) return@observe
            showConditionByState(it)
        }
        conditionViewModel.effectLiveData.observe(fragment) {
            when (it) {
                is Effect.StatusSelectedStatus -> {
                    mStatusContainer?.isSelected = it.selected
                }

                is Effect.GrouperSelectedStatus -> {
                    mGrouperContainer?.isSelected = it.selected
                }

                is Effect.SorterSelectedStatus -> {
                    mSorterContainer?.isSelected = it.selected
                }

                else -> {}
            }
        }

        conditionViewModel.resetButtonState.observe(fragment) {
            if (it) reset.visible() else reset.invisible()
        }
    }


    /**
     * 是否是正常清单页面
     * 非正常清单页面，只能用状态筛选器
     */
    private fun isNormal(): Boolean = type == JoyWorkProjectList.TYPE_NORMAL
}