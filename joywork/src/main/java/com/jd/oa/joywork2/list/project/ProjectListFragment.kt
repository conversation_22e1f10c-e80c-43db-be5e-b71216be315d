package com.jd.oa.joywork2.list.project

import android.os.Bundle
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork2.list.JoyWork2GroupBaseFragment
import com.jd.oa.joywork2.main.JoyWorkViewModel

/**
 * 清单页面，复用个人里面列表的逻辑
 */
class ProjectListFragment : JoyWork2GroupBaseFragment() {

    companion object {
        @JvmStatic
        fun loadList(
            fragmentManager: FragmentManager,
            layoutId: Int,
            projectId: String,
            forceReplace: Boolean = false,
            isArchiveProject: Boolean = false
        ) {
            val tag = "ProjectListFragment"
            if (fragmentManager.findFragmentByTag(tag) == null || forceReplace) {
                fragmentManager.commit(true) {
                    setReorderingAllowed(true)
                    replace(
                        layoutId,
                        ProjectListFragment().also {
                            it.arguments = Bundle().apply {
                                putString("projectId", projectId)
                                putBoolean("isArchiveProject", isArchiveProject)
                            }
                        },
                        tag
                    )
                }
            }
        }
    }

    override fun onClickAdd(view: View) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.isProject = true
        tmpData.taskListTypeEnum = getTaskListType()
        val groups = getGroupConfig()
        val dialogO =
            JoyWorkShortcutCreator.getShortcutDialog(
                requireActivity(),
                tmpData = tmpData,
                showGroup = true,
                projectId = getProjectId(),
                groups = groups,
                conditionViewModel.viewIdFactory?.invoke(),
                conditionViewModel.viewTypeFactory?.invoke()
            )
        dialogO.showSnackBar = true
        dialogO.successCallback = { _, _, dialog ->
            dialog?.dismiss()
            mListViewModel.initListDelay()
        }
        dialogO.show()
    }

    private val mMainViewModel: JoyWorkViewModel by activityViewModels()

    private fun getGroupConfig(): List<Group> {
        val groups = mMainViewModel.detailLiveData.value?.groups ?: ArrayList()
        val dg = groups.map {
            Group().apply {
                groupId = it.groupId
                projectId = it.projectId
                title = it.title
                type = it.type
            }
        }
        return dg
    }

    override fun getGroups() = getGroupConfig()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mListViewModel.updateItem(conditionViewModel.mMenuItemFactory!!.invoke())
    }


    override fun getProjectId(): String = arguments?.getString("projectId") ?: ""

    override fun getTaskListType(): TaskListTypeEnum = TaskListTypeEnum.PROJECT

    override fun getIsArchiveProject(): Boolean = arguments?.getBoolean("isArchiveProject") ?: false

    override fun isProject() = true
}
