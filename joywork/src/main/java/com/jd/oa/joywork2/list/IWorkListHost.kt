package com.jd.oa.joywork2.list

import android.app.Activity
import androidx.fragment.app.Fragment
import com.jd.oa.joywork.bean.JoyWork

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/13 01:24
 *
 * 用于获取宿主来启动别的页面
 * 优先Fragment，以保证回调能回调到Fragment
 *
 */
interface IWorkListHost {

    fun getHostFragment(): Fragment?

    fun getHostActivity(): Activity?

    fun onStateChanged(position: Int, work: JoyWork)
}