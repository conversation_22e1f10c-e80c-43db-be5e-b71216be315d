package com.jd.oa.joywork2.list

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.GsonBuilder
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.Dispatchers
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/29 00:58
 * 只负责container和list之前传递数据
 */
class ListContainerInheritedVM<T>(app: Application) : AndroidViewModel(app) {

    var movable = false

    val createWorkLD = MutableLiveData<Pair<T, JoyWork>>()

    val detailReturn = MutableLiveData(false)

    fun onCreateWorkSuccess(key: T?, result: String?) {
        if (result.isNullOrEmpty()) {
            return
        }
        if (key == null) {
            return
        }
        viewModelScope.safeLaunch(Dispatchers.Default) {
            val obj = JSONObject(result)
            if ("0" == obj.getString("errorCode")) {
                val content = obj.getJSONObject("content").toString()
                val gson = GsonBuilder().serializeNulls().create()
                val entity = gson.fromJson(content, JoyWork::class.java)
                createWorkLD.postValue(key to entity)
            }
        }
    }


}