package com.jd.oa.joywork2.backend

import kotlinx.coroutines.CancellableContinuation
import kotlin.coroutines.Continuation
import kotlin.coroutines.resumeWithException

object CoroutineUtils {

    fun CancellableContinuation<*>.isSafe(): Boolean {
        return isActive && !isCancelled && !isCompleted
    }

    fun <T> CancellableContinuation<in T>.safeResumeWith(value: Result<T>) {
        if (isSafe()) {
            resumeWith(value)
        }
    }

    fun <T> CancellableContinuation<in T>.safeResumeWithException(value: Exception) {
        if (isSafe()) {
            resumeWithException(value)
        }
    }
}