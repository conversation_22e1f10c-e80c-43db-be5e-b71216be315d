package com.jd.oa.joywork2.backend;

import android.text.TextUtils;

import com.jd.oa.network.httpmanager.HttpManager;
import com.wangyin.payment.jdpaysdk.util.GsonUtil;

import org.json.JSONObject;

import java.lang.reflect.Type;

public class ParseResponse<T> {
    private static final String TAG = "ParseResponse";
    protected String errorCode;
    protected String errorMessage;
    protected String body;
    protected T data;

    public static <T> ParseResponse<T> parse(String body, Type type, String key) {
        ParseResponse response = new ParseResponse(body);
        String errorMsg = null;
        String errorCode = "1";
        try {
            JSONObject jsonObject = new JSONObject(body);
            errorCode = jsonObject.getString("errorCode");
            response.setErrorCode(errorCode);
            if (jsonObject.has("errorMsg")) {
                errorMsg = jsonObject.getString("errorMsg");
            }
            response.setErrorMessage(errorMsg);
            String content;
            if (!TextUtils.isEmpty(key)) {
                content = jsonObject.getJSONObject("content").getString(key);
            } else {
                content = jsonObject.getString("content");
            }

            if (content != null) {
                if (type == String.class) {
                    response.setData(content);
                } else {
                    T data = GsonUtil.fromJson(content, type);
                    response.setData(data);
                }
            }
            if (!response.isSuccessful()) {
                return response;
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.setErrorCode(errorCode);
            if (TextUtils.isEmpty(errorMsg)) {
                response.setErrorMessage(HttpManager.getContext().getString(com.jd.oa.network.R.string.me_data_parse_error));
            } else {
                response.setErrorMessage(errorMsg);
            }
        }
        return response;
    }

    public static <T> ParseResponse<T> parse(String body, Type type) {
        return parse(body, type, "");
    }

    public ParseResponse() {
    }

    public ParseResponse(String body) {
        this.body = body;
    }

    public boolean isSuccessful() {
        return "0".equals(errorCode);
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getBody() {
        return body;
    }

}
