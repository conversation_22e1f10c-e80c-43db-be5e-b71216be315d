package com.jd.oa.joywork2.bean.title;

import android.graphics.Color;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.bean.JoyWorkTitle;

public class JoyWork2Title extends JoyWorkTitle {

    private int iconId;
    private String title;
    private int count;
    private int color;
    private Object extra;

    public JoyWork2Title() {
        // joywork2 中所有参数都无用，需要的参数自己继承该类自己添加
        super(R.string.icon_ai, "", 0, Color.TRANSPARENT);
    }
}
