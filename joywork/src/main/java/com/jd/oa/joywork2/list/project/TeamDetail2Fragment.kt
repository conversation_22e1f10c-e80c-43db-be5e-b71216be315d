package com.jd.oa.joywork2.list.project

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.JoyWorkProjectList.ListDTO
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.support.PageSharingDataManager
import com.jd.oa.joywork.support.ProjectSettingPageData
import com.jd.oa.joywork.team.ProjectSettingActivity
import com.jd.oa.joywork.team.ProjectSettingActivity2
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.list.calendar.TeamCalendarListContainer
import com.jd.oa.joywork2.list.kanban.TeamKanbanListContainer
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.JoyConditionFragment
import com.jd.oa.joywork2.main.JoyWorkViewModel
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.joywork2.view.WorkFloatMenu
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.safeLaunch
import com.jd.oa.utils.visible

/**
 * 复用JoyWork2GroupFragment的一些逻辑
 */
class TeamDetail2Fragment : BaseFragment(), View.OnClickListener {

    private lateinit var pageData: ProjectSettingPageData
    private val mMainViewModel: JoyWorkViewModel by activityViewModels()
    private val conditionViewModel: ConditionViewModel by activityViewModels()

    private val mTitleView: TextView by bindView(R.id.title)
    private val mShowCondition: IconFontView by bindView(R.id.show_condition)
    private val conditionRoot: ViewGroup by bindView(R.id.condition_root)
    private val mListViewType: IconFontView by bindView(R.id.view_type)
    private val mBack: IconFontView by bindView(R.id.back)
    private val mSetting: IconFontView by bindView(R.id.setting)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        kotlin.runCatching {
            (requireActivity() as? FunctionActivity)?.hideAction()
        }
        initUIConfig()
        initObserver()
        return inflater.inflate(R.layout.jdme_joywork_fragment_project_detail2, container, false)
    }

    private fun setupView(listDTO: ListDTO) {
        mTitleView.text = listDTO.title
        conditionViewModel.init(true, type = getType()) {
            MenuItem.Project(listDTO)
        }
    }

    private fun initObserver() {
        mMainViewModel.detailLiveData.observe(viewLifecycleOwner) {
            if (it == null) {
                return@observe
            }
            setupView(it)
            conditionViewModel.updatePermissions(it.permissions)
        }

        mMainViewModel.requestError.observe(viewLifecycleOwner) {
            if (it.first) {
                if (!it.second.isNullOrEmpty()) {
                    ToastUtils.showInfoToast(it.second)
                }
                activity?.finish()
            }
        }

        conditionViewModel.showTypeLiveData.observe(viewLifecycleOwner) {
            if (it == null) {
                return@observe
            }
            mListViewType.setText(it.second.resId)
            loadFragmentByType()
        }

        conditionViewModel.conditionContainerShow.observe(viewLifecycleOwner) {
            if (it) conditionRoot.visible() else conditionRoot.gone()
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mListViewType.setOnClickListener(this)
        mShowCondition.setOnClickListener(this)
        mBack.setOnClickListener(this)
        mSetting.setOnClickListener(this)

        JoyConditionFragment.tryLoadConditionView(
            childFragmentManager, R.id.condition_container, getType(), true
        )
        val listDTO = ListDTO().apply {
            title = getTitle()
            projectId = getProjectId()
            desc = getDesc()
            type = getType()
            icon = getIconUrl2()
        }

        val factory = {
            MenuItem.Project(listDTO)
        }
        conditionViewModel.init(true, menuItemFactory = factory)
        lifecycleScope.safeLaunch {
            conditionViewModel.initCondition({
                factory().getViewId()
            }, {
                factory().getViewType()
            })
        }
        if (mMainViewModel.detailLiveData.value == null) {
            mMainViewModel.updateListDTO(listDTO)
        }

//        ProjectListFragment.tryListView(childFragmentManager, R.id.list_container, getProjectId())
    }

    private fun loadFragmentByType(forceReplace: Boolean = false) {
        val showType = conditionViewModel.showTypeLiveData.value
        when (showType?.second) {
            TodoShowType.TABLE, TodoShowType.CARD -> {
                ProjectListFragment.loadList(
                    childFragmentManager,
                    R.id.list_container,
                    getProjectId(),
                    forceReplace,
                    getIsArchiveProject()
                )
            }

            TodoShowType.CALENDAR -> {
                TeamCalendarListContainer.loadList(
                    childFragmentManager,
                    R.id.list_container,
                    getProjectId(),
                    forceReplace,
                    getIsArchiveProject()
                )
            }

            TodoShowType.KANBAN -> {
                TeamKanbanListContainer.loadList(
                    childFragmentManager,
                    R.id.list_container,
                    getProjectId(),
                    forceReplace,
                    getIsArchiveProject()
                )
            }

            else -> {
                ProjectListFragment.loadList(
                    childFragmentManager,
                    R.id.list_container,
                    getProjectId(),
                    forceReplace,
                    getIsArchiveProject()
                )
            }
        }
    }

    private fun initUIConfig() {
        mMainViewModel.init(true)
        mMainViewModel.getProjectDetail(getProjectId())
        mMainViewModel.mainStrategy =
            if (isTab()) JoyWorkMainTabStrategy else JoyWorkMainNormalStrategy
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.back -> {
                requireActivity().finish()
            }

            R.id.setting -> {
                goSetting()
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.JOYWORK_TASKTITLE
                    )
                }
            }

            R.id.show_condition -> {
                conditionViewModel.toggleConditionContainer()
                mShowCondition.isSelected = conditionViewModel.conditionContainerShow.value == true
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_LISTMANAGEMENT
                    )
                }
            }

            R.id.view_type -> {
                WorkFloatMenu.showViewTypeMenu(
                    requireActivity(),
                    conditionViewModel.showType,
                    mListViewType,
                    conditionViewModel.showTypeItems(),
                    conditionViewModel.mMenuItemFactory?.invoke(),
                    conditionViewModel.curCondition
                ) {
                    conditionViewModel.updateListViewType(it)
                }
            }
        }
    }


    private fun goSetting() {
        if (!isNormal()) {
            goSetting2()
            return
        }
        pageData = ProjectSettingPageData()
        val key = PageSharingDataManager.addData(pageData)
        val intent = ProjectSettingActivity.getIntent(
            requireContext(), getProjectId(), key
        )
        startActivityForResult(intent, 100)
    }


    private fun goSetting2() {
        val intent = ProjectSettingActivity2.getIntent(
            requireContext(),
            getProjectId(),
            getTitle(),
            mMainViewModel.detailLiveData.value?.desc ?: ""
        )
        startActivity(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::pageData.isInitialized) {
            PageSharingDataManager.removeData(pageData)
        }

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 100 && resultCode == Activity.RESULT_OK && ::pageData.isInitialized) {
            activity?.setResult(Activity.RESULT_OK)
            pageData.getAction().apply {
                if (this and 1 != 0) {// 关闭界面
                    activity?.finish()
                } else if (this and (1 shl 1) != 0) { // 修改标题
                    mMainViewModel.getProjectDetail(getProjectId())
                } else if (this and (1 shl 2) != 0) {// 刷新界面
                    mMainViewModel.getProjectDetail(getProjectId())
                    loadFragmentByType(true)
                }
            }
        }
    }

    /**
     * 是否是正常清单页面
     */
    private fun isNormal(): Boolean {
        val a = arguments ?: return false
        if (!a.containsKey("type")) {
            return false
        }
        return a.getInt("type", JoyWorkProjectList.TYPE_NORMAL) == JoyWorkProjectList.TYPE_NORMAL
    }


    companion object {
        fun getInstance(
            isTab: Boolean,
            title: String = "",
            id: String,
            type: Int,
            iconUrl: String?,
            isArchiveProject: Boolean
        ): TeamDetail2Fragment {
            val ret = TeamDetail2Fragment()
            val bundle = Bundle()
            bundle.putBoolean("isTab", isTab)
            bundle.putString("title", title)
            bundle.putString("id", id)
            bundle.putInt("type", type)
            bundle.putString("iconUrl", iconUrl ?: "")
            bundle.putBoolean("isArchiveProject", isArchiveProject)
            ret.arguments = bundle
            return ret
        }

        private fun TeamDetail2Fragment.isTab(): Boolean {
            return arguments?.getBoolean("isTab") ?: false
        }

        private fun TeamDetail2Fragment.getTitle(): String {
            return arguments?.getString("title") ?: ""
        }

        private fun TeamDetail2Fragment.getProjectId(): String {
            return arguments?.getString("id") ?: ""
        }

        private fun TeamDetail2Fragment.getType(): Int {
            return arguments?.getInt("type", JoyWorkProjectList.TYPE_NORMAL)
                ?: JoyWorkProjectList.TYPE_NORMAL
        }

        private fun TeamDetail2Fragment.getIconUrl2(): String {
            return arguments?.getString("iconUrl") ?: ""
        }

        private fun TeamDetail2Fragment.getDesc(): String {
            return arguments?.getString("desc") ?: ""
        }

        private fun TeamDetail2Fragment.getIsArchiveProject(): Boolean {
            return arguments?.getBoolean("isArchiveProject") ?: false
        }
    }
}
