package com.jd.oa.joywork2.menu

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.removeAllIf
import com.jd.oa.joywork.team.kpi.PlaceHolderVH
import com.jd.oa.joywork2.backend.ListDivider
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import java.util.Objects

class MenuAdapter(
    private val context: Context,
    private val items: ArrayList<IMenuListItem>,
    private val mMenuViewModel: JoyWork2MenuViewModel,
    private val itemClick: (MenuItem) -> Unit,
    private val titleClick: (MenuTitle) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var archiveListExpandAble = true // 初始化默认展开
    private var archiveProjectList: MutableList<MenuItem> = mutableListOf()
    private val customViewWidget by lazy {
        setOf(MenuTitle(R.string.joywork2_menu_shortcut, null, "1"), ListDivider)
    }

    private val archiveViewWidget by lazy {
        ExpandMenuTitle(R.string.icon_add_box, R.string.joywork2_archive_project, R.string.icon_smalldown)
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mMenuView: MenuItemView = view.findViewById(R.id.mProjectItem)
    }

    inner class TitleVH(view: View) : RecyclerView.ViewHolder(view) {
        val mTitleView: TextView = view.findViewById(R.id.mTitle)
        val mIcon: TextView = view.findViewById(R.id.mIcon)
    }

    inner class ExpandTitleVH(view: View) : RecyclerView.ViewHolder(view) {
        val mExpandTitle: TextView = view.findViewById(R.id.mTitle)
        val mHeadIcon: TextView = view.findViewById(R.id.headIcon)
        val mTailIcon: TextView = view.findViewById(R.id.tailIcon)
    }

    private val mTitleIconClick = View.OnClickListener {
        titleClick(it.tag as MenuTitle)
    }

    override fun getItemViewType(position: Int): Int {
        return when (items[position]) {
            is MenuItem -> TYPE_ITEM
            is MenuTitle -> TYPE_TITLE
            is ListDivider -> TYPE_DIVIDER
            else -> TYPE_EXPAND
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_ITEM -> {
                val view =
                    context.layoutInflater.inflate(
                        R.layout.joywork2_menu_fragment_item,
                        parent,
                        false
                    )
                VH(view)
            }

            TYPE_TITLE -> {
                val view =
                    context.layoutInflater.inflate(
                        R.layout.joywork2_menu_fragment_title,
                        parent,
                        false
                    )
                TitleVH(view)
            }

            TYPE_DIVIDER -> {
                val view =
                    context.layoutInflater.inflate(
                        R.layout.joywork2_menu_fragment_divider,
                        parent,
                        false
                    )
                object : RecyclerView.ViewHolder(view) {}
            }

            TYPE_EXPAND -> {
                val view =
                    context.layoutInflater.inflate(
                        R.layout.joywork2_menu_fragment_expand_title,
                        parent,
                        false
                    )
                ExpandTitleVH(view)
            }

            else -> {
                PlaceHolderVH(parent.context)
            }
        }
    }

    override fun getItemCount(): Int {
        return items.size
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val data = items[position]
        when (holder) {
            is TitleVH -> {
                val item = data as MenuTitle
                holder.mTitleView.setText(item.titleId)
                if (item.iconId == null) {
                    holder.mIcon.gone()
                } else {
                    holder.mIcon.visible()
                    holder.mIcon.setText(item.iconId)
                    holder.mIcon.tag = item
                    holder.mIcon.setOnClickListener(mTitleIconClick)
                }
            }

            is VH -> {
                val item = data as MenuItem
                val view = holder.mMenuView
                val lp = view.layoutParams as RecyclerView.LayoutParams
                lp.leftMargin = if (item.getListType() == TaskListTypeEnum.ARCHIVE_PROJECT) {
                     DensityUtil.dp2px(context, 35f)
                } else {
                    0
                }
                view.set(
                    iconId = item.getIconId(),
                    contentText = item.getTitle(context),
                    countText = 0
                )
                view.tag = item
                view.setOnClickListener {
                    itemClick(it.tag as MenuItem)
                }
                view.changeCountVisible(View.GONE)
                holder.mMenuView.select(
                    Objects.equals(
                        mMenuViewModel.mSelectMenuItemLiveData.value,
                        item
                    )
                )
            }

            is ExpandTitleVH -> {
                val item = data as ExpandMenuTitle
                holder.apply {
                    mHeadIcon.setText(item.headIcon)
                    mExpandTitle.setText(item.titleId)
                    mTailIcon.setText(
                        if (archiveListExpandAble) R.string.icon_smalldown
                        else R.string.icon_smallright
                    )
                    mTailIcon.setOnClickListener {
                        archiveListExpandAble = !archiveListExpandAble
                        if (archiveListExpandAble) {
                            holder.mTailIcon.setText(R.string.icon_smalldown)
                            items.addAll(archiveProjectList)
                            notifyItemRangeInserted(
                                items.indexOfFirst { it is ExpandMenuTitle } + 1,
                                archiveProjectList.size
                            )
                        } else {
                            holder.mTailIcon.setText(R.string.icon_smallright)
                            items.removeAll(archiveProjectList.toSet())
                            notifyDataSetChanged()
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun replaceCustomViews(newItems: List<MenuItem.Custom>) {
        // 先获取旧列表中 CustomView 的数量
        val customItemSize = items.filterIsInstance<MenuItem.Custom>().size
        items.removeAll { it is MenuItem.Custom }
        val isExistCustomMenuTitle = items.any { it is MenuTitle && it.key == "1" }
        if (!newItems.isLegalList() && isExistCustomMenuTitle) {
            // 存在自定义视图标题、数据不合法，清除「自定义视图」
            items.removeAll(customViewWidget)
            notifyItemRangeRemoved(0, customViewWidget.size + customItemSize)
            return
        }
        if (!isExistCustomMenuTitle && newItems.isLegalList()) {
            // 不存在自定义视图标题、数据合法，首先添加「自定义视图」标题
            items.addAll(0, customViewWidget)
            items.addAll(1, newItems)
        } else if (isExistCustomMenuTitle) {
            // 存在标题，之前有数据
            items.addAll(1, newItems)
        }
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateProjectItems(
        multiProjectItems: Triple<List<MenuItem.Project>, List<MenuItem.ArchiveProject>, Boolean>
    ) {
        items.removeAllIf { it is ExpandMenuTitle }
        filterProjectItems(multiProjectItems.first, multiProjectItems.third)
        filterProjectItems(multiProjectItems.second, false)
        notifyDataSetChanged()
    }

    private fun filterProjectItems(projectItems: List<MenuItem>, append: Boolean) {
        if (!projectItems.isLegalList()) return
        if (!append) {
            if (projectItems.first() is MenuItem.ArchiveProject) {
                archiveProjectList.clear()
                archiveProjectList.addAll(projectItems)
                items.removeAllIf { it is MenuItem.ArchiveProject }
                items.add(archiveViewWidget)
                if (!archiveListExpandAble) return
            } else if (projectItems.first() is MenuItem.Project) {
                items.removeAllIf { it is MenuItem.Project }
            }
        }
        val existedIds =
            items.filterIsInstance<MenuItem.Project>().map { it.getViewId() }.toHashSet()
        val newProjectItem = projectItems.filter { !existedIds.contains(it.getViewId()) }
        items.addAll(newProjectItem)
    }

    fun countProject(): Int {
        return items.count {
            it is MenuItem.Project && it !is MenuItem.ArchiveProject
        }
    }

    companion object {
        const val TYPE_ITEM = 0
        const val TYPE_TITLE = 1
        const val TYPE_DIVIDER = 2
        const val TYPE_EXPAND = 3
    }
}