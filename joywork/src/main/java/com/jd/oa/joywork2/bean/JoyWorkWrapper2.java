package com.jd.oa.joywork2.bean;

import java.util.List;

public class JoyWorkWrapper2 {
    private List<JoyWorkGroup2> clientTasks;
    private int total;
    private int pageSize;
    private int page;

    public List<JoyWorkGroup2> getClientTasks() {
        return clientTasks;
    }

    public void setClientTasks(List<JoyWorkGroup2> clientTasks) {
        this.clientTasks = clientTasks;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
