package com.jd.oa.joywork2.list.calendar.view;

public class MeCalendarConstants {
    public static boolean showLunar = false;
    public static boolean showBottomPoint = false;
    //    public  int scheme_text_color = 0XFF
    public static int SCHEME_BG_COLOR = 0xFFFFFFFF;
    public static int SCHEME_BG_RADIUS = 7;
    public static int SCHEME_TEXT_SIZE = 11;

    public static int CURRENT_DAY_TEXT_COLOR = 0xFFFFFFFF;
    public static int CURRENT_DAY_BG_COLOR = 0xFFF63218;

    public static int SELECT_DAY_TEXT_COLOR = 0xFFF63218;
    public static int SELECT_DAY_BG_COLOR = 0xFFFEEBE8;

    //右上角标记距离整个日期View的外边距 同时影响底部圆点
    public static int PADDING = 5;

    public static int BOTTOM_POINT_RADIUS = 2;
}
