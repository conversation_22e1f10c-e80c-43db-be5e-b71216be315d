package com.jd.oa.joywork2.menu

import com.google.gson.reflect.TypeToken
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork2.backend.JoyWorkException
import com.jd.oa.joywork2.backend.JoyWorkExceptionWithMessage
import com.jd.oa.joywork2.backend.ParseResponse
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

object MenuRepo {

    suspend fun getNumber() = suspendCancellableCoroutine<Map<String, Int>> { c ->
        JoyWorkRepo.getNums2 { num ->
            if (num == null) {
                c.resumeWithException(JoyWorkException)
            } else {
                c.resumeWith(Result.success(num))
            }
        }
    }

    suspend fun getCustomViewList() = suspendCancellableCoroutine<List<MenuBean>> { coroutine ->
        val params = HashMap<String, Any>()
        val headers = HashMap<String, String>()
        val callback = object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val response = ParseResponse.parse<List<MenuBean>>(
                    info!!.result,
                    object : TypeToken<List<MenuBean>>() {}.type,
                    "customViewList"
                )
                if (response.isSuccessful) {
                    val map = response.data
                    coroutine.resumeWith(Result.success(map))
                } else {
                    onFailure(null, response.errorMessage)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {

                coroutine.resumeWithException(
                    JoyWorkExceptionWithMessage(
                        JoyWorkEx.filterErrorMsg(
                            info
                        )
                    )
                )
            }
        }
        HttpManager.color()
            .post(params, headers, "joywork.getCustomViewList.v2", callback)
    }

    suspend fun getMyHandle() = suspendCancellableCoroutine<List<MenuBean>> { c ->
        JoyWorkRepo.getMyHandle { list ->
            if (list == null) {
                c.resumeWithException(JoyWorkException)
            } else {
                c.resumeWith(Result.success(list))
            }
        }
    }
}