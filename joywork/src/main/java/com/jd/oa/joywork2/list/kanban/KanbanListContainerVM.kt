package com.jd.oa.joywork2.list.kanban

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.jd.oa.joywork.R
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork2.backend.JoyWork2RegionType
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper
import com.jd.oa.joywork2.bean.JoyWorkWrapper2
import com.jd.oa.joywork2.list.GetCustomGroupList
import com.jd.oa.joywork2.list.GetGroupedTimeList
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.utils.safeLaunch
import com.jd.oa.utils.string

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/23 16:01
 */
class KanbanListContainerVM(val app: Application) : AndroidViewModel(app) {

    val refreshState: MutableLiveData<Boolean> = MutableLiveData(false)

    val loadGroupsResult = MutableLiveData<Triple<Boolean, String?, List<Group>?>>()

    var currentGrouper: ConditionGrouper.Field? = null

    fun queryGroups(
        condition: ConditionState.Condition?,
        menuItem: MenuItem?,
        isTeamList: Boolean
    ) {
        if (condition == null) return
        if (menuItem == null) return
        currentGrouper = condition.grouper.fieldEnum
        viewModelScope.safeLaunch {
//            refreshState.value = true
            val currentGrouper = condition.grouper.fieldEnum
            when (currentGrouper) {
                ConditionGrouper.Field.START_TIME, ConditionGrouper.Field.END_TIME -> {
                    val wrapper: JoyWorkWrapper2 =
                        GetGroupedTimeList.list(condition, menuItem, 0)
                    refreshState.value = false
                    val groups = mutableListOf<Group>()
                    wrapper.clientTasks.forEach {
                        groups.add(
                            Group().apply {
                                val regionType = JoyWork2RegionType.values().firstOrNull { region ->
                                    region.value == it.blockType
                                }
                                title = if (regionType != null) app.getString(
                                    JoyWork2RegionType.getTextId(regionType)
                                ) else ""
                                groupId = it.blockType
                            }
                        )
                    }
                    loadGroupsResult.value = Triple(true, null, groups)
                }

                ConditionGrouper.Field.CUSTOM -> {
                    val customGrouper: JoyWorkCustomGrouper =
                        GetCustomGroupList.list(condition, menuItem, 0)
                    refreshState.value = false
                    val groups = mutableListOf<Group>()
                    customGrouper.safeGroups.forEach {
                        groups.add(
                            Group().apply {
                                title = it.title ?: ""
                                if (!title.isLegalString()) {
                                    title = if (isTeamList) {
                                        app.string(R.string.joywork_task_link_untitled_group)
                                    } else {
                                        app.string(R.string.joywork_self_default_group)
                                    }
                                }
                                groupId = it.groupId
                                type = it.type
                                permissions = it.permissions
                            }
                        )
                    }
                    loadGroupsResult.value = Triple(true, null, groups)
                }

                else -> {
                    refreshState.value = false
                    loadGroupsResult.value = Triple(false, null, emptyList())
                }
            }
        }
    }
}