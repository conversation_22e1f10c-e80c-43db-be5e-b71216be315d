package com.jd.oa.joywork2.input

import android.graphics.Color
import android.text.Editable
import android.text.Selection
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextWatcher
import android.view.KeyEvent
import android.widget.EditText
import com.jd.oa.joywork.bean.JoyWorkUser

class AtInit(private val editText: EditText, private val atRunnable: (AtInit) -> Unit) {
    init {
        val text = editText.text?.toString()
        if (text != null) {
            editText.setText(SpannableString.valueOf(text))
        }
        // 中间不能选中
        editText.setEditableFactory(
            NoCopySpanEditableFactory(
                SelectionSpanWatcher(
                    AtSpan::class
                )
            )
        )
        editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val tempText: String = s?.toString() ?: return
                if (count == 1 && tempText[start] == '@') { // at
                    atRunnable(this@AtInit)
                }
            }

            override fun afterTextChanged(s: Editable?) {

            }
        })
        editText.setOnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_DEL && event.action == KeyEvent.ACTION_DOWN) {
                return@setOnKeyListener KeyCodeDeleteHelper.onDelDown(
                    (v as EditText).text
                )
            }
            return@setOnKeyListener false
        }
    }

    private fun newSpannable(user: JoyWorkUser): Spannable {
        val source = "@${user.realName} "
        val ss = SpannableString.valueOf(source).apply {
            setSpan(AtSpan(Color.RED, user), 0, length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        ss.setSpan(user, 0, ss.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return ss
    }

    fun getUser(): List<JoyWorkUser> {
        val text = editText.text ?: return emptyList()
        val spans = text.getSpans(0, text.length, AtSpan::class.java)
        val ans = ArrayList<JoyWorkUser>()
        spans.forEach {
            ans.add(it.user)
        }
        return ans
    }

    fun appendAt(user: JoyWorkUser) {
        val t = editText.text
        val start = if (t == null) {
            0
        } else {
            val selStart = Selection.getSelectionStart(t)
            if (selStart <= 0) {
                0
            } else {
                selStart - 1
            }
        }
        val newSpan = newSpannable(user)
        (editText.text as SpannableStringBuilder)
            .replace(start, start + 1, newSpan)
    }
}