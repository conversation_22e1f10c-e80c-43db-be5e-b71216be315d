package com.jd.oa.joywork2.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.os.Build
import android.util.AttributeSet
import android.view.View

/**
 * <AUTHOR>
 *
 *
 *
 */
class SimpleUpArrowView : View {

    constructor(context: Context) : super(context)
    constructor(
        context: Context,
        attrs: AttributeSet
    ) : super(context, attrs)

    constructor(
        context: Context,
        attrs: AttributeSet,
        defStyleAttr: Int
    ) : super(context, attrs, defStyleAttr)

    private val paint: Paint = Paint().apply { isAntiAlias = true }
    private val paintShadow: Paint = Paint().apply { isAntiAlias = true }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawShadow(canvas)
        drawArrow(canvas)
    }

    private fun drawShadow(canvas: Canvas) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
            setLayerType(LAYER_TYPE_SOFTWARE, paintShadow)
        }
        val shadowRadius = 25f
        paintShadow.setShadowLayer(shadowRadius, 0f, -shadowRadius / 2, Color.GRAY)
        paintShadow.style = Paint.Style.STROKE
        canvas.drawPath(shadowPath(), paintShadow)
    }

    private fun drawArrow(canvas: Canvas) {
        paint.setColor(Color.WHITE)
        canvas.drawPath(arrowPath(), paint)
    }

    private fun arrowPath(): Path {
        val path = Path()
        val w = width / 2
        val h = height / 2
        path.moveTo(0f, (2 * h).toFloat())
        path.lineTo(w.toFloat(), 0f)
        path.lineTo((2 * w).toFloat(), (2 * h).toFloat())
        path.close()
        return path
    }

    private fun shadowPath(): Path {
        val shadowOffset = 5
        val path = Path()
        val w = width / 2
        val h = height / 2
        path.moveTo(0f + shadowOffset, (2 * h).toFloat())
        path.lineTo(w.toFloat(), 0f + shadowOffset)
        path.lineTo((2 * w).toFloat() - shadowOffset, (2 * h).toFloat())
        path.close()
        return path
    }
}