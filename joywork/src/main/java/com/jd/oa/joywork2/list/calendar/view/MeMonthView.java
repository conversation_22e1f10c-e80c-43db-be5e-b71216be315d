package com.jd.oa.joywork2.list.calendar.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.os.Build;
import android.text.TextUtils;

import androidx.core.content.res.ResourcesCompat;
import androidx.core.graphics.TypefaceCompat;

import com.haibin.calendarview.Calendar;
import com.haibin.calendarview.MonthView;
import com.jd.oa.joywork.R;

public class MeMonthView extends MonthView {
    boolean isShowLunar = false;
    boolean isShowPoint = false;


    private int mRadius;
    /**
     * 标记的文本画笔
     */
    private Paint mTextPaint = new Paint();

    /**
     * 24节气画笔
     */
    private Paint mSolarTermTextPaint = new Paint();

    /**
     * 背景圆点
     */
    private Paint mPointPaint = new Paint();

    /**
     * 今天的背景色
     */
    private Paint mCurrentDayPaint = new Paint();

    /**
     * 圆点半径
     */
    private float mPointRadius;

    private int mPadding;

    private float mCircleRadius;
    /**
     * 标记的圆形背景
     */
    private Paint mSchemeBasicPaint = new Paint();

    private float mSchemeBaseLine;

    public MeMonthView(Context context) {
        super(context);
        this.isShowLunar = MeCalendarConstants.showLunar;
        this.isShowPoint = MeCalendarConstants.showBottomPoint;

        //右上角标记 字 画笔
        mTextPaint.setTextSize(dipToPx(context, MeCalendarConstants.SCHEME_TEXT_SIZE));
        mTextPaint.setColor(0xff00ffff);
        mTextPaint.setAntiAlias(true);
        mTextPaint.setFakeBoldText(false);
        // 节气 字
        mSolarTermTextPaint.setColor(0xff489dff);
        mSolarTermTextPaint.setAntiAlias(true);
        mSolarTermTextPaint.setTextAlign(Paint.Align.CENTER);
        //右上角标记 背景
        mSchemeBasicPaint.setAntiAlias(true);
        mSchemeBasicPaint.setStyle(Paint.Style.FILL);
        mSchemeBasicPaint.setTextAlign(Paint.Align.CENTER);
        mSchemeBasicPaint.setFakeBoldText(true);
        mSchemeBasicPaint.setColor(MeCalendarConstants.SCHEME_BG_COLOR);
        //标记 背景圆半径
        mCircleRadius = dipToPx(getContext(), MeCalendarConstants.SCHEME_BG_RADIUS);

        //今天 背景
        mCurrentDayPaint.setAntiAlias(true);
        mCurrentDayPaint.setStyle(Paint.Style.FILL);
        mCurrentDayPaint.setColor(MeCalendarConstants.CURRENT_DAY_BG_COLOR);
        //今天 字
        mCurDayTextPaint.setColor(MeCalendarConstants.CURRENT_DAY_TEXT_COLOR);

        //下面的原点
        mPointPaint.setAntiAlias(true);
        mPointPaint.setStyle(Paint.Style.FILL);
        mPointPaint.setTextAlign(Paint.Align.CENTER);
        mPointPaint.setColor(Color.RED);
        //右上角标记距离整个日期View的外边距 同时影响底部圆点
        mPadding = dipToPx(getContext(), MeCalendarConstants.PADDING);
        //底部圆点的半径
        mPointRadius = dipToPx(context, MeCalendarConstants.BOTTOM_POINT_RADIUS);
        //计算右上角字的基准线
        Paint.FontMetrics metrics = mSchemeBasicPaint.getFontMetrics();
        mSchemeBaseLine = mCircleRadius - metrics.descent + (metrics.bottom - metrics.top) / 2 + dipToPx(getContext(), 1);

        mSelectedPaint.setColor(MeCalendarConstants.SELECT_DAY_BG_COLOR);
        mSelectTextPaint.setColor(MeCalendarConstants.SELECT_DAY_TEXT_COLOR);

        Typeface tf = Typeface.createFromAsset(context.getAssets(), "fonts/JDZhengHei-01-Regular.ttf");
        tf = TypefaceCompat.create(context, tf, 500, false);
        mCurMonthTextPaint.setColor(0xff333333);
        mCurMonthTextPaint.setTypeface(tf);
        mOtherMonthTextPaint.setColor(0xFFe1e1e1);
        mOtherMonthTextPaint.setTypeface(tf);

        mCurMonthLunarTextPaint.setColor(0xffCFCFCF);
        mCurMonthLunarTextPaint.setTypeface(tf);
        mOtherMonthLunarTextPaint.setColor(0xFFe1e1e1);
        mOtherMonthLunarTextPaint.setTypeface(tf);

        mSelectTextPaint.setTypeface(tf);

        mCurMonthTextPaint.setFakeBoldText(false);
        mOtherMonthTextPaint.setFakeBoldText(false);
        mSelectTextPaint.setFakeBoldText(false);
    }

    @Override
    protected void onPreviewHook() {
        mSolarTermTextPaint.setTextSize(mCurMonthLunarTextPaint.getTextSize());
        mRadius = Math.min(mItemWidth, mItemHeight) / 11 * 5;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
    }

    @Override
    protected boolean onDrawSelected(Canvas canvas, Calendar calendar, int x, int y, boolean hasScheme) {
        int cx = x + mItemWidth / 2;
        int cy = y + mItemHeight / 2;
        canvas.drawCircle(cx, cy, mRadius, mSelectedPaint);
        return true;
    }

    @Override
    protected void onDrawScheme(Canvas canvas, Calendar calendar, int x, int y) {
        if (isShowPoint) {
            boolean isSelected = isSelected(calendar);
            if (isSelected) {
                mPointPaint.setColor(Color.WHITE);
            } else {
                mPointPaint.setColor(Color.GRAY);
            }
            canvas.drawCircle(x + mItemWidth / 2, y + mItemHeight - 3 * mPadding, mPointRadius, mPointPaint);
        }
    }

    @SuppressWarnings("IntegerDivisionInFloatingPointContext")
    @Override
    protected void onDrawText(Canvas canvas, Calendar calendar, int x, int y, boolean hasScheme, boolean isSelected) {
        int cx = x + mItemWidth / 2;
        int cy = y + mItemHeight / 2;
        int top = isShowLunar ? y - mItemHeight / 6 : y;

        if (calendar.isCurrentDay() && !isSelected) {
            canvas.drawCircle(cx, cy, mRadius, mCurrentDayPaint);
        }
        if (hasScheme) {
            //绘制右上角标记
            canvas.drawCircle(x + mItemWidth - mPadding - mCircleRadius / 2, y + mPadding + mCircleRadius, mCircleRadius, mSchemeBasicPaint);
            mTextPaint.setColor(calendar.getSchemeColor());
            canvas.drawText(calendar.getScheme(), x + mItemWidth - mPadding - mCircleRadius, y + mPadding + mSchemeBaseLine, mTextPaint);
        }
        if (isSelected) {
            canvas.drawText(String.valueOf(calendar.getDay()), cx, mTextBaseLine + top,
                    mSelectTextPaint);
            if (isShowLunar)
                canvas.drawText(calendar.getLunar(), cx, mTextBaseLine + y + mItemHeight / 10, mSelectedLunarTextPaint);
        } else {
            canvas.drawText(String.valueOf(calendar.getDay()), cx, mTextBaseLine + top,
                    calendar.isCurrentDay() ? mCurDayTextPaint :
                            calendar.isCurrentMonth() ? mCurMonthTextPaint : mOtherMonthTextPaint);
            if (isShowLunar)
                canvas.drawText(calendar.getLunar(), cx, mTextBaseLine + y + mItemHeight / 10,
                        calendar.isCurrentDay() ? mCurDayLunarTextPaint :
                                calendar.isCurrentMonth() ? !TextUtils.isEmpty(calendar.getSolarTerm()) ? mSolarTermTextPaint :
                                        mCurMonthLunarTextPaint : mOtherMonthLunarTextPaint);
        }
    }

    /**
     * dp转px
     *
     * @param context context
     * @param dpValue dp
     * @return px
     */
    private static int dipToPx(Context context, float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }
}
