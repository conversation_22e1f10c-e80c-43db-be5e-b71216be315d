package com.jd.oa.joywork2.bean;

import com.jd.oa.joywork.bean.JoyWork;
import com.jd.oa.joywork.team.bean.ProjectGroupTypeEnum;
import com.jd.oa.joywork.team.bean.ProjectStatusEnum;

import java.util.ArrayList;
import java.util.List;

public class JoyWorkCustomGrouper {
    public List<String> permissions;
    public List<Group> groups;

    public List<String> getSafePermissions() {
        if (permissions == null) {
            permissions = new ArrayList<>();
        }
        return permissions;
    }

    // getSafeGroups() 方法
    public List<Group> getSafeGroups() {
        if (groups == null) {
            groups = new ArrayList<>();
        }
        return groups;
    }


    public static class Group {
        public Integer total;
        public Integer status;
        public List<JoyWork> tasks;
        public String title;
        public Boolean initialize = false;
        public String groupId;
        public String projectId;
        public Integer type;
        // 来源于 JoyWorkCustomGrouper
        public List<String> permissions;

        // getSafeTasks 方法
        public List<JoyWork> getSafeTasks() {
            if (tasks == null) {
                tasks = new ArrayList<>();
            }
            return tasks;
        }

        public int getSafeStatus() {
            return status == null ? ProjectStatusEnum.DELETED.getCode() : status;
        }

        public boolean isInit() {
            return initialize != null && initialize;
        }

        public List<String> getSafePermissions() {
            return permissions == null ? new ArrayList<String>() : permissions;
        }

        public int getSafeTotal() {
            return total == null ? 0 : total;
        }

        public boolean isDefaultGroup() {
            return getSafeType() == ProjectGroupTypeEnum.INITIAL.getCode();
        }

        public int getSafeType() {
            return type == null ? 0 : type;
        }

        public void fromGroup(com.jd.oa.joywork.team.bean.Group group) {
            this.groupId = group.groupId;
            this.projectId = group.projectId;
            this.type = group.type;
            this.permissions = group.permissions;
            this.total = group.total;
            this.status = group.status;
            this.initialize = group.initialize;
            this.tasks = group.tasks;
            this.title = group.title;
        }

        public void toGroup(com.jd.oa.joywork.team.bean.Group group) {
            group.groupId = this.groupId;
            group.projectId = this.projectId;
            group.type = this.type;
            group.permissions = this.permissions;
            group.total = this.total;
            group.status = this.status;
            group.initialize = this.initialize;
            group.tasks = this.tasks;
            group.title = this.title;
        }
    }
}
