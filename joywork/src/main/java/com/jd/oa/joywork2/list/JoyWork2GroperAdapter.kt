package com.jd.oa.joywork2.list

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.countJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.expandable.Groupable
import com.jd.oa.joywork.isJoyWork
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.move
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.safeGet
import com.jd.oa.joywork.self.base.adapter.DataProcessor
import com.jd.oa.joywork.team.kpi.MovePlaceHolder
import com.jd.oa.joywork.team.kpi.PlaceHolderVH
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.joywork.team.view.ItemContainer
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.joywork2.bean.title.JoyWork2LazyTitle
import com.jd.oa.joywork2.bean.title.JoyWork2MovePlaceholderItem
import com.jd.oa.joywork2.list.grouper.JoyWorkGrouper
import com.jd.oa.utils.ToastUtils
import java.util.Objects

interface JoyWork2GroperAdapterListener {
    fun itemClick(task: JoyWork, showType: TodoShowType)
}

enum class TodoShowType(val value: String, @StringRes val titleId: Int, val resId: Int) {

    TABLE(
        "list",
        R.string.joywork2_show_type_list,
        R.string.icon_edit_unorderedlist
    ),
    CARD(
        "card",
        R.string.joywork2_show_type_card,
        R.string.icon_card_view
    ),
    KANBAN(
        "kanban",
        R.string.joywork2_show_type_kanban,
        R.string.icon_data4
    ),
    CALENDAR(
        "calendar",
        R.string.joywork2_show_type_calendar,
        R.string.icon_padding_othertime
    );


    companion object {
        fun safeValueOf(type: String?): TodoShowType = values().find { it.value == type } ?: TABLE
    }
}

class JoyWork2GroperAdapter(
    private val groupList: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    private val grouper: JoyWorkGrouper,
    private val mViewModel: List2ViewModel,
    private val ceilProcessorFactoryGetter: () -> CeilProcessorFactory,
    private val context: Context,
    private val listviewType: TodoShowType,
    private val projectId: String,
    private val listener: JoyWork2GroperAdapterListener,
) :
    RecyclerView.Adapter<ViewHolder>(), JoyWorkListDrag2AdapterListener {

    private val itemClick: (JoyWork) -> Unit = { task: JoyWork ->
        listener.itemClick(task, listviewType)
    }

    val processor: DataProcessor = DataProcessor(groupList)

    val groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>
        get() {
            return processor.groups
        }

    fun getData(): List<Any> {
        return processor.getData()
    }

    private val itemType = 100 // 普通  itemType
    private val itemMovePlaceHolder = 101 // 空组站位项
    private val otherType = 1000 // 可能未考虑到的异常情况
    private val groupTypeBase = 10000 // 分组标题的 type，通过 grouper 拿到的 type 会额外添加该值

    override fun getItemViewType(position: Int): Int {
        val data = processor.getData()[position]
        val type = grouper.getItemViewType(data)
        return if (type != null) {
            groupTypeBase + type
        } else if (data is JoyWork) {
            if (data is JoyWork2MovePlaceholderItem) {
                itemMovePlaceHolder
            } else {
                itemType
            }
        } else {
            otherType
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return if (viewType >= groupTypeBase) {
            grouper.onCreateViewHolder(parent, viewType - groupTypeBase)
        } else if (viewType == itemType) {
            if (listviewType == TodoShowType.CARD) {
                CardItemViewHolder(
                    context,
                    parent,
                    ceilProcessorFactoryGetter().create(),
                    this,
                    itemClick
                )
            } else {
                TableItemViewHolder(
                    context,
                    parent,
                    ceilProcessorFactoryGetter().create(),
                    this,
                    itemClick
                )
            }
        } else if (viewType == itemMovePlaceHolder) {
            MovePlaceHolder(context)
        } else {
            PlaceHolderVH(context)
        }
    }

    override fun getItemCount(): Int {
        return getData().size
    }

    /**
     * 替换所有内容
     */
    fun replaceAllGroups(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        processor.updateAll(groups)
        notifyDataSetChanged()
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        return when (holder) {
            is PlaceHolderVH -> {

            }

            is MovePlaceHolder -> {
                val ds = processor.getData()
                val data = ds[position]
                val movePlaceholderItem = data as? JoyWork2MovePlaceholderItem ?: return
                holder.itemView.setTag(R.id.jdme_tag_id, movePlaceholderItem)
            }

            is CardItemViewHolder -> {
                val ds = processor.getData()
                val data = ds[position]
                holder.bind(data as JoyWork, ds, cardGroupLastItemRoundBg(position))
            }

            is TableItemViewHolder -> {
                val ds = processor.getData()
                val data = ds[position]
                holder.bind(data as JoyWork, ds)
            }

            else -> {
                grouper.onBindViewHolder(holder, this, position, getData()[position])
            }
        }
    }

    private fun cardGroupLastItemRoundBg(position: Int): Boolean {
        val ds = processor.getData()
        if ((position + 1) <= (ds.size - 1)) {
            val data = ds[position + 1]
            if (data is CardBottomMultiPurpose<*>) {
                return !data.showNewTask && !data.showMore
            }
            if (data is JoyWorkTitle && data.noGroupLoadMore) {
                return true
            }
            return false
        }
        return true
    }

    fun finishStatusChange(joyWork: JoyWork, adapterPosition: Int) {
        joyWork.expandableGroup.realItems.remove(joyWork)
        processor.refreshData()
        joyWork.expandableGroup.notifyItemChange()

        try {
            notifyItemRemoved(adapterPosition)
        } catch (e: Throwable) {
            notifyDataSetChanged()
        }
    }

    override fun onViewAttachedToWindow(holder: ViewHolder) {
        super.onViewAttachedToWindow(holder)
        val itemView = holder.itemView
        if (itemView is ItemContainer && !itemView.fixPos()) {
            itemView.scrollTo((itemView.parent as? HRecyclerView)?.totalDx ?: 0, 0)
        } else {
            holder.itemView.scrollTo(0, 0)
        }
    }

    fun append(params: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>?) {
        val group = params ?: return
        if (!group.isLegalList()) {
            return
        }
        val map = params.associate { g: ExpandableGroup<JoyWorkTitle, JoyWork> ->
            Pair(g.id, g.realItems)
        }
        val existedIds = HashSet<String>()
        processor.groups.forEach { g: ExpandableGroup<JoyWorkTitle, JoyWork> ->
            val old = g.realItems.filter { it.isJoyWork() }
            if (map.containsKey(g.id)) {
                val new = old + (map[g.id] ?: ArrayList())
                g.realItems.clear()
                g.realItems.addAll(new)
                g.notifyItemChange()
            }
            existedIds.add(g.id)
        }
        val newGroups = params.filter { !existedIds.contains(it.id) }
        processor.appendGroups(newGroups)
        processor.refreshData()
        notifyDataSetChanged()
    }

    fun countRegion(t: String): Int {
        return processor.groups.firstOrNull { Objects.equals(t, it.id) }?.realItems.countJoyWork()
    }

    private fun isGroupInitialized(title: JoyWorkTitle): Boolean {
        return (title as? JoyWork2LazyTitle)?.isInit ?: true
    }

    fun toggleGroup(title: JoyWorkTitle) {
        if (isGroupInitialized(title)) {
            processor.toggleGroup(title)
            notifyDataSetChanged()
        } else {
            mViewModel.loadMore(0, title.expandableGroup.id, Runnable {
                (title as? JoyWork2LazyTitle)?.isInit = true
                processor.refreshData()
                toggleGroup(title)// 再次打开
            })
        }
    }

    /**
     * 根据更新的 bean 刷新数据
     */
    fun updateSpecial(bean: Any) {
        val index = processor.getData().indexOf(bean)
        if (index >= 0) {
            notifyItemChanged(index)
        } else {
            notifyDataSetChanged()
        }
    }

    fun getJoyWorkById(id: String): JoyWork? {
        return processor.getData().firstOrNull {
            it.isJoyWork() && Objects.equals((it as JoyWork).taskId, id)
        } as? JoyWork
    }

    fun prevIndexOf(curGroupId: String): ExpandableGroup<JoyWorkTitle, JoyWork>? {
        val ans = processor.groups.indexOfFirst {
            it.id == curGroupId
        }
        return processor.groups.safeGet(ans - 1)
    }

    fun addBefore(
        title: JoyWork2ExtraTitle?,
        expandableGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
    ) {
        val index = processor.groups.indexOfFirst {
            it.id == title?.expandableGroup?.id
        }
        val insertIndex = Math.max(index, 0)
        processor.addGroup(expandableGroup, insertIndex)
        processor.refreshData()
        notifyDataSetChanged()
    }

    fun addAfter(
        title: JoyWork2ExtraTitle?,
        expandableGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
    ) {
        val index = processor.groups.indexOfFirst {
            it.id == title?.expandableGroup?.id
        }
        val insertIndex = Math.max(index, 0) + 1
        processor.addGroup(expandableGroup, insertIndex)
        processor.refreshData()
        notifyDataSetChanged()
    }

    fun countJoyWork(): Int {
        var count = 0
        groups.forEach {
            count += JoyWorkEx.countJoyWork(it.realItems)
        }
        return count
    }

    override fun getMovableVHClass(): Class<out ViewHolder> {
        return grouper.getMovableVHClass()
    }

    override fun swap(
        fromVH: ViewHolder,
        toVH: ViewHolder,
        down: Boolean
    ) {
        if (fromVH.adapterPosition == toVH.adapterPosition)
            return
        if (fromVH::class.java != getMovableVHClass()) {
            return
        }
        val fromItem = fromVH.itemView.getTag(R.id.jdme_tag_id) as JoyWork
        val toItem =
            toVH.itemView.getTag(R.id.jdme_tag_id) as? Groupable<JoyWorkTitle, JoyWork> ?: return
//        swaped = true
        Log.e("TAG", "from = ${fromVH.adapterPosition},to = ${toVH.adapterPosition}")

        // 如果拖动到分组上，需要跨分组处理
        if (toVH::class.java == getMovableVHClass() || !grouper.isGroupTitle(toVH)) { // 两个 item 交换

            var toIndex = toItem.expandableGroup.realItems.indexOf(toItem)
            val fromIndex = fromItem.expandableGroup.realItems.indexOf(fromItem)
            if (fromItem.expandableGroup == toItem.expandableGroup) {// 同组
                Log.e("TAG", "00 =$fromIndex, $toIndex")
                fromItem.expandableGroup.realItems.move(fromIndex, toIndex)
                Log.e(
                    "TAG",
                    "00 =${fromItem.expandableGroup.realItems.indexOf(fromItem)}, ${
                        toItem.expandableGroup.realItems.indexOf(toItem)
                    }"
                )
            } else {
                // 此处理论上应该执行不到，非标题 vh 应该是都属于同一组的
                if (down) toIndex++
                Log.e("TAG", "01 = $fromIndex, $toIndex")
                fromItem.expandableGroup.realItems.remove(fromItem)
                fromItem.expandableGroup.title.count--
                toItem.expandableGroup.realItems.add(toIndex, fromItem)
                toItem.expandableGroup.title.count++
            }
            toItem.expandableGroup.notifyItemChange()
        } else { // 分组标题。要么拖入，要么拖出

            // 从旧组中删除
            fromItem.expandableGroup.realItems.remove(fromItem)
            fromItem.expandableGroup.clearTmp()
            fromItem.expandableGroup.title.count--

            if (fromItem.expandableGroup.id == toItem.expandableGroup.id) {
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                // 从组内拖到标题，理论上应该只是拖出。但鬼知道会不会有考虑不到的情况，弄个 if 判断吧
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，要拖出当前分组，添加到上一个分组
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    Log.e("TAG", "11 = 0")
                    if (toGroup.expand) {
                        toGroup.realItems.add(fromItem)
                    } else {
                        toGroup.realItems.add(0, fromItem)
                    }
                    toGroup.title.count++
                } else {
                    Log.e("TAG", "11 = 1")
                    // 理论上这里执行不到
                    // 往下拖，拖入当前分组
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    toGroup.realItems.add(0, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            } else {// 不同组，理论上应该是拖入
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，拖出。理论上这里执行不到
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    Log.e(
                        "TAG",
                        "2 fromIndex = ${toGroup.realItems.size}, group = ${toGroup.title.title}"
                    )
                    toGroup.realItems.add(toGroup.realItems.size, fromItem)
                    toGroup.title.count++
                } else {// 正常的拖入
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    var insertIndex = 0
                    val r = kotlin.runCatching {
                        insertIndex = processor.getData().indexOf(toItem) - processor.getData()
                            .indexOf(toGroup.title)
                    }
                    r.exceptionOrNull()?.printStackTrace()
                    Log.e("TAG", "2 down = $insertIndex")
                    toGroup.realItems.add(insertIndex, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            }
        }
        processor.refreshData()

        notifyItemMoved(fromVH.adapterPosition, toVH.adapterPosition)
    }

    override fun canDropOver(
        recyclerView: RecyclerView,
        current: ViewHolder,
        target: ViewHolder
    ): Boolean? {
        return grouper.canDropOver(recyclerView, current, target, this)
    }

    override fun onBeginDrag(viewHolder: ViewHolder) {
        val item = viewHolder.itemView.getTag(R.id.jdme_tag_id) as? JoyWork ?: return
        processor.backup(item)
        viewHolder.itemView.setBackgroundColor(Color.parseColor("#F6F6F6"))
    }

    override fun onEndDrag(viewHolder: ViewHolder) {
        val itemView = viewHolder.itemView
        val item = itemView.getTag(R.id.jdme_tag_id) as? JoyWork ?: return
        itemView.setBackgroundColor(Color.TRANSPARENT)
        sortJoyWork2(item)
    }

    private fun sortJoyWork2(joyWork: JoyWork) {
        val param = processor.getLocationInfo(joyWork)
        JoyWorkRepo.sortProjectJoyWork(
            joyWork.taskId,
            param.blockType,
            projectId,
            param,
            object : JoyWorkUpdateCallback {
                override fun onStart() {
                }

                override fun result(success: Boolean, errorMsg: String) {
                    if (success) {
                        // 刷新整个列表。里面可能涉及到多个地方的数据修改。比如时间头的移除/添加，组中数量的修改等，还有可能是
                        processor.filterAndSyncData(context)
                        notifyDataSetChanged()
                        initGroupIfNecessary(joyWork)
                    } else {
                        // 恢复原样
                        processor.restore(joyWork)
                        notifyDataSetChanged()
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }
            })
    }

    private fun initGroupIfNecessary(joyWork: JoyWork) {
        if (isGroupInitialized(joyWork.expandableGroup.title)) {
            expandGroup(joyWork.expandableGroup.title)
        } else {
            toggleGroup(title = joyWork.expandableGroup.title)
        }
    }

    fun expandGroup(title: JoyWorkTitle) {
        if (title.expandableGroup?.expand != false) {
            return
        }
        val deltaSize = processor.toggleGroup(title)
        notifyItemRangeInserted(processor.getData().indexOf(title) + 1, Math.abs(deltaSize))
        notifyItemChanged(processor.getData().indexOf(title))
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refresh(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        groupList.clear()
        groupList.addAll(groups)
        processor.refreshData()
        this.notifyDataSetChanged()
    }
}