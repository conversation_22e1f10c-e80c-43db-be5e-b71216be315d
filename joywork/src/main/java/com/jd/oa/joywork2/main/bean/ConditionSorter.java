package com.jd.oa.joywork2.main.bean;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.jd.oa.joywork.R;

import java.util.HashMap;
import java.util.Objects;

public class ConditionSorter {
    public String field; // 排序字段
    public String order; // 顺序
    public boolean disable;
    public ConditionSorter prev;

    protected ConditionSorter() {

    }

    public ConditionSorter(Field field, Order order) {
        this(field, order, false);
    }

    public ConditionSorter(Field field, Order order, boolean disable) {
        this.field = field.value;
        this.order = order.value;
        this.disable = disable;
    }

    public String getText(Context context) {
        for (Field value : Field.values()) {
            if (Objects.equals(value.value, field)) {
                return context.getString(value.textId);
            }
        }
        return "";
    }

    public boolean canDraggable() {
        return Objects.equals(field, Field.CUSTOM.value);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(field + order);
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj instanceof ConditionSorter) {
            ConditionSorter other = (ConditionSorter) obj;
            return Objects.equals(other.field, field) && Objects.equals(other.order, order);
        }
        return false;
    }

    public HashMap<String, String> toMap() {
        HashMap<String, String> map = new HashMap<>();
        if (TextUtils.isEmpty(field)) {
            return map;
        }
        map.put("field", field);
        if (!Objects.equals(field, Field.CUSTOM.value)) {
            map.put("order", order);
        } else {
            map.put("order", Order.ASC.value);
        }
        return map;
    }

    public enum Field {
        CUSTOM("CUSTOM_ORDER", R.string.joywork2_sorter_custom),
        START_TIME("START_TIME", R.string.joywork_update_start),
        END_TIME("END_TIME", R.string.joywork_update_deadline),
        CREATE_TIME("CREATE_TIME", R.string.joywork2_sorter_create_time),
        LATEST_COMMENT("LATEST_COMMENT", R.string.joywork2_grouper_latest_comment_sort);//最新评论

        public static Field getField(String value) {
            for (Field field : Field.values()) {
                if (Objects.equals(field.value, value)) {
                    return field;
                }
            }
            return null;
        }

        public final String value;
        public final int textId;

        Field(String t, @StringRes int textId) {
            this.value = t;
            this.textId = textId;
        }
    }

    public enum Order {
        ASC("ASC"), DESC("DESC");

        public static Order getOrder(String value) {
            for (Order order : Order.values()) {
                if (Objects.equals(order.value, value)) {
                    return order;
                }
            }
            return null;
        }


        public final String value;

        Order(String t) {
            this.value = t;
        }
    }
}
