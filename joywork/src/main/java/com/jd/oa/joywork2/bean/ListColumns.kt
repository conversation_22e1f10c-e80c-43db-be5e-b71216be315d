package com.jd.oa.joywork2.bean

import androidx.annotation.Keep

@Keep
data class ListColumn(val key: String, val hidden: Boolean) {
    companion object {
        const val KEY_END_TIME = "endTime"
        const val KEY_OPERATOR = "operator"
        const val KEY_RECENT_COMMENT = "recentComment"
        const val KEY_START_TIME = "startTime"
        const val KEY_PRIORITY = "priority"
        const val KEY_PROJECT = "project"
        const val KEY_SYS_SOURCE = "sysSource"
        const val KEY_SOURCE = "source"
        const val KEY_SUB_SOURCE = "subSource"

        //扩展列-状态
        const val KEY_EXT_STATUS = "511497571508785152"
        //扩展列-进度
        const val KEY_EXT_PROGRESS = "361208658589224960"
        // 标签
        const val KEY_LABEL = "label"
        // 创建时间
        const val KEY_CREATE_TIME = "createTime"
        // 创建人
        const val KEY_CREATOR = "creator"
    }
}