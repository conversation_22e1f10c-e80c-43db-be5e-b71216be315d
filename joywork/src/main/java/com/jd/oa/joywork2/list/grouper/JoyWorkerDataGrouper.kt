package com.jd.oa.joywork2.list.grouper

import android.content.Context
import android.graphics.Color
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.countJoyWork
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.getOrNew
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.sortOwners
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.bean.ProjectGroupTypeEnum
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.bean.ProjectStatusEnum
import com.jd.oa.joywork.team.bean.ResultWithoutGroup
import com.jd.oa.joywork.utils.isLastWeek
import com.jd.oa.joywork.utils.isThisWeek
import com.jd.oa.joywork2.backend.getRegionGroups
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper
import com.jd.oa.joywork2.bean.JoyWorkWrapper2
import com.jd.oa.joywork2.bean.ListColumn
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.joywork2.bean.title.JoyWork2MovePlaceholderItem
import com.jd.oa.joywork2.bean.title.JoyWork2OnlyTextTitle
import com.jd.oa.joywork2.bean.title.JoyWork2UserExtraTitle
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.string
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.Objects

interface JoyWorkerDataGrouper {
    fun group(
        any: Any,
        context: Context,
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return ArrayList()
    }

    fun group(
        any: Any,
        context: Context,
        isProjectList: Boolean? = false,//是否是清单页面，个人和清单页面需要对数据不同处理
        customFieldGroup: CustomFieldGroup? = null,//自定义扩展列
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)? = null //根据组id返回当前组总共加载的数据
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return ArrayList()
    }
}

object CustomGroupDataGrouper : JoyWorkerDataGrouper {

    /**
     * 1. 在清单的情况下
     *      服务端只会在第一个分组时type是2
     *      前端判断 type是 2， 且 title为空时，不展示title行
     *      Title 为空时展示“未命名分组分组”
     * 2. 在个人自定义分组情况下
     *      Type为2，可能是第一个分组，也可能是任意
     *      前端仅判断title为空时展示 “默认分组”
     *
     */
    override fun group(
        any: Any,
        context: Context,
        isProjectList: Boolean?,
        customFieldGroup: CustomFieldGroup?,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val t = any as? JoyWorkCustomGrouper ?: return ArrayList()
        val groups = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val allowedCreateNewTask = if (isProjectList == true) {
            (t.permissions ?: emptyList()).contains(ProjectPermissionEnum.TASK.code)
        } else {
            true
        }
        t.safeGroups.forEach { it: JoyWorkCustomGrouper.Group ->
            if (it.safeStatus == ProjectStatusEnum.DELETED.code) {
                return@forEach
            }
            var emptyTitle = false
            if (!it.title.isLegalString()) {
                if (isProjectList == true) {
                    it.title = context.string(R.string.joywork_task_link_untitled_group)
                    emptyTitle = it.type == ProjectGroupTypeEnum.INITIAL.code
                } else {
                    it.title = context.string(R.string.joywork_self_default_group)
                }
            }
            it.permissions = t.safePermissions
            val title = JoyWork2ExtraTitle(it.title, it).apply {
                hide = emptyTitle
            }
            val items = ArrayList<JoyWork>()
            items.add(JoyWork2MovePlaceholderItem(title))
            items.addAll(it.safeTasks)
            val size = it.safeTasks.countJoyWork()
            val offset = if (isRefresh) 0 else loadOffsetChecker?.invoke(it.groupId) ?: 0
//            if ((size + offset) < it.total) {
//                items.add(JoyWorkLoadMore2(it.groupId))
//            }
//            if (!items.isLegalList()) {
//                items.add(JoyWork2MovePlaceholderItem(title))
//            }
            items.add(CardBottomMultiPurpose(it.groupId).apply {
                showNewTask = allowedCreateNewTask
                showMore = (size + offset) < it.total
            })
            val expandableGroup =
                ExpandableGroup(title, items, it.groupId, it.isInit)
            groups.add(expandableGroup)
        }
        val allowedCreateNewGroup = if (isProjectList == true) {
            (t.permissions ?: emptyList()).contains(ProjectPermissionEnum.GROUP.code)
        } else {
            true
        }
        // 有新建分组权限
        if (allowedCreateNewGroup) {
            val tmpTitle =
                ProjectAddTitle(context.string(R.string.joywork_group_new_title))
            val expandableGroup = ExpandableGroup(
                tmpTitle,
                ArrayList<JoyWork>(),
                ProjectConstant.NEW_GROUP_TITLE_ID,
                true
            )
            groups.add(expandableGroup)
        }

        return groups
    }
}

/**
 * 按 JoyWork2RegionType 进行分类
 */
object RegionDataGrouper : JoyWorkerDataGrouper {

    override fun group(
        any: Any,
        context: Context,
        isProjectList: Boolean?,
        customFieldGroup: CustomFieldGroup?,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? JoyWorkWrapper2 ?: return ArrayList()
        // 截止时间排序，返回的列表中已经分好组
        val tmpGroups = getRegionGroups(context)
        tmpGroups.forEach {
            it.title.count = 0
            it.ensureRealItems()
            it.realItems.clear()
        }
        wrapper.clientTasks.forEach { // JoyWorkGroup  it
            try {
                // 先匹配分组。接口通过 blockType/regionType 表示应该属于哪个分组
                tmpGroups.firstOrNull { g ->
                    Objects.equals(g.id, it.blockType)
                }?.apply {
                    //  ExpandableGroup<JoyWorkTitle, JoyWork> this
                    title.count = it.total
                    realItems.clear()
                    realItems.addAll(it.tasks)
                    val size = JoyWorkEx.countJoyWork(realItems)
                    val offset = if (isRefresh) 0 else loadOffsetChecker?.invoke(id) ?: 0
//                    if ((size + offset) < it.total) {
//                        realItems.add(JoyWorkLoadMore2(it.blockType))
//                    }
                    realItems.add(CardBottomMultiPurpose(it.blockType).apply {
                        showNewTask = false
                        showMore = (size + offset) < it.total
                    })
                    notifyItemChange()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        tmpGroups.removeAll {
            it.isEmpty()
        }
        // 保持展开、收起状态不变
//        tmpGroups.forEach { outer ->
//            val adapter = mItf.getRv().realAdapter()
//            if (adapter is SelfListBaseAdapter) {
//                outer.expand =
//                    adapter.groups.firstOrNull { outer.id == it.id }?.expand ?: true
//            } else {
//                outer.expand = true
//            }
//        }
        return tmpGroups
    }

    fun ExpandableGroup<*, *>.isEmpty(): Boolean {
        val result = runCatching {
            if (!realItems.isLegalList()) return@runCatching true
            if (realItems.size == 1 && realItems[0] is CardBottomMultiPurpose<*>) return@runCatching true
            return@runCatching false
        }
        return result.getOrElse { false }
    }
}


object ExecutorDataGrouper : JoyWorkerDataGrouper {
    override fun group(
        any: Any,
        context: Context
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? ResultWithoutGroup ?: return ArrayList()
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val id_joywork = mutableMapOf<String, ArrayList<JoyWork>>()
        val id_title = mutableMapOf<String, JoyWorkTitle>()
        val no_id = "executor_no_id"
        wrapper.safeTasks().forEach {
            val id = it.owner?.userId ?: no_id
            val list = id_joywork.getOrPut(id) { ArrayList() }
            list.add(it)

            if (!id_title.containsKey(id)) {
                val title =
                    it.owner?.realName ?: context.string(R.string.joywork_project_sort_no)
                val part = wrapper.createPart(id)
                id_title[id] = JoyWork2UserExtraTitle(title, it.owner, part).apply {
                    noGroupLoadMore = true
                }
            }
        }

        id_title.forEach { (id, title) ->
            val items = id_joywork.getOrNew(id) {
                ArrayList()
            }
            val expandableGroup =
                ExpandableGroup(title, items, id, true)
            ans.add(expandableGroup)
        }
        ans.removeAll {
            !it.realItems.isLegalList()
        }
        return ans
    }
}

object SourceDataGrouper : JoyWorkerDataGrouper {
    override fun group(
        any: Any,
        context: Context
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? ResultWithoutGroup ?: return ArrayList()
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val id_joywork = mutableMapOf<String, ArrayList<JoyWork>>()
        val id_title = mutableMapOf<String, JoyWorkTitle>()
        val no_id = "source_no_id"
        wrapper.safeTasks().forEach {
            val id = it.inventory ?: no_id
            val list = id_joywork.getOrPut(id) { ArrayList() }
            list.add(it)

            if (!id_title.containsKey(id)) {
                val title = it.groupTitle ?: context.string(R.string.joywork_project_none)
                val part = wrapper.createPart(id)
                id_title[id] = JoyWork2ExtraTitle(title, part).apply {
                    noGroupLoadMore = true
                }
            }
        }

        id_title.forEach { (id, title) ->
            val items = id_joywork.getOrNew(id) {
                ArrayList()
            }
            val expandableGroup =
                ExpandableGroup(title, items, id, true)
            ans.add(expandableGroup)
        }
        ans.removeAll {
            !it.realItems.isLegalList()
        }
        return ans
    }
}

object ProjectDataGrouper : JoyWorkerDataGrouper {
    override fun group(
        any: Any,
        context: Context
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? ResultWithoutGroup ?: return ArrayList()
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val id_joywork = mutableMapOf<String, ArrayList<JoyWork>>()
        val id_title = mutableMapOf<String, JoyWorkTitle>()
        val no_id = "project_no_id"
        wrapper.safeTasks().forEach {
            val id = it.project?.projectId ?: no_id
            val list = id_joywork.getOrPut(id) { ArrayList() }
            list.add(it)

            if (!id_title.containsKey(id)) {
                val title =
                    it.project?.title ?: context.string(R.string.joywork_project_sort_no)
                val part = wrapper.createPart(id)
                id_title[id] = JoyWork2ExtraTitle(title, part).apply {
                    noGroupLoadMore = true
                }
            }
        }

        id_title.forEach { (id, title) ->
            val items = id_joywork.getOrNew(id) {
                ArrayList()
            }
            val expandableGroup =
                ExpandableGroup(title, items, id, true)
            ans.add(expandableGroup)
        }
        ans.removeAll {
            !it.realItems.isLegalList()
        }
        return ans
    }
}

/**
 * copy from MyFinishFragment.handleResult
 */
object CompleteTimeDataGrouper : JoyWorkerDataGrouper {
    override fun group(
        any: Any,
        context: Context,
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? ResultWithoutGroup ?: return ArrayList()
        val map = LinkedHashMap<String, ExpandableGroup<JoyWorkTitle, JoyWork>>()
        wrapper.safeTasks().forEach { work ->
            work.sortOwners()
            val ct = work.completeTime
            val key: String
            when {
                ct == null -> {
                    key = context.resources.getString(R.string.me_web_item_other)
                }

                ct.isLastWeek() -> {
                    key = context.resources.getString(R.string.joywork_last_week)
                }

                ct.isThisWeek() -> {
                    key = context.resources.getString(R.string.joywork_this_week)
                }

                DateUtils.isSameYear(ct, System.currentTimeMillis()) -> {
                    val c = Calendar.getInstance()
                    c.timeInMillis = ct
                    val month = c.get(Calendar.MONTH)
                    key = context.resources.getStringArray(R.array.joywork_month)[month]
                }

                else -> { // 不同年
                    val c = Calendar.getInstance()
                    c.timeInMillis = ct
                    val month =
                        context.resources.getStringArray(R.array.joywork_month_short)[c.get(
                            Calendar.MONTH
                        )]
                    val year = SimpleDateFormat("yyyy", Locale.getDefault()).format(ct)
                    key = context.resources.getString(R.string.joywork_year_month, year, month)
                }
            }
            val group = map.getOrNew(key) {
                val title = JoyWorkTitle(
                    //  旧版本需要该字段，新版本没用。2022年04月08日
                    R.string.icon_banner_placeholder,
                    key,
                    0,
                    Color.TRANSPARENT
                ).apply {
                    noGroupLoadMore = true
                }
                ExpandableGroup(
                    title,
                    ArrayList(),
                    key,
                    true
                )
            }
            group.realItems.add(work)
            group.notifyItemChange()
        }

        val tmpGroups = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        map.mapKeys {
            tmpGroups.add(it.value)
        }

        tmpGroups.removeAll {
            !it.realItems.isLegalList()
        }
        tmpGroups.forEach { outer ->
            outer.title.count = 0
            outer.expand = true
        }
        return tmpGroups
    }
}


//待办状态数据分组
object ExtStatusDataGrouper : JoyWorkerDataGrouper {
    override fun group(
        any: Any,
        context: Context,
        isProjectList: Boolean?,
        customFieldGroup: CustomFieldGroup?,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? ResultWithoutGroup ?: return ArrayList()
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val id_joywork = mutableMapOf<String, ArrayList<JoyWork>>()
        val id_title = mutableMapOf<String, JoyWorkTitle>()
        val no_id = "status_no_id"
        if (customFieldGroup == null) {
            return ArrayList()
        }
        wrapper.safeTasks().forEach { task ->
            val statusId = task.statusId() ?: no_id
            if (statusId == no_id) {//同Web端对齐，没有进度的不予展示
                return@forEach
            }
            val list = id_joywork.getOrPut(statusId) { ArrayList() }
            list.add(task)
            if (!id_title.containsKey(statusId)) {
                val fieldItem = customFieldGroup.details.firstOrNull {
                    it.detailId == statusId
                }
                val title = fieldItem?.itemContent?.content
                    ?: context.string(R.string.joywork_project_sort_no)
                id_title[statusId] = JoyWork2OnlyTextTitle(title).apply {
                    noGroupLoadMore = true
                }
            }
        }

        id_title.forEach { (id, title) ->
            val items = id_joywork.getOrNew(id) {
                ArrayList()
            }
            val expandableGroup =
                ExpandableGroup(title, items, id, true)
            ans.add(expandableGroup)
        }
        ans.removeAll {
            !it.realItems.isLegalList()
        }
        return ans
    }

    private fun JoyWork.statusId(): String? {
        return safeCustomFields()[ListColumn.KEY_EXT_STATUS]?.firstOrNull()
    }
}

//待办来源分组
object BizCodeDataGrouper : JoyWorkerDataGrouper {
    override fun group(
        any: Any,
        context: Context
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? ResultWithoutGroup ?: return ArrayList()
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val id_joywork = mutableMapOf<String, ArrayList<JoyWork>>()
        val id_title = mutableMapOf<String, JoyWorkTitle>()
        val no_id = "biz_no_id"
        wrapper.safeTasks().forEach {
            val id = it.bizCode ?: no_id
            val list = id_joywork.getOrPut(id) { ArrayList() }
            list.add(it)

            if (!id_title.containsKey(id)) {
                val title = it.sourceName ?: context.string(R.string.joywork_project_none)
                id_title[id] = JoyWork2OnlyTextTitle(title).apply {
                    noGroupLoadMore = true
                }
            }
        }

        id_title.forEach { (id, title) ->
            val items = id_joywork.getOrNew(id) {
                ArrayList()
            }
            val expandableGroup =
                ExpandableGroup(title, items, id, true)
            ans.add(expandableGroup)
        }
        ans.removeAll {
            !it.realItems.isLegalList()
        }
        return ans
    }
}

//二级分类
object SubSourceDataGrouper : JoyWorkerDataGrouper {
    override fun group(
        any: Any,
        context: Context
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? ResultWithoutGroup ?: return ArrayList()
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val id_joywork = mutableMapOf<String, ArrayList<JoyWork>>()
        val id_title = mutableMapOf<String, JoyWorkTitle>()
        val no_id = "sub_source_no_id"
        wrapper.safeTasks().forEach {
            val id = it.subInventory ?: no_id
            val list = id_joywork.getOrPut(id) { ArrayList() }
            list.add(it)

            if (!id_title.containsKey(id)) {
                val title =
                    it.subTripartiteName ?: context.string(R.string.joywork_project_none)
                id_title[id] = JoyWork2OnlyTextTitle(title).apply { noGroupLoadMore = true }
            }
        }

        id_title.forEach { (id, title) ->
            val items = id_joywork.getOrNew(id) {
                ArrayList()
            }
            val expandableGroup =
                ExpandableGroup(title, items, id, true)
            ans.add(expandableGroup)
        }
        ans.removeAll {
            !it.realItems.isLegalList()
        }
        return ans
    }
}
