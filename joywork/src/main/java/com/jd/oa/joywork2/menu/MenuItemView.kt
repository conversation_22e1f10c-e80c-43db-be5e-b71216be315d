package com.jd.oa.joywork2.menu

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.ui.IconFontView

/**
 * @Author: hepiao3
 * @CreateTime: 2024/12/13
 * @Description:
 */
class MenuItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : BaseMenuItemView(context, attrs, defStyleAttr) {

    init {
        orientation = HORIZONTAL
        view = context.layoutInflater.inflate(R.layout.joywork2_drawer_item_view, this, true)
    }

    fun changeCountVisible(visibility: Int) {
        count.visibility = visibility
    }

    override fun setOnClickMenuItem(callback: (View) -> Unit) {
        setOnClickListener {
            callback(view)
        }
    }
}