package com.jd.oa.joywork2.list

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.FrameLayout
import androidx.annotation.CallSuper
import androidx.core.view.setPadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.jd.oa.around.base.HeaderFooterRecyclerAdapterWrapper
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.ui.dialog.bottomsheet.DialogConfig
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog
import com.jd.oa.ui.dialog.bottomsheet.BottomAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkDetailParam
import com.jd.oa.joywork.JoyWorkDialog.showAlertDialog
import com.jd.oa.joywork.JoyWorkMediator
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.TransferResult
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail.Project
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.ui.TaskDetailActivity
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkBlankAdapter
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.isJoyWork
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isNotTrue
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.repo.JoyWorkLocationParam
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.self.base.SelfListBaseAdapterWrapper
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.SelectProjectTaskGroupActivity
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectList
import com.jd.oa.joywork.team.bean.ProjectList.Content.List.Groups
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.dialog.DialogSupporter
import com.jd.oa.joywork.team.showRejectTaskEditDialog
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.joywork.view.JoyWorkLoadMoreFooter
import com.jd.oa.joywork.view.StickyLinearLayoutManager
import com.jd.oa.joywork.view.realAdapter
import com.jd.oa.joywork2.backend.TimestampType
import com.jd.oa.joywork2.bean.ListColumn
import com.jd.oa.joywork2.list.grouper.CustomGrouper
import com.jd.oa.joywork2.list.grouper.GrouperInitializer
import com.jd.oa.joywork2.list.grouper.JoyWorkGrouper
import com.jd.oa.joywork2.list.grouper.NoGrouper
import com.jd.oa.joywork2.list.listener.MoreButtonClickListener
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.JoyWorkViewModel
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.safeLaunch
import com.jd.oa.utils.visible

/**
 * 带分组的任务列表
 * 截止时间、自定义分组按分组加载更多
 * 其余按整个列表加载更多
 */
@Suppress("UNCHECKED_CAST")
abstract class JoyWork2GroupBaseFragment : Table2Fragment(), JoyWork2GroperAdapterListener,
    TitleCeilProcessor.TitleCeilProcessorListener,
    ExecutorsCeilProcessor.ExecutorsCeilProcessorListener,
    DeadlineCeilProcessor.DeadlineCeilProcessorListener,
    StartTimeCeilProcessor.StartTimeCeilProcessorListener,
    SourceCeilProcessor.SourceCeilProcessorListener,
    ProjectCeilProcessor.ProjectCeilProcessorListener,
    PriorityCeilProcessor.PriorityCeilProcessorListener,
    PrimarySourceCeilProcessor.PrimarySourceCeilProcessorListener,
    SecondarySourceCeilProcessor.SecondarySourceCeilProcessorListener,
    LatestCommentCeilProcessor.LatestCommentCeilProcessorListener,
    ExtMultiSelectCeilProcessor.ExtValueChangedProcessorListener,
    MoreButtonClickListener.MoreButtonCeilProcessorListener {

    private val mMainViewModel: JoyWorkViewModel by activityViewModels()
    protected val conditionViewModel: ConditionViewModel by activityViewModels()

    private val menuItemFactory: (() -> MenuItem)? by lazy {
        conditionViewModel.mMenuItemFactory
    }

    private var mPullUpLoadHelper: PullUpLoadHelper? = null

    val mListViewModel: List2ViewModel by viewModels()
    private var grouper: JoyWorkGrouper? = null

    abstract fun onClickAdd(view: View)

    abstract fun getProjectId(): String

    abstract fun getTaskListType(): TaskListTypeEnum

    abstract fun isProject(): Boolean

    abstract fun getGroups(): List<Group>

    abstract fun getIsArchiveProject(): Boolean

    private val mAddClick = View.OnClickListener {
        onClickAdd(it)
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.INCOMPLETE_CLICK1
            )
        }
    }

    override val fragment: Fragment
        get() = this


    private var mAddView: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        val rootView = inflater.inflate(R.layout.joywork2_fragment_mine, container, false)
        val root = rootView.findViewById<FrameLayout>(R.id.root)
        root.addView(view, 0)
        root.findViewById<View>(R.id.add).apply {
            mAddView = this
            setOnClickListener(mAddClick)
            val lp = (layoutParams as? ViewGroup.MarginLayoutParams)
                ?: ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            val strategy = mMainViewModel.mainStrategy
            lp.apply {
                rightMargin =
                    requireActivity().resources.getDimensionPixelSize(strategy.addViewRightMargin)
                bottomMargin =
                    requireActivity().resources.getDimensionPixelSize(strategy.addViewBottomMargin)
            }
            layoutParams = lp
        }
        return rootView
    }

    override fun onCreateCeilProcessorFactory(): CeilProcessorFactory {
        return if (conditionViewModel.showType == TodoShowType.CARD) {
            CardDefaultCeilProcessorFactory(this)
        } else if (conditionViewModel.showType == TodoShowType.TABLE) {
            val condition = conditionViewModel.mConditionValueLiveData.value
            if (condition is ConditionState.Condition && condition.listColumns != null) {
                ListColumnsCeilProcessorFactory(
                    condition.listColumns,
                    this,
                    JoyWorkViewModel.customFields,
                    TodoShowType.TABLE
                )
            } else {
                DefaultCeilProcessorFactory(this)
            }
        } else {
            DefaultCeilProcessorFactory(this)
        }
    }

    override fun onSetupView(rv: HRecyclerView, refreshLayout: SwipeRefreshLayout) {
        rv.adapter = JoyWorkBlankAdapter(rv.context)
        refreshLayout.setOnRefreshListener {
            lifecycleScope.safeLaunch {
                val localCondition =
                    mMainViewModel.isProjectList && mListViewModel.checkParams()
                if (localCondition) {
                    mListViewModel.initList()
                } else {
                    val menuItem = menuItemFactory?.invoke()
                    if (menuItem != null) {
                        if (!menuItem.needCondition()) {
                            conditionViewModel.setCondition(ConditionState.Nothing, false)
                        } else {
                            conditionViewModel.initCondition({
                                menuItem.getViewId()
                            }, {
                                menuItem.getViewType()
                            })
                        }
                    } else {
                        mListViewModel.initList()
                    }
                }
            }
        }
        initObservers()
        ItemTouchHelper(JoyWorkListDrag2(rv) {
            grouper?.canDraggable().isTrue() && mListViewModel.canDraggable() && !getIsArchiveProject()
        }).attachToRecyclerView(rv)
        if (mMainViewModel.isProjectList) {
            val permissions = mMainViewModel.detailLiveData.value?.permissions ?: emptyList()
            if (permissions.contains(ProjectPermissionEnum.TASK.code)) {
                mAddView.visible()
            } else {
                mAddView.gone()
            }
        }
    }

    private val grouperInitializer by lazy {
        GrouperInitializer(
            recyclerView = mRv,
            listViewModel = mListViewModel,
            isProjectList = mMainViewModel.isProjectList,
            projectId = getProjectId(),
            getTaskListType(),
            menuItemFactory?.invoke(),
            conditionViewModel.viewIdFactory?.invoke(),
            conditionViewModel.viewTypeFactory?.invoke()
        ){
            mMainViewModel.getProjectDetail(getProjectId())
        }
    }

    private fun getGrouper(): JoyWorkGrouper? {
        return when (val conditionState = conditionViewModel.mConditionValueLiveData.value) {
            is ConditionState.Nothing -> {
                NoGrouper
            }

            is ConditionState.Condition -> {
                return grouperInitializer.getGrouperByCondition(
                    conditionState.grouper,
                    conditionViewModel.showType
                )
            }

            else -> {
                null
            }
        }
    }

    @CallSuper
    open fun initObservers() {
        mListViewModel.updateRepoFactory {
            getGrouper()?.getRepo()
        }
        if (mMainViewModel.isProjectList) {
            mMainViewModel.detailLiveData.observe(viewLifecycleOwner) {
                val permissions = mMainViewModel.detailLiveData.value?.permissions ?: emptyList()
                if (permissions.contains(ProjectPermissionEnum.TASK.code)) {
                    mAddView.visible()
                } else {
                    mAddView.gone()
                }
            }
        }

        conditionViewModel.mConditionValueLiveData.observe(viewLifecycleOwner) { conditionState ->
            mListViewModel.updateCondition(conditionState)
        }

        mListViewModel.data.observe(viewLifecycleOwner) { state: DataState ->

            val loadOffsetChecker: (gId: String) -> Int = {
                val adapter = mRv.realAdapter as? JoyWork2GroperAdapter
                adapter?.countRegion(it) ?: 0
            }
            // 写一个 state 的 when
            when (state) {
                is DataState.RefreshSuccess -> {
                    grouper = getGrouper()
                    val group = grouper?.group(state.data, mRv.context, true, loadOffsetChecker)
                    // 更新列表
                    if (group.isLegalList()) {
                        if (state.silent) {
                            val groupAdapter = when (mRv.adapter) {
                                is JoyWork2GroperAdapter -> {
                                    mRv.adapter
                                }

                                is SelfListBaseAdapterWrapper -> {
                                    (mRv.adapter as SelfListBaseAdapterWrapper).mRealAdapter
                                }

                                else -> {
                                    null
                                }
                            }
                            (groupAdapter as? JoyWork2GroperAdapter)?.refresh(group!!)
                        } else {
                            grouper?.let { setDataOrAdapter(group!!, it) }
                        }
                    } else {
                        mRv.adapter = JoyWorkEmptyAdapter.empty(mRv.context)
                    }
                }

                is DataState.RefreshFailure -> {
                    mRv.adapter = JoyWorkEmptyAdapter.error(mRv.context, state.msg)
                }

                is DataState.LoadMoreSuccess -> {
                    grouper = getGrouper()
                    val group = grouper?.group(state.data, mRv.context, false, loadOffsetChecker)
                    // 更新列表
                    if (group.isLegalList()) {
                        val adapter = mRv.realAdapter as? JoyWork2GroperAdapter
                        adapter?.append(group)
                    }
                    var size = 0
                    group?.forEach { g ->
                        size += g.countRealItem { it.isJoyWork() }
                    }
                    loadMoreOnceFinish(size < ProjectRepo.PAGE_LIMIT)
                }

                is DataState.LoadMoreFailure -> {
                    ToastUtils.showInfoToast(state.msg)
                    loadMoreOnceFinish(false)
                }

                is DataState.NoPermission -> {
                    ToastUtils.showInfoToast(state.msg)
                    if (mMainViewModel.isProjectList) {
                        activity?.finish()
                    } else {
                        mRv.adapter = JoyWorkEmptyAdapter.error(mRv.context, state.msg)
                    }
                }
            }
        }
        mListViewModel.refreshingEffect.observe(viewLifecycleOwner) {
            mRefresh.isRefreshing = it
        }

        conditionViewModel.showTypeLiveData.observe(viewLifecycleOwner) {
            if (it == null || !it.first) {
                return@observe
            }
            if (it.second != TodoShowType.TABLE && it.second != TodoShowType.CARD) {
                return@observe
            }
            val adapter = when (mRv.adapter) {
                is JoyWork2GroperAdapter -> {
                    mRv.adapter
                }

                is SelfListBaseAdapterWrapper -> {
                    (mRv.adapter as SelfListBaseAdapterWrapper).mRealAdapter
                }

                else -> {
                    null
                }
            }
            if (context == null) return@observe
            (adapter as? JoyWork2GroperAdapter)?.run {
                mRv.layoutManager =
                    if (it.second == TodoShowType.CARD) {
                        mRv.background = DrawableEx.roundSolidDirRect(
                            Color.TRANSPARENT,
                            requireContext().resources.getDimension(R.dimen.joywork_corner),
                            DrawableEx.DIR_TOP
                        )
                        mRv.setOutlineProvider(ViewOutlineProvider.BACKGROUND)
                        mRv.setClipToOutline(true)
                        StickyLinearLayoutManager(mRv) {
                            contentContainer
                        }
                    } else {
                        mRv.background = null
                        LinearLayoutManager(context)
                    }

                val grouper = getGrouper()
                grouper?.run {
                    setDataOrAdapter(adapter.groups, grouper)
                }
            }
        }
    }

    private fun setDataOrAdapter(
        groupList: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        grouper: JoyWorkGrouper,
    ) {
        val factory = onCreateCeilProcessorFactory()
        refreshTitleItems(factory)
        val adapter = JoyWork2GroperAdapter(
            groupList,
            grouper,
            mListViewModel,
            ::onCreateCeilProcessorFactory,
            mRv.context,
            conditionViewModel.showType,
            getProjectId(),
            this
        )
        mRv.adapter = adapter
        setupLoadMore()
        val size = adapter.countJoyWork()
        loadMoreOnceFinish(size < ProjectRepo.PAGE_LIMIT)
    }

    override fun refreshTitleItems(factory: CeilProcessorFactory) {
        super.refreshTitleItems(factory)
        when (conditionViewModel.showType) {
            TodoShowType.CARD -> {
                titleContainer.gone()
                contentContainer.setPadding(DisplayUtils.dip2px(12f))
                contentContainer.setBackgroundColor(Color.TRANSPARENT)
            }

            TodoShowType.TABLE -> {
                titleContainer.visible()
                contentContainer.setPadding(0)
                contentContainer.setBackgroundColor(Color.WHITE)
            }
        }
    }


    private fun setupLoadMore() {
        if (getGrouper()?.needLoadMore().isTrue()) {
            val rv = mRv
            val oldAdapter = rv.adapter
            val oldHelper = mPullUpLoadHelper
            if (oldHelper != null) {
                rv.removeOnScrollListener(oldHelper)
            }
            mPullUpLoadHelper = PullUpLoadHelper(rv) {
                if (!mRv.isUp) {
                    loadMoreOnceFinish(false)
                    return@PullUpLoadHelper
                }

                if (getGrouper()?.needLoadMore().isNotTrue()) return@PullUpLoadHelper

                Log.e(TAG, "setupLoadMore: ${System.identityHashCode(mPullUpLoadHelper)}")
                val a = rv.realAdapter()
                val adapterGroups = (a as? JoyWork2GroperAdapter) ?: return@PullUpLoadHelper
                mListViewModel.loadMore(adapterGroups.countJoyWork(), "", null)
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_KANBAN_VIEWMORE
                    )
                }
            }
            mPullUpLoadHelper?.setLoadFooter(JoyWorkLoadMoreFooter(requireContext()))
            val a = rv.adapter
            if (a is HeaderFooterRecyclerAdapterWrapper && oldAdapter != null) {
                rv.adapter = SelfListBaseAdapterWrapper(oldAdapter, a)
            }
        } else {
            mPullUpLoadHelper = null
        }
    }

    private fun loadMoreOnceFinish(finish: Boolean) {
        if (mPullUpLoadHelper != null && getGrouper()?.needLoadMore().isTrue()) {
            if (finish) {
                mPullUpLoadHelper?.setComplete()
            } else {
                mPullUpLoadHelper?.setLoaded()
            }
        }
    }

    // JoyWork2GroperAdapterListener
    override fun itemClick(task: JoyWork, showType: TodoShowType) {
        JoyWorkMediator.goDetail(
            this,
            JoyWorkDetailParam(
                taskId = task.taskId,
                projectId = task.projectId,
                taskName = task.title
            ).apply {
                from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                reqCode = 10
                obj = DetailReturnParcel()
            })
        if (showType == TodoShowType.TABLE) {
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LIST_VIEWER_TASK_CLICK_ROW
                )
            }
        } else {
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_CLICK
                )
            }
        }

    }

    // CeilChangedListener

    override fun onCeilChanged(ceilType: CeilType, work: JoyWork) {
        when (ceilType) {
            CeilType.EXECUTORS,
            CeilType.START_TIME,
            CeilType.EXT_STATE,
            CeilType.END_TIME -> {
                mListViewModel.initListDelay()
            }

            CeilType.PRIORITY,
            CeilType.EXT_PROGRESS -> {
                // 修改优先级，只需要本地刷新，不请求接口
                val adapter = mRv.realAdapter as? JoyWork2GroperAdapter ?: return
                adapter.updateSpecial(work)
            }
        }
    }

    // TitleCeilProcessorListener
    override fun needIndentation(): Boolean {
        return false
    }

    override fun onFinishStatusChange(joyWork: JoyWork, adapterPosition: Int) {
        val a = activity ?: return
        if (joyWork.isFinish) {
            ProjectConstant.sendBroadcast(
                a, ProjectConstant.UNFINISH_ACTION
            )
            joyWork.uiTaskStatus = TaskStatusEnum.UN_FINISH.code;
        } else {
            ProjectConstant.sendBroadcast(
                a,
                ProjectConstant.FINISH_ACTION
            )
            joyWork.uiTaskStatus = TaskStatusEnum.FINISH.code
            if (joyWork.isFinish && joyWork.isDup) {
                ToastUtils.showInfoToast(R.string.joywork_dup_work_finish_tips)
            }
        }
        (mRv.realAdapter as? JoyWork2GroperAdapter)?.finishStatusChange(joyWork, adapterPosition)
        if (conditionViewModel.showType == TodoShowType.CARD) {
            mListViewModel.initListDelay()
        }
    }

    // DeadlineCeilProcessor
    override fun deadlineTimestampType(joyWork: JoyWork): TimestampType {
        return TimestampType.DEADLINE
//        val curStatus = mTabViewModel.mConditionValueLiveData.value as? ConditionState.Condition
//        return if ((curStatus?.status) == TaskStatusEnum.FINISH) {
//            TimestampType.FINISH_TIME
//        } else {
//            TimestampType.DEADLINE
//        }
    }


    // StartCeilProcessor

    // ProjectCeilProcessorListener
    override fun afterProjectDeleted(joyWork: JoyWork) {
        mListViewModel.initListDelay()
//        val adapter = mRv.realAdapter as? JoyWork2GroperAdapter ?: return
//        adapter.updateSpecial(joyWork)
    }

    override fun afterProjectAdded(taskId: String, project: Project) {
        mListViewModel.initListDelay()
//        val adapter = mRv.realAdapter as? JoyWork2GroperAdapter ?: return
//        val joywork = adapter.getJoyWorkById(taskId) ?: return
//        val ps = joywork.projects ?: ArrayList()
//        ps.add(project)
//        joywork.projects = ps
//        adapter.updateSpecial(joywork)
    }

    override fun isCustomGroup() = getGrouper() is CustomGrouper

    override fun selectGroup(taskId: String, selectGroupId: String?) {
        DialogSupporter(null, requireContext()).listGroup(
            getGroups() as ArrayList<Group>,
            selectGroupId
        ) { group: Group? ->
            if (group == null) return@listGroup
            JoyWorkRepo.sortProjectJoyWork(taskId, group.groupId, getProjectId(), JoyWorkLocationParam(),
                object : JoyWorkUpdateCallback {
                    override fun result(success: Boolean, errorMsg: String) {
                        if (success) {
                            mListViewModel.initListDelay()
                            ToastUtils.showToast(R.string.joywork_move_success)
                        } else {
                            ToastUtils.showToast(R.string.joywork_move_failure)
                        }
                    }

                    override fun onStart() {
                    }
                })
        }
    }

    override fun transferTask(work: JoyWork) {
        val ls = ArrayList<MemberEntityJd>()
        work.owners.filter { it.taskStatus == TaskStatusEnum.UN_FINISH.code }.forEach {
            val jd = MemberEntityJd()
            jd.mApp = it.app
            jd.mId = it.emplAccount
            ls.add(jd)
        }
        ExecutorUtils.selectTransfer(requireActivity(), ls, {
            val member = Members()
            it?.first()?.let { m ->
                member.fromDD(m)
            }
            JoyWorkRepo.transfer(
                work.taskId,
                member
            ) { success: Boolean, result: TransferResult?, _: String? ->
                if (success && result != null) {
                    mListViewModel.initList()
                    ToastUtils.showToast(R.string.joywork_transfer_success)
                } else {
                    ToastUtils.showToast(R.string.joywork_transfer_failure)
                }
            }
        }, {
            // failure
        })
    }

    override fun deleteTask(work: JoyWork, position: Int) {
        showAlertDialog(
            requireContext(),
            resources.getString(R.string.joywork_delete_button_dialog_title),
            resources.getString(R.string.joywork_delete_button_dialog_sub_message),
            R.string.joywork_delete_button_dialog_confirm,
            R.string.cancel,
            {
                TaskDetailWebservice.deleteTask(work.taskId, null,
                    object : TaskDetailWebservice.TaskCallback() {
                        override fun onSuccess(info: ResponseInfo<String?>?) {
                            super.onSuccess(info)
                            (mRv.realAdapter as? JoyWork2GroperAdapter)?.finishStatusChange(
                                work,
                                position
                            )
                            ToastUtils.showToast(R.string.around_delete_success)
                        }

                        override fun onFailure(exception: HttpException?, info: String?) {
                            super.onFailure(exception, info)
                            ToastUtils.showToast(R.string.joywork_delete_failure)
                        }
                    })
            },
            { _, it -> it.dismiss() }
        )
    }

    override fun rejectTask(work: JoyWork, position: Int) {
        showRejectTaskEditDialog(requireContext()) {
            JoyWorkRepo.reject(work.taskId, it) { msg ->
                msg?.let {
                    (mRv.realAdapter as? JoyWork2GroperAdapter)?.finishStatusChange(work, position)
                    ToastUtils.showToast(R.string.joywork_reject_success)
                } ?: ToastUtils.showToast(R.string.joywork_reject_failure)
            }
        }
    }

    override fun isArchived(): Boolean = getIsArchiveProject()

    private val mCallbacks = HashMap<Int, Any>()
    private val mData = HashMap<Int, String>()

    override fun gotoSelectProject(
        joyWork: JoyWork,
        callback: ProjectAddCallback
    ) {
        val number = 100
        mCallbacks[number] = callback
        mData[number] = joyWork.taskId
        val i = Intent(
            requireActivity(),
            SelectProjectTaskGroupActivity::class.java
        )
        val ps = ArrayList<Project>()
        if (joyWork.projects.isLegalList()) {
            val t = joyWork.projects.map {
                val p = Project(
                    it.groupId,
                    it.groupTitle,
                    it.title,
                    it.projectId,
                    it.icon
                )
                p.permissions = it.permissions
                p
            }
            ps.addAll(t)
        }
        i.putExtra("linkedProjects", ps)
        startActivityForResult(i, number)
    }

    override fun gotoDialogSelectProject(joyWork: JoyWork, callback: ProjectAddCallback) {
        TaskDetailWebservice.getProjectList(0, object: TaskDetailWebservice.TaskCallback() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val gson = Gson()
                val projectList = gson.fromJson(info?.result, ProjectList::class.java)
                if (projectList.content.list != null && projectList.content.list.size > 0) {
                    val actions = projectList.content.list.map {
                        BottomAction(
                            iconStyle = R.style.BottomSheetDialogIcon_Project,
                            title = it.title,
                            data = it
                        )
                    }
                    val config = DialogConfig().apply {
                        height = DensityUtil.dp2px(requireContext(), 406f)
                        theme = R.style.JoyWorkSubDialogStyle
                    }
                    var sheetDialog: JoyBottomSheetDialog<ProjectList.Content.List>? = null
                    val builder = JoyBottomSheetDialog.Builder<ProjectList.Content.List>(
                        requireContext()
                    ).apply {
                        setActions(actions)
                        setTitle(requireContext().resources.getString(R.string.joywork_task_link_select_project))
                        setConfig(config)
                        setCheckStyle(R.style.BottomSheetDialogIcon_Right)
                        setOnItemClickListener(false) { _, position ->
                            gotoSelectGroupDialog(
                                joyWork,
                                projectList.content.list[position]
                            ) { project, isDel ->
                                callback.invoke(project, isDel)
                                sheetDialog?.dismiss()
                            }
                        }
                    }
                    sheetDialog = JoyBottomSheetDialog(requireContext(), builder)
                    sheetDialog.show()
                }
            }
        })
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val keys = ArrayList<Int>(mData.size)
        val values = ArrayList<String>(mData.size)
        mData.forEach {
            if (it.value.isLegalString()) {
                keys.add(it.key)
                values.add(it.value)
            }
        }
        outState.putIntArray("joywork2group_mdata_keys", keys.toIntArray())
        outState.putStringArray("joywork2group_mdata_values", values.toTypedArray())
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        val keys = savedInstanceState?.getIntArray("joywork2group_mdata_keys") ?: return
        val values = savedInstanceState.getStringArray("joywork2group_mdata_values") ?: return
        if (keys.size == values.size) {
            keys.forEachIndexed { index, i ->
                mData[i] = values[index]
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val a = activity ?: return
        if (resultCode == Activity.RESULT_OK && requestCode == 100) {
            val callback = mCallbacks[requestCode] as? ProjectAddCallback
            val p = data?.getParcelableExtra<Project>("project")
            if (p != null) {
                if (callback != null) {
                    callback.invoke(p, false)
                } else {
                    val taskId = mData.get(requestCode) ?: return
                    ProjectCeilProcessor.addProjects(p, taskId, a) { id, pIn ->
                        afterProjectAdded(id, pIn)
                    }
                    mCallbacks.remove(requestCode)
                }
            }
        } else if (requestCode == 10) {
            val returnParcel =
                data?.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as? DetailReturnParcel
            if (returnParcel?.update == true) {
                mListViewModel.refreshList()
            }
        }
    }

    private fun gotoSelectGroupDialog(
        joyWork: JoyWork,
        list: ProjectList.Content.List?,
        callback: (Project?, isDel: Boolean) -> Unit
    ) {
        val ps = ArrayList<Project>()
        if (joyWork.projects.isLegalList()) {
            val t = joyWork.projects.map {
                val p = Project(
                    it.groupId,
                    it.groupTitle,
                    it.title,
                    it.projectId,
                    it.icon
                )
                p.permissions = it.permissions
                p
            }
            ps.addAll(t)
        }
        val selectGroupId = ps.find { link -> link.projectId == list?.projectId }?.groupId ?: ""
        val actions = list?.groups?.map {
            BottomAction(
                title = it.title.ifEmpty { requireContext().resources.getString(R.string.joywork_task_link_untitled_group) },
                isSelect = selectGroupId == it.groupId,
                data = it
            )
        }?.toMutableList() ?: arrayListOf()
        val config = DialogConfig().apply {
            height = DensityUtil.dp2px(requireContext(), 406f)
            theme = R.style.JoyWorkSubDialogStyle
        }
        var sheetDialog: JoyBottomSheetDialog<Groups>? = null
        val builder = JoyBottomSheetDialog.Builder<Groups>(requireContext()).apply {
            setTitle(requireContext().resources.getString(R.string.joywork_select_group_list_title))
            setActions(actions)
            setConfig(config)
            setNegativeButton(R.string.icon_direction_left)
            setCheckStyle(R.style.BottomSheetDialogCheck_Tick)
            setOnItemClickListener(false) { action, _ ->
                val group = action.data
                if (selectGroupId == group.groupId) return@setOnItemClickListener
                val project = Project(
                    group.groupId,
                    group.title,
                    list?.title,
                    group.projectId,
                    null
                )
                callback.invoke(project, false)
                sheetDialog?.dismiss()
            }
        }
        sheetDialog = JoyBottomSheetDialog(requireContext(), builder)
        sheetDialog.show()
    }
}

open class ListColumnsCeilProcessorFactory(
    val columns: List<ListColumn>,
    val listener: JoyWork2GroupBaseFragment,
    val customFields: List<CustomFieldGroup> = emptyList(),
    val showType: TodoShowType = TodoShowType.TABLE,
) : CeilProcessorFactory {
    override fun create(): List<CeilProcessor> {
        val customFieldGroupFinder: (key: String) -> CustomFieldGroup? = { key ->
            customFields.firstOrNull { customField ->
                customField.columnId == key
            }
        }
        val processors = columns.filter { !it.hidden }.mapNotNull {
            when (it.key) {
                ListColumn.KEY_END_TIME -> DeadlineCeilProcessor(listener, showType)
                ListColumn.KEY_OPERATOR -> ExecutorsCeilProcessor(
                    listener,
                    listener.requireActivity(),
                    showType
                )

                ListColumn.KEY_RECENT_COMMENT -> LatestCommentCeilProcessor(listener)
                ListColumn.KEY_START_TIME -> StartTimeCeilProcessor(listener)
                ListColumn.KEY_PRIORITY -> PriorityCeilProcessor(listener)
                ListColumn.KEY_PROJECT -> ProjectCeilProcessor(listener)
                ListColumn.KEY_SYS_SOURCE -> SourceCeilProcessor(listener)
                ListColumn.KEY_SOURCE -> PrimarySourceCeilProcessor(listener)
                ListColumn.KEY_SUB_SOURCE -> SecondarySourceCeilProcessor(listener)
                ListColumn.KEY_LABEL -> LabelCeilProcessor()
                ListColumn.KEY_CREATE_TIME -> CreateTimeCeilProcessor()
                ListColumn.KEY_CREATOR -> CreatorCeilProcessor()
                ListColumn.KEY_EXT_STATUS, ListColumn.KEY_EXT_PROGRESS -> {
                    val customFieldGroup = customFieldGroupFinder(it.key)
                    customFieldGroup?.run {
                        when (it.key) {
                            ListColumn.KEY_EXT_STATUS -> ExtStatusCeilProcessor(
                                customFieldGroup,
                                listener
                            )

                            ListColumn.KEY_EXT_PROGRESS -> ExtProgressCeilProcessor(
                                customFieldGroup,
                                listener
                            )

                            else -> null
                        }
                    } ?: run {
                        null
                    }
                }

                else -> null
            }
        }
        return listOf<CeilProcessor>(
            TitleCeilProcessor(
                listener,
                listener,
                processors.size + 1
            )
        ) + processors
    }
}

class DefaultCeilProcessorFactory(listener: JoyWork2GroupBaseFragment) :
    ListColumnsCeilProcessorFactory(
        listOf(
            ListColumn(ListColumn.KEY_END_TIME, false),
            ListColumn(ListColumn.KEY_OPERATOR, false),
            ListColumn(ListColumn.KEY_RECENT_COMMENT, true),
            ListColumn(ListColumn.KEY_START_TIME, true),
            ListColumn(ListColumn.KEY_PRIORITY, false),
            ListColumn(ListColumn.KEY_PROJECT, true),
            ListColumn(ListColumn.KEY_SYS_SOURCE, true),
            ListColumn(ListColumn.KEY_SOURCE, true),
            ListColumn(ListColumn.KEY_SUB_SOURCE, true),
        ),
        listener,
    )

class CardDefaultCeilProcessorFactory(listener: JoyWork2GroupBaseFragment) :
    ListColumnsCeilProcessorFactory(
        listOf(
            ListColumn(ListColumn.KEY_END_TIME, false),
            ListColumn(ListColumn.KEY_OPERATOR, false),
            ListColumn(ListColumn.KEY_RECENT_COMMENT, false),
            ListColumn(ListColumn.KEY_START_TIME, true),
            ListColumn(ListColumn.KEY_PRIORITY, false),
            ListColumn(ListColumn.KEY_PROJECT, true),
            ListColumn(ListColumn.KEY_SYS_SOURCE, true),
            ListColumn(ListColumn.KEY_SOURCE, true),
            ListColumn(ListColumn.KEY_SUB_SOURCE, true),
            ListColumn(ListColumn.KEY_LABEL, false),
        ),
        listener,
        showType = TodoShowType.CARD
    )
