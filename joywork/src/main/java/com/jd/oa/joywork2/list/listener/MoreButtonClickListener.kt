package com.jd.oa.joywork2.list.listener

import android.content.Context
import android.view.View
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.jd.oa.joywork.DeleteAction
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkOp
import com.jd.oa.joywork.MoveAction
import com.jd.oa.joywork.RejectAction
import com.jd.oa.joywork.TransferAction
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.hasSpecialPermission
import com.jd.oa.joywork.isSelf
import com.jd.oa.joywork.team.showGroupAction
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.clickEvent

/**
 * @Author: hepiao3
 * @CreateTime: 2024/12/24
 * @Description:
 */
class MoreButtonClickListener(
    private val work: JoyWork,
    private val context: Context,
    moreButton: IconFontView,
    private val holder: ViewHolder,
    private val listener: MoreButtonCeilProcessorListener
) : View.OnClickListener {
    private val actionList = arrayListOf<JoyWorkAction>()
    private var bottomAction: JoyWorkAction? = null

    init {
        if (hasSpecialPermission(JoyWorkOp.UPDATE, work.enablePermission, work.permission) &&
            !work.isUIFinish && listener.isCustomGroup()
        ) {
            actionList.add(MoveAction(context))
        }
        if (hasSpecialPermission(JoyWorkOp.ASSIGN, work.enablePermission, work.permission) &&
            work.owners?.firstOrNull { it.isSelf() } != null && !work.isUIFinish
        ) {
            actionList.add(TransferAction(context))
        }
        if (work.assigner != null && !work.assigner.isSelf()
            && hasSpecialPermission(JoyWorkOp.ASSIGN, work.enablePermission, work.permission)
            && work.owners?.find { it.isSelf() } != null && !work.isUIFinish
        ) {
            actionList.add(RejectAction(context))
        }
        bottomAction = if (hasSpecialPermission(
                JoyWorkOp.DELETE,
                work.enablePermission,
                work.permission
            )
        ) {
            DeleteAction(context)
        } else null
        moreButton.isVisible =
            if (listener.isArchived()) false else !(actionList.isEmpty() && bottomAction == null)
    }

    override fun onClick(v: View?) {
        showGroupAction(context, actionList, bottomAction) {
            when (it) {
                is MoveAction -> {
                    clickEvent(JoyWorkConstant.MOBILE_EVENT_CARD_MOVE_TO)
                    listener.selectGroup(
                        work.taskId,
                        (work.expandableGroup?.title as? JoyWork2ExtraTitle)?.extraGroup?.groupId
                    )
                }

                is TransferAction -> {
                    clickEvent(JoyWorkConstant.MOBILE_EVENT_CARD_TRANSFER)
                    listener.transferTask(work)
                }

                is RejectAction -> {
                    clickEvent(JoyWorkConstant.MOBILE_EVENT_CARD_REFUSE)
                    listener.rejectTask(work, holder.bindingAdapterPosition)
                }

                is DeleteAction -> {
                    clickEvent(JoyWorkConstant.MOBILE_EVENT_CARD_DELETE)
                    listener.deleteTask(work, holder.bindingAdapterPosition)
                }
            }
        }
    }

    interface MoreButtonCeilProcessorListener {
        fun isCustomGroup(): Boolean
        fun selectGroup(taskId: String, selectGroupId: String?)
        fun transferTask(work: JoyWork)
        fun deleteTask(work: JoyWork, position: Int)
        fun rejectTask(work: JoyWork, position: Int)
        fun isArchived(): Boolean
    }
}