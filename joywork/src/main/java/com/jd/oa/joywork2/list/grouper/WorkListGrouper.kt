package com.jd.oa.joywork2.list.grouper

import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper
import com.jd.oa.joywork2.bean.JoyWorkWrapper2
import com.jd.oa.joywork2.list.GetCustomGroupList
import com.jd.oa.joywork2.list.GetGroupedTimeList
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.menu.MenuItem

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/27 00:20
 */


interface IWorkListGrouper {

    suspend fun loadData(
        condition: ConditionState,
        item: MenuItem,
        regionType: String,
        dynamicLimitSize: Int = ProjectRepo.PAGE_LIMIT
    ): WorkListWrapper

    suspend fun loadMore(
        condition: ConditionState, item: MenuItem, offset: Int, regionType: String
    ): WorkListWrapper

}


object TimeListGrouper : IWorkListGrouper {

    override suspend fun loadData(
        condition: ConditionState, item: MenuItem, regionType: String, dynamicLimitSize: Int
    ): WorkListWrapper {
        val wrapper: JoyWorkWrapper2 =
            GetGroupedTimeList.list(condition, item, dynamicLimitSize, regionType)
        return WorkListWrapper(extract(wrapper), wrapper.total)
    }

    override suspend fun loadMore(
        condition: ConditionState, item: MenuItem, offset: Int, regionType: String
    ): WorkListWrapper {
        val wrapper: JoyWorkWrapper2 =
            GetGroupedTimeList.loadMore(condition, item, offset, regionType)
        return WorkListWrapper(extract(wrapper), wrapper.total)
    }

    private fun extract(wrapper: JoyWorkWrapper2): List<JoyWork> {
        val workList = mutableListOf<JoyWork>()
        if (wrapper.clientTasks.isLegalList()) {
            wrapper.clientTasks.forEach {
                if (it?.tasks.isLegalList()) {
                    workList.addAll(it.tasks)
                }
            }
        }
        return workList
    }
}

object CustomListGrouper : IWorkListGrouper {

    override suspend fun loadData(
        condition: ConditionState,
        item: MenuItem,
        regionType: String,
        dynamicLimitSize: Int,
    ): WorkListWrapper {
        val customGrouper: JoyWorkCustomGrouper =
            GetCustomGroupList.list(condition, item, dynamicLimitSize, regionType)
        return extract(customGrouper)
    }

    override suspend fun loadMore(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        regionType: String
    ): WorkListWrapper {
        val customGrouper: JoyWorkCustomGrouper =
            GetCustomGroupList.loadMore(condition, item, offset, regionType)
        return extract(customGrouper)
    }

    private fun extract(customGrouper: JoyWorkCustomGrouper): WorkListWrapper {
        val workList = mutableListOf<JoyWork>()
        var total = 0
        customGrouper.groups.forEach {
            workList.addAll(it.tasks)
            total = it.total
        }
        return WorkListWrapper(workList, total)
    }
}

data class WorkListWrapper(
    val works: List<JoyWork>,
    val total: Int
)