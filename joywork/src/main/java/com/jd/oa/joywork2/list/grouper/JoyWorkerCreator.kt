package com.jd.oa.joywork2.list.grouper

import android.app.Activity
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.shortcut.SuccessSnackBar
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.dialog.CreateDialog
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle

interface JoyWorkerCreator {
    fun createJoyWork(
        activity: Activity,
        listType: TaskListTypeEnum,
        projectId: String,
        groupId: String? = null,
        groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        success: (JoyWork) -> Unit
    ): Boolean {
        return false
    }

    fun createJoyWorkNew(
        activity: Activity,
        listType: TaskListTypeEnum,
        projectId: String,
        groupId: String? = null,
        groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        viewId: String?,
        viewType: String?,
        success: (String?) -> Unit
    ): Boolean {
        return false
    }
}

object CustomGroupCreator : JoyWorkerCreator {
    override fun createJoyWork(
        activity: Activity,
        listType: TaskListTypeEnum,
        projectId: String,
        groupId: String?,
        groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        success: (JoyWork) -> Unit
    ): Boolean {
        val dg = ArrayList<Group>()
        val config = ShortcutDialogTmpData()
        var selectGroupId = groupId
        groups.filter {
            it.title is JoyWork2ExtraTitle
        }.forEach { group ->
            val gg = (group.title as JoyWork2ExtraTitle).extraGroup
            if (gg != null) {
                val g = Group()
                gg.toGroup(g)
                dg.add(g)
                if (selectGroupId.isNullOrEmpty() && g.isDefaultGroup) {
                    selectGroupId = g.groupId
                }
            }
        }
        config.hideGroups = true
        config.isProject = false
        config.selectGroupId = selectGroupId
        if (listType == TaskListTypeEnum.HANDLE) {
            val self = JoyWorkUser.getSelf();
            self.setToChief()
            config.owners = listOf(self)
        }

        CreateDialog(activity, dg, projectId, config).apply {
            successCallback = { joywork, dialog ->
                dialog.dismiss()
                success(joywork)
                SuccessSnackBar(activity).show(joywork)
            }
        }.show()
        return true
    }

}

object CustomGroupCreatorNew : JoyWorkerCreator {
    override fun createJoyWorkNew(
        activity: Activity,
        listType: TaskListTypeEnum,
        projectId: String,
        groupId: String?,
        groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        viewId: String?,
        viewType: String?,
        success: (String?) -> Unit
    ): Boolean {
        val dg = ArrayList<Group>()
        val config = ShortcutDialogTmpData()
        var selectGroupId = groupId
        groups.filter {
            it.title is JoyWork2ExtraTitle
        }.forEach { group ->
            val gg = (group.title as JoyWork2ExtraTitle).extraGroup
            if (gg != null) {
                val g = Group()
                gg.toGroup(g)
                dg.add(g)
                if (selectGroupId.isNullOrEmpty() && g.isDefaultGroup) {
                    selectGroupId = g.groupId
                }
            }
        }
        val isProjectList = listType == TaskListTypeEnum.PROJECT
        config.taskListTypeEnum = listType
        config.hideGroups = !isProjectList
        config.selectGroupId = selectGroupId
        config.isProject = isProjectList
        if (config.taskListTypeEnum == TaskListTypeEnum.HANDLE) {
            val self = JoyWorkUser.getSelf()
            self.setToChief()
            config.owners = listOf(self)
        }
        val dialogO =
            JoyWorkShortcutCreator.getShortcutDialog(
                activity,
                tmpData = config,
                showGroup = true,
                projectId = projectId,
                groups = dg,
                viewId,
                viewType
            )
        dialogO.showSnackBar = true
        dialogO.successCallback = { result, _, dialog ->
            dialog?.dismiss()
            success(result)
        }
        dialogO.show()

        return true
    }

}
