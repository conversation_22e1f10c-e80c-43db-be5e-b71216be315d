package com.jd.oa.joywork2.list.kanban

import com.jd.oa.joywork2.list.WorkListBaseViewModel
import com.jd.oa.joywork2.list.grouper.CustomListGrouper
import com.jd.oa.joywork2.list.grouper.IWorkListGrouper
import com.jd.oa.joywork2.list.grouper.TimeListGrouper
import com.jd.oa.joywork2.main.bean.ConditionGrouper

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/11 23:01
 */
class KanbanListVM : WorkListBaseViewModel() {

    var grouperField: ConditionGrouper.Field = ConditionGrouper.Field.CUSTOM

    override val listGrouper: IWorkListGrouper
        get() = getListGrouperByField()


    private fun getListGrouperByField(): IWorkListGrouper {
        return when (grouperField) {
            ConditionGrouper.Field.CUSTOM -> {
                CustomListGrouper
            }

            ConditionGrouper.Field.END_TIME -> {
                TimeListGrouper
            }

            ConditionGrouper.Field.START_TIME -> {
                TimeListGrouper
            }

            else -> {
                TimeListGrouper
            }
        }
    }

}