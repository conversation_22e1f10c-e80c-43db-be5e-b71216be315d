package com.jd.oa.joywork2.list

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.ProjectErrorCode
import com.jd.oa.joywork2.backend.JoyWorkHttpException
import com.jd.oa.joywork2.list.grouper.IWorkListGrouper
import com.jd.oa.joywork2.list.grouper.TimeListGrouper
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.delay

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/11 23:01
 */
abstract class WorkListBaseViewModel : ViewModel() {

    abstract val listGrouper: IWorkListGrouper

    companion object {
        const val LOAD_MORE_FAIL = 0
        const val LOAD_MORE_FINISH = 1
        const val LOAD_MORE_COMPLETE = 2
    }

    sealed class DataState {
        class RefreshResult(
            val success: Boolean,
            val data: List<JoyWork>? = null,
            val msg: String? = null,
        ) : DataState()

        class LoadMoreResult(
            val result: Int = LOAD_MORE_FAIL,
            val data: List<JoyWork>? = null,
            val msg: String? = null,
        ) : DataState()
    }

    fun loadData(
        condition: ConditionState,
        item: MenuItem,
        regionType: String,
        dynamicLimitSize: Int,
        delayLoad: Boolean
    ) {
        viewModelScope.safeLaunch {
            try {
                updateRefreshing(true)
                if (delayLoad) delay(1000)
                val wrapper = listGrouper.loadData(condition, item, regionType, dynamicLimitSize)
                updateData(DataState.RefreshResult(true, wrapper.works))
            } catch (e: JoyWorkHttpException) {
                val target = ProjectErrorCode.values()
                    .firstOrNull { it.code == e.code }
                if (target != null
                ) {
                    updateData(
                        DataState.RefreshResult(
                            false, msg = JoyWorkEx.filterErrorMsg(
                                target.hint
                            )
                        )
                    )

                } else {
                    updateData(
                        DataState.RefreshResult(
                            false, msg = JoyWorkEx.filterErrorMsg(e.message)
                        )
                    )
                }
            } finally {
                updateRefreshing(false)
            }
        }
    }

    fun loadMore(
        condition: ConditionState,
        item: MenuItem,
        offset: Int,
        regionType: String
    ) {
        viewModelScope.safeLaunch {
            try {
                val wrapper = listGrouper.loadMore(
                    condition,
                    item,
                    offset,
                    regionType
                )
                val complete = offset >= wrapper.total
                updateData(
                    DataState.LoadMoreResult(
                        if (complete) LOAD_MORE_COMPLETE else LOAD_MORE_FINISH,
                        wrapper.works
                    )
                )
            } catch (e: JoyWorkHttpException) {
                val target = ProjectErrorCode.values()
                    .firstOrNull { it.code == e.code }
                if (target != null
                ) {
                    updateData(
                        DataState.LoadMoreResult(
                            LOAD_MORE_FAIL, msg = JoyWorkEx.filterErrorMsg(
                                target.hint
                            )
                        )
                    )
                }
            } finally {

            }
        }
    }

    // 数据列表
    private val _data = MutableLiveData<DataState>()
    val data: LiveData<DataState> = _data

    protected fun updateData(state: DataState) {
        _data.value = state
    }

    private val _refreshingEffect = MutableLiveData(false)
    val refreshingEffect: LiveData<Boolean> = _refreshingEffect
    protected fun updateRefreshing(newValue: Boolean) {
        _refreshingEffect.value = newValue
    }

}