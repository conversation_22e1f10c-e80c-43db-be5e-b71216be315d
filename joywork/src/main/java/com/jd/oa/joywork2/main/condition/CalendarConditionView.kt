package com.jd.oa.joywork2.main.condition

import android.util.TypedValue.COMPLEX_UNIT_DIP
import android.view.View
import android.view.ViewGroup
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.core.view.setPadding
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.JoyConditionFragment
import com.jd.oa.utils.ColorEx
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.JoyWorkUtils.bindView

/**
 * Created by AS
 * <AUTHOR> zhengy<PERSON>uang
 * @create 2024/9/10 11:20
 * type
 */
class CalendarConditionView(
    fragment: JoyConditionFragment,
    conditionViewModel: ConditionViewModel,
    isProjectList: Boolean = false,
) : ConditionView(fragment, conditionViewModel, isProjectList) {


    override val conditionView: HorizontalScrollView by fragment.bindView(R.id.calendar_condition)

    private val conditionLinearLayout: LinearLayout by fragment.bindView(R.id.container_linear)

    override fun onViewCreated(view: View) {
        val data = mutableListOf(
            TaskStatusEnum.UN_FINISH,
            TaskStatusEnum.RISK,
        )

        val dp1 = DisplayUtils.dip2px(1f)
        data.forEachIndexed { _, taskStatusEnum ->
            val text = TextView(view.context)
            text.setTextSize(COMPLEX_UNIT_DIP, 12f)
            text.setText(taskStatusEnum.getStringId())
            text.setBackgroundResource(R.drawable.joywork2_status_condition_sel)
            text.setTextColor(
                ColorEx.addItem(
                    ColorEx.Item().apply {
                        selected = true
                        color = ContextCompat.getColor(view.context, R.color.color_F63218)
                    },
                    ColorEx.Item().apply {
                        selected = false
                        color = ContextCompat.getColor(view.context, R.color.color_1B1B1B)
                    },
                )
            )
            text.tag = taskStatusEnum.code
            text.isSelected =
                taskStatusEnum.code == conditionViewModel.calendarTaskStateLD.value!!.code
            val layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            text.setPadding(dp1 * 8)
            layoutParams.setMargins(dp1 * 12, 0, 0, 0)
            text.setOnClickListener { v ->
                val parent = v.parent as ViewGroup
                (parent.children).forEach { child ->
                    child.isSelected = child.tag == taskStatusEnum.code
                }
                conditionViewModel.calendarTaskStateLD.value = taskStatusEnum
            }
            conditionLinearLayout.addView(text, layoutParams)
        }
    }

    fun updateState(status: TaskStatusEnum) {
        conditionLinearLayout.runCatching {
            (conditionLinearLayout.children).forEach { child ->
                child.isSelected = child.tag == status.code
            }
        }
    }

}