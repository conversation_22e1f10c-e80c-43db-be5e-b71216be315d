package com.jd.oa.joywork2.main.condition

import android.view.View
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.JoyConditionFragment
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/10 11:13
 */
abstract class ConditionView(
    val fragment: JoyConditionFragment,
    val conditionViewModel: ConditionViewModel,
    val isProjectList: Boolean = false,
) {

    abstract val conditionView: View

    abstract fun onViewCreated(view: View)

    fun show() {
        conditionView.visible()
    }

    fun hide() {
        conditionView.gone()
    }

}