package com.jd.oa.joywork2.list.grouper

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.showNewGroupDialog
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.invisible
import com.jd.oa.utils.visible

class LoadMoreVH(
    context: Context,
    parent: ViewGroup
) : RecyclerView.ViewHolder(
    context.inflater.inflate(
        R.layout.jdme_joywork_list_item_loadmore,
        parent,
        false
    )
) {
    private val loadMore = itemView.findViewById<LinearLayout>(R.id.loadMore)
    private val loadMoreText = itemView.findViewById<TextView>(R.id.loadMoreText)
    private val loadMoreIcon = itemView.findViewById<View>(R.id.loadMoreIcon)
    private val pb = itemView.findViewById<View>(R.id.pb)
    private val LoadMoreJoyWork.group: Group
        get() {
            return expandableGroup.title.extra as Group
        }

    private fun updateUI(loadMoreJoyTask: LoadMoreJoyWork) {
        if (loadMoreJoyTask.isLoading) {
            pb.visible()
            loadMoreText.setText(R.string.libui_loading)
            loadMoreIcon.gone()
        } else {
            pb.gone()
            loadMoreText.setText(R.string.me_load_more_data)
            loadMoreIcon.visible()
        }
    }

    fun <T : LoadMoreJoyWork> bind(
        joyWork: T,
        adapter: RecyclerView.Adapter<*>,
        callback: (joyWork: T, completeRunnable: Runnable) -> Unit
    ) {
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        updateUI(joyWork)
        loadMore.tag = joyWork
        loadMore.setOnClickListener {
            val loadMoreJoyWork = it.tag as T
            if (loadMoreJoyWork.isLoading) {
                return@setOnClickListener
            }
            loadMoreJoyWork.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            callback(loadMoreJoyWork) {
                loadMoreJoyWork.isLoading = false
                adapter.notifyDataSetChanged()
            }
        }
    }
}

class LoadMoreVH2(
    context: Context,
    parent: ViewGroup
) : RecyclerView.ViewHolder(
    context.inflater.inflate(
        R.layout.jdme_joywork_list_item_loadmore,
        parent,
        false
    )
) {
    private val loadMore = itemView.findViewById<LinearLayout>(R.id.loadMore)
    private val loadMoreText = itemView.findViewById<TextView>(R.id.loadMoreText)
    private val loadMoreIcon = itemView.findViewById<View>(R.id.loadMoreIcon)
    private val pb = itemView.findViewById<View>(R.id.pb)
    private val LoadMoreJoyWork.group: Group
        get() {
            return expandableGroup.title.extra as Group
        }

    private fun updateUI(cardBottomItem: CardBottomMultiPurpose<*>) {
        if (cardBottomItem.isLoading) {
            pb.visible()
            loadMoreText.setText(R.string.libui_loading)
            loadMoreIcon.gone()
        } else {
            pb.gone()
            loadMoreText.setText(R.string.me_load_more_data)
            loadMoreIcon.visible()
        }
    }

    fun <S, T : CardBottomMultiPurpose<S>> bind(
        joyWork: T,
        adapter: RecyclerView.Adapter<*>,
        callback: (payload: S, completeRunnable: Runnable) -> Unit
    ) {
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        updateUI(joyWork)
        loadMore.tag = joyWork
        loadMore.setOnClickListener {
            val loadMoreJoyWork = it.tag as T
            if (loadMoreJoyWork.isLoading) {
                return@setOnClickListener
            }
            loadMoreJoyWork.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            callback(joyWork.payload) {
                loadMoreJoyWork.isLoading = false
                adapter.notifyDataSetChanged()
            }
        }
    }
}

class TableIconTextExpandableVH<T>(
    context: Context,
    parent: ViewGroup,
    private val itemClick: (T) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork2_grouper_text,
            parent,
            false
        )
    ), Exchangeable {
    override var exchangeable = false

    private val title by bindView<TextView>(R.id.title)
    private val indicator by bindView<TextView>(R.id.indicator)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_joywork_group_small).toInt()

    private val click = View.OnClickListener {
        itemClick(it.getTag(R.id.jdme_tag_id) as T)
    }

    // extra 会在点击事件中原样带回，不做任何处理
    fun bind(
        text: String,
        expanded: Boolean,
        first: Boolean,
        extra: T,
    ) {
        if (first) {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingSmall,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        } else {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingBig,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        }

        title.text = text
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }

        itemView.setTag(R.id.jdme_tag_id, extra)
        itemView.setOnClickListener(click)
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class OnlyTextExpandableVH<T>(
    context: Context,
    parent: ViewGroup,
    private val itemClick: (T) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork2_grouper_only_text,
            parent,
            false
        )
    ), Exchangeable {
    override var exchangeable = false

    private val title by bindView<TextView>(R.id.title)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_joywork_group_small).toInt()

    private val click = View.OnClickListener {
        itemClick(it.getTag(R.id.jdme_tag_id) as T)
    }

    // extra 会在点击事件中原样带回，不做任何处理
    fun bind(
        text: String,
        first: Boolean,
        extra: T,
    ) {
        if (first) {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingSmall,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        } else {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingBig,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        }

        title.text = text
        itemView.setTag(R.id.jdme_tag_id, extra)
        itemView.setOnClickListener(click)
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class UserExpandableVH<T>(
    context: Context,
    parent: ViewGroup,
    private val itemClick: (T) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork2_grouper_user,
            parent,
            false
        )
    ), Exchangeable {
    private val title = itemView.findViewById<TextView>(R.id.title)
    private val avatar = itemView.findViewById<ImageView>(R.id.avatar)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_joywork_group_small).toInt()

    override var exchangeable: Boolean = false

    private val click = View.OnClickListener {
        val t = it.getTag(R.id.jdme_tag_id) as T
        itemClick(t)
    }

    fun bind(titleStr: String, icon: String?, first: Boolean, extra: T) {
        itemView.setTag(R.id.jdme_tag_id, extra)
        itemView.setOnClickListener(click)

        if (first) {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingSmall,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        } else {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingBig,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        }

        title.text = titleStr
        avatar.visible()
        JoyWorkViewItem.avatar(avatar, icon)
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "ProjectGroupTextImageVH【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class TextWithActionExpandableVH<T>(
    context: Context,
    parent: ViewGroup,
    private val itemClick: (T) -> Unit,
    private val actionClick: (T) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork2_vh_text_with_extra,
            parent,
            false
        )
    ), Exchangeable {
    override var exchangeable = false

    private val title by bindView<TextView>(R.id.title)
    private val indicator by bindView<TextView>(R.id.indicator)
    private val actionView by bindView<View>(R.id.icon)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_joywork_group_small).toInt()

    private val click = View.OnClickListener {
        itemClick(it.getTag(R.id.jdme_tag_id) as T)
    }

    private val actionClickListener = View.OnClickListener {
        actionClick(it.getTag(R.id.jdme_tag_id) as T)
    }

    // extra 会在点击事件中原样带回，不做任何处理
    fun bind(
        text: String,
        expanded: Boolean,
        first: Boolean,
        actionShow: Boolean,
        extra: T,
    ) {
        if (first) {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingSmall,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        } else {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingBig,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        }

        title.text = text
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }

        itemView.setTag(R.id.jdme_tag_id, extra)
        itemView.setOnClickListener(click)

        actionView.setTag(R.id.jdme_tag_id, extra)
        actionView.setOnClickListener(actionClickListener)
        if (actionShow) {
            actionView.visible()
        } else {
            actionView.invisible()
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class SelfListNewGroupVH2(
    context: Context,
    parent: ViewGroup,
    private val createGroup: (name: String, joyWorkTitle: ProjectAddTitle) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_project_new_group,
            parent,
            false
        )
    ) {

    private val root = itemView.findViewById<View>(R.id.root)

    fun bind(joyWorkTitle: ProjectAddTitle) {
        root.setTag(R.id.jdme_tag_id, joyWorkTitle)
        root.setOnClickListener {
            newGroup(it.getTag(R.id.jdme_tag_id) as ProjectAddTitle)
        }
    }

    private fun newGroup(joyWorkTitle: ProjectAddTitle) {
        showNewGroupDialog(itemView.context) {
            createGroup(it, joyWorkTitle)
        }
    }


    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}