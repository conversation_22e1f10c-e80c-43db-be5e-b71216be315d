package com.jd.oa.joywork2.list.grouper

import android.app.Activity
import android.content.Context
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.ProjectDelete
import com.jd.oa.joywork.ProjectGroupSort
import com.jd.oa.joywork.ProjectNewAfter
import com.jd.oa.joywork.ProjectNewBefore
import com.jd.oa.joywork.ProjectRename
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectGroupTypeEnum
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.kpi.PlaceHolderVH
import com.jd.oa.joywork.team.showGroupAction
import com.jd.oa.joywork.team.showGroupDelAlertDialog
import com.jd.oa.joywork.team.showGroupNameEditDialog
import com.jd.oa.joywork.team.showGroupSortDialog
import com.jd.oa.joywork.team.showNewGroupDialog
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.joywork2.bean.title.JoyWork2MovePlaceholderItem
import com.jd.oa.joywork2.list.FindMyTasksRepo
import com.jd.oa.joywork2.list.GetCustomGroupList
import com.jd.oa.joywork2.list.GetGroupedTimeList
import com.jd.oa.joywork2.list.ItemViewHolder
import com.jd.oa.joywork2.list.ListRepo
import com.jd.oa.joywork2.list.TileListRepo
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent

enum class GrouperTitleType {
    NO,// 无分组
    PROJECT,// 清单
    MY_FINISH,// 已完成
    SOURCE,// 来源
    EXECUTOR,// 执行人分组
    LOAD_MORE, // 加载更多
    END_TIME,// 截止时间
    CUSTOM_GROUP, // 自定义分组
    ADD_GROUP, // 添加分组
    SUB_SOURCE, // 二级来源
    BIZ_CODE, // 待办业务来源
    EXT_STATUS, // 待办状态
    EMPTY_TITLE, // 待办状态
    GROUP_BOTTOM, // card视图底部多用途项
}

fun interface LoadMoreCallback<T> {
    fun loadMore(t: T, completeRunnable: Runnable)
}

fun interface TitleCallback {
    fun click(title: JoyWorkTitle)
}

// 用于处理各分组的标题
abstract class JoyWorkGrouper {

    abstract fun getRepo(): ListRepo

    abstract fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)? //用于检查当前加载的总数据
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>

    fun getItemViewType(data: Any): Int? {
        return onGetItemViewType(data)?.ordinal
    }

    abstract fun onGetItemViewType(data: Any): GrouperTitleType?

    abstract fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder

    abstract fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    )

    open fun createJoyWork(
        activity: Activity,
        listType: TaskListTypeEnum,
        groupId: String? = null,
        groups: java.util.ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        viewId: String?,
        viewType: String?,
        success: (String?) -> Unit
    ) = false

    open fun needLoadMore(): Boolean {
        return false
    }

    open fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ): Boolean? {
        return false
    }

    open fun canDraggable() = false
    open fun isGroupTitle(viewHolder: RecyclerView.ViewHolder): Boolean {
        return false
    }

    open fun getMovableVHClass(): Class<out ViewHolder> = ItemViewHolder::class.java
}

// 给无分组使用的一个 Grouper
object NoGrouper : JoyWorkGrouper() {
    override fun getRepo(): ListRepo {
        return FindMyTasksRepo
    }

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return ArrayList()
    }

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return PlaceHolderVH(parent.context)
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {

    }
}

abstract class FinishTimeGrouper : JoyWorkGrouper() {
    override fun getRepo() = TileListRepo
    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
//        val wrapper = any as? JoyWorkWrapper ?: return ArrayList()
//        return MyFinishFragment.handleResult(wrapper, context.resources)
        return CompleteTimeDataGrouper.group(any, context)
    }

    override fun needLoadMore(): Boolean {
        return true
    }
}


abstract class EndTimeGrouper : JoyWorkGrouper() {
    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return RegionDataGrouper.group(any, context, null, null, isRefresh, loadOffsetChecker)
    }

    override fun getRepo() = GetGroupedTimeList
}

interface CustomGrouperListener {

    /**
     * 查找前一个分组。底部新建时需要拿到前一个分组信息
     */
    fun preGroup(curGroupId: String): ExpandableGroup<JoyWorkTitle, JoyWork>?

    fun afterRenameSuccess(name: String, id: String, title: JoyWork2ExtraTitle)

    fun delGroup(groupId: String, isHard: Boolean)

    fun getProjectId(): String

    /**
     * 新建分组后
     * @param title: 新建的分组以哪个为锚点
     * @param expandableGroup: 新建的分组
     */
    fun afterCreateSuccess(
        isBefore: Boolean,
        title: JoyWork2ExtraTitle?,
        expandableGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
    )

    fun currentGroups(
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>

    fun onSortedGroups(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>)

    fun onWorkCreated(result: String?)

    fun currentTaskListType(): TaskListTypeEnum

    fun getViewId(): String?

    fun getViewType(): String?
}

fun interface CustomGroupListener {
    fun expand(title: JoyWorkTitle)
}

abstract class CustomGrouper(
    private val groupListener: CustomGroupListener,
    val listener: CustomGrouperListener,
    val isProjectList: Boolean,
) : JoyWorkGrouper() {

    var draggable = true

    override fun canDraggable(): Boolean {
        return !isProjectList || draggable
    }

    override fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>
    ): Boolean? {

        if (target is TextWithActionExpandableVH<*>) {
            val title =
                target.itemView.getTag(R.id.jdme_tag_id) as? JoyWork2ExtraTitle ?: return null
            // 如果是分组标题且初始化过，需要先打开
            if (!title.expandableGroup.expand && title.isInit()) {
                groupListener.expand(title)
                return true
            }
            val adapterPosition = target.adapterPosition
            if (adapterPosition <= 0 || adapterPosition >= adapter.itemCount - 1) {
                return false
            }
            // 上下拖动时如果同样是分组，还可以拖进去
            // 此时分组未初始化，只需要将当前任务放置在分组最前端，放置接口成功后会请求该分组，进而打开分组
            return if (current.adapterPosition > adapterPosition) {
                // 上拖
                val pre = adapter.getItemViewType(adapterPosition - 1)
                adapter.getItemViewType(target.adapterPosition) == pre || current.itemViewType == pre
            } else if (current.absoluteAdapterPosition < adapterPosition) {
                // 下拖，应该都可以拖入，存储至本分组中
                true
//                val next = adapter.getItemViewType(adapterPosition + 1)
//                adapter.getItemViewType(target.adapterPosition) == next || current.itemViewType == next
            } else {
                null
            }
        }

        return null
    }


    override fun getRepo() = GetCustomGroupList

    protected fun showAction(
        isProjectList: Boolean,
        todoShowType: TodoShowType,
        title: JoyWork2ExtraTitle,
        group: JoyWorkCustomGrouper.Group,
        context: Context,
    ) {
        val pair = getAllGroupAction(todoShowType, group, context)
        showGroupAction(context, pair.first, pair.second) {
            when (it) {
                is ProjectRename -> {
                    showGroupNameEditDialog(context, group.title) {
                        renameNet(title, group.groupId, it, group, context)
//                        gl.rename(it, group.groupId)
                    }

                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_MORE_RENAME.takeIf {
                            !isProjectList
                        } ?: JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_MORE_RENAME
                    )
                }

                is ProjectNewAfter -> {
                    newGroup(false, title, context)

                    clickEvent {
                        ClickEventParam(
                            eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_INSERTGROUPRIGHT.takeIf {
                                !isProjectList
                            } ?: JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_INSERTGROUPRIGHT
                        )
                    }
                }

                is ProjectGroupSort -> {
                    val groups = listener.currentGroups()
                    val gs = groups?.filter { g ->
                        g.title is JoyWork2ExtraTitle
                    } ?: emptyList()
                    if (gs.isLegalList()) {
                        showGroupSortDialog(
                            context,
                            listener.getProjectId(),
                            gs.toMutableList()
                        ) { newList ->
                            listener.onSortedGroups(newList)
                        }
                    }
                    clickEvent {
                        ClickEventParam(
                            eventId = JoyWorkConstant.MOBILE_EVENT_TASK_HOME_GROUP_MORE_RANK_GROUP
                        )
                    }
                }

                is ProjectNewBefore -> {
                    newGroup(true, title, context)

                    clickEvent {
                        ClickEventParam(
                            eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_INSERTGROUPLEFT.takeIf {
                                !isProjectList
                            } ?: JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_INSERTGROUPLEFT
                        )
                    }
                }

                is ProjectDelete -> {
                    showGroupDelAlertDialog(context) {
                        delNet(group.projectId, group.groupId, it)
                    }

                    clickEvent {
                        ClickEventParam(
                            eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_MORE_DELETE.takeIf {
                                !isProjectList
                            } ?: JoyWorkConstant.MOBILE_EVENT_TASK_LIST_GROUP_MORE_DELETE
                        )
                    }
                }
            }
        }
    }

    private fun delNet(projectId: String, groupId: String, isHard: Boolean) {
        ProjectRepo.delGroup(projectId, groupId, isHard) { it, msg ->
            if (it) {
                listener.delGroup(groupId, isHard)
            } else {
                ToastUtils.showInfoToast(msg ?: "")
            }
        }
    }

    private fun renameNet(
        title: JoyWork2ExtraTitle,
        id: String,
        newName: String,
        group: JoyWorkCustomGrouper.Group,
        context: Context
    ) {
        ProjectRepo.updateGroup(id, newName) updateGroup@{ success: Boolean, msg: String? ->
            if (success) {
                title.title = newName
                group.title = newName
                group.type = ProjectGroupTypeEnum.COMMON.code
                listener.afterRenameSuccess(newName, id, title)
            } else {
                Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                    .show()
            }
        }
    }

    private fun newGroup(isBefore: Boolean, title: JoyWork2ExtraTitle, context: Context) {
        showNewGroupDialog(context) { name: String ->
            createGroupNet(name, isBefore, title, context)
        }
    }

    protected fun createGroupNet(
        name: String,
        isBefore: Boolean,
        title: JoyWork2ExtraTitle?,
        context: Context,
    ) {
        val groupId = (title?.extraObj as? JoyWorkCustomGrouper.Group)?.groupId
        ProjectRepo.createGroup(
            listener.getProjectId(),
            name,
            if (isBefore) {
                null
            } else groupId,
            if (isBefore) groupId else null
        ) { success: Boolean, groupNet: Group?, msg: String? ->
            if (success && groupNet != null) {
                val resultGroup = JoyWorkCustomGrouper.Group()
                resultGroup.fromGroup(groupNet)
                val group = title?.extraObj as? JoyWorkCustomGrouper.Group
                resultGroup.permissions = group?.permissions
                val titleNew = JoyWork2ExtraTitle(resultGroup.title, resultGroup)
                val items = ArrayList<JoyWork>()
                items.add(JoyWork2MovePlaceholderItem(title))
                items.addAll(resultGroup.safeTasks)
                items.add(CardBottomMultiPurpose(resultGroup.groupId).apply {
                    showNewTask = true
                    showMore = false
                })
                val expandableGroup =
                    ExpandableGroup(titleNew, items, resultGroup.groupId, resultGroup.isInit)
                listener.afterCreateSuccess(isBefore, title, expandableGroup)
            } else {
                Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                    .show()
            }
        }
    }

    private fun getAllGroupAction(
        todoShowType: TodoShowType,
        group: JoyWorkCustomGrouper.Group,
        context: Context
    ): Pair<List<JoyWorkAction>, JoyWorkAction?> {
        val r = ArrayList<JoyWorkAction>()
        // 默认分组不能前插
        if (!group.isDefaultGroup) {
            // 默认分组不能改名
            r.add(ProjectRename(context))
        }
        r.add(ProjectNewBefore(context))
        r.add(ProjectNewAfter(context))
        r.add(ProjectGroupSort(context))
        return Pair(r, if (!group.isDefaultGroup) ProjectDelete(context) else null)
    }

    override fun createJoyWork(
        activity: Activity,
        listType: TaskListTypeEnum,
        groupId: String?,
        groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        viewId: String?,
        viewType: String?,
        success: (String?) -> Unit
    ) = CustomGroupCreatorNew.createJoyWorkNew(
        activity,
        listType,
        listener.getProjectId(),
        groupId,
        groups,
        viewId,
        viewType,
        success
    )

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val customGrouper = any as? JoyWorkCustomGrouper
        draggable =
            customGrouper?.safePermissions?.contains(ProjectPermissionEnum.GROUP.code) ?: true
        return CustomGroupDataGrouper.group(
            any,
            context,
            isProjectList,
            null,
            isRefresh,
            loadOffsetChecker
        )
    }
}

abstract class ExecutorGrouper : JoyWorkGrouper() {
    override fun getRepo() = TileListRepo

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return ExecutorDataGrouper.group(any, context)
    }

    override fun needLoadMore(): Boolean {
        return true
    }
}


abstract class ProjectGrouper : JoyWorkGrouper() {
    override fun getRepo() = TileListRepo

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return ProjectDataGrouper.group(any, context)
    }

    override fun needLoadMore(): Boolean {
        return true
    }
}

abstract class SourceGrouper : JoyWorkGrouper() {
    override fun getRepo() = TileListRepo

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return SourceDataGrouper.group(any, context)
    }

    override fun needLoadMore(): Boolean {
        return true
    }
}


abstract class ExtStatusGrouper(
    private val isProjectList: Boolean,
    private val customFieldGroup: CustomFieldGroup?
) : JoyWorkGrouper() {
    override fun getRepo() = TileListRepo

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return ExtStatusDataGrouper.group(
            any,
            context,
            isProjectList,
            customFieldGroup,
            isRefresh,
            loadOffsetChecker
        )
    }

    override fun needLoadMore(): Boolean {
        return true
    }
}


abstract class BizCodeGrouper : JoyWorkGrouper() {
    override fun getRepo() = TileListRepo

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return BizCodeDataGrouper.group(any, context)
    }

    override fun needLoadMore(): Boolean {
        return true
    }
}


abstract class SubSourceGrouper : JoyWorkGrouper() {
    override fun getRepo() = TileListRepo

    override fun group(
        any: Any,
        context: Context,
        isRefresh: Boolean,
        loadOffsetChecker: ((gId: String) -> Int)?
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return SubSourceDataGrouper.group(any, context)
    }

    override fun needLoadMore(): Boolean {
        return true
    }
}