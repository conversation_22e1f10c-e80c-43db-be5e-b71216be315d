package com.jd.oa.joywork2.main.bean

import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork2.main.ConditionState
import java.util.Objects

infix fun ConditionSorter?.was(sorter: ConditionSorter.Field) =
    Objects.equals(this?.field, sorter.value)

infix fun ConditionGrouper?.was(grouper: ConditionGrouper.Field) =
    Objects.equals(this?.field, grouper.value)

infix fun ConditionGrouper?.not(grouper: ConditionGrouper.Field) =
    !Objects.equals(this?.field, grouper.value)

/**
 * 是否适配card视图
 */
fun ConditionGrouper?.compatibleWithCardViewType() =
    Objects.equals(this?.field, ConditionGrouper.Field.START_TIME.value) ||
    Objects.equals(this?.field, ConditionGrouper.Field.END_TIME.value) ||
    Objects.equals(this?.field, ConditionGrouper.Field.CUSTOM.value)

fun ConditionGrouper?.compatibleWithSystemView() =
    Objects.equals(this?.field, ConditionGrouper.Field.SOURCE.value) ||
            Objects.equals(this?.field, ConditionGrouper.Field.SUB_SOURCE.value)

fun ConditionGrouper?.finishStatusGroupList(condition: ConditionState.Condition): Boolean {
    return if (condition.status == TaskStatusEnum.FINISH) {
        Objects.equals(this?.field, ConditionGrouper.Field.FINISH_TIME.value) ||
                Objects.equals(this?.field, ConditionGrouper.Field.CUSTOM.value)
    } else true
}