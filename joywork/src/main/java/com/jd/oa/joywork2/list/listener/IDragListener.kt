package com.jd.oa.joywork2.list.listener

import androidx.recyclerview.widget.RecyclerView.ViewHolder

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/29 00:05
 */
interface IDragListener {
    fun onStopDrag(viewHolder: ViewHolder)
    fun onStartDrag(viewHolder: ViewHolder)
    fun onMove(fromPosition: Int, toPosition: Int)
}

open class EmptyDragListener : IDragListener {
    override fun onStopDrag(viewHolder: ViewHolder) {
    }

    override fun onStartDrag(viewHolder: ViewHolder) {
    }

    override fun onMove(fromPosition: Int, toPosition: Int) {
    }

}