package com.jd.oa.joywork2.list

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.self.base.SelfListItemVH
import com.jd.oa.joywork.team.kpi.MovePlaceHolder
import com.jd.oa.joywork.view.realAdapter

interface JoyWorkListDrag2AdapterListener {
    // return: 按住可拖起的 vh
    fun getMovableVHClass(): Class<out RecyclerView.ViewHolder>

    fun swap(
        fromVH: RecyclerView.ViewHolder,
        toVH: RecyclerView.ViewHolder,
        down: Boolean
    )

    fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean?

    fun onBeginDrag(viewHolder: RecyclerView.ViewHolder)
    fun onEndDrag(viewHolder: RecyclerView.ViewHolder)
}

class JoyWorkListDrag2(private val rv: RecyclerView, private val draggable: () -> Boolean) :
    ItemTouchHelper.Callback() {

    private fun getListener(): JoyWorkListDrag2AdapterListener? {
        return rv.realAdapter() as? JoyWorkListDrag2AdapterListener
    }

    private fun adapter(): RecyclerView.Adapter<*>? {
        return rv.realAdapter()
    }

    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val a = adapter()
        val listener = getListener()
        if (a == null || (viewHolder::class.java != listener?.getMovableVHClass())) {
            return makeMovementFlags(0, 0)
        }
        if (!draggable()) {
            return makeMovementFlags(0, 0)
        }
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        return makeMovementFlags(dragFlags, 0)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val a = getListener() ?: return false
        a.swap(
            viewHolder,
            target,
            viewHolder.adapterPosition < target.adapterPosition,
        )
        return true
    }

    // 返回值表示 current 与 target 是否可交换
    override fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val a = adapter() ?: return false
        val listener = getListener() ?: return false
        if (target::class.java == listener.getMovableVHClass() || target is MovePlaceHolder) {
            return true
        }
        if (target.adapterPosition == 0) {// 最上面不能交换。如果最上面是同类型的，也可以交换，由上一个 if 保证
            return false
        }

        // 最后一个，可以交换
        if (target.adapterPosition >= a.itemCount - 1) {
            return false
        }

        val ans = listener.canDropOver(recyclerView, current, target)
        if (ans != null) {
            return ans
        }
        return if (current.adapterPosition > target.adapterPosition) {
            val pre = a.getItemViewType(target.adapterPosition - 1)
            pre == a.getItemViewType(current.adapterPosition)
        } else {
            val next = a.getItemViewType(target.adapterPosition + 1)
            next == a.getItemViewType(current.adapterPosition)
        }
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        // 只有 item 可拖动
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && viewHolder != null && viewHolder::class.java == getListener()?.getMovableVHClass()) {
            getListener()?.onBeginDrag(viewHolder)
            return
        }
        super.onSelectedChanged(viewHolder, actionState)
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        getListener()?.onEndDrag(viewHolder)
        super.clearView(recyclerView, viewHolder)
    }
}