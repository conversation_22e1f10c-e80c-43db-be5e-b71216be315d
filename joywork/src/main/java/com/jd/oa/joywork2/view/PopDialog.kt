package com.jd.oa.joywork2.view

import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ListAdapter
import android.widget.ListView
import android.widget.PopupWindow
import com.jd.oa.joywork.R

class PopDialog(
    private val context: Context
) : PopupWindow(
    LayoutInflater.from(context).inflate(R.layout.joywork2_pop_dialog, null),
    ViewGroup.LayoutParams.MATCH_PARENT,
    ViewGroup.LayoutParams.WRAP_CONTENT
) {

    init {
        contentView.findViewById<View>(R.id.space).setOnClickListener {
            dismiss()
        }
        isFocusable = true
        isOutsideTouchable = true
    }

    fun setAdapter(adapter: ListAdapter) {
        val list = contentView.findViewById<ListView>(R.id.mListView)
        list.adapter = adapter
    }

    fun onConfigurationChanged(newConfig: Configuration) {
        val params = contentView.layoutParams
        params.width = getScreenWidth()
        contentView.layoutParams = params
        update(params.width, params.height)
    }


    private fun getScreenWidth(): Int {
        return getDisplayMetrics().widthPixels
    }

    private fun getDisplayMetrics(): DisplayMetrics {
        val dm = DisplayMetrics()
        val windowManager =
            context.getSystemService(Application.WINDOW_SERVICE) as WindowManager
        windowManager.defaultDisplay.getMetrics(dm)
        return dm
    }
}