package com.jd.oa.joywork2.list

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager2.widget.ViewPager2
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.ext.getParentViewModel
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkDialog.showAlertDialog
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.TransferResult
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.ui.TaskDetailActivity
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.repo.JoyWorkLocationParam
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.dialog.DialogSupporter
import com.jd.oa.joywork.team.showRejectTaskEditDialog
import com.jd.oa.joywork.view.JoyWorkLoadMoreFooter
import com.jd.oa.joywork.view.realAdapter
import com.jd.oa.joywork.view.red
import com.jd.oa.joywork2.list.WorkListBaseViewModel.Companion.LOAD_MORE_COMPLETE
import com.jd.oa.joywork2.list.WorkListBaseViewModel.DataState
import com.jd.oa.joywork2.list.listener.DeadLineClickListener
import com.jd.oa.joywork2.list.listener.EmptyDragListener
import com.jd.oa.joywork2.list.listener.ExecutorClickListener
import com.jd.oa.joywork2.list.listener.MoreButtonClickListener
import com.jd.oa.joywork2.list.listener.StartTimeClickListener
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.ui.recycler.SpaceItemDecoration
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.safeLaunch
import com.jd.oa.utils.vertical
import com.jd.oa.utils.visible
import kotlinx.coroutines.delay

abstract class WorkListBaseFragment<KeyType, T : WorkListBaseViewModel> : BaseFragment(),
    StartTimeClickListener.OnStartTimeChangedListener,
    DeadLineClickListener.OnDeadLineChangedListener,
    ExecutorClickListener.OnExecutorChangedListener,
    MoreButtonClickListener.MoreButtonCeilProcessorListener,
    IWorkListHost {

    abstract val menuItemFactory: (() -> MenuItem)?
    abstract val conditionFactory: () -> ConditionState.Condition
    abstract val viewModel: T
    private val containerInheritedVM by lazy {
        getParentViewModel<ListContainerInheritedVM<KeyType>>()
    }

    protected var projectId = ""
    protected var regionType = ""
    protected var isProject = false
    protected var keyData: KeyType? = null
    protected var groups: List<Group>? = null
    private val isArchiveProject: Boolean by lazy {
        arguments?.getBoolean("isArchiveProject") ?: false
    }

    //优先初始化
    private var rootContainer: ViewGroup? = null

    private val mRefresh by lazy {
        rootContainer!!.findViewById<SwipeRefreshLayout>(R.id.refresh)
    }

    private val recyclerView by lazy {
        rootContainer!!.findViewById<RecyclerView>(R.id.recyclerView)
    }

    private val emptyView by lazy {
        rootContainer!!.findViewById<View>(R.id.empty_layout)
    }

    private val failView by lazy {
        rootContainer!!.findViewById<View>(R.id.fail_layout)
    }

    protected val adapter: WorkListAdapter by lazy {
        WorkListAdapter(
            this@WorkListBaseFragment,
            projectId,
            regionType,
            this,
            this,
            this,
            this
        )
    }

    private var mPullUpLoadHelper: PullUpLoadHelper? = null
    protected val conditionViewModel: ConditionViewModel by activityViewModels()


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        rootContainer = inflater.inflate(
            R.layout.jdme_joywork2_list,
            container,
            false
        ) as ViewGroup?
        mRefresh.red(requireActivity())
        recyclerView.vertical()
        //添加分割线
        recyclerView.addItemDecoration(SpaceItemDecoration(DisplayUtils.dip2px(12f)))
        recyclerView.adapter = adapter
        val helper = ItemTouchHelper(WorkListTouchCallback(object : EmptyDragListener() {

            override fun onStartDrag(viewHolder: RecyclerView.ViewHolder) {
                super.onStartDrag(viewHolder)
                mRefresh.isEnabled = false
                enableViewPager(false)
                adapter.onStartDrag(viewHolder)
            }

            override fun onStopDrag(viewHolder: RecyclerView.ViewHolder) {
                super.onStopDrag(viewHolder)
                mRefresh.isEnabled = true
                enableViewPager(true)
                adapter.onStopDrag(viewHolder)
            }

            override fun onMove(fromPosition: Int, toPosition: Int) {
                super.onMove(fromPosition, toPosition)
                adapter.onMove(fromPosition, toPosition)
            }

            private fun enableViewPager(enable: Boolean) {
                activity?.run {
                    findViewById<ViewPager2>(R.id.viewPager)?.run {
                        isUserInputEnabled = enable
                    }
                }
            }
        }) {
            containerInheritedVM.movable
        })
        helper.attachToRecyclerView(recyclerView)
        return rootContainer
    }

    private fun setupLoadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(recyclerView) {
            loadData(false)
        }
        mPullUpLoadHelper?.setLoadFooter(JoyWorkLoadMoreFooter(requireContext()))
    }

    private fun loadMoreOnceFinish(finish: Boolean) {
        val helper = mPullUpLoadHelper ?: return
        if (finish) {
            helper.setComplete()
        } else {
            helper.setLoaded()
        }
    }

    override fun isCustomGroup(): Boolean {
        val conditionState = conditionViewModel.mConditionValueLiveData.value
        return conditionState is ConditionState.Condition && groups.isLegalList()
                && conditionState.grouper.fieldEnum == ConditionGrouper.Field.CUSTOM
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mRefresh.setOnRefreshListener {
            clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_FRESH)
            loadData(true)
        }
        setupLoadMore()
        initObservers()
    }

    open fun loadData(refresh: Boolean, keepSize: Boolean = false) {
        menuItemFactory?.run {
            if (refresh) {
                val limitSize = if (keepSize) {
                    val offset = getOffset()
                    if (offset < ProjectRepo.PAGE_LIMIT) ProjectRepo.PAGE_LIMIT else offset
                } else {
                    ProjectRepo.PAGE_LIMIT
                }
                viewModel.loadData(
                    conditionFactory.invoke(),
                    this.invoke(),
                    regionType,
                    limitSize,
                    keepSize
                )
            } else {
                viewModel.loadMore(
                    conditionFactory.invoke(),
                    this.invoke(),
                    getOffset(),
                    regionType
                )
            }
        }
    }

    fun loadDataDelay(refresh: Boolean, keepSize: Boolean = false) {
        viewLifecycleOwner.lifecycleScope.safeLaunch {
            delay(1000L)
            loadData(refresh, keepSize)
        }
    }

    fun getOffset(): Int = (recyclerView.realAdapter as? WorkListAdapter)?.itemCount ?: 0

    private fun initObservers() {
        viewModel.refreshingEffect.observe(viewLifecycleOwner) {
            mRefresh.isRefreshing = it
        }
        viewModel.data.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            when (it) {
                is DataState.RefreshResult -> {
                    recyclerView.gone()
                    emptyView.gone()
                    failView.gone()
                    if (it.success) {
                        if (!it.data.isLegalList()) {
                            emptyView.visible()
                            loadMoreOnceFinish(true)
                        } else {
                            recyclerView.visible()
                            adapter.refreshList(it.data ?: emptyList())
                            loadMoreOnceFinish((it.data?.size ?: 0) < ProjectRepo.PAGE_LIMIT)
                        }
                    } else {
                        if (adapter.isEmpty()) {
                            failView.visible()
                            if (it.msg.isLegalString()) {
                                val tvError = failView.findViewById<TextView>(R.id.tv_error)
                                tvError?.run {
                                    tvError.text = it.msg
                                }
                            }
                        } else {
                            recyclerView.visible()
                        }
                    }
                }

                is DataState.LoadMoreResult -> {
                    if (it.data.isLegalList()) {
                        adapter.append(it.data ?: emptyList())
                    }
                    loadMoreOnceFinish(it.result == LOAD_MORE_COMPLETE)
                }
            }
        }

        containerInheritedVM.createWorkLD.observe(viewLifecycleOwner) {
            runCatching {
                if (it == null) return@observe
                if (!keyDateEquals(it.first)) return@observe
                emptyView.gone()
                failView.gone()
                recyclerView.visible()
//                adapter.append(mutableListOf(it.second))
//                recyclerView.scrollToPosition(adapter.itemCount - 1)
                adapter.addNew(it.second)
                recyclerView.scrollToPosition(0)
            }
        }
    }

    abstract fun keyDateEquals(other: KeyType): Boolean

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //详情返回
        if (requestCode == 10) {
            val returnParcel =
                data?.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as? DetailReturnParcel
            if (returnParcel?.update == true) {
//                loadData(true)
                containerInheritedVM.detailReturn.postValue(true)
            }
        }
    }

    override fun getHostFragment(): Fragment? = this


    override fun getHostActivity(): Activity? = activity

    override fun onStateChanged(position: Int, work: JoyWork) {
        if (adapter.isEmpty()) {
            emptyView.visible()
            recyclerView.gone()
        }
    }

    override fun onDeadLineChanged(position: Int, work: JoyWork) {
        containerInheritedVM.detailReturn.postValue(true)
    }

    override fun onStartTimeChanged(position: Int, work: JoyWork) {
        containerInheritedVM.detailReturn.postValue(true)
    }

    override fun onExecutorLineChanged(position: Int, work: JoyWork) {
        containerInheritedVM.detailReturn.postValue(true)
    }

    override fun selectGroup(taskId: String, selectGroupId: String?) {
        DialogSupporter(null, requireContext()).listGroup(
            groups as ArrayList<Group>,
            selectGroupId ?: regionType
        ) { group: Group? ->
            if (group == null) return@listGroup
            JoyWorkRepo.sortProjectJoyWork(taskId, group.groupId, projectId, JoyWorkLocationParam(),
                object : JoyWorkUpdateCallback {
                    override fun result(success: Boolean, errorMsg: String) {
                        if (success) {
                            loadDataDelay(true)
                            ToastUtils.showToast(R.string.joywork_move_success)
                        } else {
                            ToastUtils.showToast(R.string.joywork_move_failure)
                        }
                    }

                    override fun onStart() {
                    }
                })
        }
    }

    override fun transferTask(work: JoyWork) {
        val ls = ArrayList<MemberEntityJd>()
        work.owners.filter { it.taskStatus == TaskStatusEnum.UN_FINISH.code }.forEach {
            val jd = MemberEntityJd()
            jd.mApp = it.app
            jd.mId = it.emplAccount
            ls.add(jd)
        }
        ExecutorUtils.selectTransfer(requireActivity(), ls, {
            val member = Members()
            it?.first()?.let { m ->
                member.fromDD(m)
            }
            JoyWorkRepo.transfer(
                work.taskId,
                member
            ) { success: Boolean, result: TransferResult?, _: String? ->
                if (success && result != null) {
                    loadDataDelay(true)
                    ToastUtils.showToast(R.string.joywork_transfer_success)
                } else {
                    ToastUtils.showToast(R.string.joywork_transfer_failure)
                }
            }
        }, {
            // failure
        })
    }

    override fun deleteTask(work: JoyWork, position: Int) {
        showAlertDialog(
            requireContext(),
            resources.getString(R.string.joywork_delete_button_dialog_title),
            resources.getString(R.string.joywork_delete_button_dialog_sub_message),
            R.string.joywork_delete_button_dialog_confirm,
            R.string.cancel,
            {
                TaskDetailWebservice.deleteTask(work.taskId, null,
                    object : TaskDetailWebservice.TaskCallback() {
                        override fun onSuccess(info: ResponseInfo<String?>?) {
                            super.onSuccess(info)
                            adapter.remove(work, position)
                            ToastUtils.showToast(R.string.around_delete_success)
                        }

                        override fun onFailure(exception: HttpException?, info: String?) {
                            super.onFailure(exception, info)
                            ToastUtils.showToast(R.string.joywork_delete_failure)
                        }
                    })
            },
            { _, it -> it.dismiss() }
        )
    }

    override fun rejectTask(work: JoyWork, position: Int) {
        showRejectTaskEditDialog(requireContext()) {
            JoyWorkRepo.reject(work.taskId, it) { msg ->
                msg?.let {
                    adapter.remove(work, position)
                    ToastUtils.showToast(R.string.joywork_reject_success)
                } ?: ToastUtils.showToast(R.string.joywork_reject_failure)
            }
        }
    }

    override fun isArchived(): Boolean = isArchiveProject
}


