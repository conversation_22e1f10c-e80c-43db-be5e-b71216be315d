package com.jd.oa.joywork2.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork2.menu.MenuItem


class Tab2ViewModel : ViewModel() {

    //condition container switch show or hide
    private val _mConditionShowOrHideLiveData = MutableLiveData<Boolean>()
    val mConditionShowOrHideLiveData: LiveData<Boolean> = _mConditionShowOrHideLiveData
    fun showOrHideConditionIcon(show: Boolean) {
        _mConditionShowOrHideLiveData.value = show
    }

    // 定义 toggleMenu 的 livedata
    private val _mToggleMenuLiveData = MutableLiveData<Boolean>()
    val mToggleMenuLiveData: LiveData<Boolean> = _mToggleMenuLiveData

    fun openMenu() {
        _mToggleMenuLiveData.value = true
    }

    fun closeMenu() {
        _mToggleMenuLiveData.value = false
    }
}
