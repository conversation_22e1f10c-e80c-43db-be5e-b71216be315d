package com.jd.oa.joywork2.list.kanban.group

import android.content.Context
import android.widget.Toast
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.ProjectDelete
import com.jd.oa.joywork.ProjectGroupSort
import com.jd.oa.joywork.ProjectNewAfter
import com.jd.oa.joywork.ProjectNewBefore
import com.jd.oa.joywork.ProjectRename
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectGroupTypeEnum
import com.jd.oa.joywork.team.showGroupAction
import com.jd.oa.joywork.team.showGroupDelAlertDialog
import com.jd.oa.joywork.team.showGroupNameEditDialog
import com.jd.oa.joywork.team.showNewGroupDialog
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/28 15:25
 */

interface IGroupActionListener {
    fun onSortedGroups(groups: List<Group>)

    fun delGroup(group: Group, isHard: Boolean)

    fun afterRenameSuccess(group: Group, newName: String)

    fun afterCreateSuccess(isBefore: Boolean, anchor: Group, newGroup: Group)
}

open class EmptyGroupListener : IGroupActionListener {
    override fun onSortedGroups(groups: List<Group>) {
    }

    override fun delGroup(group: Group, isHard: Boolean) {
    }

    override fun afterRenameSuccess(group: Group, newName: String) {
    }

    override fun afterCreateSuccess(isBefore: Boolean, anchor: Group, newGroup: Group) {
    }
}

fun showGroupAction(
    context: Context,
    projectId: String,
    group: Group,
    groups: List<Group>,
    onActionSelected: (JoyWorkAction) -> Unit,
    groupActionListener: IGroupActionListener = EmptyGroupListener()
) {
    val pair = getAllGroupAction(context, group)
    showGroupAction(context, pair.first, pair.second) {

        when (it) {
            is ProjectRename -> {
                showGroupNameEditDialog(context, group.title) { newName ->
                    renameNet(group, newName, context, groupActionListener)
                }
            }

            is ProjectNewAfter -> {
                newGroup(context, projectId, false, group, groupActionListener)
            }

            is ProjectGroupSort -> {
                if (groups.isLegalList()) {
                    showGroupSortDialog(
                        context,
                        projectId,
                        groups
                    ) { newList ->
                        groupActionListener.onSortedGroups(newList)
                    }
                }
            }

            is ProjectNewBefore -> {
                newGroup(context, projectId, true, group, groupActionListener)
            }

            is ProjectDelete -> {
                showGroupDelAlertDialog(context) {
                    delNet(projectId, group, it, groupActionListener)
                }
            }
        }
        onActionSelected.invoke(it)
    }
}

private fun delNet(
    projectId: String,
    group: Group,
    isHard: Boolean,
    groupActionListener: IGroupActionListener?
) {
    ProjectRepo.delGroup(projectId, group.groupId, isHard) { it, msg ->
        if (it) {
            groupActionListener?.delGroup(group, isHard)
        } else {
            ToastUtils.showInfoToast(msg ?: "")
        }
    }
}

private fun renameNet(
    group: Group,
    newName: String,
    context: Context,
    groupActionListener: IGroupActionListener?
) {
    ProjectRepo.updateGroup(group.groupId, newName) updateGroup@{ success: Boolean, msg: String? ->
        if (success) {
            group.title = newName
            group.type = ProjectGroupTypeEnum.COMMON.code
            groupActionListener?.afterRenameSuccess(group, newName)
        } else {
            Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                .show()
        }
    }
}

fun newGroup(
    context: Context,
    projectId: String,
    isBefore: Boolean,
    anchor: Group,
    groupActionListener: IGroupActionListener?
) {
    showNewGroupDialog(context) { name: String ->
        createGroupNet(name, projectId, isBefore, anchor, context, groupActionListener)
    }
}

fun createGroupNet(
    name: String,
    projectId: String,
    isBefore: Boolean,
    anchor: Group,
    context: Context,
    groupActionListener: IGroupActionListener?
) {
    val groupId = anchor.groupId
    ProjectRepo.createGroup(
        projectId,
        name,
        if (isBefore) {
            null
        } else groupId,
        if (isBefore) groupId else null
    ) { success: Boolean, groupNet: Group?, msg: String? ->
        if (success && groupNet != null) {
            groupActionListener?.afterCreateSuccess(isBefore, anchor, groupNet)
        } else {
            Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                .show()
        }
    }
}

private fun getAllGroupAction(
    context: Context,
    group: Group,
): Pair<List<JoyWorkAction>, JoyWorkAction?> {
    val r = ArrayList<JoyWorkAction>()
    r.add(ProjectRename(context))
    r.add(ProjectNewBefore(context, false))
    r.add(ProjectNewAfter(context, false))
    r.add(ProjectGroupSort(context))
    return Pair(r, if (!group.isDefaultGroup) ProjectDelete(context) else null)
}