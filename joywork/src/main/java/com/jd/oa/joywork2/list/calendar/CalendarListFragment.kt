package com.jd.oa.joywork2.list.calendar

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.haibin.calendarview.Calendar
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork2.list.WorkListBaseFragment
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.menu.MenuItem

/**
 * 日历列表
 */
class CalendarListFragment : WorkListBaseFragment<Calendar, CalendarListVM>() {

    override val viewModel: CalendarListVM by viewModels()

    override val menuItemFactory: (() -> MenuItem)? by lazy {
        conditionViewModel.mMenuItemFactory
    }
    override val conditionFactory: () -> ConditionState.Condition
        get() = throw NotImplementedError()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        regionType = arguments?.getString("regionType") ?: ""
        projectId = arguments?.getString("projectId") ?: ""
        keyData = arguments?.getSerializable("date") as? Calendar
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        conditionViewModel.calendarTaskStateLD.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            loadData(true)
        }
    }

    override fun keyDateEquals(other: Calendar): Boolean = other == keyData

    override fun loadData(refresh: Boolean, keepSize: Boolean) {
        menuItemFactory?.run {
            viewModel.loadDataByTime(
                keyData ?: CalendarHelper().today,
                this.invoke(),
                conditionViewModel.calendarTaskStateLD.value!!,
                if (refresh) 0 else getOffset(),
                getLimitSize(refresh, keepSize),
            )
        }
    }

    private fun getLimitSize(refresh: Boolean, keepSize: Boolean): Int {
        if (!refresh) return ProjectRepo.PAGE_LIMIT
        if (!keepSize) return ProjectRepo.PAGE_LIMIT
        val offset = getOffset()
        return if (offset < ProjectRepo.PAGE_LIMIT) ProjectRepo.PAGE_LIMIT else offset
    }
}


