package com.jd.oa.joywork2.view

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout

class AutoSelectLinearLayout(context: Context, attributeSet: AttributeSet) :
    LinearLayout(context, attributeSet) {

    override fun setSelected(selected: <PERSON><PERSON>an) {
        super.setSelected(selected)
        // 遍历子 view，将所有子 view 的 isSelected 属性设置为 selected
        for (i in 0 until childCount) {
            getChildAt(i).isSelected = selected
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        for (i in 0 until childCount) {
            getChildAt(i).isEnabled = enabled
        }
    }

}