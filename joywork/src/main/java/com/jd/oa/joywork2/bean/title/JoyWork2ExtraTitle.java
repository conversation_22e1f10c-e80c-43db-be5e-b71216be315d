package com.jd.oa.joywork2.bean.title;

import androidx.annotation.NonNull;

import com.jd.oa.joywork.view.StickyHeader;
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper;

public class JoyWork2ExtraTitle extends JoyWork2LazyTitle implements StickyHeader {
    // 构造函数
    public String title;
    public Object extraObj;
    public boolean hide;

    public JoyWork2ExtraTitle(String title, Object extra) {
        this.title = title;
        this.extraObj = extra;
    }

    @NonNull
    @Override
    public String toString() {
        return "title = " + title + ", extra = "
                + extraObj;
    }

    public JoyWorkCustomGrouper.Group getExtraGroup() {
        if (extraObj instanceof JoyWorkCustomGrouper.Group) {
            return (JoyWorkCustomGrouper.Group) extraObj;
        } else {
            return null;
        }
    }

    @Override
    public boolean isInit() {
        return getExtraGroup() == null || getExtraGroup().isInit();
    }

    @Override
    public void setInit(boolean init) {
        if(getExtraGroup() != null){
            getExtraGroup().initialize = init;
        }
    }
}
