package com.jd.oa.joywork2.list.grouper

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWorkLoadMore2
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.kpi.PlaceHolderVH
import com.jd.oa.joywork.team.kpi.PlaceHolderVH2
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.joywork2.bean.title.JoyWork2OnlyTextTitle
import com.jd.oa.joywork2.bean.title.JoyWork2UserExtraTitle
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.list.TableItemViewHolder


class TableFinishTimeGrouper(private val titleCallback: TitleCallback) : FinishTimeGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.MY_FINISH
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.MY_FINISH.ordinal) {
            TableIconTextExpandableVH<JoyWorkTitle>(parent.context, parent) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is TableIconTextExpandableVH<*>) {
            val title = (data as? JoyWorkTitle) ?: return
            (holder as? TableIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                title
            )
        }
    }
}

class TableEndTimeGrouper(
    private val loadMoreCallback: LoadMoreCallback<String>,
    private val titleCallback: TitleCallback,
) : EndTimeGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWork2OnlyTextTitle -> {
                GrouperTitleType.END_TIME
            }

            is CardBottomMultiPurpose<*> -> {
                GrouperTitleType.GROUP_BOTTOM
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.END_TIME.ordinal) {
            TableIconTextExpandableVH<JoyWork2OnlyTextTitle>(parent.context, parent) { title ->
                titleCallback.click(title)
            }
        } else {
            LoadMoreVH2(parent.context, parent)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is TableIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            (holder as? TableIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                title
            )
        } else if (holder is LoadMoreVH2) {
            val work = data as? CardBottomMultiPurpose<String> ?: return
            if (work.showMore) {
                holder.itemView.layoutParams = RecyclerView.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                holder.bind(
                    work,
                    adapter = adapter
                ) { payload: String, completeRunnable ->
                    loadMoreCallback.loadMore(payload, completeRunnable)
                }
            } else {
                holder.itemView.setLayoutParams(RecyclerView.LayoutParams(0, 0))
            }

        }
    }
}

class TableCustomGrouper(
    groupListener: CustomGroupListener,
    private val loadMoreCallback: LoadMoreCallback<String>,
    private val titleCallback: TitleCallback,
    listener: CustomGrouperListener,
    isProjectList: Boolean,
) : CustomGrouper(groupListener, listener, isProjectList) {

    override fun isGroupTitle(viewHolder: RecyclerView.ViewHolder): Boolean {
        return viewHolder::class.java == TextWithActionExpandableVH::class.java
    }

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWork2ExtraTitle -> {
                if (data.hide)
                    GrouperTitleType.EMPTY_TITLE
                else
                    GrouperTitleType.CUSTOM_GROUP
            }

            is JoyWorkLoadMore2 -> {
                GrouperTitleType.LOAD_MORE
            }

            is CardBottomMultiPurpose<*> -> {
                GrouperTitleType.GROUP_BOTTOM
            }

            is ProjectAddTitle -> {
                GrouperTitleType.ADD_GROUP
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            GrouperTitleType.CUSTOM_GROUP.ordinal -> {
                TextWithActionExpandableVH(parent.context, parent, { title: JoyWork2ExtraTitle ->
                    titleCallback.click(title)
                }, { title: JoyWork2ExtraTitle ->
                    showAction(
                        isProjectList,
                        TodoShowType.TABLE,
                        title,
                        title.extraObj as JoyWorkCustomGrouper.Group,
                        parent.context
                    )
                })
            }

            GrouperTitleType.ADD_GROUP.ordinal -> {
                SelfListNewGroupVH2(parent.context, parent) { name, joyWorkTitle: ProjectAddTitle ->
                    val group = joyWorkTitle.expandableGroup
                    val preGroup = listener.preGroup(group.id)
                    createGroupNet(
                        name,
                        false,
                        preGroup?.title as? JoyWork2ExtraTitle,
//                        ((preGroup?.title as? JoyWork2ExtraTitle))?.extra as? JoyWorkCustomGrouper.Group,
                        parent.context
                    )
                }
            }

            GrouperTitleType.EMPTY_TITLE.ordinal -> {
                //height为0会导致下拉刷新不可用
                PlaceHolderVH2(parent.context, 1)
            }

            GrouperTitleType.GROUP_BOTTOM.ordinal -> {
                LoadMoreVH2(parent.context, parent)
            }

            else -> {
                LoadMoreVH2(parent.context, parent)
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {

        when (holder) {
            is TextWithActionExpandableVH<*> -> {
                val actionShow: JoyWork2ExtraTitle.() -> Boolean = {
                    val group = extraObj as? JoyWorkCustomGrouper.Group
                    if (isProjectList && group != null) {
                        (group.permissions
                            ?: emptyList()).contains(ProjectPermissionEnum.GROUP.code)
                    } else {
                        true
                    }
                }
                val title = (data as? JoyWork2ExtraTitle) ?: return
                (holder as? TextWithActionExpandableVH<JoyWork2ExtraTitle>)?.bind(
                    title.title ?: "",
                    data.expandableGroup!!.expand,
                    position == 0,
                    actionShow(title),
                    title
                )
            }

            is LoadMoreVH2 -> {
                val work = data as? CardBottomMultiPurpose<String> ?: return
                if (work.showMore) {
                    holder.itemView.layoutParams = RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    holder.bind(
                        work,
                        adapter = adapter
                    ) { payload: String, completeRunnable ->
                        loadMoreCallback.loadMore(payload, completeRunnable)
                    }
                } else {
                    holder.itemView.setLayoutParams(RecyclerView.LayoutParams(0, 0))
                }

            }

            is SelfListNewGroupVH2 -> {
                holder.bind(data as ProjectAddTitle)
            }
        }
    }

    override fun getMovableVHClass(): Class<out RecyclerView.ViewHolder> =
        TableItemViewHolder::class.java
}

class TableExecutorGrouper : ExecutorGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWork2UserExtraTitle -> {
                GrouperTitleType.EXECUTOR
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.EXECUTOR.ordinal) {
            UserExpandableVH<JoyWork2UserExtraTitle>(parent.context, parent) { title ->
                //  titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is UserExpandableVH<*>) {
            val title = (data as? JoyWork2UserExtraTitle) ?: return
            (holder as? UserExpandableVH<JoyWork2UserExtraTitle>)?.bind(
                title.title ?: "",
                title.user?.headImg,
                position == 0,
                title
            )
        }
    }
}


class TableProjectGrouper : ProjectGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.PROJECT
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.PROJECT.ordinal) {
            OnlyTextExpandableVH<JoyWorkTitle>(parent.context, parent) { title ->
                //  titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is OnlyTextExpandableVH<*>) {
            val title = (data as? JoyWork2ExtraTitle) ?: return
            (holder as? OnlyTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                position == 0,
                title
            )
        }
    }

}

class TableSourceGrouper : SourceGrouper() {
    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.SOURCE
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.SOURCE.ordinal) {
            OnlyTextExpandableVH<JoyWorkTitle>(parent.context, parent) { title ->
                //  titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is OnlyTextExpandableVH<*>) {
            val title = (data as? JoyWork2ExtraTitle) ?: return
            (holder as? OnlyTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                position == 0,
                title
            )
        }
    }
}


class TableExtStatusGrouper(
    isProjectList: Boolean,
    customFieldGroup: CustomFieldGroup?
) : ExtStatusGrouper(isProjectList, customFieldGroup) {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.EXT_STATUS
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.EXT_STATUS.ordinal) {
            OnlyTextExpandableVH<JoyWork2OnlyTextTitle>(parent.context, parent) { title ->
                //  titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is OnlyTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            (holder as? OnlyTextExpandableVH<JoyWork2OnlyTextTitle>)?.bind(
                title.title ?: "",
                position == 0,
                title
            )
        }
    }
}


class TableBizCodeGrouper : BizCodeGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.BIZ_CODE
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.BIZ_CODE.ordinal) {
            OnlyTextExpandableVH<JoyWork2OnlyTextTitle>(parent.context, parent) { title ->
                //  titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is OnlyTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            (holder as? OnlyTextExpandableVH<JoyWork2OnlyTextTitle>)?.bind(
                title.title ?: "",
                position == 0,
                title
            )
        }
    }

}


class TableSubSourceGrouper : SubSourceGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.SUB_SOURCE
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.SUB_SOURCE.ordinal) {
            OnlyTextExpandableVH<JoyWork2OnlyTextTitle>(parent.context, parent) { title ->
                //  titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is OnlyTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            (holder as? OnlyTextExpandableVH<JoyWork2OnlyTextTitle>)?.bind(
                title.title ?: "",
                position == 0,
                title
            )
        }
    }
}