package com.jd.oa.joywork2.list.grouper

import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.view.realAdapter
import com.jd.oa.joywork2.bean.ListColumn
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.joywork2.list.JoyWork2GroperAdapter
import com.jd.oa.joywork2.list.List2ViewModel
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.main.JoyWorkViewModel
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.clickEvent

/**
 * 添加card以后逻辑趋于复杂，抽出管理类来处理
 *
 */

class GrouperInitializer(
    private val recyclerView: RecyclerView,
    private val listViewModel: List2ViewModel,
    private val isProjectList: Boolean,
    private val projectId: String,
    private val taskListTypeEnum: TaskListTypeEnum,
    private val menuItem: MenuItem?,
    private val viewId: String?,
    private val viewType: String?,
    private val afterGroupChanged: () -> Unit
) : CustomGrouperListener, CustomGroupListener {

    private val titleCallback: TitleCallback by lazy {
        TitleCallback start@{ workTitle ->
            val adapter = recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return@start
            adapter.toggleGroup(workTitle)
        }
    }

    private val loadMoreCallback: LoadMoreCallback<String> by lazy {
        LoadMoreCallback { region: String, completeRunnable ->
            val adapter =
                recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return@LoadMoreCallback
            listViewModel.loadMore(adapter.countRegion(region), region, completeRunnable)
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_KANBAN_VIEWMORE
                )
            }
        }
    }


    //table、card做隔离
    fun getGrouperByCondition(
        conditionGrouper: ConditionGrouper, todoShowType: TodoShowType
    ): JoyWorkGrouper? {

        return when (conditionGrouper.fieldEnum) {
            ConditionGrouper.Field.END_TIME, ConditionGrouper.Field.START_TIME -> {
                if (todoShowType == TodoShowType.TABLE) TableEndTimeGrouper(
                    loadMoreCallback,
                    titleCallback
                )
                else CardEndTimeGrouper(loadMoreCallback, titleCallback, menuItem)
            }

            ConditionGrouper.Field.CUSTOM -> {
                if (todoShowType == TodoShowType.TABLE) TableCustomGrouper(
                    this, loadMoreCallback, titleCallback, this, isProjectList
                ) else CardCustomGrouper(
                    this, loadMoreCallback, titleCallback, this, isProjectList, menuItem
                )
            }

            ConditionGrouper.Field.EXECUTOR -> {
                if (todoShowType == TodoShowType.TABLE) TableExecutorGrouper()
                else CardExecutorGrouper(titleCallback)
            }

            ConditionGrouper.Field.PROJECT -> {
                if (todoShowType == TodoShowType.TABLE) TableProjectGrouper()
                else CardProjectGrouper(titleCallback)
            }

            ConditionGrouper.Field.SOURCE -> {
                if (todoShowType == TodoShowType.TABLE) TableSourceGrouper()
                else CardSourceGrouper(titleCallback)
            }

            ConditionGrouper.Field.FINISH_TIME -> {
                if (todoShowType == TodoShowType.TABLE) TableFinishTimeGrouper(titleCallback)
                else CardFinishTimeGrouper(titleCallback)
            }

            ConditionGrouper.Field.EXT_STATUS -> {
                val customFieldGroup = JoyWorkViewModel.customFields.first {
                    it.columnId == ListColumn.KEY_EXT_STATUS
                }
                if (todoShowType == TodoShowType.TABLE) TableExtStatusGrouper(
                    isProjectList,
                    customFieldGroup
                )
                else CardExtStatusGrouper(isProjectList, customFieldGroup, titleCallback)
            }

            ConditionGrouper.Field.BIZ_CODE -> {
                if (todoShowType == TodoShowType.TABLE) TableBizCodeGrouper()
                else CardBizCodeGrouper(titleCallback)
            }

            ConditionGrouper.Field.SUB_SOURCE -> {
                if (todoShowType == TodoShowType.TABLE) TableSubSourceGrouper()
                else CardSubSourceGrouper(titleCallback)
            }

            else -> {
                null
            }
        }
    }

    override fun preGroup(curGroupId: String): ExpandableGroup<JoyWorkTitle, JoyWork>? {
        val adapter = recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return null
        return adapter.prevIndexOf(curGroupId)
    }

    override fun afterRenameSuccess(name: String, id: String, title: JoyWork2ExtraTitle) {
        val adapter = recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return
        adapter.updateSpecial(title)
        afterGroupChanged.invoke()
    }

    override fun delGroup(groupId: String, isHard: Boolean) {
        listViewModel.initList()
        afterGroupChanged.invoke()
    }

    override fun getProjectId(): String {
        return projectId
    }

    override fun afterCreateSuccess(
        isBefore: Boolean,
        title: JoyWork2ExtraTitle?,
        expandableGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
    ) {
        val adapter = recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return
        // 前插
        if (isBefore) {
            adapter.addBefore(title, expandableGroup)
        } else {// 后插
            adapter.addAfter(title, expandableGroup)
        }
        afterGroupChanged.invoke()
    }

    override fun currentGroups(): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val adapter = recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return ArrayList()
        return adapter.groups
    }

    override fun onSortedGroups(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        val adapter = recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return
        adapter.replaceAllGroups(groups)
        afterGroupChanged.invoke()
    }

    override fun expand(title: JoyWorkTitle) {
        val adapter = recyclerView.realAdapter as? JoyWork2GroperAdapter ?: return
        adapter.expandGroup(title)
    }

    override fun onWorkCreated(result: String?) {
        listViewModel.initListDelay()
    }

    override fun currentTaskListType(): TaskListTypeEnum = taskListTypeEnum

    override fun getViewId(): String? = viewId

    override fun getViewType(): String? = viewType
}