package com.jd.oa.joywork2.backend

import android.content.Context
import android.graphics.Color
import androidx.annotation.StringRes
import com.jd.oa.joywork.R
import com.jd.oa.joywork.RegionTypeEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork2.bean.title.JoyWork2OnlyTextTitle
import com.jd.oa.joywork2.bean.title.JoyWork2Title
import com.jd.oa.utils.string
import java.util.Objects

enum class TimestampType {
    FINISH_TIME,
    START_TIME,
    CREATE_TIME,
    DEADLINE
}


enum class JoyWork2RegionType(val value: String) {
    STARTED("STARTED"),// 已开始
    OVERDUE("OVERDUE"),// 已逾期
    TODAY("TODAY"),// 今天
    NEXT_SEVEN_DAYS("NEXT_SEVEN_DAYS"),// 未来七天
    AFTER("AFTER"),// 以后
    NOT_SCHEDULED("NOT_SCHEDULED");// 未安排

    companion object {
        fun getTextId(value: JoyWork2RegionType): Int {
            return when (value) {
                STARTED -> R.string.joywork2_started
                OVERDUE -> R.string.joywork_self_overdue
                TODAY -> R.string.joywork_today
                NEXT_SEVEN_DAYS -> R.string.joywork_next_7d
                AFTER -> R.string.joywork_self_future
                NOT_SCHEDULED -> R.string.joywork_self_no_schedule
            }
        }

        fun fromValue(value: String?): JoyWork2RegionType {
            for (regionType in values()) {
                if (Objects.equals(regionType.value, value)) {
                    return regionType
                }
            }
            throw IllegalArgumentException("No matching constant for regionType $value")
        }
    }
}

fun getRegionGroups(context: Context): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
    val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
    JoyWork2RegionType.values().forEach {
        ans.add(
            ExpandableGroup(
                JoyWork2OnlyTextTitle(
                    context.string(JoyWork2RegionType.getTextId(it))
                ),
                ArrayList<JoyWork>(),
                it.value,
                true
            )
        )
    }
    // 2022年07月20日   不显示已完成分组
    ans.removeAll {
        it.id == RegionTypeEnum.FINISH.code
    }
    return ans
}

/**
 * 任务来源
 */
enum class JoyWorkSource(val bizCode: String) {
    JOYSPACE("47b90c70859a4e5e829347536eefc923"), // 云文档
    IM("96a4458d4b19415ea5dd4d9fe9ceb973"), // 聊天
    MEETING("0kgMy77X8l4Ng8WiOoYC5KnHGoaQbH"), // 会议
    MEETING2("k4MnU8mbEh3TI6i34j42JTQbDp2mjx"),// 会议，线上 id
    PROJECT("2lEBRnGHeTQYMn5zPpJGIbjyuW9RLo"), // 项目
    TARGET("rKkJ42ZiwbNIEvMFXee7eGNaCGXTgl"), // 目标
    HUIJI("chtMi1hH5KJF4M2qagQZNMjudlt2sN"),// 慧记
    HR("jd-affairs"), // 京东人事
    CUSTOM("custom-view"),// 自定义视图
    ALL("all-to-do"), // 全部待办
}

fun String?.toJoyWorkSource(): JoyWorkSource? {
    return JoyWorkSource.values().firstOrNull {
        it.bizCode == this
    }
}

fun JoyWorkSource.iconId(): Int {
    return when (this) {
        JoyWorkSource.MEETING2 -> R.string.icon_tabbar_videocamera_de
        JoyWorkSource.JOYSPACE -> R.string.icon_general_file
        JoyWorkSource.HUIJI -> R.string.icon_tabbar_hj_de
        JoyWorkSource.IM -> R.string.icon_general_im
        JoyWorkSource.MEETING -> R.string.icon_tabbar_videocamera_de
        JoyWorkSource.PROJECT -> R.string.icon_tabbar_project_se
        JoyWorkSource.TARGET -> R.string.icon_tabbar_taget_de
        JoyWorkSource.HR -> R.string.icon_general_hr
        JoyWorkSource.ALL -> R.string.icon_weeklytodo
        JoyWorkSource.CUSTOM -> R.string.icon_taskview_new
    }
}