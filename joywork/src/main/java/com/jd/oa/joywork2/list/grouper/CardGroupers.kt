package com.jd.oa.joywork2.list.grouper

import android.app.Activity
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.countJoyWork
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.kpi.PlaceHolderVH
import com.jd.oa.joywork2.bean.JoyWorkCustomGrouper
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.joywork2.bean.title.JoyWork2OnlyTextTitle
import com.jd.oa.joywork2.bean.title.JoyWork2UserExtraTitle
import com.jd.oa.joywork2.list.CardItemViewHolder
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.clickEvent


class CardFinishTimeGrouper(private val titleCallback: TitleCallback) : FinishTimeGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.MY_FINISH
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.MY_FINISH.ordinal) {
            CardIconTextExpandableVH<JoyWorkTitle>(parent.context, parent, true) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWorkTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        }
    }
}

class CardEndTimeGrouper(
    private val loadMoreCallback: LoadMoreCallback<String>,
    private val titleCallback: TitleCallback,
    private val menuItem: MenuItem?
) : EndTimeGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWork2OnlyTextTitle -> {
                GrouperTitleType.END_TIME
            }

            is CardBottomMultiPurpose<*> -> {
                GrouperTitleType.GROUP_BOTTOM
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.END_TIME.ordinal) {
            CardIconTextExpandableVH<JoyWork2OnlyTextTitle>(parent.context, parent, false) { title ->
                titleCallback.click(title)
            }
        } else {
            CardBottomMultiPurposeVH(parent.context, parent, menuItem)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        } else if (holder is CardBottomMultiPurposeVH) {
            val item = data as? CardBottomMultiPurpose<String> ?: return
            holder.bind(
                item,
                adapter = adapter
            ) { payload: String, completeRunnable ->
                loadMoreCallback.loadMore(payload, completeRunnable)
            }
        }
    }
}

class CardCustomGrouper(
    groupListener: CustomGroupListener,
    private val loadMoreCallback: LoadMoreCallback<String>,
    private val titleCallback: TitleCallback,
    listener: CustomGrouperListener,
    isProjectList: Boolean,
    private val menuItem: MenuItem?
) : CustomGrouper(groupListener, listener, isProjectList) {

    override fun isGroupTitle(viewHolder: RecyclerView.ViewHolder): Boolean {
        return viewHolder::class.java == CardTextWithActionExpandableVH::class.java
    }

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWork2ExtraTitle -> {
                GrouperTitleType.CUSTOM_GROUP
            }

            is ProjectAddTitle -> {
                GrouperTitleType.ADD_GROUP
            }

            is CardBottomMultiPurpose<*> -> {
                GrouperTitleType.GROUP_BOTTOM
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            GrouperTitleType.CUSTOM_GROUP.ordinal -> {
                CardTextWithActionExpandableVH(
                    parent.context,
                    parent,
                    { title: JoyWork2ExtraTitle ->
                        titleCallback.click(title)
                    },
                    { title: JoyWork2ExtraTitle ->
                        showAction(
                            isProjectList,
                            TodoShowType.CARD,
                            title,
                            title.extraObj as JoyWorkCustomGrouper.Group,
                            parent.context
                        )
                    })
            }

            GrouperTitleType.ADD_GROUP.ordinal -> {
                CardNewGroupVH(parent.context, parent) { name, joyWorkTitle: ProjectAddTitle ->
                    val group = joyWorkTitle.expandableGroup
                    val preGroup = listener.preGroup(group.id)
                    createGroupNet(
                        name,
                        false,
                        preGroup?.title as? JoyWork2ExtraTitle,
//                        ((preGroup?.title as? JoyWork2ExtraTitle))?.extra as? JoyWorkCustomGrouper.Group,
                        parent.context
                    )
                }
            }

            GrouperTitleType.GROUP_BOTTOM.ordinal -> {
                CardBottomMultiPurposeVH(parent.context, parent, menuItem)
            }

            else -> {
                LoadMoreVH(parent.context, parent)
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {

        when (holder) {
            is CardTextWithActionExpandableVH<*> -> {
                val actionShow: JoyWork2ExtraTitle.() -> Boolean = {
                    val group = extraObj as? JoyWorkCustomGrouper.Group
                    if (isProjectList && group != null) {
                        (group.permissions
                            ?: emptyList()).contains(ProjectPermissionEnum.GROUP.code)
                    } else {
                        true
                    }
                }
                val title = (data as? JoyWork2ExtraTitle) ?: return
                (holder as? CardTextWithActionExpandableVH<JoyWork2ExtraTitle>)?.bind(
                    title.title ?: "",
                    actionShow(title),
                    title,
                    title.expandableGroup
                )
            }

            is CardBottomMultiPurposeVH -> {
                val item = data as? CardBottomMultiPurpose<String> ?: return
                holder.bind(
                    item,
                    adapter = adapter,
                    createWorkCallback = { groupId ->
                        createJoyWork(
                            holder.itemView.context as Activity,
                            listener.currentTaskListType(),
                            groupId,
                            listener.currentGroups(),
                            listener.getViewId(),
                            listener.getViewType()
                        ) {
                            listener.onWorkCreated(it)
                        }
                        clickEvent {
                            ClickEventParam(
                                eventId = if (isProjectList)
                                    JoyWorkConstant.MOBILE_EVENT_TASK_CHECK_LIST_GROUP_NEW_TASK
                                else
                                    JoyWorkConstant.MOBILE_EVENT_TASK_HOME_GROUP_NEW_TASK
                            )
                        }
                    }
                ) { payload: String, completeRunnable ->
                    loadMoreCallback.loadMore(payload, completeRunnable)
                }
            }

            is CardNewGroupVH -> {
                holder.bind(data as ProjectAddTitle, isProjectList)
            }
        }
    }

    override fun getMovableVHClass(): Class<out RecyclerView.ViewHolder> =
        CardItemViewHolder::class.java
}

class CardExecutorGrouper(val titleCallback: TitleCallback) : ExecutorGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWork2UserExtraTitle -> {
                GrouperTitleType.EXECUTOR
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.EXECUTOR.ordinal) {
            CardIconTextExpandableVH<JoyWorkTitle>(parent.context, parent, true) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2UserExtraTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        }
    }
}


class CardProjectGrouper(val titleCallback: TitleCallback) : ProjectGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.PROJECT
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.PROJECT.ordinal) {
            CardIconTextExpandableVH<JoyWorkTitle>(parent.context, parent, true) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
//        if (holder is OnlyTextExpandableVH<*>) {
//            val title = (data as? JoyWork2ExtraTitle) ?: return
//            (holder as? OnlyTextExpandableVH<JoyWorkTitle>)?.bind(
//                title.title ?: "",
//                position == 0,
//                title
//            )
//        }
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2ExtraTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWork2ExtraTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        }
    }

}

class CardSourceGrouper(val titleCallback: TitleCallback) : SourceGrouper() {
    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.SOURCE
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.SOURCE.ordinal) {
            CardIconTextExpandableVH<JoyWorkTitle>(parent.context, parent, true) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2ExtraTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        }
    }
}


class CardExtStatusGrouper(
    isProjectList: Boolean,
    customFieldGroup: CustomFieldGroup?,
    val titleCallback: TitleCallback
) : ExtStatusGrouper(isProjectList, customFieldGroup) {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.EXT_STATUS
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.EXT_STATUS.ordinal) {
            CardIconTextExpandableVH<JoyWorkTitle>(parent.context, parent, true) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWork2OnlyTextTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        }
    }
}


class CardBizCodeGrouper(val titleCallback: TitleCallback) : BizCodeGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.BIZ_CODE
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.BIZ_CODE.ordinal) {
            CardIconTextExpandableVH<JoyWorkTitle>(parent.context, parent, true) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        }
    }

}


class CardSubSourceGrouper(val titleCallback: TitleCallback) : SubSourceGrouper() {

    override fun onGetItemViewType(data: Any): GrouperTitleType? {
        return when (data) {
            is JoyWorkTitle -> {
                GrouperTitleType.SUB_SOURCE
            }

            else -> {
                null
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == GrouperTitleType.SUB_SOURCE.ordinal) {
            CardIconTextExpandableVH<JoyWorkTitle>(parent.context, parent, true) { title ->
                titleCallback.click(title)
            }
        } else {
            PlaceHolderVH(parent.context)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        adapter: RecyclerView.Adapter<*>,
        position: Int,
        data: Any
    ) {
        if (holder is CardIconTextExpandableVH<*>) {
            val title = (data as? JoyWork2OnlyTextTitle) ?: return
            val count = title.expandableGroup.realItems.countJoyWork()
            (holder as? CardIconTextExpandableVH<JoyWorkTitle>)?.bind(
                title.title ?: "",
                data.expandableGroup!!.expand,
                position == 0,
                count,
                title
            )
        }
    }
}