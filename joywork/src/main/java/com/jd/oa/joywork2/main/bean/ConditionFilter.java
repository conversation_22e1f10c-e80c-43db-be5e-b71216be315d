package com.jd.oa.joywork2.main.bean;

import android.text.TextUtils;


import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class ConditionFilter {
    @SerializedName("logic")
    public String logic; // 筛选条件的关系，“所有” 或者 “之一”
    @SerializedName("conditions")
    public List<ConditionFilterValue> conditions; // 筛选条件列表

    private ConditionFilter() {
    }

    private ConditionFilter(String logic, List<ConditionFilterValue> conditionFilterValues) {
        this.logic = logic;
        this.conditions = conditionFilterValues;
    }

    private static final ConditionFilter sDefaultInstance = new ConditionFilter();

    public static ConditionFilter getDefaultInstance() {
        if (sDefaultInstance.conditions == null) {
            sDefaultInstance.conditions = new ArrayList<>();
        }
        if (TextUtils.isEmpty(sDefaultInstance.logic)) {
            sDefaultInstance.logic = Logic.ALL.value;
        }
        return sDefaultInstance;
    }

    /**
     * 过滤掉没有内容的筛选
     */
    public ConditionFilter filter() {
        if (conditions == null) {
            return new ConditionFilter(logic, new ArrayList<>());
        }
        List<ConditionFilterValue> filterConditions = new ArrayList<>();
        for (ConditionFilterValue conditionFilterValue : conditions) {
            if (conditionFilterValue.isValid()) {
                filterConditions.add(conditionFilterValue);
            }
        }
        return new ConditionFilter(logic, filterConditions);
    }

    enum Logic {
        ALL("ALL"),
        ONE("ONE");
        final String value;

        Logic(String value) {
            this.value = value;
        }
    }

    public static class ConditionFilterValue {
        // 筛选字段
        // 如果field是时间相关的，values不赋值，将时间范围赋值到 gt 和 lt 上
        // 如果field是人员相关的，values中元素为 erp + ":" + ddAppId 拼接的字符串
        public String field; // 对应下面 Field 字段。
        // 字段的值列表
        public List<String> values;

        // 字段值的运算关系: 包含、不包含、为空、不为空。对应下面 Operation
        public String operation;

        // 如果 field 是时间类型，使用gt，lt
        public String gt;
        public String lt;

        private boolean isValid() {
            return field != null && !TextUtils.isEmpty(field.trim());
        }
    }

    public enum Field {
        // 与人员相关的
        CREATOR("CREATOR"),// 创建人
        EXECUTOR("EXECUTOR"), // 执行人
        FOLLOWER("FOLLOWER"), // 关注人
        // 与时间相关的
        START_TIME("START_TIME"), // 开始时间
        END_TIME("END_TIME"), // 截止时间
        CREATE_TIME("CREATE_TIME"), // 创建时间
        FINISH_TIME("FINISH_TIME"), // 完成时间
        // 其他
        PRIORITY("PRIORITY"), // 优先级
        SOURCE("SOURCE"), // 待办来源
        PROJECT("PROJECT"); // 所属清单

        public final String code;

        Field(String code) {
            this.code = code;
        }

        // 根据 code 获取实例
        public static Field getByCode(String code) {
            for (Field value : values()) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            throw new IllegalArgumentException("Unknown Field code: " + code);
        }
    }

    public enum Operation {
        INCLUDE("INCLUDE"), // 包含
        EXCLUDE("EXCLUDE"), // 不包含
        IS_NULL("IS_NULL"), // 为空
        NOT_NULL("NOT_NULL"); // 非空

        public final String code;

        Operation(String code) {
            this.code = code;
        }

        // 根据 code 获取实例
        public static Operation getByCode(String code) {
            for (Operation value : values()) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            throw new IllegalArgumentException("Unknown Operation code: " + code);
        }
    }
}
