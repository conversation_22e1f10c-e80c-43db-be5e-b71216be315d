package com.jd.oa.joywork2.list.calendar

import android.view.View
import androidx.fragment.app.activityViewModels
import com.haibin.calendarview.Calendar
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork2.main.ConditionViewModel
import com.jd.oa.joywork2.menu.JoyWork2MenuViewModel

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/16 19:17
 */
class SelfCalendarListContainer : CalendarListContainer() {

    companion object {
        const val TAG = "SelfCalendarListContainer"
    }

    private val mMenuViewModel: JoyWork2MenuViewModel by activityViewModels()
    private val conditionViewModel: ConditionViewModel by activityViewModels()

    override fun onClickAddOnDay(view: View, calendar: Calendar) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.taskListTypeEnum = getTaskListType()
        if (tmpData.taskListTypeEnum == TaskListTypeEnum.HANDLE) {
            val self = JoyWorkUser.getSelf()
            self.setToChief()
            tmpData.owners = listOf(self)
        }
        val dialogO =
            JoyWorkShortcutCreator.getShortcutDialog(
                requireActivity(),
                tmpData = tmpData,
                showGroup = false,
                projectId = getProjectId(),
                viewId = conditionViewModel.viewIdFactory?.invoke(),
                viewType = conditionViewModel.viewTypeFactory?.invoke()
            )
        dialogO.showSnackBar = true
        dialogO.successCallback = { result, value, dialog ->
            dialog?.dismiss()
            listContainerInheritedVM.onCreateWorkSuccess(CalendarHelper.timeInMillisToCalendar(value.endTime), result)
        }
        dialogO.show()
    }

    override fun getProjectId(): String {
        val self = JoyWorkUser.getSelf()
        return "${self.userId}_" + "$" + "_${self.teamId}"
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return mMenuViewModel.mSelectMenuItemLiveData.value?.getListType()
            ?: TaskListTypeEnum.HANDLE
    }

    override val isProject: Boolean
        get() = false

    override fun getIsArchiveProject(): Boolean = false
}