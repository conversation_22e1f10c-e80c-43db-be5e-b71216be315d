package com.jd.oa.joywork2.list.listener

import android.view.View
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkDetailParam
import com.jd.oa.joywork.JoyWorkMediator
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork2.list.IWorkListHost

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/13 23:47
 */
interface IViewClickListener : View.OnClickListener {

    val host: IWorkListHost?

    fun gotoDetail(work: JoyWork, isCommentClick: Boolean) {
        if (host == null) {
            return
        }
        if (host!!.getHostFragment() != null) {
            JoyWorkMediator.goDetail(host!!.getHostFragment()!!, JoyWorkDetailParam(
                taskId = work.taskId, projectId = work.projectId, taskName = work.title
            ).apply {
                from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                obj = DetailReturnParcel()
                commentId = if (isCommentClick) work.latestComment?.commentId else null
                reqCode = 10
            })
        } else {
            host!!.getHostActivity()?.run {
                JoyWorkMediator.goDetail(this, JoyWorkDetailParam(
                    taskId = work.taskId, projectId = work.projectId, taskName = work.title
                ).apply {
                    from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                    obj = DetailReturnParcel()
                    commentId = if (isCommentClick) work.latestComment?.commentId else null
                    reqCode = 10
                })
            }
        }
    }

}