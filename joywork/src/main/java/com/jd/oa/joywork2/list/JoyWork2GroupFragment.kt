package com.jd.oa.joywork2.list

import android.view.View
import android.widget.RelativeLayout
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.view.realAdapter
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.joywork2.menu.JoyWork2MenuViewModel
import com.jd.oa.utils.JoyWorkUtils.bindView

/**
 * 带分组的任务列表
 * 截止时间、自定义分组按分组加载更多
 * 其余按整个列表加载更多
 */
@Suppress("UNCHECKED_CAST")
class JoyWork2GroupFragment : JoyWork2GroupBaseFragment() {

    private val mMenuViewModel: JoyWork2MenuViewModel by activityViewModels()
    private val addView: RelativeLayout by bindView(R.id.add)

    override fun onClickAdd(view: View) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.taskListTypeEnum = getTaskListType()
        if (tmpData.taskListTypeEnum == TaskListTypeEnum.HANDLE) {
            val self = JoyWorkUser.getSelf();
            self.setToChief()
            tmpData.owners = listOf(self)
        }
        val groupCfg = getGroupConfig()
        tmpData.selectGroupId = groupCfg.first
//        //个人待办的非自定义分组，不展示分组，因此也不传递projectId
//        val projectId = if (isCustomGroup()) getProjectId() else null
        val dialogO =
            JoyWorkShortcutCreator.getShortcutDialog(
                requireActivity(),
                tmpData = tmpData,
                showGroup = isCustomGroup(),
                projectId = getProjectId(),
                groups = groupCfg.second,
                conditionViewModel.viewIdFactory?.invoke(),
                conditionViewModel.viewTypeFactory?.invoke()
            )
        dialogO.showSnackBar = true
        dialogO.successCallback = { _, _, dialog ->
            dialog?.dismiss()
            mListViewModel.initListDelay()
        }
        dialogO.show()
    }

    override fun getGroups() = getGroupConfig().second

    override fun getIsArchiveProject(): Boolean = false

    private fun getGroupConfig(): Pair<String?, List<Group>> {
        val adapter = mRv.realAdapter as? JoyWork2GroperAdapter ?: return Pair(null, ArrayList())
        val srcGroups = adapter.groups
        val groups = ArrayList<Group>()
        var selectGroupId: String? = null
        srcGroups.filter {
            it.title is JoyWork2ExtraTitle
        }.forEach { group ->
            val gg = (group.title as JoyWork2ExtraTitle).extraGroup
            if (gg != null) {
                val g = Group()
                gg.toGroup(g)
                groups.add(g)
                if (g.isDefaultGroup) {
                    selectGroupId = g.groupId
                }
            }
        }
        return Pair(selectGroupId, groups)
    }

    companion object {
        const val FRG_TAG = "JoyWork2GroupFragment"
    }

    override fun initObservers() {
        mMenuViewModel.mSelectMenuItemLiveData.observe(viewLifecycleOwner) { item ->
            mListViewModel.updateItem(item)
            addView.isVisible = item.canCreateTask
        }
        super.initObservers()
    }

    override fun getProjectId(): String {
        val self = JoyWorkUser.getSelf()
        return "${self.userId}_" + "$" + "_${self.teamId}"
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return mMenuViewModel.mSelectMenuItemLiveData.value?.getListType()
            ?: TaskListTypeEnum.HANDLE
    }

    override fun isProject() = false

}
