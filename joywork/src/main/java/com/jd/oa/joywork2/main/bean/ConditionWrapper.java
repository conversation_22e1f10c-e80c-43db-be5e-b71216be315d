package com.jd.oa.joywork2.main.bean;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork2.bean.ListColumn;
import com.jd.oa.joywork2.main.ConditionState;

import java.util.List;

public class ConditionWrapper {
    @SerializedName("filter")
    public ConditionFilter filter;
    @SerializedName("grouper")
    public ConditionGrouper grouper;
    @SerializedName("sorter")
    public ConditionSorter sorter;
    @SerializedName("operator")
    public ConditionOperator operator;

    public Integer taskStatus;
    public String viewId;
    public String viewType;
    public String listColumns;
    //控制重置按钮
    public boolean isDefault;
    public String showType;
    public boolean showTypeDisable;
    public boolean calendarTypeDisable;

    public ConditionState.Condition toCondition() {
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.Companion.getInstance(taskStatus);
        return new ConditionState.Condition(
                taskStatusEnum,
                sorter,
                filter,
                grouper,
                listColumns != null ? new Gson().fromJson(listColumns, new TypeToken<List<ListColumn>>() {
                }.getType())
                        : null,
                operator,
                isDefault
        );
    }
}