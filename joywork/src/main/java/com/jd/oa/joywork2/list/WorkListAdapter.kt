package com.jd.oa.joywork2.list

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout.LayoutParams
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.JoyWorkLevel
import com.jd.oa.joywork.JoyWorkOp
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.OperateButton
import com.jd.oa.joywork.detail.canTransfer
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.hasSpecialPermission
import com.jd.oa.joywork.indexAtWithNull
import com.jd.oa.joywork.isJoyWork
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isSelf
import com.jd.oa.joywork.repo.JoyWorkLocationParam
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.sortOwners
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.joywork2.bean.WorkTimeCheckResult
import com.jd.oa.joywork2.list.listener.DeadLineClickListener
import com.jd.oa.joywork2.list.listener.ExecutorClickListener
import com.jd.oa.joywork2.list.listener.IDragListener
import com.jd.oa.joywork2.list.listener.LastCommentClickListener
import com.jd.oa.joywork2.list.listener.MoreButtonClickListener
import com.jd.oa.joywork2.list.listener.SimpleItemClickListener
import com.jd.oa.joywork2.list.listener.StartTimeClickListener
import com.jd.oa.joywork2.viewholder.WorkItemViewHolder
import com.jd.oa.joywork2.viewholder.checkWorkTime
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.allChildrenGone
import com.jd.oa.utils.click
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.color
import com.jd.oa.utils.gone
import com.jd.oa.utils.logE
import com.jd.oa.utils.visible
import java.util.Collections

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/13 00:13
 */
class WorkListAdapter(
    private val workListHost: IWorkListHost,
    private val projectId: String,
    private val regionType: String,
    private val onDeadLineChangedListener: DeadLineClickListener.OnDeadLineChangedListener,
    private val onStartTimeChangedListener: StartTimeClickListener.OnStartTimeChangedListener,
    private val onExecutorChangedListener: ExecutorClickListener.OnExecutorChangedListener,
    private val onOperateButtonClickListener: MoreButtonClickListener.MoreButtonCeilProcessorListener
) :
    RecyclerView.Adapter<WorkItemViewHolder>(), IDragListener {

    private var dataList = mutableListOf<JoyWork>()
    private var sorted = false
    private var backup: MutableList<JoyWork>? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WorkItemViewHolder {
        return WorkItemViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.joywork2_list_simple_item, parent, false)
        )
    }

    override fun onBindViewHolder(holder: WorkItemViewHolder, position: Int) {
        val work = dataList[position]
        work.runCatching {
            holder.itemView.setOnClickListener(SimpleItemClickListener(workListHost, work))
            holder.bindPriority(work)
            holder.bindTitle(work)
            val checkResult = checkWorkTime(holder.itemView.context, work)
            holder.bindStartTime(work, checkResult)
            holder.bindHyphen(work, checkResult)
            holder.bindDeadLine(work, checkResult)
            holder.bindExecutors(work, checkResult.breakLine)
            holder.bindLastComment(work, checkResult.breakLine)
            holder.bindLabels(work)
            // holder.bindOperateButton(work)
            holder.moreButton.setOnClickListener(
                MoreButtonClickListener(
                    work,
                    holder.itemView.context,
                    holder.moreButton,
                    holder,
                    onOperateButtonClickListener
                )
            )
            if (holder.secondLine.allChildrenGone(false)) {
                holder.secondLine.gone()
            } else {
                holder.secondLine.visible()
            }
            if (holder.thirdLine.allChildrenGone(false)) {
                holder.thirdLine.gone()
            } else {
                holder.thirdLine.visible()
            }
        }.onFailure {
            logE {
                "WorkListAdapter-->onBindViewHolder e = $it"
            }
        }
        holder.itemView.setTag(R.id.jdme_tag_id, work)
    }

    private fun WorkItemViewHolder.bindPriority(work: JoyWork) {
        (priorityPlaceHolder.background as? GradientDrawable)?.run {
            val level = JoyWorkLevel.NO.getLevel(work.priorityType ?: JoyWorkLevel.NO.value)
            setColor(level.getCardTypeBgColor())
            if(level != JoyWorkLevel.NO){
                root.setBackgroundResource(R.drawable.joywork_right_round_8)
            } else {
                root.setBackgroundResource(R.drawable.jdme_all_round_8)
            }
        }
    }

    private fun WorkItemViewHolder.bindTitle(work: JoyWork) {
        JoyWorkViewItem.title(work, title)
        cb.setTag(R.id.jdme_tag_id, work)
        cbContainer.setTag(R.id.jdme_tag_id, work)
        JoyWorkViewItem.checkboxNew(
            work,
            cb,
            cb2,
            cbContainer
        ) { work: JoyWork, cbView: View ->
            JDMAUtils.onEventClick(
                JoyWorkConstant.INCOMPLETE_FINISH,
                JoyWorkConstant.INCOMPLETE_FINISH
            )
            val context = cbView.context
            JoyWorkViewItem.finishAction(work, context, object : JoyWorkUpdateCallback {
                override fun result(success: Boolean, errorMsg: String) {
                    work.isFinishing = false
                    if (success) {
                        try {
                            val tmp = cbView.tag as JoyWork
                            onFinishStatusChange(context, tmp, adapterPosition)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            result(false, JoyWorkEx.filterErrorMsg(""))
                        }
                    } else {
                        notifyItemChanged(adapterPosition)
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }

                override fun onStart() {
                    work.isFinishing = true
                    notifyItemChanged(adapterPosition)
                }
            })
        }
    }

    private fun WorkItemViewHolder.bindHyphen(work: JoyWork, checkResult: WorkTimeCheckResult) {
        if (checkResult.showHyphen) {
            hyphen.visible()
        } else {
            hyphen.gone()
        }
        if (checkResult.warn)
            hyphen.setTextColor(deadline.context.color(R.color.joywork_red))
        else
            hyphen.setTextColor(Color.parseColor("#8F959E"))
    }

    private fun WorkItemViewHolder.bindStartTime(work: JoyWork, checkResult: WorkTimeCheckResult) {
        if (checkResult.showStart) {
            startTime.visible()
            startTime.tag = work
            startTime.click(
                StartTimeClickListener(
                    adapterPosition,
                    work,
                    onStartTimeChangedListener
                )
            )
            if (checkResult.warn)
                startTime.setTextColor(deadline.context.color(R.color.joywork_red))
            else
                startTime.setTextColor(Color.parseColor("#8F959E"))
            startTime.text = checkResult.startContent
        } else {
            startTime.gone()
        }
    }

    private fun WorkItemViewHolder.bindDeadLine(work: JoyWork, checkResult: WorkTimeCheckResult) {
        if (checkResult.showEnd) {
            deadline.visible()
            deadline.tag = work
            deadline.click(DeadLineClickListener(adapterPosition, onDeadLineChangedListener))
            if (checkResult.warn)
                deadline.setTextColor(deadline.context.color(R.color.joywork_red))
            else
                deadline.setTextColor(Color.parseColor("#8F959E"))
            deadline.text = checkResult.endContent
        } else {
            deadline.gone()
        }
    }

    private fun WorkItemViewHolder.bindExecutors(work: JoyWork, lineBreak: Boolean) {
        work.sortOwners()
        val avatarView = if (lineBreak) {
            mAvatarView.gone()
            mAvatarViewThird.visible()
            mAvatarViewThird
        } else {
            mAvatarViewThird.gone()
            mAvatarView.visible()
            mAvatarView
        }
        JoyWorkViewItem.avatarView(work, avatarView, null) { joyWork ->
            if (!joyWork.owners.isLegalList() || joyWork.owners.size >= 2) {
                ""
            } else {
                joyWork.owners.firstOrNull()?.realName ?: ""
            }
        }
        if (work.canTransfer()) {
            avatarView.tag = work
            avatarView.setOnClickListener(
                ExecutorClickListener(
                    work,
                    bindingAdapterPosition
                ) { p, w ->
                    onExecutorChangedListener.onExecutorLineChanged(p, w)
                }
            )
        } else {
            avatarView.tag = null
            avatarView.isEnabled = false
            avatarView.isClickable = false
        }
    }

    private fun WorkItemViewHolder.bindLastComment(work: JoyWork, lineBreak: Boolean) {
        val comment = work.latestComment
        val commentView = if (lineBreak) {
            commentDate.gone()
            commentDateThird.visible()
            commentDateThird
        } else {
            commentDateThird.gone()
            commentDate.visible()
            commentDate
        }
        if (comment != null && comment.updateTime != null && comment.updateTime != 0L) {
            commentView.visible()
        } else {
            commentView.gone()
        }
        commentView.tag = work
        commentView.setOnClickListener(
            LastCommentClickListener(
                adapterPosition,
                work,
                workListHost
            )
        )
    }

    private fun WorkItemViewHolder.bindLabels(work: JoyWork) {
        JoyWorkViewItem.loadLabel(work, labelContainer)
    }

    private fun WorkItemViewHolder.bindOperateButton(work: JoyWork) {
        val operateButtonList = getAllOperateButton(work, onOperateButtonClickListener)
        operateButtonContainer.removeAllViews()
        operateButtonContainer.isVisible = operateButtonList.isNotEmpty()
        operateButtonList.forEach { button ->
            val buttonView = TextView(operateButtonContainer.context).apply {
                text = button.title(context)
                val lp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                lp.rightMargin = DensityUtil.dp2px(context, 12f)
                layoutParams = lp
                setPadding(
                    DensityUtil.dp2px(context, 16f),
                    DensityUtil.dp2px(context, 6f),
                    DensityUtil.dp2px(context, 16f),
                    DensityUtil.dp2px(context, 6f)
                )
                val buttonTextColor =
                    if (button.isMain) R.color.color_F63218 else R.color.color_FF000000
                setTextColor(context.getColor(buttonTextColor))
                val buttonBackground =
                    if (button.isMain) R.drawable.stroke_all_f63218 else R.drawable.stroke_all_dcdee0
                setBackgroundResource(buttonBackground)
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
                setOnClickListener {
                    when (button) {
                        is OperateButton.MoveToButton -> {
                            clickEvent(JoyWorkConstant.MOBILE_EVENT_CARD_MOVE_TO)
                            onOperateButtonClickListener.selectGroup(work.taskId, null)
                        }

                        is OperateButton.TransferButton -> {
                            clickEvent(JoyWorkConstant.MOBILE_EVENT_CARD_TRANSFER)
                            onOperateButtonClickListener.transferTask(work)
                        }

                        is OperateButton.DeleteButton -> {
                            clickEvent(JoyWorkConstant.MOBILE_EVENT_CARD_DELETE)
                            onOperateButtonClickListener.deleteTask(work, bindingAdapterPosition)
                        }
                    }
                }
            }
            operateButtonContainer.addView(buttonView)
        }
    }

    private fun getAllOperateButton(
        work: JoyWork,
        listener: MoreButtonClickListener.MoreButtonCeilProcessorListener
    ): ArrayList<OperateButton> {
        val operateButtonList = arrayListOf<OperateButton>()
        if (hasSpecialPermission(JoyWorkOp.UPDATE, work.enablePermission, work.permission) &&
            !work.isUIFinish && listener.isCustomGroup()) {
            operateButtonList.add(OperateButton.MoveToButton)
        }
        if (hasSpecialPermission(JoyWorkOp.ASSIGN, work.enablePermission, work.permission) &&
            work.owners?.firstOrNull { it.isSelf() } != null) {
            operateButtonList.add(OperateButton.TransferButton)
        }
        if (hasSpecialPermission(JoyWorkOp.DELETE, work.enablePermission, work.permission)) {
            operateButtonList.add(OperateButton.DeleteButton)
        }

        return operateButtonList
    }

    private fun onFinishStatusChange(context: Context, joyWork: JoyWork, adapterPosition: Int) {
        if (joyWork.isFinish) {
            ProjectConstant.sendBroadcast(
                context, ProjectConstant.UNFINISH_ACTION
            )
            joyWork.uiTaskStatus = TaskStatusEnum.UN_FINISH.code;
        } else {
            ProjectConstant.sendBroadcast(
                context,
                ProjectConstant.FINISH_ACTION
            )
            joyWork.uiTaskStatus = TaskStatusEnum.FINISH.code
            if (joyWork.isFinish && joyWork.isDup) {
                ToastUtils.showInfoToast(R.string.joywork_dup_work_finish_tips)
            }
        }
        dataList.remove(joyWork)
        notifyItemRemoved(adapterPosition)
        workListHost.onStateChanged(adapterPosition, joyWork)
    }


    override fun getItemCount(): Int = dataList.size

    fun isEmpty() = dataList.isEmpty()

    @SuppressLint("NotifyDataSetChanged")
    fun refreshList(data: List<JoyWork>) {
        dataList.clear()
        dataList.addAll(data)
        notifyDataSetChanged()
    }

    fun addNew(work: JoyWork) {
        runCatching {
            dataList.add(0, work)
            notifyItemRangeInserted(0, 1)
        }
    }

    fun append(data: List<JoyWork>) {
        kotlin.runCatching {
            val size = dataList.size
            dataList.addAll(data)
            notifyItemRangeInserted(size, data.size)
        }
    }

    fun remove(work: JoyWork, position: Int) {
        runCatching {
            dataList.remove(work)
            notifyItemRemoved(position)
            workListHost.onStateChanged(position, work)
        }
    }

    override fun onMove(fromPosition: Int, toPosition: Int) {
        kotlin.runCatching {
            Collections.swap(dataList, fromPosition, toPosition)
            notifyItemMoved(fromPosition, toPosition)
            sorted = true
        }
    }

    override fun onStartDrag(viewHolder: ViewHolder) {
        viewHolder.itemView.findViewById<View>(R.id.root)?.run {
            setBackgroundResource(R.drawable.joywork_all_round_8_gray)
        }
        backup = dataList
    }

    override fun onStopDrag(viewHolder: ViewHolder) {
        viewHolder.itemView.findViewById<View>(R.id.root)?.run {
            setBackgroundResource(R.drawable.jdme_all_round_8)
        }
        if (sorted) {
            val item = viewHolder.itemView.getTag(R.id.jdme_tag_id) as? JoyWork ?: return
            sortJoyWork2(item)
            sorted = false
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun restore() {
        if (backup.isLegalList()) {
            dataList = backup!!
            notifyDataSetChanged()
            backup = null
            sorted = false
        }
    }

    private fun sortJoyWork2(joyWork: JoyWork) {
        val param = JoyWorkLocationParam()
        if ((joyWork.planTime ?: -1) >= 0)
            param.planTime = "${joyWork.planTime}"
        param.blockType = regionType
        val index = dataList.indexOf(joyWork)
        if (index > 0) {
            val front = dataList.indexAtWithNull(index - 1)
            if (front?.isJoyWork() == true) {
                param.front = front.taskId
            }
            val after = dataList.indexAtWithNull(index + 1)
            if (after?.isJoyWork() == true) {
                param.after = after.taskId
            }
        }
        JoyWorkRepo.sortProjectJoyWork(
            joyWork.taskId,
            param.blockType,
            projectId,
            param,
            object : JoyWorkUpdateCallback {
                override fun onStart() {
                }

                @SuppressLint("NotifyDataSetChanged")
                override fun result(success: Boolean, errorMsg: String) {
                    if (!success) {
                        restore()
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }
            })
    }


}