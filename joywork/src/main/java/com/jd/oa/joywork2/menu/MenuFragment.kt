package com.jd.oa.joywork2.menu

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.Router
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.TeamDetailActivity
import com.jd.oa.joywork.team.create.TeamCreateActivity
import com.jd.oa.joywork.view.ProjectListLoadMoreFooter
import com.jd.oa.joywork.view.realAdapter
import com.jd.oa.joywork2.backend.JoyWorkSource
import com.jd.oa.joywork2.list.project.TeamDetail2Activity
import com.jd.oa.joywork2.main.JoyWorkViewModel
import com.jd.oa.joywork2.main.Tab2ViewModel
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.Constants
import com.jd.oa.utils.Constants.KEY_JOY_WORK_OA
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.grayResourceSwitchEnable
import com.jd.oa.utils.isVisible
import com.jd.oa.utils.padding
import com.jd.oa.utils.vertical
import com.jd.oa.utils.visible
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import java.util.Objects

internal class MenuFragment : BaseFragment() {

    companion object {
        const val FRG_TAG = "MenuFragment"
    }

    private val mMenuViewModel: JoyWork2MenuViewModel by activityViewModels()
    private val mTab2ViewModel: Tab2ViewModel by activityViewModels()
    private val mMainViewModel: JoyWorkViewModel by activityViewModels()

    private val mDrawerLayout: SimpleDrawerLayout by bindView(R.id.mDrawerLayout)
    private val mFixedMenuContainer: LinearLayout by bindView(R.id.mFixedMenuContainer)

    //    private val mProjectItem: MenuItemView by bindView(R.id.mProjectItem)
    private val mRv: RecyclerView by bindView(R.id.mRv)
    private val mMenu: ViewGroup by bindView(R.id.mMenu)

    private var mPullUpLoadHelper: PullUpLoadHelper? = null
    private var mFooterView: ProjectListLoadMoreFooter? = null

    private val mFixedItems =
        mutableListOf(MenuItem.MyHandle, MenuItem.MyApproval, MenuItem.MyCooperation, MenuItem.MyAssign)

    private var mCreateProjectLauncher: ActivityResultLauncher<Intent>? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mCreateProjectLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
                if (it.resultCode == Activity.RESULT_OK) {
                    mMenuViewModel.getProjectItems(false)
                }
            }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.joywork2_menu_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!KEY_JOY_WORK_OA.grayResourceSwitchEnable(default = "1")) {
            mFixedItems.remove(MenuItem.MyApproval)
        }
        mMenu.setOnClickListener {
            //点击 menu 区域不关闭
            if (!mDrawerLayout.isVisible()) {
                mDrawerLayout.closeDrawer(mMenu)
            }
        }
        mDrawerLayout.setOnClickListener {
            mTab2ViewModel.closeMenu()
        }
        mDrawerLayout.addDrawerListener(object : SimpleDrawerLayout.DrawerListener {
            override fun onDrawerOpened() {
                // 待我处理子项内容请求
                mMenuViewModel.fetchMyHandle()
                // 自定义视图
                mMenuViewModel.getCustomView()
                // 归档清单、正常清单
                mMenuViewModel.getProjectItems(false)
            }

            override fun onDrawerClosed() {
                mDrawerLayout.gone()
            }
        })
        mMenu.padding(top = QMUIStatusBarHelper.getStatusbarHeight(view.context))
        mRv.vertical()
        val list = ArrayList<IMenuListItem>()
        list.add(MenuTitle(R.string.joywork_tabbar_title_team, R.string.icon_prompt_add, "2"))
        mRv.adapter = MenuAdapter(view.context, list, mMenuViewModel, {
            changeMenuItem(it)
        }, { title: MenuTitle ->
            if (Objects.equals(title.key, "2")) {
                mCreateProjectLauncher?.launch(Intent(context, TeamCreateActivity::class.java))
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_NAV_TAB_NEW_CHECKLIST
                    )
                }
            }
        })
        initFixedItems(mFixedMenuContainer)
        initViewModels()
        setupLoadMore()
    }

    private fun initFixedItems(container: ViewGroup) {
        container.removeAllViews()
        mFixedItems.forEach {
            val item = if (it.getListType() == TaskListTypeEnum.HANDLE
                && Constants.KEY_JOY_WORK_MY_HANDLE.grayResourceSwitchEnable()) {
                ExpandMenuItemView(container.context)
            } else {
                MenuItemView(container.context)
            }
            item.apply {
                set(
                    iconId = it.getIconId(),
                    contentText = it.getTitle(container.context),
                    countText = 0
                )
                tag = it
                setOnClickMenuItem { view ->
                    (view.tag as? MenuItem)?.let { item ->
                        changeMenuItem(item)
                    } ?: mTab2ViewModel.closeMenu()
                }
            }
            container.addView(item)
        }
    }

    private fun setupLoadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(mRv) {
            val adapter = mRv.realAdapter as? MenuAdapter ?: return@PullUpLoadHelper
            mMenuViewModel.getProjectItems(true, adapter.countProject())
        }
        mFooterView = ProjectListLoadMoreFooter(requireContext())
        mPullUpLoadHelper?.setLoadFooter(mFooterView)
    }

    private fun loadMoreOnceFinish(curSize: Int, limitSize: Int = ProjectRepo.PROJECT_PAGE_LIMIT) {
        val helper = mPullUpLoadHelper ?: return
        if (curSize == 0 || curSize < limitSize) {
            helper.setComplete()
        } else {
            helper.setLoaded()
        }
    }

    private fun changeMenuItem(menuItem: MenuItem) {
        if (menuItem is MenuItem.ExpandMenuItem && Objects.equals(
                menuItem.data.type,
                JoyWorkSource.HR.bizCode
            )
        ) {// 人事管理，需要跳转至原来的清单界面
            jumpToTeamDetail(
                menuItem.data.title,
                JoyWorkSource.HR.bizCode,
                JoyWorkProjectList.TYPE_OTHER,
                null,
            )
        } else if (menuItem is MenuItem.Project) {
            val dao = menuItem.data
            jumpToTeamDetail(
                dao.title,
                dao.projectId,
                JoyWorkProjectList.TYPE_NORMAL,
                dao.iconUrl,
                menuItem is MenuItem.ArchiveProject
            )
        } else if (menuItem is MenuItem.MyApproval) {
            Router.build(DeepLink.APPROVAL_PAGE).go(requireActivity())
        } else {
            mMenuViewModel.updateSelectMenuItem(menuItem)
        }
        mTab2ViewModel.closeMenu()
        clickMenuEvent(menuItem)
    }

    private fun clickMenuEvent(menuItem: MenuItem) {
        val clickId = when (menuItem) {
            MenuItem.MyHandle -> {
                JoyWorkConstant.JOYWORK_MYEXECUTION
            }

            MenuItem.MyApproval -> {
                JoyWorkConstant.JOYWORK_MYAPPROVAL
            }

            MenuItem.MyCooperation -> {
                JoyWorkConstant.JOYWORK_MYFOLLOWED
            }

            MenuItem.MyAssign -> {
                JoyWorkConstant.JOYWORK_MYCREATION
            }

            is MenuItem.Custom -> {
                // 原先自定义视图中的 MenuItem 移动到了「待我处理」
                null
            }

            is MenuItem.Project -> {
                JoyWorkConstant.JOYWORK_LIST
            }

            is MenuItem.MyProjectList -> {
                null
            }

            is MenuItem.ExpandMenuItem -> {
                menuItem.mobileEvent
            }

        }
        clickId?.run {
            clickEvent {
                ClickEventParam(
                    eventId = clickId
                )
            }
        }
    }

    private fun jumpToTeamDetail(
        title: String,
        id: String,
        type: Int,
        iconUrl: String?,
        isArchiveProject: Boolean = false
    ) {
        val a = activity ?: return
        val intent = if (type == JoyWorkProjectList.TYPE_NORMAL) {
            TeamDetail2Activity.inflateIntent(
                context = requireActivity(),
                title = title,
                id = id,
                isTab = mMainViewModel.mainStrategy is JoyWorkMainTabStrategy,
                type = type,
                iconUrl = iconUrl,
                isArchiveProject = isArchiveProject
            )
        } else {
            TeamDetailActivity.inflateIntent(
                context = requireActivity(),
                title = title,
                id = id,
                isTab = mMainViewModel.mainStrategy is JoyWorkMainTabStrategy,
                type = type,
                iconUrl = iconUrl
            )
        }
        a.startActivity(intent)
    }

    private var mLastSelectedMenuItem: MenuItem? = null
    private fun initViewModels() {
        mTab2ViewModel.mToggleMenuLiveData.observe(viewLifecycleOwner) {
            if (it) {
                mDrawerLayout.visible()
                mDrawerLayout.openDrawer(mMenu)
            } else {
                mDrawerLayout.closeDrawer(mMenu)
            }
        }
        mMenuViewModel.mSelectMenuItemLiveData.observe(viewLifecycleOwner) {
            updateSelectedItem(it)
            mLastSelectedMenuItem = it
        }
        mMenuViewModel.mMenuItemLiveData.observe(viewLifecycleOwner) {
            (mRv.realAdapter as? MenuAdapter)?.replaceCustomViews(it)
        }
        mMenuViewModel.mAllTypeProjectLiveData.observe(viewLifecycleOwner) {
            val adapter = mRv.realAdapter as? MenuAdapter ?: return@observe
            adapter.updateProjectItems(it)
            loadMoreOnceFinish(it.first.size)
        }
        // 监听并更新「待我处理」子 Menu 的 icon 和 content、num
        mMenuViewModel.myHandleLiveData.observe(viewLifecycleOwner) {
            // 监听并更新待我处理（子 Menu）、我关注的、我指派的，累计的待办数量
            for (i in 0 until mFixedMenuContainer.childCount) {
                val childView = mFixedMenuContainer.getChildAt(i)
                if (childView is BaseMenuItemView) {
                    val menuItem = childView.tag as MenuItem
                    childView.set(countText = it.second[menuItem.getViewId()])
                }
            }
            if (!Constants.KEY_JOY_WORK_MY_HANDLE.grayResourceSwitchEnable()) return@observe
            mFixedMenuContainer.findViewWithTag<ExpandMenuItemView>(MenuItem.MyHandle)
                ?.updateItemList(it) {
                    if(mMenuViewModel.mSelectMenuItemLiveData.value is MenuItem.ExpandMenuItem) {
                        mMenuViewModel.updateSelectMenuItem(MenuItem.MyHandle)
                    }
                }
        }
    }

    private fun updateSelectedItem(item: MenuItem) {
        updateSelectedItem(mLastSelectedMenuItem, false)
        updateSelectedItem(item, true)
    }

    private fun updateSelectedItem(item: MenuItem?, selected: Boolean) {
        val v = view ?: return
        if (item != null) {
            val itemView = v.findViewWithTag(item) as? BaseMenuItemView
            itemView?.select(selected)
            if (itemView == null && item is MenuItem.ExpandMenuItem) {
                mFixedMenuContainer
                    .findViewWithTag<ExpandMenuItemView>(MenuItem.MyHandle)
                    .selectedMenuItem(item.getViewId(), selected)
            }
        }
    }
}