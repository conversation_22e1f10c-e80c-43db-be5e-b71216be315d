package com.jd.oa.joywork2.menu;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.joywork2.backend.JoyWorkSource;

import java.util.Objects;

public class MenuBean {

    @SerializedName("viewType")
    public String viewType;  //  对应下面的 ViewType，与 bizCode 相结合才能具体确定是哪一个
    @SerializedName("viewName")
    public String title;

    @SerializedName("viewId")
    public String id;
    public String bizCode;
    public String icon;
    public boolean canCreateTask;
    @SerializedName("mobilePoint")
    public String mobileEvent;

    public String getType() {
        // viewType 中有一些需要细分的分类，所有此处进行转换
        if (Objects.equals(ViewType.SYS_PROJECT.code, this.viewType)) {// 清单，从原来清单中挪至自定义视图的
            return JoyWorkSource.HR.getBizCode();
        } else if (Objects.equals(ViewType.SYS_VIEW.code, this.viewType)) { // 系统默认添加的自定义视图
            for (JoyWorkSource value : JoyWorkSource.values()) {
                if(Objects.equals(this.bizCode, value.getBizCode())){
                    return value.getBizCode();
                }
            }
//            if (Objects.equals(this.bizCode, JoyWorkSource.TARGET.getBizCode())) {
//                return JoyWorkSource.TARGET.getBizCode();
//            } else if (Objects.equals(this.bizCode, JoyWorkSource.PROJECT.getBizCode())) {
//                return JoyWorkSource.PROJECT.getBizCode();
//            } else if (Objects.equals(this.bizCode, JoyWorkSource.MEETING.getBizCode())) {
//                return JoyWorkSource.MEETING.getBizCode();
//            } else if (Objects.equals(this.bizCode, JoyWorkSource.ALL.getBizCode())) {
//                return JoyWorkSource.ALL.getBizCode();
//            } else if(Objects.equals(this.bizCode,JoyWorkSource.MEETING2.getBizCode())){
//                return JoyWorkSource.MEETING2.getBizCode();
//            }
        }
        return JoyWorkSource.CUSTOM.getBizCode();
    }

    public enum ViewType {
        SYS_PERSONAL_VIEW("SYS_PERSONAL_VIEW"),
        PERSONAL_VIEW("PERSONAL_VIEW"), // 个人列表视图  我处理的，我指派的，我关注的
        CUSTOM_VIEW("CUSTOM_VIEW"), // 自定义视图，除上述外所有视图
        SYS_VIEW("SYS_VIEW"), // 系统预设视图，目标、项目、会议等预设的视图
        SYS_PROJECT("SYS_PROJECT"); // 系统预设清单，人事管理等原清单
        public final String code;

        ViewType(String code) {
            this.code = code;
        }
    }
}
