package com.jd.oa.joywork2.menu

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkNum
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.bold
import com.jd.oa.utils.normal

/**
 * @Author: hepiao3
 * @CreateTime: 2024/12/4
 * @Description:
 */
open class BaseMenuItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val contentText: TextView by lazy { findViewById(R.id.mContentText) }
    protected val contentView: ViewGroup by lazy { findViewById(R.id.mContentView) }
    protected val icon: IconFontView by lazy { findViewById(R.id.mIcon) }
    protected val count: TextView by lazy { findViewById(R.id.mCount) }
    protected lateinit var view: View

    open fun setOnClickMenuItem(callback: (View) -> Unit) {}

    fun select(s: Boolean) {
        contentView.isSelected = s
        count.isSelected = s
        if (s) {
            contentText.bold()
        } else {
            contentText.normal()
        }
    }

    fun set(iconId: Int? = null, contentText: String? = null, countText: Int? = null) {
        if (iconId != null) {
            icon.setText(iconId)
        }
        if (contentText != null) {
            this.contentText.text = contentText
        }
        if (countText != null) {
            count.text = JoyWorkNum.getStrNum(countText)
        }
    }
}