package com.jd.oa.joywork2.list

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.team.bean.ProjectErrorCode
import com.jd.oa.joywork2.backend.JoyWorkHttpException
import com.jd.oa.joywork2.main.ConditionState
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.delay

private data class Params(val condition: ConditionState?, val item: MenuItem?)

sealed class DataState {
    // 不同接口返回的格式不一样，没法统一
    class RefreshSuccess(val data: Any, val silent: Boolean = false) : DataState()
    class LoadMoreSuccess(val data: Any) : DataState()

    class RefreshFailure(val msg: String) : DataState()
    class LoadMoreFailure(val msg: String) : DataState()
    class NoPermission(val msg: String) : DataState()
}

class List2ViewModel : ViewModel() {

    private var repoFactory: (() -> ListRepo?)? = null
    fun updateRepoFactory(repoFactory: () -> ListRepo?) {
        this.repoFactory = repoFactory
    }

    private var _params = Params(null, null)

    // 数据列表
    private val _data = MutableLiveData<DataState>()
    val data: LiveData<DataState> = _data

    private fun updateData(state: DataState) {
        _data.value = state
    }

    fun updateCondition(conditionState: ConditionState) {
        if (conditionState is ConditionState.Loading) {
            updateRefreshing(true)
        }
        updateConditionInternal(conditionState)
    }

    private fun updateConditionInternal(newCondition: ConditionState) {
        _params = _params.copy(condition = newCondition)
        initList()
    }

    fun updateItem(item: MenuItem) {
        _params = _params.copy(item = item)
    }

    fun initListDelay(delayMillis: Long = 1500) {
        viewModelScope.safeLaunch {
            delay(delayMillis)
            initList()
        }
    }

    fun initList() {
        viewModelScope.safeLaunch {
            if (!checkParams()) {
                return@safeLaunch
            }
            val c = _params.condition
            val i = _params.item
            updateRefreshing(true)
            try {
                val repoTmp = repoFactory?.invoke()
                if (repoTmp != null) {
                    val wrapper = repoTmp.list(c!!, i!!)
                    updateData(DataState.RefreshSuccess(wrapper))
                }
            } catch (e: JoyWorkHttpException) {
                val target = ProjectErrorCode.values()
                    .firstOrNull { it.code == e.code }
                if (target != null
                ) {
                    updateData(
                        DataState.NoPermission(
                            JoyWorkEx.filterErrorMsg(
                                target.hint
                            )
                        )
                    )
                } else {
                    updateData(DataState.RefreshFailure(JoyWorkEx.filterErrorMsg(e.message)))
                }
            } finally {
                updateRefreshing(false)
            }
        }
    }

    fun refreshList() {
        viewModelScope.safeLaunch {
            if (!checkParams()) {
                return@safeLaunch
            }
            val c = _params.condition
            val i = _params.item
            try {
                val repoTmp = repoFactory?.invoke()
                if (repoTmp != null) {
                    val wrapper = repoTmp.list(c!!, i!!)
                    updateData(DataState.RefreshSuccess(wrapper, silent = true))
                }
            } catch (e: JoyWorkHttpException) {
                //updateData(DataState.RefreshFailure(JoyWorkEx.filterErrorMsg(e.message)))
                val target = ProjectErrorCode.values()
                    .firstOrNull { it.code == e.code }
                if (target != null
                ) {
                    updateData(
                        DataState.NoPermission(
                            JoyWorkEx.filterErrorMsg(
                                target.hint
                            )
                        )
                    )
                }
            }
        }
    }

    fun loadMore(offset: Int, regionType: String, completeRunnable: Runnable?) {
        viewModelScope.safeLaunch {
            if (!checkParams()) {
                return@safeLaunch
            }
            val c = _params.condition
            val i = _params.item
            try {
                val repoTmp = repoFactory?.invoke()
                if (repoTmp != null) {
                    val wrapper = repoTmp.loadMore(c!!, i!!, offset, regionType)
                    updateData(DataState.LoadMoreSuccess(wrapper))
                }
            } catch (e: JoyWorkHttpException) {
                updateData(DataState.LoadMoreFailure(JoyWorkEx.filterErrorMsg(e.message)))
            } finally {
                completeRunnable?.run()
            }
        }
    }

    fun checkParams(): Boolean {
        val i = _params.item ?: return false
        if (i.needCondition()) {
            return _params.condition is ConditionState.Condition
        }
        return true
    }

    // 创建一个 refreshing:Boolean 的 LiveData
    private val _refreshingEffect = MutableLiveData(false)
    val refreshingEffect: LiveData<Boolean> = _refreshingEffect
    private fun updateRefreshing(newValue: Boolean) {
        _refreshingEffect.value = newValue
    }

    fun canDraggable(): Boolean {
        val i: ConditionState.Condition =
            (_params.condition as? ConditionState.Condition) ?: return false
        return i.sorter.canDraggable()
    }

}