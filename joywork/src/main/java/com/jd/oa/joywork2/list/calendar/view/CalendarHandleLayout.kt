package com.jd.oa.joywork2.list.calendar.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.widget.LinearLayout
import kotlin.math.abs

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/12 00:18
 */
class CalendarHandleLayout : LinearLayout {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)
    constructor(
        context: Context, attrs: AttributeSet,
        defStyleAttr: Int
    ) : super(context, attrs, defStyleAttr)

    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop.toFloat()

    var calendarHandleTouchListener: CalendarHandleTouchListener? = null

    private var currentY: Float = 0f

    private var notified = false

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) {
            return super.onTouchEvent(event)
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                currentY = event.rawY
                notified = false
            }

            MotionEvent.ACTION_MOVE -> {
                notify(event)
            }

            MotionEvent.ACTION_UP -> {
                notify(event)
            }
        }
        return super.dispatchTouchEvent(event)
    }


    private fun notify(event: MotionEvent) {
        if (notified) return
        val y = event.rawY
        if (abs(y - currentY) > touchSlop) {
            calendarHandleTouchListener?.onMoveSomeDistance(y < currentY)
            notified = true
        }
    }


    interface CalendarHandleTouchListener {
        fun onMoveSomeDistance(up: Boolean)
    }

}