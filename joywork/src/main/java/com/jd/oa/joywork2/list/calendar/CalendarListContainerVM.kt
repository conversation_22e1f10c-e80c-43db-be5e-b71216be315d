package com.jd.oa.joywork2.list.calendar

import android.annotation.SuppressLint
import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.haibin.calendarview.Calendar
import com.jd.oa.joywork.isTrue
import com.jd.oa.timezone.HolidayScheme
import com.jd.oa.timezone.WeekDayScheme
import com.jd.oa.timezone.HolidayHelper
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.safeLaunch
import java.text.SimpleDateFormat

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/11 23:01
 */
class CalendarListContainerVM : ViewModel() {

    val holidaysLD = MutableLiveData<Map<String, Calendar>>()
    @SuppressLint("SimpleDateFormat")
    fun loadHolidays(context: Context) {
        if (!LocaleUtils.getUserSetLocaleStr(context).lowercase().startsWith("zh")) return
        viewModelScope.safeLaunch {
            val results = HolidayHelper.queryHolidays()
            val holidayMap = mutableMapOf<String, Calendar>()
            results.forEach {
                val holidays = it.value
                holidayMap += holidays.associate { holiday ->
                    val format = SimpleDateFormat("yyyy-MM-dd")
                    val scheme = if (holiday.isOffDay.isTrue()) HolidayScheme() else WeekDayScheme()
                    val date = format.parse(holiday.date!!)
                    val cal = java.util.Calendar.getInstance()
                    cal.timeInMillis = date?.time ?: 0
                    val calendar = Calendar()
                    calendar.year = cal.get(java.util.Calendar.YEAR)
                    calendar.month = cal.get(java.util.Calendar.MONTH) + 1
                    calendar.day = cal.get(java.util.Calendar.DATE)
                    calendar.schemeColor = scheme.getSchemeColor()
                    calendar.scheme = scheme.getSchemeText(context)
                    calendar.toString() to calendar
                }
            }
            holidaysLD.postValue(holidayMap)
        }
    }
}