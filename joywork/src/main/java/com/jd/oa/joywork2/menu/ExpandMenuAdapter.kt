package com.jd.oa.joywork2.menu

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkNum
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.sp.JoyWorkPreference.KV_ENTITY_JDME_MY_TASK_HANDLE_List
import com.jd.oa.joywork2.menu.ExpandMenuAdapter.ExpandMenuViewHolder
import com.jd.oa.utils.JsonUtils

/**
 * @Author: hepiao3
 * @CreateTime: 2024/12/4
 * @Description:
 */
class ExpandMenuAdapter(
    private val context: Context,
    private var items: MutableList<MenuBean> = arrayListOf()
) : RecyclerView.Adapter<ExpandMenuViewHolder>() {

    private val menuCountMap: MutableMap<String, Int> = mutableMapOf()
    private lateinit var onClick: (View) -> Unit
    // String: key, Int: index
    private var selectItem: Pair<String, Int>? = null
    private var lastSelectedPosition = -1
    private val preference: JoyWorkPreference by lazy { JoyWorkPreference(context) }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): ExpandMenuViewHolder {
        val view =
            context.layoutInflater.inflate(
                R.layout.joywork2_menu_expand_item_view,
                parent,
                false
            )
        return ExpandMenuViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: ExpandMenuViewHolder,
        position: Int
    ) {
        val item = MenuItem.ExpandMenuItem(items[position])
        holder.apply {
            itemView.apply {
                tag = item
                setOnClickListener { onClick(holder.itemView) }
                isSelected = position == selectItem?.second
            }
            Glide.with(context).load(item.iconUrl).into(mIcon)
            mContentText.text = item.getTitle(context)
            mCount.text = JoyWorkNum.getStrNum(menuCountMap[item.getViewId()])
        }
    }

    override fun onViewDetachedFromWindow(holder: ExpandMenuViewHolder) {
        super.onViewDetachedFromWindow(holder)
        // 「待我处理」子项 List 添加缓存，只存储最新的一个值，而不是每次 replace 时都去更新
        preference.put(KV_ENTITY_JDME_MY_TASK_HANDLE_List, JsonUtils.getGson().toJson(items.toList()))
    }

    override fun getItemCount(): Int {
        return items.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun replaceAllData(combineResult: Pair<List<MenuBean>, Map<String, Int>>): Boolean {
        // 判断上次选中的 item 是否仍存在于新列表中
        val selectedItemIndex = combineResult.first.indexOfFirst {
            it.id == selectItem?.first && (selectItem?.second ?: 0) >= 0
        }
        selectItem = selectItem?.copy(second = selectedItemIndex)
        lastSelectedPosition = -1

        // 更新 content 数据
        items.apply {
            clear()
            addAll(combineResult.first)
        }
        // 更新 todoNum 数据
        menuCountMap.apply {
            clear()
            putAll(combineResult.second)
        }
        notifyDataSetChanged()

        // 返回是否需要重置选中态
        return selectedItemIndex == -1
    }

    fun setMenuOnClick(onClick: (View) -> Unit) {
        this.onClick = onClick
    }

    fun selectedMenuItem(viewId: String, isSelected: Boolean) {
        lastSelectedPosition = selectItem?.second ?: -1
        notifyItemChanged(lastSelectedPosition)
        val selectedPosition = if (!isSelected) {
            -1
        } else {
            items.indexOfFirst { it.id == viewId }
        }
        selectItem = Pair(viewId, selectedPosition)
        notifyItemChanged(selectedPosition)
    }

    inner class ExpandMenuViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val mContentText: TextView = view.findViewById(R.id.mContentText)
        val mIcon: ImageView = view.findViewById(R.id.mIcon)
        val mCount: TextView = view.findViewById(R.id.mCount)
    }

}