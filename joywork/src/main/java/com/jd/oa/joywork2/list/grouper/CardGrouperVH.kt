package com.jd.oa.joywork2.list.grouper

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.countJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.multiPurposeItem
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.showNewGroupDialog
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.JoyWorkUtils.bindView
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.invisible
import com.jd.oa.utils.visible

class CardIconTextExpandableVH<T>(
    context: Context,
    parent: ViewGroup,
    private val noGroupLoadMore: Boolean,
    private val itemClick: (T) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork2_card_grouper_item_text,
            parent,
            false
        )
    ), Exchangeable {
    override var exchangeable = false

    private val title by bindView<TextView>(R.id.title)
    private val indicator by bindView<TextView>(R.id.indicator)
    private val tvCount by bindView<TextView>(R.id.count)
    private val bottomEdge by bindView<View>(R.id.bottom_edge)
    private val bottomSpace by bindView<View>(R.id.bottom_space)
    private val topSpace by bindView<View>(R.id.top_space)

    private val click = View.OnClickListener {
        itemClick(it.getTag(R.id.jdme_tag_id) as T)
    }

    // extra 会在点击事件中原样带回，不做任何处理
    @SuppressLint("SetTextI18n")
    fun bind(
        text: String,
        expanded: Boolean,
        first: Boolean,
        count: Int,
        extra: T,
    ) {
        title.text = text
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }
        tvCount.text = count.toString()
        tvCount.gone()
        itemView.setTag(R.id.jdme_tag_id, extra)
        itemView.setOnClickListener(click)

        if (expanded) {
            if (count == 0) {
                bottomEdge.visible()
                bottomSpace.gone()
            } else {
                bottomEdge.gone()
                bottomSpace.gone()
            }
        } else {
            bottomEdge.visible()
            if (noGroupLoadMore) {
                bottomSpace.gone()
            } else {
                bottomSpace.visible()
            }
        }

        if (noGroupLoadMore) {
            if (!first)
                topSpace.visible()
            else
                topSpace.gone()
        } else {
            topSpace.gone()
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class CardTextWithActionExpandableVH<T>(
    context: Context,
    parent: ViewGroup,
    private val itemClick: (T) -> Unit,
    private val actionClick: (T) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork2_card_vh_text_with_extra,
            parent,
            false
        )
    ), Exchangeable {
    override var exchangeable = false

    private val title by bindView<TextView>(R.id.title)
    private val indicator by bindView<TextView>(R.id.indicator)
    private val actionView by bindView<View>(R.id.icon)
    private val tvCount by bindView<TextView>(R.id.count)
    private val bottomEdge by bindView<View>(R.id.bottom_edge)
    private val bottomSpace by bindView<View>(R.id.bottom_space)

    private val click = View.OnClickListener {
        itemClick(it.getTag(R.id.jdme_tag_id) as T)
    }

    private val actionClickListener = View.OnClickListener {
        actionClick(it.getTag(R.id.jdme_tag_id) as T)
    }

    // extra 会在点击事件中原样带回，不做任何处理
    @SuppressLint("SetTextI18n")
    fun bind(
        text: String,
        actionShow: Boolean,
        extra: T,
        group: ExpandableGroup<JoyWorkTitle, JoyWork>
    ) {
        val count = group.realItems.countJoyWork()
        val expanded = group.expand
        title.text = text
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }
        tvCount.text = count.toString()
        tvCount.gone()
        itemView.setTag(R.id.jdme_tag_id, extra)
        itemView.setOnClickListener(click)

        actionView.setTag(R.id.jdme_tag_id, extra)
        actionView.setOnClickListener(actionClickListener)
        if (actionShow) {
            actionView.visible()
        } else {
            actionView.invisible()
        }

        if (expanded) {
            if (count == 0) {
                val multiPurpose = group.realItems.multiPurposeItem()
                if (multiPurpose != null) {
                    if (multiPurpose.showMore || multiPurpose.showNewTask) {
                        bottomEdge.gone()
                        bottomSpace.gone()
                    } else {
                        bottomEdge.visible()
                        bottomSpace.gone()
                    }
                } else {
                    bottomEdge.visible()
                    bottomSpace.visible()
                }
            } else {
                bottomEdge.gone()
                bottomSpace.gone()
            }
        } else {
            bottomEdge.visible()
            bottomSpace.visible()
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class CardNewGroupVH(
    context: Context,
    parent: ViewGroup,
    private val createGroup: (name: String, joyWorkTitle: ProjectAddTitle) -> Unit
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_card_new_group,
            parent,
            false
        )
    ) {

    private val root = itemView.findViewById<View>(R.id.root)

    fun bind(joyWorkTitle: ProjectAddTitle, isProjectList: Boolean) {
        root.setTag(R.id.jdme_tag_id, joyWorkTitle)
        root.setOnClickListener {
            clickEvent(
                if (isProjectList) JoyWorkConstant.MOBILE_EVENT_TASK_CHECKLIST_GROUP_NEWGROUP
                else JoyWorkConstant.MOBILE_EVENT_TASK_HOME_GROUP_NEWGROUP
            )
            newGroup(it.getTag(R.id.jdme_tag_id) as ProjectAddTitle)
        }
    }

    private fun newGroup(joyWorkTitle: ProjectAddTitle) {
        showNewGroupDialog(itemView.context) {
            createGroup(it, joyWorkTitle)
        }
    }


    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class CardBottomMultiPurposeVH(
    context: Context,
    parent: ViewGroup,
    private val menuItem: MenuItem?
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork2_list_kanban_more_item,
            parent,
            false
        )
    ) {

    private val topDivider = itemView.findViewById<View>(R.id.top_divider)
    private val moreRoot = itemView.findViewById<View>(R.id.more_root)
    private val tvNewTask = itemView.findViewById<TextView>(R.id.tv_new_task)
    private val loadMoreRoot = itemView.findViewById<View>(R.id.load_more_root)
    private val loadMorePb = itemView.findViewById<View>(R.id.pb)
    private val loadMoreTv = itemView.findViewById<TextView>(R.id.tv_load_more)

    private fun updateUI(item: CardBottomMultiPurpose<*>) {
        if (item.isLoading) {
            loadMorePb.visible()
            loadMoreTv.setText(R.string.libui_loading)
        } else {
            loadMorePb.gone()
            loadMoreTv.setText(R.string.me_load_more_data)
        }
    }

    fun <S> bind(
        item: CardBottomMultiPurpose<S>,
        adapter: RecyclerView.Adapter<*>,
        createWorkCallback: ((groupId: S) -> Unit)? = null,
        callback: (payload: S, completeRunnable: Runnable) -> Unit
    ) {
        if (item.showMore) loadMoreRoot.visible() else loadMoreRoot.gone()
        if (item.showNewTask && menuItem?.canCreateTask == true) tvNewTask.visible() else tvNewTask.gone()
        if (item.showMore || item.showNewTask) moreRoot.visible() else moreRoot.gone()
        if (item.showMore || item.showNewTask) topDivider.visible() else topDivider.gone()
        val padding = DisplayUtils.dip2px(12f)
        if (item.showNewTask) {
            loadMoreRoot.setPadding(0, 0, 0, padding)
        } else {
            loadMoreRoot.setPadding(0, padding, 0, padding)
        }
        itemView.setTag(R.id.jdme_tag_id, item)
        updateUI(item)
        loadMoreRoot.tag = item
        loadMoreRoot.setOnClickListener {
            val loadMoreItem = it.tag as CardBottomMultiPurpose<*>
            if (loadMoreItem.isLoading) {
                return@setOnClickListener
            }
            loadMoreItem.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            callback(item.payload) {
                loadMoreItem.isLoading = false
                adapter.notifyDataSetChanged()
            }
        }

        tvNewTask.setOnClickListener {
            createWorkCallback?.invoke(item.payload)
        }

    }


    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}