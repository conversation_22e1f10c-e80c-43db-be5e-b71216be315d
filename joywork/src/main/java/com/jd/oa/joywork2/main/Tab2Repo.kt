package com.jd.oa.joywork2.main

import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork2.backend.JoyWorkExceptionWithData
import com.jd.oa.joywork2.backend.ParseResponse
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.main.bean.ConditionWrapper
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.network.post
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

object Tab2Repo {
    suspend fun getTabCondition(viewId: String, viewType: String) =
        suspendCancellableCoroutine<ConditionWrapper> { coroutine ->
            val params = HashMap<String, Any>()
            params["viewId"] = viewId
            params["viewType"] = viewType
            val headers = HashMap<String, String>()
            val callback = object : SimpleRequestCallback<String>() {
                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    val response = ParseResponse.parse<ConditionWrapper>(
                        info!!.result,
                        ConditionWrapper::class.java,
                    )
                    if (response.isSuccessful) {
                        val map = response.data
                        coroutine.resumeWith(Result.success(map))
                    } else {
                        onFailure(null, response.errorMessage)
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    coroutine.resumeWithException(
                        JoyWorkExceptionWithData(
                            Triple(
                                viewId, viewType, JoyWorkEx.filterErrorMsg(
                                    info
                                )
                            )
                        )
                    )
                }
            }
            HttpManager.color()
                .post(params, headers, "joywork.getListConditionInfo", callback)
        }

    fun saveTabCondition(
        condition: ConditionState.Condition,
        viewId: String,
        viewType: String,
        showType: String,
        callback: ((success: Boolean) -> Unit)? = null
    ) {

        // 不关心结果，直接请求完事
        val params = HashMap<String, Any>()
        params["viewId"] = viewId
        params["viewType"] = viewType
        params["showType"] = showType
        condition.fillHashMap(params)
        // 列表、卡片、看板视图设置“已完成”过滤条件后，切换为日历视图，保存condition时需要将status重置为“未完成”状态
        if (showType == TodoShowType.CALENDAR.value && condition.status.code == TaskStatusEnum.FINISH.code) {
            params["taskStatus"] = TaskStatusEnum.UN_FINISH.code
        }
        val headers = HashMap<String, String>()
        val callback = object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                callback?.invoke(true)
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                callback?.invoke(false)
            }
        }
        HttpManager.color()
            .post(params, headers, "joywork.saveListCondition", callback)
    }

    fun clearCondition(viewId: String, callback: (success: Boolean) -> Unit) {
        val params = HashMap<String, Any>()
        params["viewId"] = viewId
        params["jdmeAppId"] = HttpManager.getConfig().userInfo.appId
        val headers = HashMap<String, String>()
        val callback = object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                callback.invoke(true)
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                callback.invoke(false)
            }
        }
        HttpManager.color()
            .post(params, headers, "joywork.removeCustomView", callback)
    }

    suspend fun resetListCondition(
        viewId: String,
        viewType: String
    ): ApiResponse<String> {
        return post<String>("joywork.resetListCondition") {
            mutableMapOf(
                Pair("viewId", viewId),
                Pair("viewType", viewType),
            )
        }
    }
}