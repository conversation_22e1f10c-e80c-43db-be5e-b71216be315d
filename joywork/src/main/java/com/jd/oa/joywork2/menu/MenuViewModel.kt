package com.jd.oa.joywork2.menu

import android.content.Context
import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.team.JoyWorkProjectCallback
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork2.backend.JoyWorkException
import com.jd.oa.joywork2.backend.iconId
import com.jd.oa.joywork2.backend.toJoyWorkSource
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.async
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

fun MenuItem.fillHashMap(params: HashMap<String, Any>) {
    params["viewId"] = getViewId()
    params["viewType"] = getViewType()
    if (this is MenuItem.MyHandle) {
        params["userRole"] = TaskUserRole.OWNER.code
    }
    if (this is MenuItem.MyAssign) {
        params["userRole"] = TaskUserRole.ASSIGN.code
    }
    if (this is MenuItem.MyCooperation) {
        params["userRole"] = TaskUserRole.EXECUTOR.code
    }
}

/**
 * 用于标记可使用在 [MenuFragment] 中 [RecycleView]
 */
interface IMenuListItem

data class MenuTitle(
    @StringRes val titleId: Int,
    @StringRes val iconId: Int? = null,
    val key: Any
) : IMenuListItem

data class ExpandMenuTitle(
    @StringRes val headIcon: Int,
    @StringRes val titleId: Int,
    @StringRes val tailIcon: Int,
    val key: Any = 0
) : IMenuListItem

sealed class MenuItem : IMenuListItem {

    abstract fun getTitle(context: Context): String
    abstract fun getIconId(): Int

    abstract fun getViewId(): String

    abstract fun getViewType(): String

    open fun needCondition() = false

    abstract fun getListType(): TaskListTypeEnum

    override fun equals(other: Any?): Boolean {
        val otherItem = other as? MenuItem ?: return false
        return otherItem.getViewId() == getViewId()
    }

    override fun hashCode(): Int {
        return getViewId().hashCode()
    }

    // 点击 MenuItem 后决定视图页面是否展示待办新建按钮
    open val canCreateTask: Boolean = true

    class Custom(val data: MenuBean) : MenuItem() {

        override fun needCondition(): Boolean {
            return true
        }

        // 自定义视图
        override fun getTitle(context: Context): String {
            return data.title ?: ""
        }

        override fun getIconId(): Int {
            return data.type.toJoyWorkSource()!!.iconId()
        }

        override fun getViewId(): String {
            return data.id
        }

        override fun getViewType(): String {
            return data.viewType
        }

        override fun getListType() = TaskListTypeEnum.PROJECT

    }

    object MyHandle : MenuItem() {
        override fun getListType() = TaskListTypeEnum.HANDLE

        override fun needCondition(): Boolean {
            return true
        }

        override fun getTitle(context: Context): String {
            return context.resources.getString(R.string.joywork_my_tasks)
        }

        override fun getIconId(): Int {
            return R.string.icon_general_user
        }

        override fun getViewId(): String {
            return joinId("execute")
        }

        override fun getViewType(): String {
            return MenuBean.ViewType.PERSONAL_VIEW.code
        }
    }

    object MyApproval : MenuItem() {
        override fun getListType() = TaskListTypeEnum.APPROVAL

        override fun needCondition(): Boolean {
            return true
        }

        override fun getTitle(context: Context): String {
            return context.resources.getString(R.string.joywork_task_approval)
        }

        override fun getIconId(): Int {
            return R.string.icon_sky_approval
        }

        override fun getViewId(): String {
            return joinId("oa")
        }

        override fun getViewType(): String {
            return MenuBean.ViewType.PERSONAL_VIEW.code
        }
    }

    object MyCooperation : MenuItem() {
        override fun getListType() = TaskListTypeEnum.FOLLOW

        override fun needCondition(): Boolean {
            return true
        }

        override fun getTitle(context: Context): String {
            return context.resources.getString(R.string.joywork_task_cooperator)
        }

        override fun getIconId(): Int {
            return R.string.icon_padding_concernedpeople
        }

        override fun getViewId(): String {
            return joinId("follow")
        }

        override fun getViewType(): String {
            return MenuBean.ViewType.PERSONAL_VIEW.code
        }
    }

    object MyAssign : MenuItem() {
        override fun getListType() = TaskListTypeEnum.ASSIGN

        override fun needCondition(): Boolean {
            return true
        }

        override fun getTitle(context: Context): String {
            return context.resources.getString(R.string.joywork_task_assignor)
        }

        override fun getIconId(): Int {
            return R.string.icon_general_send
        }

        override fun getViewId(): String {
            return joinId("assign")
        }

        override fun getViewType(): String {
            return MenuBean.ViewType.PERSONAL_VIEW.code
        }
    }

    // 我的清单
    open class Project(open val data: JoyWorkProjectList.ListDTO) : MenuItem() {
        override fun getTitle(context: Context): String {
            return data.title
        }

        override fun getIconId(): Int {
            return R.string.icon_project
        }

        override fun getListType(): TaskListTypeEnum {
            return TaskListTypeEnum.PROJECT
        }

        // 两方法无用
        override fun getViewId(): String {
            return data.projectId
        }

        override fun getViewType(): String {
//            return joinId("project_list")
            return "PERSONAL_PROJECT"
        }

        override fun needCondition(): Boolean = true
    }

    // 已归档清单
    class ArchiveProject(override val data: JoyWorkProjectList.ListDTO) : Project(data) {
        override fun getListType(): TaskListTypeEnum {
            return TaskListTypeEnum.ARCHIVE_PROJECT
        }
    }

    object MyProjectList : MenuItem() {
        override fun getListType() = TaskListTypeEnum.PROJECT

        override fun getTitle(context: Context): String {
            return context.resources.getString(R.string.joywork_tabbar_title_team)
        }

        override fun getIconId(): Int {
            return R.string.icon_project
        }

        // 这两个方法在此类中无用
        override fun getViewId(): String {
            return joinId("project_list")
        }

        override fun getViewType(): String {
            return "project_list"
        }
    }

    class ExpandMenuItem(val data: MenuBean): MenuItem() {
        override fun needCondition(): Boolean {
            return true
        }

        // 自定义视图
        override fun getTitle(context: Context): String {
            return data.title ?: ""
        }

        override fun getIconId(): Int {
            return -1
        }

        override fun getViewId(): String {
            return data.id
        }

        override fun getViewType(): String {
            return data.viewType
        }

        override fun getListType() = TaskListTypeEnum.HANDLE

        override val canCreateTask: Boolean = data.canCreateTask

        val iconUrl: String
            get() = data.icon

        val mobileEvent: String
            get() = data.mobileEvent

    }

    protected fun joinId(key: String): String {
        return listOf(
            PreferenceManager.UserInfo.getUserId(),
            PreferenceManager.UserInfo.getTeamId(),
            key
        ).joinToString(separator = "_\$_")
    }
}

class JoyWork2MenuViewModel : ViewModel() {
    private val _myHandleLiveData = MutableLiveData<Pair<List<MenuBean>, Map<String, Int>>>()
    val myHandleLiveData: LiveData<Pair<List<MenuBean>, Map<String, Int>>> = _myHandleLiveData

    fun fetchMyHandle() {
        viewModelScope.safeLaunch {
            try {
                val requestContent = async { MenuRepo.getMyHandle() }
                val requestTodoNum = async { MenuRepo.getNumber() }
                _myHandleLiveData.value = Pair(requestContent.await(), requestTodoNum.await())
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
    }

    // 选中的菜单项
    private val _mSelectMenuItemLiveData =
        MutableLiveData<MenuItem>(MenuItem.MyHandle)

    val mSelectMenuItemLiveData: LiveData<MenuItem> = _mSelectMenuItemLiveData

    fun updateSelectMenuItem(position: MenuItem): Boolean {
        if (_mSelectMenuItemLiveData.value == position) {
            return false
        }
        _mSelectMenuItemLiveData.postValue(position)
        return true
    }

    // 菜单列表返回结果
    private val _mMenuItemLiveData =
        MutableLiveData<ArrayList<MenuItem.Custom>>()
    val mMenuItemLiveData: LiveData<ArrayList<MenuItem.Custom>> = _mMenuItemLiveData

    private val _mAllTypeProjectLiveData =
        MutableLiveData<Triple<List<MenuItem.Project>, List<MenuItem.ArchiveProject>, Boolean>>()
    val mAllTypeProjectLiveData:
            LiveData<Triple<List<MenuItem.Project>, List<MenuItem.ArchiveProject>, Boolean>> =
        _mAllTypeProjectLiveData

    fun getProjectItems(append: Boolean, offset: Int = 0) {
        viewModelScope.safeLaunch {
            /*
            * 使用 async 绑定两个接口请求并不是在逻辑上两接口有依赖，只是在 UI 展示上整合两个接口数据更方便，
            * 因此当其中一个接口数据请求失败时，不能影响另一个接口。
            */
            _mAllTypeProjectLiveData.value = try {
                val projectListDeferred = async {
                    runCatching { getProjectList(offset) }.getOrElse {
                        emptyList()
                    }
                }
                val archiveListDeferred = async {
                    runCatching { getArchiveList() }.getOrElse {
                        emptyList()
                    }
                }

                // 等待请求结果
                val projectList = projectListDeferred.await()
                val archiveList = archiveListDeferred.await()

                Triple(projectList, archiveList, append)
            } catch (e: Throwable) {
                Triple(emptyList(), emptyList(), append)
            }
        }
    }

    fun getCustomView() {
        viewModelScope.safeLaunch {
            try {
                val value = ArrayList<MenuItem.Custom>()
                MenuRepo.getCustomViewList().forEach { item ->
                    value.add(
                        MenuItem.Custom(item)
                    )
                }
                _mMenuItemLiveData.postValue(value)
            } catch (e: Throwable) {
                // 列表失败，此处不需要处理
                e.printStackTrace()
            }
        }
    }

    private suspend fun getProjectList(offset: Int) =
        suspendCancellableCoroutine<List<MenuItem.Project>> { c ->
            viewModelScope.safeLaunch {
                ProjectRepo.projectList(
                    object : JoyWorkProjectCallback() {
                        override fun call(wrapper: JoyWorkProjectList, rawData: String) {
                            val newList =
                                wrapper.list ?: ArrayList<JoyWorkProjectList.ListDTO>()
                            val list = newList.map {
                                MenuItem.Project(it)
                            }
                            c.resumeWith(Result.success(list))
                        }

                        override fun onError(msg: String) {
                            ToastUtils.showToast(msg)
                            c.resumeWithException(JoyWorkException)
                        }
                    }, offset
                )
            }
    }

    private suspend fun getArchiveList() =
        suspendCancellableCoroutine<List<MenuItem.ArchiveProject>> { c ->
            viewModelScope.safeLaunch {
                ProjectRepo.getArchiveList(
                    object : JoyWorkProjectCallback() {
                        override fun call(wrapper: JoyWorkProjectList, rawData: String) {
                            val newList =
                                wrapper.list ?: ArrayList<JoyWorkProjectList.ListDTO>()
                            val list = newList.map {
                                MenuItem.ArchiveProject(it)
                            }
                            c.resumeWith(Result.success(list))
                        }

                        override fun onError(msg: String) {
                            c.resumeWithException(JoyWorkException)
                        }
                    }
                )
            }
        }
}