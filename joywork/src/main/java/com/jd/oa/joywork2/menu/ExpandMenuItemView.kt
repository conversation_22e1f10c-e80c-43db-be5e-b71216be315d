package com.jd.oa.joywork2.menu

import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.Router
import com.google.gson.reflect.TypeToken
import com.jd.oa.joywork.R
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.sp.JoyWorkPreference.KV_ENTITY_JDME_MY_TASK_HANDLE_List
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.router.DeepLink
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.Constants
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.grayResourceSwitchEnable

/**
 * @Author: hepiao3
 * @CreateTime: 2024/12/4
 * @Description:
 */
class ExpandMenuItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : BaseMenuItemView(context, attrs, defStyleAttr) {
    private var isExpanded = true
    private val recyclerView: RecyclerView by lazy { findViewById(R.id.childMenuContainer) }
    private val toggleButton: IconFontView by lazy { findViewById(R.id.toggle_button) }
    private val taskSetting: IconFontView by lazy { findViewById(R.id.task_setting) }
    private val expandMenuAdapter: ExpandMenuAdapter by lazy {
        // 只有首次创建时才会去获取本地缓存
        val type = object : TypeToken<List<MenuBean>>() {}.type
        val menuListString = JoyWorkPreference(context).get(KV_ENTITY_JDME_MY_TASK_HANDLE_List)
        ExpandMenuAdapter(
            context,
            if (menuListString.isNotEmpty() && Constants.KEY_JOY_WORK_MY_HANDLE.grayResourceSwitchEnable()) {
                JsonUtils.getGson().fromJson<List<MenuBean>>(menuListString, type).toMutableList()
            } else {
                arrayListOf()
            }
        )
    }

    init {
        orientation = VERTICAL
        view = LayoutInflater.from(context).inflate(R.layout.joywork2_menu_expand_view, this, true)
        recyclerView.apply {
            this.adapter = expandMenuAdapter
            layoutManager = LinearLayoutManager(context)
        }
        toggleButton.setOnClickListener {
            if (isExpanded) collapse() else expand()
        }
    }

    fun selectedMenuItem(viewId: String, isSelected: Boolean) {
        expandMenuAdapter.selectedMenuItem(viewId, isSelected)
    }

    private fun expand() {
        recyclerView.visibility = VISIBLE
        toggleButton.text = resources.getString(R.string.icon_smalldown)
        isExpanded = true
    }

    private fun collapse() {
        recyclerView.visibility = GONE
        toggleButton.text = resources.getString(R.string.icon_smallright)
        isExpanded = false
    }

    override fun setOnClickMenuItem(callback: (View) -> Unit) {
        contentView.setOnClickListener { callback(view) }
        expandMenuAdapter.setMenuOnClick { callback(it) }
        taskSetting.apply {
            tag = this
            setOnClickListener {
                callback(this)
                val url = Uri.parse(DeepLink.SETTING_TASK).buildUpon()
                Router.build(url.build().toString()).go(context)
            }
            if (MultiAppConstant.isSaasFlavor()) this.isVisible = false
        }
    }

    fun updateItemList(
        combineResult: Pair<List<MenuBean>, Map<String, Int>>,
        callback: () -> Unit
    ) {
        toggleButton.isVisible = combineResult.first.isNotEmpty()
        if (expandMenuAdapter.replaceAllData(combineResult)) callback()
    }
}
