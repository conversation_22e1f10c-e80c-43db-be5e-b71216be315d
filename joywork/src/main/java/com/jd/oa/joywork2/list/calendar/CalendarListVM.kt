package com.jd.oa.joywork2.list.calendar

import androidx.lifecycle.viewModelScope
import com.haibin.calendarview.Calendar
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.ProjectErrorCode
import com.jd.oa.joywork2.list.WorkListBaseViewModel
import com.jd.oa.joywork2.list.grouper.IWorkListGrouper
import com.jd.oa.joywork2.list.grouper.WorkListWrapper
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.joywork2.menu.fillHashMap
import com.jd.oa.network.post
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.safeLaunch

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/11 23:01
 */
class CalendarListVM : WorkListBaseViewModel() {

    override val listGrouper: IWorkListGrouper
        get() = throw NotImplementedError()

    fun loadDataByTime(
        calendar: Calendar,
        menuItem: MenuItem,
        statusEnum: TaskStatusEnum,
        offset: Int = 0,
        dynamicLimitSize: Int
    ) {
        viewModelScope.safeLaunch {
            val response = post<GetByTimeResp>(action = "joywork.getByTime") {
                val param = mutableMapOf<String, Any>(
                    Pair("start", DateUtils.getDailyStartTime(calendar.timeInMillis)),
                    Pair("end", DateUtils.getDailyEndTime(calendar.timeInMillis)),
                    Pair("taskStatus", statusEnum.code),
                    //移动端写死true
                    Pair("dayIncludeOverdue", true),
                    Pair("offset", offset),
                    Pair("limit", dynamicLimitSize),
                )
                val menuParam = HashMap<String, Any>()
                menuItem.fillHashMap(menuParam)
                val role = when (menuItem) {
                    MenuItem.MyHandle -> TaskUserRole.OWNER
                    MenuItem.MyCooperation -> TaskUserRole.EXECUTOR
                    MenuItem.MyAssign -> TaskUserRole.ASSIGN
                    else -> {
                        null
                    }
                }
                role?.run {
                    addParams(menuParam)
                }
                param.putAll(menuParam)
                param
            }
            if (response.isSuccessful) {
                val wrapper = extractWorkList(response.data)
                if (offset == 0) {
                    updateData(DataState.RefreshResult(true, wrapper.works))
                } else {
                    val complete = offset >= wrapper.total
                    updateData(
                        DataState.LoadMoreResult(
                            if (complete) LOAD_MORE_COMPLETE else LOAD_MORE_FINISH,
                            wrapper.works
                        )
                    )
                }

            } else {
                val target = ProjectErrorCode.values()
                    .firstOrNull { it.code == response.errorCode }
                if (target != null
                ) {
                    if (offset == 0) {
                        updateData(
                            DataState.RefreshResult(
                                false, msg = JoyWorkEx.filterErrorMsg(
                                    target.hint
                                )
                            )
                        )
                    } else {
                        updateData(
                            DataState.LoadMoreResult(
                                LOAD_MORE_FAIL, msg = JoyWorkEx.filterErrorMsg(
                                    target.hint
                                )
                            )
                        )
                    }
                } else {
                    if (offset == 0) {
                        updateData(
                            DataState.RefreshResult(
                                false, msg = JoyWorkEx.filterErrorMsg(response.errorMessage)
                            )
                        )
                    }
                }
            }
            updateRefreshing(false)
        }
    }

    private fun extractWorkList(resp: GetByTimeResp?): WorkListWrapper {
        val workList = mutableListOf<JoyWork>()
        var total = 0
        resp?.task?.forEach {
            it.list?.run {
                workList.addAll(this)
            }
            total = it.total
        }
        return WorkListWrapper(workList, total)
    }

    class GetByTimeResp {
        var task: List<GetByTimeItem>? = null
    }

    class GetByTimeItem {
        var start: Long = 0
        var end: Long = 0
        var list: List<JoyWork>? = null
        var total: Int = 0
    }
}