package com.jd.oa.joywork2.list.calendar

import com.jd.oa.joywork.todayTime
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.LocaleUtils
import java.text.DateFormatSymbols
import java.util.Calendar
import java.util.Date


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/14 16:27
 */
class CalendarHelper {

    companion object {
        private const val MAX_VALUE = Int.MAX_VALUE
        private const val MIDDLE_VALUE = MAX_VALUE / 2


        fun timeInMillisToCalendar(timeInMillis: Long): com.haibin.calendarview.Calendar {
            val calender = Calendar.getInstance()
            calender.timeInMillis = timeInMillis
            return com.haibin.calendarview.Calendar().apply {
                year = calender.get(Calendar.YEAR)
                month = calender.get(Calendar.MONTH) + 1
                day = calender.get(Calendar.DATE)
            }
        }
    }

    val today: com.haibin.calendarview.Calendar = com.haibin.calendarview.Calendar().apply {
        val d = Date()
        year = DateUtils.getDate("yyyy", d)
        month = DateUtils.getDate("MM", d)
        day = DateUtils.getDate("dd", d)
        isCurrentDay = true
    }

    fun getMaxValue(): Int = MAX_VALUE

    fun getMiddleValue(): Int = MIDDLE_VALUE


    fun getTargetCalendar(targetPos: Int): com.haibin.calendarview.Calendar {
        val diff = targetPos - MIDDLE_VALUE
        if (diff == 0) {
            return today
        }
        val calender = Calendar.getInstance()
        calender.timeInMillis = today.timeInMillis
        calender.add(Calendar.DATE, diff)
        return com.haibin.calendarview.Calendar().apply {
            year = calender.get(Calendar.YEAR)
            month = calender.get(Calendar.MONTH) + 1
            day = calender.get(Calendar.DATE)
        }
    }

    fun getTargetPosition(calendar: com.haibin.calendarview.Calendar): Int {
        val diff = today.differ(calendar)
        return getMiddleValue() - diff
    }

    fun jumpWeek(
        selected: com.haibin.calendarview.Calendar,
        next: Boolean
    ): com.haibin.calendarview.Calendar {
        val calender = Calendar.getInstance()
        calender.timeInMillis = selected.timeInMillis
        calender.add(Calendar.DATE, if (next) 7 else -7)
        return com.haibin.calendarview.Calendar().apply {
            year = calender.get(Calendar.YEAR)
            month = calender.get(Calendar.MONTH) + 1
            day = calender.get(Calendar.DATE)
        }
    }

    fun currentMonth(calendar: com.haibin.calendarview.Calendar = today): String {
        return runCatching {
            val locale = LocaleUtils.getSystemLocale()
            when (locale.language) {
                "en" -> {
                    val shortMonth = DateFormatSymbols().shortMonths[calendar.month - 1]
                    "${calendar.year} $shortMonth"
                }

                "zh" -> {
                    "${calendar.year} ${calendar.month}月"
                }

                else -> {
                    "${calendar.year} ${calendar.month}月"
                }
            }
        }.getOrNull() ?: ""
    }

    fun currentWeek(
        calendar: com.haibin.calendarview.Calendar = today,
        monIsFirstDayOfWeek: Boolean = true
    ): String {
        return runCatching {
            val cal = Calendar.getInstance()
            cal.timeInMillis = calendar.timeInMillis
            val locale = LocaleUtils.getSystemLocale()
            if (monIsFirstDayOfWeek)
                cal.firstDayOfWeek = Calendar.MONDAY
            else
                cal.firstDayOfWeek = Calendar.SUNDAY
            val weekOfYear = cal.get(Calendar.WEEK_OF_YEAR)
            when (locale.language) {
                "en" -> {
                    "W${weekOfYear}"
                }

                "zh" -> {
                    "第${weekOfYear}周"
                }

                else -> {
                    "第${weekOfYear}周"
                }
            }
        }.getOrNull() ?: ""
    }

    fun createWorkOnDay(calendar: com.haibin.calendarview.Calendar): Long {
        if (calendar == today) return todayTime()
        val diff = calendar.differ(today)
        val calender = Calendar.getInstance()
        calender.timeInMillis = today.timeInMillis
        calender.add(Calendar.DATE, diff)
        return DateUtils.getSpecialHour(calender.timeInMillis, 16)
    }

}