package com.jd.oa.joywork2.list.kanban

import android.view.View
import androidx.fragment.app.activityViewModels
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.ProjectDelete
import com.jd.oa.joywork.ProjectNewAfter
import com.jd.oa.joywork.ProjectNewBefore
import com.jd.oa.joywork.ProjectRename
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork2.menu.JoyWork2MenuViewModel
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.string

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/23 16:59
 */
class SelfKanbanListContainer : KanbanListContainer() {

    companion object {
        const val TAG = "SelfKanbanListContainer"
    }

    private val mMenuViewModel: JoyWork2MenuViewModel by activityViewModels()
    override fun getDefaultGroupTitle(): String {
        return context?.string(R.string.joywork_self_default_group) ?: ""
    }

    override fun getProjectId(): String {
        val self = JoyWorkUser.getSelf()
        return "${self.userId}_" + "$" + "_${self.teamId}"
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return mMenuViewModel.mSelectMenuItemLiveData.value?.getListType()
            ?: TaskListTypeEnum.HANDLE
    }

    override val isTeamList: Boolean = false

    override fun onCreateGroup(view: View, group: Group?, groups: List<Group>?) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.taskListTypeEnum = getTaskListType()
        if (tmpData.taskListTypeEnum == TaskListTypeEnum.HANDLE) {
            val self = JoyWorkUser.getSelf();
            self.setToChief()
            tmpData.owners = listOf(self)
        }
        tmpData.selectGroupId = group?.groupId
//        //个人待办的非自定义分组，不展示分组，因此也不传递projectId
//        val projectId = if (isCustomGroup()) getProjectId() else null
        val dialogO =
            JoyWorkShortcutCreator.getShortcutDialog(
                requireActivity(),
                tmpData = tmpData,
                showGroup = isCustomGrouper(),
                projectId = getProjectId(),
                groups = groups,
                conditionViewModel.viewIdFactory?.invoke(),
                conditionViewModel.viewTypeFactory?.invoke()
            )
        dialogO.showSnackBar = true
        dialogO.successCallback = { result, value, dialog ->
            dialog?.dismiss()
            var groupId = value.groupId
            if (groupId.isNullOrEmpty()) {
                groupId = value.selId
            }
            listContainerInheritedVM.onCreateWorkSuccess(groupId, result)
        }
        dialogO.show()
    }

    override fun onGroupActionSelected(action: JoyWorkAction) {
        when (action) {
            is ProjectRename -> {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_MORE_RENAME
                )
            }

            is ProjectNewAfter -> {
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_INSERTGROUPRIGHT
                    )
                }
            }

            is ProjectNewBefore -> {

                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_INSERTGROUPLEFT
                    )
                }
            }

            is ProjectDelete -> {

                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_GROUP_MORE_DELETE
                    )
                }
            }
        }
    }

    override fun hasPermission(permissionEnum: ProjectPermissionEnum): Boolean = true

    override fun getIsArchiveProject(): Boolean = false
}