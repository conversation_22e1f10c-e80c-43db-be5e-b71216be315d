package com.jd.oa.joywork2.list.listener

import android.view.View
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork2.list.IWorkListHost

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/13 23:43
 */
class SimpleItemClickListener(private val workListHost: IWorkListHost, private val work: JoyWork) :
    IViewClickListener {
    override val host: IWorkListHost
        get() = workListHost

    override fun onClick(v: View?) {
        gotoDetail(work, false)
    }
}