package com.jd.oa.joywork2.backend

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.joywork.dialog.thirdparty.ListShitDialogAdapter
import com.jd.oa.joywork2.backend.CoroutineUtils.isSafe
import com.jd.oa.joywork2.backend.CoroutineUtils.safeResumeWith
import com.jd.oa.joywork2.backend.CoroutineUtils.safeResumeWithException
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.joywork2.main.bean.ConditionSorter
import com.jd.oa.joywork2.view.PopDialog
import com.jd.oa.utils.bold
import com.jd.oa.utils.gone
import com.jd.oa.utils.normal
import com.jd.oa.utils.visible
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.Objects

suspend fun showGrouperDialog2(
    context: Context,
    anchor: View,
    currentValue: ConditionGrouper,
    filter: ((ConditionGrouper.Field) -> Boolean)? = null
) = suspendCancellableCoroutine<ConditionGrouper> { c ->
    val data = ConditionGrouper.Field.values().toList().filter {
        filter?.invoke(it) ?: true
    }
    val dialog = PopDialog(context)

    val adapter = object : ListShitDialogAdapter<ConditionGrouper.Field>(data) {
        private fun isCurrent(field: ConditionGrouper.Field): Boolean {
            return Objects.equals(currentValue.field, field.value)
        }

        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            val view = context.layoutInflater.inflate(
                R.layout.joywork2_status_grouper_item,
                parent,
                false
            )
            val tv: TextView = view.findViewById(R.id.content)
            val d: ConditionGrouper.Field = items[position]
            tv.setText(d.textId)
            view.tag = d
            view.setOnClickListener {
                if (c.isSafe()) {
                    val tagData = it.tag as ConditionGrouper.Field
                    c.resumeWith(Result.success(ConditionGrouper(tagData)))
                    dialog.dismiss()
                }
            }
            val check = view.findViewById<TextView>(R.id.check)
            if (isCurrent(d)) {
                check.visible()
                tv.bold()
                tv.isSelected = true
                check.isSelected = true
            } else {
                check.gone()
                tv.normal()
                tv.isSelected = false
                check.isSelected = false
            }
            return view
        }
    }
    dialog.setOnDismissListener {
        c.safeResumeWithException(JoyWorkException)
    }
    dialog.setAdapter(adapter)
    dialog.showAsDropDown(anchor)
}

suspend fun showStatusDialog2(
    context: Context,
    anchor: View,
    currentStatus: TaskStatusEnum,
    filter: ((List<TaskStatusEnum>) -> List<TaskStatusEnum>)? = null
) = suspendCancellableCoroutine<TaskStatusEnum> { c ->
    val data = mutableListOf<TaskStatusEnum>()
    data.add(TaskStatusEnum.RISK)
    data.add(TaskStatusEnum.UN_FINISH)
    data.add(TaskStatusEnum.FINISH)
    val dialog = PopDialog(context)
    val adapter = object : ListShitDialogAdapter<TaskStatusEnum>(filter?.invoke(data) ?: data) {
        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            val view = context.layoutInflater.inflate(
                R.layout.joywork2_status_grouper_item,
                parent,
                false
            )
            val tv: TextView = view.findViewById(R.id.content)
            val d: TaskStatusEnum = items[position]
            tv.setText(d.getStringId())
            view.tag = d
            view.setOnClickListener {
                if (c.isSafe()) {
                    val tagData = it.tag as TaskStatusEnum
                    c.resumeWith(Result.success(tagData))
                    dialog.dismiss()
                }
            }
            val check = view.findViewById<TextView>(R.id.check)
            if (currentStatus == d) {
                check.visible()
                tv.bold()
                tv.isSelected = true
                check.isSelected = true
            } else {
                check.gone()
                tv.normal()
                tv.isSelected = false
                check.isSelected = false
            }
            return view
        }
    }
    dialog.setAdapter(adapter)
    dialog.setOnDismissListener {
        c.safeResumeWithException(JoyWorkException)
    }
    dialog.showAsDropDown(anchor)
}

suspend fun showSorterDialog2(
    context: Context,
    anchor: View,
    currentValue: ConditionSorter,
    filter: ((List<ConditionSorter.Field>) -> List<ConditionSorter.Field>)? = null
) = suspendCancellableCoroutine<ConditionSorter> { c ->
    val data = ConditionSorter.Field.values().toList()
    val dialog = PopDialog(context)
    val adapter = object :
        ListShitDialogAdapter<ConditionSorter.Field>(if (filter == null) data else filter(data)) {

        private val mArrowClick = View.OnClickListener {
            val condition: ConditionSorter.Field =
                it.getTag(R.id.jdme_tag_id) as ConditionSorter.Field
            val order: ConditionSorter.Order = it.tag as ConditionSorter.Order
            success(condition, order)
        }

        private fun isCurrent(field: ConditionSorter.Field): Boolean {
            return Objects.equals(currentValue.field, field.value)
        }

        private fun success(field: ConditionSorter.Field, order: ConditionSorter.Order) {
            success(ConditionSorter(field, order))
        }

        private fun success(sorter: ConditionSorter) {
            if (c.isSafe()) {
                c.resumeWith(Result.success(sorter))
                dialog.dismiss()
            }
        }

        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            val view = context.layoutInflater.inflate(
                R.layout.joywork2_sorter_item,
                parent,
                false
            )
            val tv: TextView = view.findViewById(R.id.content)
            val asc = view.findViewById<TextView>(R.id.asc)
            val desc = view.findViewById<TextView>(R.id.desc)
            val d: ConditionSorter.Field = items[position]
            tv.setText(d.textId)
            view.tag = d
            view.setOnClickListener {
                val tagData = it.tag as ConditionSorter.Field
                if (isCurrent(tagData)) {
                    success(currentValue)
                } else {
                    success(tagData, ConditionSorter.Order.ASC)
                }
            }
            if (isCurrent(d)) {
                tv.bold()
                tv.isSelected = true
                // 自定义 需要单独处理
                if (d == ConditionSorter.Field.CUSTOM) {
                    asc.gone()
                    desc.isSelected = true
                    desc.setText(R.string.icon_prompt_check)
                } else {
                    asc.visible()
                    asc.tag = ConditionSorter.Order.ASC
                    asc.setTag(R.id.jdme_tag_id, d)
                    asc.setOnClickListener(mArrowClick)
                    asc.isSelected =
                        Objects.equals(currentValue.order, ConditionSorter.Order.ASC.value)

                    desc.isSelected =
                        Objects.equals(currentValue.order, ConditionSorter.Order.DESC.value)
                    desc.setText(R.string.icon_direction_arrowdown)
                }

                desc.visible()
                desc.setOnClickListener(mArrowClick)
                desc.tag = ConditionSorter.Order.DESC
                desc.setTag(R.id.jdme_tag_id, d)
            } else {
                tv.normal()
                asc.gone()
                desc.gone()
                tv.isSelected = false
            }
            return view
        }
    }
    dialog.setAdapter(adapter)
    dialog.setOnDismissListener {
        c.safeResumeWithException(JoyWorkException)
    }
    dialog.showAsDropDown(anchor)
}