package com.jd.oa.joywork2.main

import android.content.Context
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWorkProjectList.TYPE_NORMAL
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork2.backend.JoyWorkException
import com.jd.oa.joywork2.backend.JoyWorkExceptionWithData
import com.jd.oa.joywork2.backend.showGrouperDialog2
import com.jd.oa.joywork2.backend.showSorterDialog2
import com.jd.oa.joywork2.backend.showStatusDialog2
import com.jd.oa.joywork2.bean.ListColumn
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.joywork2.main.bean.ConditionFilter
import com.jd.oa.joywork2.main.bean.ConditionGrouper
import com.jd.oa.joywork2.main.bean.ConditionOperator
import com.jd.oa.joywork2.main.bean.ConditionSorter
import com.jd.oa.joywork2.main.bean.compatibleWithCardViewType
import com.jd.oa.joywork2.main.bean.compatibleWithSystemView
import com.jd.oa.joywork2.main.bean.finishStatusGroupList
import com.jd.oa.joywork2.main.bean.not
import com.jd.oa.joywork2.main.bean.was
import com.jd.oa.joywork2.menu.MenuBean
import com.jd.oa.joywork2.menu.MenuItem
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.flow.MutableSharedFlow
import java.util.Objects

fun ConditionState.Condition.fillHashMap(params: HashMap<String, Any>) {
    params["taskStatus"] = status.code
    params["filter"] = filter.filter()
    params["sorter"] = sorter.toMap()
    params["grouper"] = grouper.toMap()
}


sealed class ConditionState {
    object Nothing : ConditionState() // 不需要状态、分组、排序
    object Loading : ConditionState() // 正在从网络请求状态、分组、排序信息
    data class Condition(
        val status: TaskStatusEnum,
        val sorter: ConditionSorter,
        val filter: ConditionFilter,
        val grouper: ConditionGrouper,
        val listColumns: List<ListColumn>?,
        val operator: ConditionOperator?,
        val isDefault: Boolean,
    ) : ConditionState() // 拿到状态、分组、排序信息
}


class ConditionViewModel : ViewModel() {

    private var mViewIdFactory: (() -> String)? = null
    val viewIdFactory: (() -> String)?
        get() = mViewIdFactory

    private var mViewTypeFactory: (() -> String)? = null
    val viewTypeFactory: (() -> String)?
        get() = mViewTypeFactory

    var mMenuItemFactory: (() -> MenuItem)? = null

    //是否是清单页面
    private var isProjectList = false

    //是清单页面时用来区分是正常清单还是京东人事
    private var type = TYPE_NORMAL


    fun init(isProjectList: Boolean, type: Int = TYPE_NORMAL, menuItemFactory: () -> MenuItem) {
        <EMAIL> = {
            menuItemFactory().getViewId()
        }
        <EMAIL> = {
            menuItemFactory().getViewType()
        }
        this.mMenuItemFactory = menuItemFactory
        this.isProjectList = isProjectList
        this.type = type
    }

    val conditionContainerShow = MutableLiveData(false)
    fun toggleConditionContainer() {
        val old = conditionContainerShow.value
        conditionContainerShow.value = !(old ?: false)
    }

    /**
     * SharedFlow 可以配置为非粘性流，不会自动发送之前的值, replay = 0 时表示不保留任何历史值，
     * 当新的 Fragment 或者 Activity 创建时不会重复通知观察者。
     * 在每次 change MenuItem 时会创建新的JoyWork2GroupFragment实例，如果数据通过 LiveData 暂存，
     * 每次新的JoyWork2GroupFragment可见后（onResume）后，LiveData 已经通知过的旧值还是会再次通知给当前观察者，
     * 对于新的JoyWork2GroupFragment实例观察者来说这是一个脏数据，我们需要舍弃调。
     */
    // 状态、筛选、分组
    private val _mConditionValueLiveData = MutableSharedFlow<ConditionState>(replay = 0)
    // 为了避免较多的原始代码改动，这里将 SharedFlow 转换成 LiveData
    val mConditionValueLiveData: LiveData<ConditionState> = _mConditionValueLiveData.asLiveData()
    fun setCondition(value: ConditionState, needSync: Boolean) {
        if (needSync) {
            saveCondition(value)
        }
        viewModelScope.safeLaunch {
            _mConditionValueLiveData.emit(value)
        }
    }

    private fun saveCondition(
        value: ConditionState? = mConditionValueLiveData.value,
        listViewType: TodoShowType = showType
    ) {
        if (value !is ConditionState.Condition) {
            return
        }
        val type = mViewTypeFactory?.invoke()
        val id = mViewIdFactory?.invoke()
        if (type.isLegalString() && id.isLegalString()) {
            Tab2Repo.saveTabCondition(
                value, viewIdForProjectList(id!!), type!!, listViewType.value
            )
        }
    }

    //2024.08版本清单条件也按个人维度存储了
    //但是仅限于存、取这两个接口更换viewId这个参数
    private fun viewIdForProjectList(viewId: String): String {
        if (isProjectList) {
            return listOf(
                PreferenceManager.UserInfo.getUserId(),
                PreferenceManager.UserInfo.getTeamId(),
                viewId
            ).joinToString(separator = "_\$_")
        }
        return viewId
    }

    //是否展示reset
    val resetButtonState = MutableLiveData<Boolean>()

    private fun updateResetState(visible: Boolean) {
//        val checkValue = if (!hasPermission()) false else visible
//        if (resetButtonState.value == checkValue) {
//            return
//        }
//        resetButtonState.value = visible
        if (resetButtonState.value == visible) {
            return
        }
        resetButtonState.value = visible
    }


    // 权限
    var permissions = emptyList<String>()
    fun updatePermissions(ls: List<String>?) {
        permissions = ls ?: emptyList()
//        val visible = resetButtonState.value!!
//        val toState = if (visible && !hasPermission()) {
//            false
//        } else {
//            visible
//        }
//        updateResetState(toState)
    }


    private fun hasPermission(): Boolean =
        if (isProjectList) permissions.contains(ProjectPermissionEnum.View.code)
        else true


    suspend fun initCondition(
        viewIdFactory: () -> String = mViewIdFactory ?: { "" },
        viewTypeFactory: () -> String = mViewTypeFactory ?: { "" },
    ) {
        try {
            setCondition(ConditionState.Loading, false)

            val ans = Tab2Repo.getTabCondition(
                viewIdForProjectList(viewIdFactory.invoke()), viewTypeFactory.invoke()
            )
            val sameWithRequest = if (isProjectList) {
                //清单viewType返回可能不一致，自测试到了这种情况
                true
            } else {
                Objects.equals(ans.viewId, viewIdFactory.invoke()) && Objects.equals(
                    ans.viewType, viewTypeFactory.invoke()
                )
            }
            if (sameWithRequest) {
                updateResetState(!ans.isDefault)
                // 在「我指派的」「我关注的」的 viewType 如果为 kanban 直接替换成 card
                var type = TodoShowType.safeValueOf(ans.showType)
                if (type == TodoShowType.KANBAN && ans.toCondition().status == TaskStatusEnum.FINISH
                    && (mMenuItemFactory?.invoke() == MenuItem.MyCooperation
                            || mMenuItemFactory?.invoke() == MenuItem.MyAssign
                            || mMenuItemFactory?.invoke() is MenuItem.Custom)) {
                    type = TodoShowType.CARD
                }
                updateListViewType(type, true)
                showTypeDisableLiveData = ans.showTypeDisable
                calendarTypeDisable = ans.calendarTypeDisable
                val condition = fixupCondition(ans.toCondition(), viewTypeFactory())
                // 只有返回的结果是当前需要的才保存
                setCondition(condition, false)
            }
        } catch (e: Exception) {
            val triple = (e as? JoyWorkExceptionWithData)?.data as? Triple<String, String, String>
            if (Objects.equals(viewIdFactory.invoke(), triple?.first) && Objects.equals(
                    viewTypeFactory.invoke(), triple?.second
                )
            ) {
                // 只有当前条件的请求才能生效
                setCondition(
                    defaultCondition(), false
                )
            }
        }
    }


    private fun getDefaultSorter(): ConditionSorter {
        return ConditionSorter(
            if (isProjectList) ConditionSorter.Field.CUSTOM
            else ConditionSorter.Field.END_TIME, ConditionSorter.Order.ASC
        )
    }

    private fun getDefaultFilter(): ConditionFilter {
        return ConditionFilter.getDefaultInstance()
    }

    private fun getDefaultGrouper(): ConditionGrouper {
        return ConditionGrouper(
            if (isProjectList) ConditionGrouper.Field.CUSTOM
            else ConditionGrouper.Field.END_TIME
        )
    }

    /**
     * 后台返回的 status 是完成时，
     * 如果从完成切到未完成时，由于没有前置数据，界面会有问题
     */
    private fun fixupCondition(
        condition: ConditionState.Condition, viewType: String
    ): ConditionState.Condition {
        val grouper = if (viewType == MenuBean.ViewType.SYS_VIEW.code) {
            val grouper = if (condition.grouper.compatibleWithSystemView())
                condition.grouper
            else
                ConditionGrouper(ConditionGrouper.Field.SOURCE)
            grouper.apply {
                disable = true
            }
        } else {
            if (showType == TodoShowType.KANBAN) {
                if (condition.grouper.compatibleWithCardViewType()) condition.grouper
                else ConditionGrouper(ConditionGrouper.Field.CUSTOM)
            } else {
                condition.grouper
            }
        }
        val sorter = condition.sorter.apply {
            disable = grouper.field == ConditionGrouper.Field.FINISH_TIME.value
            if (condition.status == TaskStatusEnum.FINISH && field == ConditionSorter.Field.CUSTOM.value) {
                order = ConditionSorter.Order.ASC.value
                field = ConditionSorter.Field.END_TIME.value
            }
        }

        return condition.copy(
            sorter = sorter, grouper = grouper
        )
    }

    /**
     * 状态切换，要考虑已完成跟其他状态之间的切换，有些值是互斥的，比如sort的拖拽和已完成状态是互斥的
     */
    private fun updateStatus(status: TaskStatusEnum, viewType: String?) {
        val curValue = curCondition ?: return
        updateResetState(true)
        if (curValue.status == status) {
            return
        }
        if (status == TaskStatusEnum.FINISH
            && (mMenuItemFactory?.invoke() == MenuItem.MyAssign
                    || mMenuItemFactory?.invoke() == MenuItem.MyCooperation
                    || mMenuItemFactory?.invoke() is MenuItem.Custom)) {
            updateListViewType(TodoShowType.CARD, false)
        }
        val condition = if (isProjectList) {
            updateProjectViewStatus(status, curValue)
        } else {
            if (viewType == MenuBean.ViewType.SYS_VIEW.code) {
                updateOtherViewStatus(status, curValue)
            } else {
                updatePersonalViewStatus(status, curValue)
            }
        }
        setCondition(condition, true)
        val eventId = when(status) {
            TaskStatusEnum.UN_FINISH -> JoyWorkConstant.JOYWORK_MYEXECUTION_COMPLETE
            TaskStatusEnum.RISK -> JoyWorkConstant.MOBILE_EVENT_TASKS_VIEW_FILTER_RISK
            else -> null
        }
        eventId?.let {
            clickEvent {
                ClickEventParam(
                    eventId = eventId
                )
            }
        }
    }

    /**
     * 切换状态经常有自定义关联切换，适合进行细化拆分
     * 清单视图切换状态
     */
    private fun updateProjectViewStatus(
        toStatus: TaskStatusEnum, curCondition: ConditionState.Condition
    ): ConditionState.Condition {
        val condition = if (toStatus == TaskStatusEnum.FINISH) {
            val toGrouper = if (showType == TodoShowType.KANBAN)
                getDefaultGrouper()
            else
                ConditionGrouper(ConditionGrouper.Field.FINISH_TIME)
            val toSorter = if (curCondition.sorter.field == ConditionSorter.Field.CUSTOM.value) {
                ConditionSorter(ConditionSorter.Field.END_TIME, ConditionSorter.Order.ASC)
            } else curCondition.sorter
            // 变为完成状态
            curCondition.copy(
                status = toStatus,
                sorter = toSorter,
                grouper = toGrouper
            )
        } else if (curCondition.status == TaskStatusEnum.FINISH) {
            //完成状态变为其他状态
            val sorterGrouperPair = if (curCondition.sorter.disable) {
                Pair(getDefaultSorter(), getDefaultGrouper())
            } else {
                Pair(curCondition.sorter, curCondition.grouper)
            }
            curCondition.copy(
                status = toStatus,
                sorter = sorterGrouperPair.first,
                grouper = sorterGrouperPair.second
            )
        } else {
            curCondition.copy(status = toStatus)
        }
        condition.sorter.disable = condition.status == TaskStatusEnum.FINISH &&
                condition.grouper was ConditionGrouper.Field.FINISH_TIME
        return condition
    }

    /**
     * 个人视图切换状态
     */
    private fun updatePersonalViewStatus(
        toStatus: TaskStatusEnum, curCondition: ConditionState.Condition
    ): ConditionState.Condition {
        val condition = if (toStatus == TaskStatusEnum.FINISH) {
            //变为完成状态
            val toGrouper = if (showType == TodoShowType.KANBAN)
                ConditionGrouper(ConditionGrouper.Field.CUSTOM)
            else
                ConditionGrouper(ConditionGrouper.Field.FINISH_TIME)
            val toSorter = if (curCondition.sorter.field == ConditionSorter.Field.CUSTOM.value) {
                ConditionSorter(ConditionSorter.Field.END_TIME, ConditionSorter.Order.ASC)
            } else curCondition.sorter
            curCondition.copy(
                status = toStatus,
                sorter = toSorter,
                grouper = toGrouper
            )
        } else if (curCondition.status == TaskStatusEnum.FINISH) {
            //完成状态变为其他状态
            val sorterGrouperPair = if (curCondition.sorter.disable) {
                val sorter = if (curCondition.sorter was ConditionSorter.Field.CUSTOM) {
                    ConditionSorter(
                        ConditionSorter.Field.END_TIME, ConditionSorter.Order.ASC
                    )
                } else {
                    curCondition.sorter
                }
                Pair(sorter, getDefaultGrouper())
            } else {
                Pair(curCondition.sorter, curCondition.grouper)
            }
            curCondition.copy(
                status = toStatus,
                sorter = sorterGrouperPair.first,
                grouper = sorterGrouperPair.second
            )
        } else {
            curCondition.copy(status = toStatus)
        }
        condition.sorter.disable = condition.status == TaskStatusEnum.FINISH &&
                condition.grouper was ConditionGrouper.Field.FINISH_TIME
        return condition
    }

    /**
     * 其他切换状态，如SYS_VIEW，会议待办
     */
    private fun updateOtherViewStatus(
        toStatus: TaskStatusEnum, curCondition: ConditionState.Condition
    ): ConditionState.Condition {
        val grouper = if (curCondition.grouper.compatibleWithSystemView())
            curCondition.grouper
        else
            ConditionGrouper(ConditionGrouper.Field.SOURCE)
        grouper.disable = true
        return curCondition.copy(status = toStatus,
            sorter = curCondition.sorter,
            grouper = grouper
        )
    }

    private fun updateSorter(sorter: ConditionSorter) {
        val curValue = curCondition ?: return
        updateResetState(true)
        setCondition(curValue.copy(sorter = sorter), true)
    }

    private fun updateGrouper(grouper: ConditionGrouper) {
        val curValue = curCondition ?: return
        if (curValue.grouper == grouper) {
            return
        }
        updateResetState(true)
        var sorter = curValue.sorter
        if (curValue.sorter was ConditionSorter.Field.CUSTOM) {
            if (curValue.status == TaskStatusEnum.FINISH || grouper not ConditionGrouper.Field.CUSTOM) {
                sorter = ConditionSorter(
                    ConditionSorter.Field.END_TIME, ConditionSorter.Order.ASC
                )
            }
        }
        sorter.disable =
            curValue.status == TaskStatusEnum.FINISH && grouper was ConditionGrouper.Field.FINISH_TIME

        setCondition(
            curValue.copy(
                grouper = grouper, sorter = sorter
            ), true
        )
    }

    fun showStatusDialog(context: Context, anchor: View) {
        // 条件需要从后台接口拉取，在接口返回之前没有数据。如果接口失败，会有默认数据
        // 此处为防止在接口返回之前用户点击
        val condition = curCondition ?: return
        val curStatus = condition.status
        viewModelScope.safeLaunch {
            try {
                updateEffect(Effect.StatusSelectedStatus(true))
                val status = showStatusDialog2(context, anchor, curStatus) { list ->
                    list
                    // 旧逻辑只有在我执行、指派、关注三个列表中才允许有已完成
//                    val curItem = mMenuItemFactory?.invoke() ?: return@showStatusDialog2 list
//                    if (!mItemsHadFinish.contains(curItem)) {
//                        list.toMutableList() - TaskStatusEnum.FINISH
//                    } else {
//                        list
//                    }
                }
                updateStatus(status, mMenuItemFactory?.invoke()?.getViewType())
            } catch (e: JoyWorkException) {
                // cancel
            } finally {
                updateEffect(Effect.StatusSelectedStatus(false))
            }
        }
    }

    fun showGrouperDialog(context: Context, anchor: View) {
        // 条件需要从后台接口拉取，在接口返回之前没有数据。如果接口失败，会有默认数据
        // 此处为防止在接口返回之前用户点击
        val condition = curCondition ?: return
        val curGrouper = condition.grouper
        viewModelScope.safeLaunch {
            try {
                updateEffect(Effect.GrouperSelectedStatus(true))
                val grouper = showGrouperDialog2(context, anchor, curGrouper) {
                    val compatibleWithCARDViewType =
                        when (showType) {
                            TodoShowType.KANBAN -> {
                                ConditionGrouper(it).compatibleWithCardViewType()
                            }
                            TodoShowType.CARD, TodoShowType.TABLE -> {
                                ConditionGrouper(it).finishStatusGroupList(condition)
                            }
                            else -> {
                                true
                            }
                        }
                    val result = when (it) {
                        ConditionGrouper.Field.CUSTOM -> {
                            // 「待我处理」、「我创建的」、「他人指派」、「清单」有自定义分组
                            viewTypeFactory?.invoke() == MenuBean.ViewType.SYS_PERSONAL_VIEW.code
                                    || mMenuItemFactory?.invoke() == MenuItem.MyHandle
                                    || (isProjectList && type == TYPE_NORMAL)
                        }

                        ConditionGrouper.Field.FINISH_TIME -> {
                            condition.status == TaskStatusEnum.FINISH && (showType != TodoShowType.KANBAN)
                        }

                        ConditionGrouper.Field.START_TIME, ConditionGrouper.Field.END_TIME -> {
                            condition.status != TaskStatusEnum.FINISH
                        }

                        else -> {
                            true
                        }
                    }
                    compatibleWithCARDViewType && result
                }
                updateGrouper(grouper)
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.JOYWORK_MYEXECUTION_GOUP
                    )
                }
            } catch (e: JoyWorkException) {
                // cancel
            } finally {
                updateEffect(Effect.GrouperSelectedStatus(false))
            }
        }
    }

    fun showSorterDialog(context: Context, anchor: View) {
        // 条件需要从后台接口拉取，在接口返回之前没有数据。如果接口失败，会有默认数据
        // 此处为防止在接口返回之前用户点击
        val condition = curCondition ?: return
        val curStatus = condition.sorter
        viewModelScope.safeLaunch {
            try {
                updateEffect(Effect.SorterSelectedStatus(true))
                val sorter = showSorterDialog2(context, anchor, curStatus) {
                    if (!customSorterEnable(curCondition?.grouper) || curCondition?.status == TaskStatusEnum.FINISH) {
                        it.toMutableList() - ConditionSorter.Field.CUSTOM
                    } else {
                        it
                    }
                }
                updateSorter(sorter)
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.JOYWORK_MYEXECUTION_RANK
                    )
                }
            } catch (e: JoyWorkException) {
                // cancel
            } finally {
                updateEffect(Effect.SorterSelectedStatus(false))
            }
        }
    }

    val curCondition: ConditionState.Condition?
        get() = mConditionValueLiveData.value as? ConditionState.Condition

    // 允许排序中有“自定义排序“的分组
    private val customSorterGrouper = listOf(ConditionGrouper.Field.CUSTOM.value)

//        listOf(ConditionGrouper.Field.NO.value, ConditionGrouper.Field.CUSTOM.value)

    private fun customSorterEnable(grouper: ConditionGrouper?) =
        customSorterGrouper.contains(grouper?.field)

    // effect 相关的 livedata
    private val _effectLiveData = MutableLiveData<Effect>()
    val effectLiveData: LiveData<Effect> = _effectLiveData
    fun updateEffect(effect: Effect) {
        _effectLiveData.value = effect
    }

    fun clearCondition() {
        Tab2Repo.clearCondition(mViewIdFactory?.invoke() ?: "") {
            if (it) {
//                updateResetState(false)
//                setCondition(
//                    defaultCondition(), false
//                )
                viewModelScope.safeLaunch {
                    initCondition()
                }
            }
        }

    }

    fun resetCondition() {
        viewModelScope.safeLaunch {
            val type = mViewTypeFactory?.invoke()
            val id = mViewIdFactory?.invoke()
            if (type.isLegalString() && id.isLegalString()) {
                val result = Tab2Repo.resetListCondition(
                    viewIdForProjectList(id!!),
                    type!!
                )
                if (result.isSuccessful) {
                    initCondition()
                }
            }
        }
    }

    fun defaultCondition(listColumns: List<ListColumn>? = null): ConditionState.Condition =
        ConditionState.Condition(
            TaskStatusEnum.UN_FINISH,
            getDefaultSorter(),
            getDefaultFilter(),
            getDefaultGrouper(),
            listColumns,
            null,
            true
        )


    val showTypeLiveData = MutableLiveData(Pair(false, TodoShowType.TABLE))

    val showType
        get() = showTypeLiveData.value?.second ?: TodoShowType.TABLE

    /**
     * 数据从server来，就不通知List更新了，
     * 会统一通过setCondition通知
     */
    fun updateListViewType(type: TodoShowType, fromServer: Boolean = false) {
        if (type == showType) {
            return
        }
        var notifyListChange = !fromServer
        if (!fromServer && (type == TodoShowType.KANBAN)) {
            val curValue = curCondition ?: return
            val grouper = if (!curCondition?.grouper.compatibleWithCardViewType()) {
                //condition更新会带动页面更新
                notifyListChange = false
                if (curCondition?.status == TaskStatusEnum.FINISH) {
                    ConditionGrouper(ConditionGrouper.Field.CUSTOM)
                } else getDefaultGrouper()
            } else {
                curValue.grouper
            }
            curValue.sorter.disable = curCondition?.status == TaskStatusEnum.FINISH
                    && grouper was ConditionGrouper.Field.FINISH_TIME
            setCondition(curValue.copy(grouper = grouper), true)
        }
        showTypeLiveData.value = Pair(notifyListChange, type)
        if (!fromServer) {
            saveCondition(listViewType = type)
        }

        if (!fromServer) {

            val clickId = when (type) {

                TodoShowType.CARD -> {
                    JoyWorkConstant.MOBILE_EVENT_TASK_LIST_CARD.takeIf {
                        isProjectList
                    } ?: JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_CARD_VIEWER
                }

                TodoShowType.CALENDAR -> {
                    JoyWorkConstant.MOBILE_EVENT_TASK_LIST_CALENDAR.takeIf {
                        isProjectList
                    } ?: JoyWorkConstant.MOBILE_EVENT_TASK_MY_CALENDAR
                }

                TodoShowType.TABLE -> {
                    JoyWorkConstant.MOBILE_EVENT_TASK_LIST_LISTVIEWER.takeIf {
                        isProjectList
                    } ?: JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_LISTVIEWER
                }

                else -> {
                    JoyWorkConstant.MOBILE_EVENT_TASK_LIST_LISTVIEWER.takeIf {
                        isProjectList
                    } ?: JoyWorkConstant.MOBILE_EVENT_TASK_MYEXECUTION_LISTVIEWER
                }
            }
            ClickEventParam(
                eventId = clickId
            )
        }

    }

    var showTypeDisableLiveData = false
    var calendarTypeDisable = false

    fun showTypeItems(): List<TodoShowType> {
        var items = TodoShowType.values().toList()
        if (showTypeDisableLiveData) {
            items = items.filterNot {
                it.value == TodoShowType.CARD.value
            }
        }
        if (calendarTypeDisable) {
            items = items.filterNot {
                it.value == TodoShowType.CALENDAR.value
            }
        }
        return items
    }

    val calendarTaskStateLD = MutableLiveData(TaskStatusEnum.UN_FINISH)
}


sealed class Effect {
    class StatusSelectedStatus(val selected: Boolean) : Effect()
    class SorterSelectedStatus(val selected: Boolean) : Effect()
    class GrouperSelectedStatus(val selected: Boolean) : Effect()
}