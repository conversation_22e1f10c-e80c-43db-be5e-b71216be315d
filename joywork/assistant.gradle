import java.util.regex.Matcher
import java.util.regex.Pattern

//自动同步中文文档中未添加到英文的内容
task "AAsyncZh" {
    group "me tools"

    doLast {
        File zhFile = new File("${project.projectDir.absolutePath}/src/main/res/values/strings.xml")
        File enFile = new File("${project.projectDir.absolutePath}/src/main/res/values-en/strings.xml")
        File enFileTmp = new File("${project.projectDir.absolutePath}/src/main/res/values-en/strings.xml.tmp")

        Map<String, String> zhMap = new HashMap<>()
        zhFile.eachLine {
            it = it.trim()
            if (it.startsWith("<string name")) {
                def start = it.indexOf('"') + 1
                def end = it.indexOf('"', start)
                def key = it.substring(start, end)
                start = it.indexOf(">") + 1
                end = it.lastIndexOf("</")
                zhMap[key] = it.substring(start, end)
            } else if (it == "</resources>") {
                return
            }
        }
        enFile.eachLine {
            def old = it
            it = it.trim()
            if (it.startsWith("<string name")) {
                def start = it.indexOf('"') + 1
                def end = it.indexOf('"', start)
                def key = it.substring(start, end)
                zhMap.remove(key)
            }
        }
        if (!zhMap.isEmpty()) {
            enFileTmp.createNewFile()
            enFile.eachLine {
                def old = it
                it = it.trim()
                if (it == "</resources>") {
                    zhMap.each { key, value ->
                        enFileTmp.append("    <string name=\"$key\">$value</string>\n".toCharArray())
                    }
                }
                enFileTmp.append("$old\n".toCharArray())
            }
            enFile.delete()
            enFileTmp.renameTo(enFile)
        }
    }
}

task "AAextractZh" {
    group "me tools"
    doLast {
        File file = new File("${project.projectDir.absolutePath}/src/main/res/values-en/strings.xml")
        File dstFile = new File("${project.projectDir.absolutePath}/translate.md")
        if (dstFile.exists()) {
            dstFile.delete()
        }
        dstFile.createNewFile()
        dstFile.write("|  中文   | 英文  |\n" +
                "|  ----  | ----  |\n")
        file.eachLine {
            def content = it.trim()
            if (isContainChinese(content)) {
                dstFile.append("| ${extractZh(it)} | |\n".toCharArray())
            }
        }
    }
}

AAextractZh.dependsOn AAsyncZh


def extractZh(String content) {
    Pattern p = Pattern.compile("\">([\\s\\S]+)</string>")
    def c = p.matcher(content)
    c.find()
    return c.group(1)
}

def isContainChinese(String str) {
    Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
    Matcher m = p.matcher(str);
    if (m.find()) {
        return true;
    }
    return false;
}