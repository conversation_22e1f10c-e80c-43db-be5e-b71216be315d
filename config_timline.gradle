ext {
    compileSdkVersion = COMPILE_SDK_VERSION
    TIMLINE_COMPILE_SUPPORT = [
            annotations : 'androidx.annotation:annotation:1.1.0',
            appcompat   : 'androidx.appcompat:appcompat:1.6.1',
            cardview    : 'androidx.cardview:cardview:1.0.0',
            design      : 'com.google.android.material:material:1.0.0',
            exifinterface:"com.android.support:exifinterface:${SUPPORT_VERSION}",
            palette     : "com.android.support:palette-v7:${SUPPORT_VERSION}",
            recyclerview: 'androidx.recyclerview:recyclerview:1.2.0',
            supportV4   : "com.android.support:support-v4:${SUPPORT_VERSION}"
    ]
//      buildToolsVersion = "26.0.2"
    minSdkVersion = 21
    targetSdkVersion = 23
    supportVersion = "28.0.0"
    constraintVersion = "1.1.0"
    gsonVersion = "2.8.2"
    glideVersion = "4.9.0"
    glideTransformationsVersion = "2.0.1"
    eventbusVersion = "2.4.1"
    okhttpVersion = "3.12.13"
    lifecycleVersion = "1.1.0"
    wcdbVersion = "1.0.8"
    multidexVersion = "1.0.3"
    wheelPickerVersion = "1.5.4"
    photoViewVersion = "1.2.4"
    protobufVersion = "2.5.0"
    dependencyModel = "module" //module or aar
    XLOG_GROUP = 'com.tencent.mars'
    XLOG_VERSION_NAME = '1.0.7'
    XLOG_VERSION_NAME_SUFFIX = ''
    //kotlinVersion = "1.3.31"
    coroutinesVersion = "1.3.5"
    kotlinVersion = "1.6.21"
    contextInjector = "1.0.8"
}