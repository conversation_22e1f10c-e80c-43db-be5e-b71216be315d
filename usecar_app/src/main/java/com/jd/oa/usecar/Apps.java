package com.jd.oa.usecar;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.util.Log;

import com.chenenyu.router.Configuration;
import com.chenenyu.router.Router;
import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.GlobalLocalLightBC;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.fragment.dialog.WebActionDialog;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.NetModule;

import org.json.JSONObject;

import java.util.ArrayList;

import static com.jd.oa.router.RouterConstant.ACTIVITY_URI_AppMain;


public class Apps extends Application implements GlobalLocalLightBC.LightEventListener {

    final static String TAG = "Apps";

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);

        //ApplicationInfo applicationInfo = getApplicationInfo();
        AppBase.setAppContext(this);
    }


    @Override
    public void onCreate() {
        super.onCreate();

        initApp(this);

        AppBase.iAppBase = new AppBase.IAppBase<ArrayList<Object>, Object>() {
            @Override
            public void showChattingActivity(Context context, String erp) {

            }

            @Override
            public void qrCodeDecode(Context context, WebActionDialog dialog, String imageUrl) {

            }

            @Override
            public void handlePushBizData(Context context, JSONObject bizData) {

            }

            @Override
            public void showContactDetailInfo(Context context, String userName) {

            }

//            @Override
//            public com.google.zxing.Result bitmapDecoderGetRawResult(Context context, Bitmap bitmap) {
//                return null;
//            }

            @Override
            public void onQrResultForMigrate(String data) {

            }

            @Override
            public void registerTimlineMessage(String flag, TimlineMessageListener listener) {

            }

            @Override
            public void unregisterListener(String flag, TimlineMessageListener listener) {

            }

            @Override
            public void gotoMemberList(Activity activity, int requestCode, Object entity, Callback<ArrayList<Object>> cb) {

            }

            @Override
            public String getTimlineAppId() {
                return null;
            }

            @Override
            public void loginTimline() {

            }

            @Override
            public void setKickOut(boolean kickOut) {

            }

            @Override
            public void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl) {

            }

            @Override
            public void imSharePic(String title, String content, String url, String icon, String type) {

            }

            @Override
            public void imClearNoticeUnReadCount(String noticeId) {

            }

            @Override
            public boolean isVirtualErp() {
                return false;
            }
        };
    }

    // 最小的初始化
    private void initApp(Application app) {

        AppBase.DEBUG = BuildConfig.DEBUG;
        AppBase.VERSION_NAME = BuildConfig.VERSION_NAME;
        AppBase.BUILD_TYPE = BuildConfig.BUILD_TYPE;

        // 加载主题
        AppBase.jdme_AppTheme_Defalut = R.style.jdme_AppTheme_Defalut;
        AppBase.MEWhiteTheme = R.style.MEWhiteTheme;

        Router.initialize(new Configuration.Builder()
                // 调试模式，开启后会打印log
                .setDebuggable(BuildConfig.DEBUG)
                // 模块名(即project.name)，每个使用Router的module都要在这里注册
                .registerModules("usecar_app", "common", "login", "libvehicle", "libtravel")
                .build());

        JdmeRounter.init(app);
        NetModule.initNetModule(app);
        JMEWebview.initSDK(app);

        // 接收重新登录接口
        registerLocalBroadCast();
    }

    void getMetaData(Application application) {
        ApplicationInfo appInfo;
        try {
            appInfo = application.getPackageManager()
                    .getApplicationInfo(application.getPackageName(), PackageManager.GET_META_DATA);
            String msg = appInfo.metaData.getString("PunchNotifyProvider");

        } catch (PackageManager.NameNotFoundException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
    }

    private void registerLocalBroadCast() {
        // 注册全局局部广播事件
        try {

            IntentFilter filter = new IntentFilter();
            LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(this);
            filter.addAction(GlobalLocalLightBC.ACTION);
            lbm.registerReceiver(new GlobalLocalLightBC(this), filter);
        } catch (Exception e) {
            Log.e(TAG, "unregisterLocalNotifyReceiver:Exception:=" + e.toString());
        }

        // 监听第三方模块 == 用户登出广播
        IntentFilter filter = new IntentFilter(getPackageName() + "_response_header_action");
        LocalBroadcastManager.getInstance(this).registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String code = intent.getStringExtra("operCode");
                String msg = intent.getStringExtra("message");
                if ("0000".equals(code)) {   // 用户登出
                    userKickOut(msg);
                }
            }
        }, filter);

    }

    @Override
    public void onLightEventNotify(Intent intent) {
        String event = intent.getStringExtra(GlobalLocalLightBC.KEY_EVENT);

        if (GlobalLocalLightBC.EVENT_USER_KICK_OUT.equals(event)) {   // 全局踢出事件
            Bundle b = intent.getBundleExtra(GlobalLocalLightBC.KEY_EVENT_VALUE);
            String message = (b != null ? b.getString("message") : "");
            userKickOut(message);
        }

    }

    private void userKickOut(String message) {
        // 移除本地信息
        PreferenceManager.UserInfo.removeAll(); // 移除当前用户配置信息
        MyPlatform.setCurrentUser(null);
        login(message);
    }

    private void login(String message) {
        Intent intent = new Intent(AppBase.getAppContext(), LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);       // 添加清空栈标记
        intent.putExtra(LoginActivity.EXTRA_ROUTER, ACTIVITY_URI_AppMain);
        intent.putExtra(LoginActivity.EXTRA_MESSAGE_TIP, message);  //踢出登陆的tip
        startActivity(intent);
    }
}
