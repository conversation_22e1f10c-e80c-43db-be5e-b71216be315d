<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jd.oa.usecar">

    <application
        android:name=".Apps"
        android:allowBackup="false"
        android:label="@string/app_name"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="allowBackup, label">

        <activity
            android:name=".StartupActivity"
            android:label="@string/app_name">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.jd.oa.business.login.controller.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/me_app_name"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
    </application>

</manifest>