#!/bin/bash

bundleDir=$1
targetDir=./app/src/me

cp -f ${bundleDir}/JDReactJoySpaceHub.jsbundle ${targetDir}/assets/jdreact/JDReactJoySpaceHub/JDReactJoySpaceHub.jsbundle
cp -f ${bundleDir}/JDReactJoySpaceHub.version ${targetDir}/assets/jdreact/JDReactJoySpaceHub/JDReactJoySpaceHub.version

cp -af ${bundleDir}/raw/. ${targetDir}/assets/rnhtml/jdreactjoyspacehub

cp -af ${bundleDir}/drawable-xxxhdpi/. ${targetDir}/res/drawable-xxxhdpi
cp -af ${bundleDir}/drawable-xxhdpi/. ${targetDir}/res/drawable-xxhdpi
cp -af ${bundleDir}/drawable-xhdpi/. ${targetDir}/res/drawable-xhdpi
cp -af ${bundleDir}/drawable-hdpi/. ${targetDir}/res/drawable-hdpi
cp -af ${bundleDir}/drawable-mdpi/. ${targetDir}/res/drawable-mdpi

echo 'done'