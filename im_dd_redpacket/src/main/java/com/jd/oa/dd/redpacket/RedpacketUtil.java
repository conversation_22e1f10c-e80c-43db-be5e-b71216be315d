package com.jd.oa.dd.redpacket;

import android.app.Activity;

import com.wangyin.payment.jdpaysdk.JDPay;
import com.wangyin.payment.jdpaysdk.counter.entity.CPPaySettingEntranceParam;

public class RedpacketUtil {

    public static void openSetting(Activity activity, String pin, String sessionKey) {
        CPPaySettingEntranceParam paySettingParam = new CPPaySettingEntranceParam();
        paySettingParam.setPin(pin);
        paySettingParam.setSessionKey(sessionKey);
        paySettingParam.setMode("Native");
        paySettingParam.setAppSource("jdme");
        JDPay.paySetting(activity, paySettingParam);
    }
}
