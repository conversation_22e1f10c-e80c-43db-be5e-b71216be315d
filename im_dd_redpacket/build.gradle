apply plugin: 'com.android.library'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILD_TOOLS_VERSION

    defaultConfig {
        vectorDrawables.useSupportLibrary = true
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        versionCode 1
        versionName "1.0"

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }


}

dependencies {
//    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
//    implementation SUBBUS_COMPILE_SUPPORT.design
//    implementation SUBBUS_COMPILE_SUPPORT.annotations
//    implementation SUBBUS_COMPILE_SUPPORT.appcompat
//    implementation SUBBUS_COMPILE_SUPPORT.recyclerview
//    implementation('com.jd.jrapp.jdpay:jdpaysdk-all:1.0.0.17-202004151224-5.4.0-SNAPSHOT') {
//        exclude group: 'com.jd.jrapp.library', module: 'tbs-x5'
//        exclude group: 'com.android.support', module: 'appcompat-v7'
//        exclude group: 'com.android.support', module: 'recyclerview-v7'
//        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
//        exclude group: 'com.alibaba', module: 'fastjson'
//        exclude group: 'com.google.code.gson', module: 'gson'
//    }
}
