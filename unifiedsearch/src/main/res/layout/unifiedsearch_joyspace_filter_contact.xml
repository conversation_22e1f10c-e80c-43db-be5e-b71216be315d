<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center">

    <TextView
        android:id="@+id/tv_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="8dp"
        android:textColor="@color/unifiedsearch_color_joyspace_filter"
        android:textSize="14sp"
        tools:text="他人创建"/>

<!--    <com.jd.oa.ui.OffsetLayout-->
<!--        android:id="@+id/layout_avatar_container"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginStart="4dp"-->
<!--        android:gravity="center_horizontal"-->
<!--        app:offset="3.5dp"-->
<!--        android:orientation="horizontal">-->
<!--    </com.jd.oa.ui.OffsetLayout>-->
    <FrameLayout
        android:id="@+id/layout_avatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp">
        <ViewStub
            android:id="@+id/stub_avatar"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center"
            android:layout="@layout/unifiedsearch_contact_avatar" />
        <TextView
            android:id="@+id/tv_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#FFF63218"
            tools:text="2"/>
    </FrameLayout>
</LinearLayout>
