<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white"
    android:paddingHorizontal="16dp">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginVertical="12dp"
        tools:src="@drawable/jdme_ic_app_default" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="10dp"
        android:layout_toEndOf="@id/iv_image"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="16sp"
                tools:text="远程桌面应用" />

            <TextView
                android:id="@+id/tv_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:gravity="center"
                android:paddingHorizontal="2dp"
                android:paddingVertical="0dp"
                android:textColor="#999999"
                android:textSize="9dp"
                android:visibility="gone"
                tools:text="new"
                tools:visibility="visible" />
        </LinearLayout>


        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/unifiedsearch_color_9D9D9D"
            android:textSize="14sp"
            tools:text="通过远程控制他人桌面实现IT运维等场景化支通过远程控制他人桌面实现IT运维等场景化支" />
    </LinearLayout>

</RelativeLayout>