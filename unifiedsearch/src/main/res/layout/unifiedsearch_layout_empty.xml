<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal">

    <ImageView
        android:id="@+id/iv_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_empty"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginTop="150dp"
        android:src="@drawable/unifiedsearch_img_empty"/>

    <TextView
        android:id="@+id/tv_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_check"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_empty"
        android:layout_marginTop="16dp"
        android:textColor="#1B1B1B"
        android:textSize="16sp"
        android:textStyle="bold"
        android:text="@string/unifiedsearch_no_result"/>
    <TextView
        android:id="@+id/tv_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_empty"
        app:layout_constraintBottom_toTopOf="@id/tv_feedback"
        android:paddingHorizontal="24dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:textColor="#6A6A6A"
        android:text="@string/unifiedsearch_no_result_change_keyword"/>
    <TextView
        android:id="@+id/tv_feedback"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_check"
        android:layout_marginTop="40dp"
        android:paddingHorizontal="24dp"
        android:gravity="center"
        android:textColor="#6A6A6A"
        android:text="@string/me_all_search_section_no_result"/>

</androidx.constraintlayout.widget.ConstraintLayout>