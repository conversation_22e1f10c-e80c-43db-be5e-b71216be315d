<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:outlineProvider="bounds">

    <RelativeLayout
        android:id="@+id/rl_content"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@color/white">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/ift_opt_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:gravity="center"
            android:textSize="@dimen/JMEIcon_22"
            app:icon_font_path="iconfont/iconfont.ttf"
            tools:text="@string/icon_padding_minuscircle"
            tools:textColor="#EE5A55" />

        <TextView
            android:id="@+id/tv_tab_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:layout_toEndOf="@id/ift_opt_icon"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="16dp"
            tools:text="联系人" />

        <ImageView
            android:id="@+id/iv_tab_name_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:layout_toEndOf="@id/ift_opt_icon"
            android:visibility="gone"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/ift_tab_drag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="16dp"
            android:gravity="center"
            android:text="@string/icon_edit_justify"
            android:textColor="#CDCDCD"
            android:textSize="@dimen/JMEIcon_22"
            app:icon_font_path="iconfont/iconfont.ttf" />

        <View
            android:id="@+id/v_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="#FFE6E6E6" />
    </RelativeLayout>

</LinearLayout>