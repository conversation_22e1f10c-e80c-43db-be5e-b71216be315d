<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:outlineProvider="bounds">

    <LinearLayout
        android:id="@+id/rl_content"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#6C6C6C"
            android:textSize="14sp"
            tools:text="已添加" />
    </LinearLayout>
</LinearLayout>