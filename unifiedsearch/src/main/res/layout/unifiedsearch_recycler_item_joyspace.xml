<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/unifiedsearch_joyspace_item_padding_horizontal"
    android:paddingVertical="12dp">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="@dimen/unifiedsearch_joyspace_item_icon_size"
        android:layout_height="@dimen/unifiedsearch_joyspace_item_icon_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#4C94EA" />

    <ImageView
        android:id="@+id/iv_unread"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/unifiedsearch_unread_icon"
        android:visibility="invisible"
        app:layout_constraintCircle="@id/iv_image"
        app:layout_constraintCircleAngle="315"
        app:layout_constraintCircleRadius="20dp"
        app:layout_constraintLeft_toLeftOf="@id/iv_image"
        app:layout_constraintTop_toTopOf="@id/iv_image" />

    <ImageView
        android:id="@+id/iv_shortcut"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/unifiedsearch_joyspace_shortcut"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/iv_image"
        app:layout_constraintCircle="@id/iv_image"
        app:layout_constraintCircleAngle="135"
        app:layout_constraintCircleRadius="20dp"
        app:layout_constraintLeft_toLeftOf="@id/iv_image"
        app:layout_constraintRight_toRightOf="@id/iv_image"
        app:layout_constraintTop_toTopOf="@id/iv_image"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/unifiedsearch_joyspace_item_preview_text_padding"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/unifiedsearch_color_1B1B1B"
        android:textSize="16sp"
        app:layout_constraintLeft_toRightOf="@id/iv_image"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_image"
        tools:text="ICON设计规范之页面图标尺寸ICON设计规范之页面图标尺寸ICON设计规范之页面图标尺寸" />

    <TextView
        android:id="@+id/tv_preview"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textSize="14dp"
        android:textColor="@color/unifiedsearch_color_1B1B1B"
        android:lineSpacingExtra="2dp"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="这些数据反映的都是产品业务线的总体情况数这些数据反映的都是产品业务线的总体情况数这些数据反映的都是产品业务线的总体情况数这些数据反映的都是产品业务线的总体情况数" />

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/unifiedsearch_color_9D9D9D"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_preview"
        tools:text="最近浏览于 昨天 10:33 最近浏览于 昨天 10:33 最近浏览于 昨天 10:33" />

    <LinearLayout
        android:id="@+id/ll_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_subtitle"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_tag1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxEms="8"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:singleLine="true"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="湿哒哒大叔大婶实打实"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_tag2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:maxEms="8"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:singleLine="true"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="111111111111111"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_tag_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@drawable/unifiedsearch_search_bg_more"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:textColor="#1B1B1B"
            android:textSize="12dp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="+99" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>