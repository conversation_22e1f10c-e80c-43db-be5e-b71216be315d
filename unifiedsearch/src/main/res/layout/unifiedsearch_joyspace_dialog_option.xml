<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/unifiedsearch_option_dialog_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginVertical="12dp"
        android:textSize="16sp"
        android:textColor="#FF232930"
        tools:text="打开时间"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.8dp"
        android:background="#FFF0F3F3"
        android:visibility="gone"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="10dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="16dp">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycle_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </RelativeLayout>

</LinearLayout>