<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <HorizontalScrollView
                    android:id="@+id/scroll_types"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none"
                    android:overScrollMode="never">
                    <FrameLayout
                        android:id="@+id/types_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                        <RadioGroup
                            android:id="@+id/radio_group"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">
                            <RadioButton
                                android:id="@+id/radio_all"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/UnifiedSearchJoySpaceFilterStyle"
                                android:checked="true"
                                android:text="@string/unifiedsearch_joyspace_filter_related"/>
                            <RadioButton
                                android:id="@+id/radio_received"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/UnifiedSearchJoySpaceFilterStyle"
                                android:text="@string/unifiedsearch_joyspace_filter_received"/>
                            <RadioButton
                                android:id="@+id/radio_recent"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/UnifiedSearchJoySpaceFilterStyle"
                                android:text="@string/unifiedsearch_joyspace_filter_recent"/>
                            <RadioButton
                                android:id="@+id/radio_created"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/UnifiedSearchJoySpaceFilterStyle"
                                android:text="@string/unifiedsearch_joyspace_filter_created_by_myself"/>
                            <RadioButton
                                android:id="@+id/radio_sent"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/UnifiedSearchJoySpaceFilterStyle"
                                android:text="@string/unifiedsearch_joyspace_filter_sent"/>
                            <RadioButton
                                android:id="@+id/radio_public"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/UnifiedSearchJoySpaceFilterStyle"
                                android:text="@string/unifiedsearch_joyspace_filter_public"/>
                        </RadioGroup>
                        <ImageView
                            android:id="@+id/iv_tab_indicator"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:src="@drawable/unifiedsearch_tab_indicator"
                            android:visibility="invisible"
                            tools:visibility="visible"/>
                    </FrameLayout>
                </HorizontalScrollView>
                <ImageView
                    android:id="@+id/iv_edge"
                    android:layout_width="wrap_content"
                    android:layout_height="42dp"
                    android:layout_gravity="end|center_vertical"
                    android:src="@drawable/unifiedsearch_schedule_option_mask"
                    android:visibility="gone"
                    tools:visibility="visible"/>

            </FrameLayout>

            <ImageButton
                android:id="@+id/btn_sort_condition"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:padding="8dp"
                android:background="@null"
                android:src="@drawable/unifiedsearch_sort_condition_down_black" />

        </LinearLayout>

        <com.jd.oa.unifiedsearch.joyspace.view.FiltersContainerLayout
            android:id="@+id/layout_filter_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible"
            tools:visibility="visible"/>

        <FrameLayout
            android:id="@+id/content"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible"
                tools:visibility="visible"
                tools:listitem="@layout/unifiedsearch_recycler_item_joyspace"
                tools:itemCount="20"/>

            <include
                android:id="@+id/layout_loading"
                android:visibility="invisible"
                layout="@layout/unifiedsearch_layout_loading"/>

        </FrameLayout>
    </LinearLayout>

    <ViewStub
        android:id="@+id/stub_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/unifiedsearch_layout_empty"
        android:visibility="gone"/>

    <include
        android:id="@+id/layout_error"
        layout="@layout/unifiedsearch_layout_error"
        tools:visibility="invisible"/>

    <include android:id="@+id/layout_sensitive"
        layout="@layout/unifiedsearch_layout_sensitive_word"
        android:visibility="invisible" />

<!--    <ViewStub-->
<!--        android:id="@+id/stub_sensitive"-->
<!--        android:layout="@layout/unifiedsearch_layout_sensitive_word"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:layout_marginTop="150dp"-->
<!--        android:visibility="gone"/>-->

</FrameLayout>
