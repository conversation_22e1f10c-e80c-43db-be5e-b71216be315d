<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="top"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="15dp"
        android:background="@drawable/unifiedsearch_all_card_top" />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginStart="8dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/unifiedsearch_all_card_bottom" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/unifiedsearch_all_card_top"
        android:gravity="center_vertical"
        android:orientation="vertical" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/unifiedsearch_all_card_bottom"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/section_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="5dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/section_content_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="18dp"
                android:textStyle="bold"
                tools:text="DAU" />

            <TextView
                android:id="@+id/section_content_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:textColor="@color/unifiedsearch_color_6A6A6A"
                android:textSize="14dp"
                tools:text="每日活跃用户/日活" />

            <TextView
                android:id="@+id/section_content_val"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:lineSpacingMultiplier="1.2"
                android:maxLines="4"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="14dp"
                tools:text="全称Daily Active User，即日活跃用户数量。 常用于反映网站、互联网应用或网络游戏的运营情况，DAU通常统计一日（统计日）之内，登录或使用了某个产品的用户数（去除重复登录的用户），这与流量统计工具里的访客...这与流量统计工具里的访客...这与流量统计工具里的访客...这与流量统计工具里的访客...这与流量统计工具里的访客" />

        </LinearLayout>

        <View
            android:id="@+id/section_bottom_split"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/unifiedsearch_color_E1E3E6" />

        <LinearLayout
            android:id="@+id/section_bottom"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="3dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="3dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_more"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/me_all_search_words_mean"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="14dp"
                tools:text="查看词条详情" />

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:text="@string/icon_direction_right"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="@dimen/JMEIcon_16"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>