<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/jdme_ripple_white"
    android:paddingVertical="8dp"
    android:paddingHorizontal="12dp">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        tools:src="@drawable/jdme_ic_approval_default" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="10dp"
        android:layout_toEndOf="@id/iv_image"
        android:orientation="vertical">

        <RelativeLayout
            android:gravity="left"
            android:layout_width="match_parent"
            android:layout_height="24dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@id/tv_status_tag"
                android:ellipsize="end"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="16sp"
                tools:text="李波的企业信息化部资源" />

            <TextView
                android:layout_alignParentRight="true"
                android:id="@+id/tv_status_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:paddingHorizontal="4dp"
                android:paddingVertical="1dp"
                android:layout_marginVertical="3dp"
                android:gravity="center_vertical"
                android:textSize="10sp"
                android:visibility="gone"
                tools:textColor="#F63218"
                tools:background="@drawable/unifiedsearch_approval_status_tag_active"
                tools:text="待审批"
                tools:visibility="visible" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:layout_marginTop="2dp"
            android:maxLines="1"
            android:textColor="@color/unifiedsearch_color_1B1B1B"
            android:textSize="14sp"
            tools:text="缩容零售JDos私有云资源, 请内部合理实用部门jdos资源，优先使用公有云集群和CHO镜像，缩容零售JDos私有云" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_applicant_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="2dp"
                android:maxLines="1"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="14sp"
                tools:text="申请人  张大白" />

            <TextView
                android:id="@+id/tv_application_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:layout_marginTop="2dp"
                android:maxLines="1"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="14sp"
                tools:text="申请时间  2024年05月02日 13:45" />
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>