<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="8dp"
    android:background="@drawable/unifiedsearch_ai_section_card"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="2dp"
        android:background="@drawable/unifiedsearch_ai_seciton_content_bg"
        android:orientation="vertical"
        android:paddingTop="10dp">

        <LinearLayout
            android:id="@+id/section_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="5dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/section_content_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:background="@drawable/unifiedsearch_ai_item_title_bg"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="18dp"
                android:textStyle="bold"
                tools:text="办理员工卡" />

            <LinearLayout
                android:id="@+id/section_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="visible">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_marginRight="5dp"
                    android:src="@drawable/unifiedsearch_ai_item_icon" />

                <TextView
                    android:id="@+id/section_content_sub_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_search_section_ai_sub_title"
                    android:textColor="@color/unifiedsearch_color_6A6A6A"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/section_content_sub_title_use"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="5dp"
                    android:text="@string/me_search_section_ai_sub_title_use"
                    android:textColor="@color/unifiedsearch_color_6A6A6A"
                    android:textSize="14dp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <com.jd.oa.ui.CircleImageView
                    android:id="@+id/section_content_sub_title_use_icon"
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginRight="3dp"
                    android:visibility="gone"
                    tools:src="@drawable/unifiedsearch_ai_item_icon"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/section_content_sub_title_use_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/unifiedsearch_color_1869F5"
                    android:textSize="14dp"
                    android:visibility="gone"
                    tools:text="SSC小助手"
                    tools:visibility="visible" />

            </LinearLayout>

            <include layout="@layout/unifiedsearch_section_ai_layout_loading" />

            <TextView
                android:id="@+id/section_content_val"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:lineSpacingMultiplier="1.2"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="14dp"
                android:visibility="gone"
                tools:text="全称Daily Active User，即日活跃用户数量。 常用于反映网站、互联网应用或网络游戏的运营情况，DAU通常统计一日（统计日）之内，登录或使用了某个产品的用户数（去除重复登录的用户），这与流量统计工具里的访客...这与流量统计工具里的访客...这与流量统计工具里的访客...这与流量统计工具里的访客...这与流量统计工具里的访客"
                tools:visibility="visible" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/section_op"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="8dp"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:id="@+id/section_content_op"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_op_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_search_section_ai_op_expand"
                    android:textColor="#1869F5"
                    android:textSize="14dp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/ift_op_flag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5dp"
                    android:text="@string/icon_direction_down"
                    android:textColor="#1869F5"
                    android:textSize="@dimen/JMEIcon_12"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:background="@drawable/unifiedsearch_ai_item_opration_bg"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingLeft="15dp"
                android:paddingTop="3dp"
                android:paddingRight="15dp"
                android:paddingBottom="3dp">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/tv_op_up"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:padding="5dp"
                    android:text="@string/icon_general_like"
                    android:textColor="#000000"
                    android:textSize="@dimen/JMEIcon_16"/>

                <View
                    android:layout_width="1dp"
                    android:layout_height="10dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:background="#CECECE" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/tv_op_down"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:padding="5dp"
                    android:text="@string/icon_general_like1"
                    android:textColor="#000000"
                    android:textSize="@dimen/JMEIcon_16"/>
            </LinearLayout>

        </RelativeLayout>

        <View
            android:id="@+id/section_bottom_split"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/unifiedsearch_color_E1E3E6"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/section_bottom"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="3dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="3dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_more"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/me_all_search_words_mean"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="16dp"
                tools:text="查看全部" />

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:text="@string/icon_direction_right"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="@dimen/JMEIcon_14"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/section_content_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/unifiedsearch_color_20D7D7D7"
            android:orientation="vertical"
            android:paddingLeft="12dp"
            android:paddingTop="12dp"
            android:paddingRight="12dp"
            android:paddingBottom="12dp"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/me_search_section_ai_op_info"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="16dp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/section_content_info_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="vertical" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>