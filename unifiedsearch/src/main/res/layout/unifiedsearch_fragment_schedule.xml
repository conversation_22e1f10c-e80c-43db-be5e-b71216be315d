<?xml version="1.0" encoding="utf-8"?>

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <!--选项-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp">
                <!--左右滑动-->
                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|center_vertical"
                    android:scrollbars="none">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingTop="4dp"
                        android:orientation="horizontal">
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="12dp"
                            android:orientation="vertical">
                            <LinearLayout
                                android:id="@+id/calendar_dialog_btn"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:paddingHorizontal="12dp"
                                android:background="@drawable/unifiedsearch_filter_layout_bg">
                                <TextView
                                    android:id="@+id/calendar_dialog_btn_tv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingVertical="8dp"
                                    android:textColor="#FF1B1B1B"
                                    android:textSize="14sp"
                                    android:text="@string/schedule_search_option_calendar"
                                    android:textAlignment="center" />
                                <com.jd.oa.unifiedsearch.joyspace.view.FilterArrow
                                    android:id="@+id/arrow_calendar"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="2dp" />
                            </LinearLayout>
                            <View
                                android:id="@+id/view_calendar_bottom_padding"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/unifiedsearch_joyspace_filter_margin_bottom" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/select_contact_dialog_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="@dimen/unifiedsearch_joyspace_filter_margin_bottom"
                            android:paddingHorizontal="12dp"
                            android:background="@drawable/unifiedsearch_filter_layout_bg">
                            <TextView
                                android:id="@+id/select_contact_btn_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingVertical="8dp"
                                android:textColor="#FF1B1B1B"
                                android:textSize="14sp"
                                android:text="@string/schedule_search_option_contact"
                                android:textAlignment="center" />
                            <FrameLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content">
                                <LinearLayout
                                    android:id="@+id/more_tip"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:layout_marginStart="2dp">
                                    <TextView
                                        android:id="@+id/more_textview"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="#FFF63218"
                                        android:textSize="12sp"
                                        android:textAlignment="center"
                                        tools:text="10"/>
                                </LinearLayout>
                                <com.jd.oa.ui.CircleImageViewWithGap
                                    android:id="@+id/first_avatar"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginStart="2dp"
                                    app:me_border_color="#FFF7F7"
                                    app:me_border_width="1dp"
                                    tools:src="@drawable/jdme_app_icon"
                                    tools:visibility="invisible"/>
                            </FrameLayout>
                            <com.jd.oa.unifiedsearch.joyspace.view.FilterArrow
                                android:id="@+id/arrow_participants"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="2dp" />
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/date_picker_dialog_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="@dimen/unifiedsearch_joyspace_filter_margin_bottom"
                            android:paddingHorizontal="12dp"
                            android:gravity="center_vertical"
                            android:background="@drawable/unifiedsearch_filter_layout_bg">
                            <TextView
                                android:id="@+id/date_picker_btn_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingVertical="8dp"
                                android:textColor="#FF1B1B1B"
                                android:textSize="14sp"
                                android:text="@string/schedule_search_option_time"
                                android:textAlignment="center" />
                            <com.jd.oa.unifiedsearch.joyspace.view.FilterArrow
                                android:id="@+id/arrow_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="2dp" />
                        </LinearLayout>
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="12dp"
                            android:orientation="vertical">
                            <LinearLayout
                                android:id="@+id/btn_category"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingHorizontal="12dp"
                                android:gravity="center_vertical"
                                android:background="@drawable/unifiedsearch_filter_layout_bg">
                                <TextView
                                    android:id="@+id/tv_category"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingVertical="8dp"
                                    android:textColor="#FF1B1B1B"
                                    android:textSize="14sp"
                                    android:text="@string/calendar_schedule_category_all"
                                    android:textAlignment="center" />
                                <com.jd.oa.unifiedsearch.joyspace.view.FilterArrow
                                    android:id="@+id/arrow_category"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="2dp" />
                            </LinearLayout>
                            <View
                                android:id="@+id/view_category_bottom_padding"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/unifiedsearch_joyspace_filter_margin_bottom"
                                tools:background="@color/colorPrimary"/>
                        </LinearLayout>

                    </LinearLayout>
                </HorizontalScrollView>
                <View
                    android:id="@+id/mask"
                    android:layout_width="4dp"
                    android:layout_height="36dp"
                    android:layout_gravity="end|center_vertical"
                    android:background="@drawable/unifiedsearch_schedule_option_mask" />
            </FrameLayout>
            <!--重置-->
            <LinearLayout
                android:id="@+id/reset_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:textColor="@color/unifiedsearch_reset_color"
                    android:textSize="14sp"
                    android:text="@string/schedule_search_reset"
                    android:textAlignment="center" />
            </LinearLayout>
        </LinearLayout>

        <!--搜索列表-->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/search_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/empty_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="invisible"
        tools:visibility="visible">
        <View
            android:id="@+id/empty_view_image"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginTop="150dp"
            android:layout_marginBottom="16dp"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/empty_view_text_start"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/empty_view_text_middle"
                app:layout_constraintHorizontal_chainStyle="packed"
                android:textColor="#FF8F959E"/>
            <TextView
                android:id="@+id/empty_view_text_middle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@id/empty_view_text_start"
                app:layout_constraintEnd_toStartOf="@+id/empty_view_text_end"
                app:layout_constraintWidth_default="wrap"
                android:textColor="#FF8F959E"
                android:maxLines="1"
                android:ellipsize="end"/>
            <TextView
                android:id="@+id/empty_view_text_end"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@id/empty_view_text_middle"
                app:layout_constraintEnd_toEndOf="parent"
                android:textColor="#FF8F959E"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <Button
            android:id="@+id/btn_retry"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:paddingVertical="8dp"
            android:paddingHorizontal="32dp"
            android:textColor="#FF232930"
            android:textSize="16dp"
            android:text="@string/unifiedsearch_joyspace_retry"
            android:background="@drawable/unifiedsearch_btn_retry"
            tools:visibility="invisible"/>
    </LinearLayout>

    <ViewStub
        android:id="@+id/stub_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/unifiedsearch_layout_empty"
        android:visibility="gone" />
</FrameLayout>