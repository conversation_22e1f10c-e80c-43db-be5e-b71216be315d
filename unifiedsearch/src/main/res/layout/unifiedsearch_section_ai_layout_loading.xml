<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_loading"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:layout_gravity="left"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingTop="5dp"
    android:paddingBottom="5dp">

    <ImageView
        android:id="@+id/iv_loading_indicator"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginRight="5dp"
        android:background="@drawable/unifiedsearch_loading2" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/unifiedsearch_loading2"
        android:textColor="#BFC1C4"
        android:textSize="14dp" />
</LinearLayout>