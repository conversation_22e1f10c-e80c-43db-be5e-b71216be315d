<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="android.widget.LinearLayout"
    android:orientation="vertical"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="12dp"
        android:textColor="#FF232930"
        android:textSize="16sp"
        tools:text="按相关度"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:orientation="horizontal">

        <include
            android:id="@+id/tv_left"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            layout="@layout/unifiedsearch_joyspace_sort_condition_item"/>

        <View
            android:id="@+id/divider"
            android:layout_width="12dp"
            android:layout_height="match_parent"/>

        <include
            android:id="@+id/tv_right"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            layout="@layout/unifiedsearch_joyspace_sort_condition_item"/>

    </LinearLayout>
</merge>