<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="8dp"
    android:layout_marginTop="12dp"
    android:layout_marginRight="8dp"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginBottom="5dp"
        android:text="@string/me_search_section_recommend_title"
        android:textColor="@color/unifiedsearch_color_1B1B1B"
        android:textSize="14sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/section_content_info_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:background="@drawable/unifiedsearch_ai_section_card"
        android:orientation="vertical"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        tools:layout_height="100dp" />
</LinearLayout>