<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="FrameLayout">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_centerInParent="true"
            android:textColor="@color/textColorPrimary"
            android:paddingVertical="12dp"
            android:text="@string/loading" />
        <ProgressBar
            android:id="@+id/pb_loading"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@id/tv_state"
            android:layout_toLeftOf="@id/tv_state"
            android:indeterminate="true"
            android:indeterminateTintMode="src_atop"
            android:indeterminateTint="#F63218"/>
        <LinearLayout
            android:id="@+id/layout_complete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:paddingTop="12dp"
            android:paddingBottom="12dp">
            <TextView
                android:id="@+id/tv_feedback"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#1B1B1B"
                android:text="@string/me_all_search_section_no_result"/>
        </LinearLayout>
    </RelativeLayout>
</FrameLayout>