<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="6dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingStart="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/unifiedsearch_joyspace_folder_search_input_bg">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:paddingVertical="8dp"
                    android:textSize="@dimen/JMEIcon_16"
                    android:text="@string/icon_general_search"
                    android:textColor="#FFBFC1C4"/>

                <ImageView
                    android:id="@+id/iv_folder_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/unifiedsearch_joyspace_folder_normal"/>

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1">

                    <EditText
                        android:id="@+id/et_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:textSize="14sp"
                        android:textColor="#FF232930"
                        android:imeOptions="actionSearch" />

                    <LinearLayout
                        android:id="@+id/layout_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/unifiedsearch_joyspace_folder_search_hint_start"/>
                        <TextView
                            android:id="@+id/tv_hint_text"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:maxLines="1"
                            android:ellipsize="end"
                            tools:text="文件夹文件夹文件夹文件夹文件夹"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="64dp"
                            android:text="@string/unifiedsearch_joyspace_folder_search_hint_end"/>
                    </LinearLayout>
                </FrameLayout>

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/icon_clear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingVertical="8dp"
                    android:paddingHorizontal="12dp"
                    android:text="@string/icon_padding_closecircle"
                    android:textColor="#FFBFC1C4"
                    android:textSize="@dimen/JMEIcon_16"
                    android:visibility="gone"
                    tools:visibility="visible"/>

            </LinearLayout>

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:paddingHorizontal="12dp"
                android:paddingVertical="8dp"
                android:textColor="#FF232930"
                android:text="@string/unifiedsearch_joyspace_cancel"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingTop="8dp">

            <com.jd.oa.unifiedsearch.joyspace.view.FiltersContainerLayout
                android:id="@+id/layout_filters_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"/>

            <View
                android:id="@+id/divider"
                android:layout_width="0.5dp"
                android:layout_height="14dp"
                android:background="#FFDEE0E3"
                android:visibility="gone"
                tools:visibility="visible"/>

            <ImageButton
                android:id="@+id/btn_sort_condition"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:padding="8dp"
                android:background="@drawable/jdme_ripple_white"
                android:src="@drawable/unifiedsearch_sort_condition_down_black"/>
        </LinearLayout>

        <FrameLayout
            android:id="@+id/content"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible"
                tools:visibility="visible"
                tools:listitem="@layout/unifiedsearch_recycler_item_joyspace"
                tools:itemCount="20"/>

            <include
                android:id="@+id/layout_loading"
                android:visibility="invisible"
                layout="@layout/unifiedsearch_layout_loading"/>

            <ViewStub
                android:id="@+id/stub_empty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout="@layout/unifiedsearch_layout_empty"/>

            <include
                android:id="@+id/layout_error"
                layout="@layout/unifiedsearch_layout_error"
                android:visibility="invisible"/>

        </FrameLayout>

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginVertical="8dp"
            android:paddingVertical="8dp"
            android:paddingHorizontal="32dp"
            android:orientation="horizontal"
            android:gravity="start"
            android:background="@drawable/unifiedsearch_joyspace_folder_search_all">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textSize="@dimen/JMEIcon_16"
                android:text="@string/icon_general_search"
                android:textColor="#FF232930"/>
            <com.google.android.flexbox.FlexboxLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:flexWrap="wrap"
                app:justifyContent="center">
                <TextView
                    android:id="@+id/tv_search_all_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:textColor="#FF232930"
                    android:text="@string/unifiedsearch_joyspace_folder_not_current"/>

                <TextView
                    android:id="@+id/tv_search_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:textColor="@color/unifiedsearch_highlight_color"
                    android:text="@string/unifiedsearch_joyspace_folder_search_all"/>
            </com.google.android.flexbox.FlexboxLayout>
        </LinearLayout>
    </FrameLayout>

</FrameLayout>