<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">
    <LinearLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/unifiedsearch_calendar_picker_header_bg">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="#232930"
                android:textSize="16dp"
                android:text="@string/schedule_search_option_calendar"
                android:textAlignment="center"
                android:includeFontPadding="false"/>
            <LinearLayout
                android:id="@+id/ok_btn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:textColor="@color/unifiedsearch_primary_color"
                    android:textSize="16dp"
                    android:text="@string/schedule_search_calendar_dialog_ok"
                    android:textAlignment="center"
                    android:includeFontPadding="false"/>
            </LinearLayout>
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#DEE0E3"/>

        <com.jd.oa.unifiedsearch.joyday.view.calendarpicker.CalendarListRecyclerView
            android:id="@+id/calendar_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="vertical"
            android:scrollbarThumbVertical="@drawable/unifiedsearch_calendar_scrollbar"
            android:scrollbarStyle="outsideOverlay"/>
    </LinearLayout>
</FrameLayout>