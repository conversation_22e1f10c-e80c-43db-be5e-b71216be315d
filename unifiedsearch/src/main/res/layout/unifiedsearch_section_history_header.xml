<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp">

        <TextView
            android:id="@+id/header_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:text="@string/me_all_search_history"
            android:textColor="#000000"
            android:textSize="16dp"
            android:textStyle="bold" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/header_clean"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginRight="16dp"
            android:text="@string/icon_edit_delete"
            android:textColor="@color/unifiedsearch_color_9D9D9D"
            android:textSize="@dimen/JMEIcon_16"/>

    </RelativeLayout>
</FrameLayout>