<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/unifiedsearch_discovery"
    android:layout_marginTop="8dp"
    android:paddingTop="10dp"
    android:paddingBottom="12dp"
    android:layout_marginHorizontal="8dp"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_gravity="center_horizontal">

        <TextView
            android:id="@+id/header_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:text="@string/me_all_search_history"
            android:textColor="#000000"
            android:textSize="14sp"
            android:textStyle="bold" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/header_clean"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:text="@string/icon_edit_delete"
            android:textColor="@color/unifiedsearch_color_9D9D9D"
            android:textSize="@dimen/JMEIcon_16"/>

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycle_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        tools:itemCount="3"
        tools:visibility="visible" />


</LinearLayout>