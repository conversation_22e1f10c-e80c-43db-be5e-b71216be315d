<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white"
    android:layout_marginTop="4dp"
    android:paddingVertical="8dp"
    android:paddingHorizontal="12dp">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        tools:src="@drawable/jdme_ic_process_default" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="10dp"
        android:layout_toEndOf="@id/iv_image"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/unifiedsearch_color_1B1B1B"
            android:textSize="16sp"
            tools:text="企业IT线上业务数据查询及处理申请流程" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/unifiedsearch_color_1B1B1B"
            android:layout_marginTop="2dp"
            android:textSize="14sp"
            tools:text="缩容零售JDos私有云资源, 请内部合理实用部门jdos资源，优先使用公有云集群和CHO镜像，缩容零售JDos私有云" />

        <TextView
            android:id="@+id/tv_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/unifiedsearch_color_9D9D9D"
            android:layout_marginTop="2dp"
            android:textSize="14sp"
            tools:text="IT类流程/企业IT部流程" />

    </LinearLayout>

</RelativeLayout>