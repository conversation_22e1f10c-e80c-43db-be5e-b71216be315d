<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="invisible"
                tools:itemCount="3"
                tools:listitem="@layout/unifiedsearch_recycler_item_app"
                tools:visibility="visible" />

            <include layout="@layout/unifiedsearch_pub_layout_no_result" />
        </FrameLayout>

        <include layout="@layout/unifiedsearch_pub_feedback_content" />

    </LinearLayout>


    <include
        android:id="@+id/layout_loading"
        layout="@layout/unifiedsearch_layout_loading"
        android:visibility="invisible" />

    <LinearLayout
        android:id="@+id/layout_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="150dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="visible"
        tools:ignore="UseCompoundDrawables"
        tools:visibility="invisible">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/unifiedsearch_placeholder" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/unifiedsearch_placeholder_text"
            android:textColor="#FF8F959E" />
    </LinearLayout>

    <include
        android:id="@+id/layout_error"
        layout="@layout/unifiedsearch_layout_error"
        tools:visibility="invisible" />

</FrameLayout>