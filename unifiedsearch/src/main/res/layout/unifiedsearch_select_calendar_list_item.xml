<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/calendar_separator"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:orientation="horizontal"
        android:background="#F6F6F6"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/calendar_header"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:visibility="gone">
        <TextView
            android:id="@+id/calendar_header_textview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:textColor="#62656D"
            android:textSize="16dp"
            android:textAlignment="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:ellipsize="end"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/calendar_item"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal"
        android:background="@color/white"
        android:gravity="center_vertical">
        <CheckBox
            android:id="@+id/calendar_item_checkbox"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/unifiedsearch_calendar_picker_item_cb"
            android:button="@null"
            android:clickable="false"/>
        <TextView
            android:id="@+id/calendar_item_textview"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="16dp"
            android:textColor="#232930"
            android:textSize="14dp"
            android:textAlignment="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:ellipsize="end"/>
    </LinearLayout>
</LinearLayout>
