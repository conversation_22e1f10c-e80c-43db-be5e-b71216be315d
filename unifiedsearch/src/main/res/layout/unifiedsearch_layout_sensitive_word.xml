<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="150dp"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/unifiedsearch_joyspace_sensitive_word"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/unifiedsearch_joyspace_sensitive"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#FF1B1B1B"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:paddingHorizontal="24dp"
        android:text="@string/unifiedsearch_joyspace_sensitive_tips"
        android:textSize="12sp"
        android:textColor="#FF6A6A6A"
        android:gravity="center"/>
</LinearLayout>