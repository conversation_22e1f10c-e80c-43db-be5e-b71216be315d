<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.unifiedsearch.joyspace.view.FilterLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginTop="@dimen/unifiedsearch_joyspace_filter_margin_bottom"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/layout_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/unifiedsearch_filter_layout_bg">
        <TextView
            android:id="@+id/tv_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingVertical="8dp"
            android:textColor="@color/unifiedsearch_color_joyspace_filter"
            android:textSize="14sp"
            tools:text="发送人"/>
        <com.jd.oa.unifiedsearch.joyspace.view.FilterArrow
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"/>
    </LinearLayout>

    <View
        android:id="@+id/view_bottom_padding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/unifiedsearch_joyspace_filter_margin_bottom" />

</com.jd.oa.unifiedsearch.joyspace.view.FilterLayout>