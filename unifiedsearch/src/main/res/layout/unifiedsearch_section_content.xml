<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/unifiedsearch_all_card_bottom" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/unifiedsearch_all_card_bottom"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/section_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:orientation="vertical"
            tools:layout_height="100dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycle_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="10dp"
            android:visibility="gone"
            tools:itemCount="3"
            tools:visibility="visible" />

        <View
            android:id="@+id/section_bottom_split"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/unifiedsearch_color_E1E3E6" />

        <LinearLayout
            android:id="@+id/section_bottom"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="3dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="3dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:text="@string/icon_general_search"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="@dimen/JMEIcon_18"/>

            <TextView
                android:id="@+id/tv_more"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="10dp"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="16dp"
                tools:text="查看更多相关" />

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:text="@string/icon_direction_right"
                android:textColor="@color/unifiedsearch_color_9D9D9D"
                android:textSize="@dimen/JMEIcon_16"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>