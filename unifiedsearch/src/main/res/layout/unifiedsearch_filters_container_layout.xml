<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_filter_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_reset"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp"
        android:textSize="14sp"
        android:textColor="#FF1869F5"
        android:background="@drawable/jdme_ripple_white"
        android:visibility="gone"
        android:text="@string/unifiedsearch_joyspace_option_reset"
        android:gravity="center"
        tools:visibility="visible"/>

    <HorizontalScrollView
        android:id="@+id/layout_subtype_scrollview"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tv_reset"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:scrollbars="none"
        android:overScrollMode="never">
        <LinearLayout
            android:id="@+id/layout_subtype_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"/>
    </HorizontalScrollView>

    <ImageView
        android:id="@+id/iv_reset_edge"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tv_reset"
        android:src="@drawable/unifiedsearch_schedule_option_mask"
        android:visibility="gone"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout>