<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/bar_btn"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:src="@mipmap/mae_album_ic_action_back" />

    <TextView
        android:id="@+id/bar_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="5dp"
        android:layout_toRightOf="@+id/bar_btn"
        android:text="@string/me_all_search_tab_setting"
        android:textColor="#000000"
        android:textSize="20dp" />

    <TextView
        android:id="@+id/bar_right_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:text="@string/me_save"
        android:textColor="@color/red_warn"
        android:textSize="18dp" />
</RelativeLayout>
