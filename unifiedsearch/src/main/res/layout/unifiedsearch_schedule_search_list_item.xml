<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/schedule_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">
        <TextView
            android:id="@+id/schedule_header_textview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginVertical="6dp"
            android:textColor="@color/unifiedsearch_schedule_date_color"
            android:textSize="16dp"
            android:textAlignment="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="2023年11月8日"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="24dp"
        android:paddingEnd="16dp"
        android:paddingVertical="10dp"
        android:orientation="horizontal">
        <com.jd.oa.ui.IconFontView
            android:id="@+id/schedule_item_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="12dp"
            android:text="@string/icon_prompt_circle"
            android:textColor="@color/unifiedsearch_joyday_uncheck_color"
            android:textSize="@dimen/JMEIcon_16"/>
        <ImageView
            android:id="@+id/schedule_item_circle"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginTop="5dp"
            tools:background="@color/colorPrimary"/>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="6dp"
            android:orientation="vertical">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:id="@+id/schedule_item_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:textColor="@color/unifiedsearch_title_text_color"
                    android:textSize="16dp"
                    android:textAlignment="center"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:drawablePadding="6dp"
                    android:drawableEnd="@drawable/unifiedsearch_ic_repeat"
                    tools:text="测试案例同步会测试案例同步会测试案例同步会测试案例同步会" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/layout_title"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:id="@+id/schedule_item_time_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="8dp"
                    android:textColor="@color/unifiedsearch_sub_title_text_color"
                    android:textSize="12dp"
                    android:textAlignment="center"
                    android:includeFontPadding="false"
                    tools:text="时间：18:00-18:30"/>

                <TextView
                    android:id="@+id/schedule_item_from_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:textColor="@color/unifiedsearch_sub_title_text_color"
                    android:textSize="12dp"
                    android:textAlignment="center"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="组织者：林晓晓"/>
                <TextView
                    android:id="@+id/schedule_item_calendar_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:textColor="@color/unifiedsearch_sub_title_text_color"
                    android:textSize="12dp"
                    android:textAlignment="center"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:visibility="visible"
                    tools:text="日历：王小波"/>
                <TextView
                    android:id="@+id/schedule_item_location_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    android:textColor="@color/unifiedsearch_sub_title_text_color"
                    android:textSize="12dp"
                    android:textAlignment="center"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="克罗地亚会议"/>
                <TextView
                    android:id="@+id/schedule_item_subcategory"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    android:textColor="@color/unifiedsearch_sub_title_text_color"
                    android:textSize="12dp"
                    android:textAlignment="center"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="类型：决策会"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
    <View
        android:id="@+id/schedule_day_separator"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="10dp"
        android:background="#E1E3E6"
        android:visibility="gone"
        tools:visibility="visible"/>

</LinearLayout>
