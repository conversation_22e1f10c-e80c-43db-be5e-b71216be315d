<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white"
    android:paddingHorizontal="16dp">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_org_image"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginVertical="12dp"
        tools:src="@drawable/unifiedsearch_organization_structure" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="10dp"
        android:layout_toEndOf="@id/iv_org_image"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/tv_org_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/unifiedsearch_color_1B1B1B"
                android:textSize="16sp"
                tools:text="组织架构" />
            <TextView
                android:id="@+id/tv_org_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/unifiedsearch_org_relate_label"
                android:textSize="9sp"
                android:textColor="@color/jdsaas_color_blue_1869F5"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp"
                android:layout_marginLeft="9dp"
                android:background="@drawable/unifiedsearch_org_label_bg" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_org_member"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/unifiedsearch_color_9D9D9D"
            android:textSize="14sp"
            tools:text="包含:测试" />
    </LinearLayout>

</RelativeLayout>