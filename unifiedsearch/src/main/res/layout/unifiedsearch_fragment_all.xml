<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/unifiedsearch_all_bg"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycle_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        tools:itemCount="20" />

    <include
        android:id="@+id/layout_loading"
        layout="@layout/unifiedsearch_layout_loading"
        android:visibility="invisible" />

    <LinearLayout
        android:id="@+id/layout_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="150dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/unifiedsearch_placeholder" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/unifiedsearch_placeholder_text"
            android:textColor="#FF8F959E" />
    </LinearLayout>
</FrameLayout>