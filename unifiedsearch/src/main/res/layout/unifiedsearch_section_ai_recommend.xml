<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/unifiedsearch_ai_section_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="8dp"
    android:layout_marginTop="12dp"
    android:layout_marginRight="8dp"
    android:padding="1dp"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardElevation="0dp"
        app:cardCornerRadius="8dp">
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"
                android:src="@drawable/unifiedsearch_airecommend_background"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginHorizontal="11dp">
                    <ImageView
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:id="@+id/title_image"
                        android:layout_marginEnd="4dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ai_search_recommend_scope"/>
                    <TextView
                        app:layout_constraintStart_toEndOf="@id/title_image"
                        android:layout_marginStart="4dp"
                        app:layout_constraintTop_toTopOf="@id/title_image"
                        app:layout_constraintBottom_toBottomOf="@id/title_image"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:text="@string/get_answers_from_ai"
                        android:textColor="@color/unifiedsearch_color_1B1B1B"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/ai_recommendation_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="vertical"
                    android:paddingBottom="3dp"
                    tools:layout_height="100dp" />
            </LinearLayout>
        </FrameLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>