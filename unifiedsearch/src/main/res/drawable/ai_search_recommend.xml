<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="17dp" android:viewportHeight="17" android:viewportWidth="16" android:width="16dp">
      
    <path android:fillColor="#E5E5E5" android:pathData="M0,0h16v17h-16z"/>
      
    <group>
            
        <clip-path android:pathData="M-118,-1474.43h6602v2375h-6602z"/>
            
        <path android:fillColor="#464646" android:pathData="M-118,-1474.43h6602v2375h-6602z"/>
            
        <group>
                  
            <clip-path android:pathData="M-24,-267.43h375v812h-375z"/>
                  
            <path android:fillColor="#F4F5F6" android:pathData="M-24,-267.43h375v812h-375z"/>
                  
            <path android:fillColor="#ffffff" android:pathData="M-12,-5.43C-12,-9.848 -8.418,-13.43 -4,-13.43H331C335.418,-13.43 339,-9.848 339,-5.43V230.57C339,234.989 335.418,238.57 331,238.57H-4C-8.418,238.57 -12,234.989 -12,230.57V-5.43Z"/>
                  
            <path android:fillAlpha="0.6" android:pathData="M-12,-5.43C-12,-9.848 -8.418,-13.43 -4,-13.43H331C335.418,-13.43 339,-9.848 339,-5.43V230.57C339,234.989 335.418,238.57 331,238.57H-4C-8.418,238.57 -12,234.989 -12,230.57V-5.43Z">
                        
                <aapt:attr name="android:fillColor">
                              
                    <gradient android:endX="-28.241" android:endY="169.537" android:startX="344.475" android:startY="-3.35" android:type="linear">
                                    
                        <item android:color="#FFD6F5FF" android:offset="0"/>
                                    
                        <item android:color="#FFE5F4FF" android:offset="0.16"/>
                                    
                        <item android:color="#FFF0F4FB" android:offset="0.361"/>
                                    
                        <item android:color="#FFFFF4F6" android:offset="0.621"/>
                                    
                        <item android:color="#FFFFF5F7" android:offset="0.795"/>
                                    
                        <item android:color="#FFFFF9EF" android:offset="1"/>
                                  
                    </gradient>
                            
                </aapt:attr>
                      
            </path>
                  
            <path android:fillColor="#00000000" android:pathData="M-11.5,-5.43C-11.5,-9.572 -8.142,-12.93 -4,-12.93H331C335.142,-12.93 338.5,-9.572 338.5,-5.43V230.57C338.5,234.712 335.142,238.07 331,238.07H-4C-8.142,238.07 -11.5,234.712 -11.5,230.57V-5.43Z" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1"/>
                  
            <group>
                        
                <clip-path android:pathData="M0,0.57h16v16h-16z"/>
                        
                <path android:fillType="evenOdd" android:pathData="M12.271,0.783L12.947,2.626C12.964,2.672 12.991,2.715 13.026,2.75C13.061,2.786 13.103,2.813 13.15,2.83L14.978,3.515C15.282,3.632 15.271,4.066 14.96,4.165L13.161,4.738C13.112,4.754 13.066,4.781 13.029,4.817C12.991,4.854 12.962,4.898 12.944,4.947L12.271,6.787C12.246,6.854 12.201,6.912 12.141,6.953C12.082,6.993 12.011,7.014 11.939,7.012C11.868,7.01 11.798,6.986 11.741,6.943C11.683,6.9 11.641,6.839 11.62,6.771L11.052,4.96C11.036,4.907 11.007,4.859 10.968,4.82C10.929,4.781 10.882,4.752 10.829,4.735L9.04,4.165C8.972,4.144 8.912,4.101 8.869,4.044C8.826,3.987 8.802,3.918 8.8,3.846C8.798,3.775 8.818,3.705 8.858,3.645C8.898,3.586 8.956,3.541 9.023,3.515L10.842,2.833C10.891,2.815 10.935,2.786 10.971,2.748C11.007,2.711 11.033,2.665 11.049,2.616L11.62,0.8C11.641,0.731 11.683,0.671 11.741,0.628C11.798,0.584 11.868,0.56 11.94,0.559C12.012,0.557 12.082,0.578 12.142,0.618C12.201,0.659 12.246,0.716 12.271,0.783ZM7.89,1.955C4.748,1.955 2.2,4.503 2.2,7.646C2.2,10.789 4.748,13.336 7.89,13.336C11.033,13.336 13.581,10.789 13.581,7.646C13.581,7.434 13.569,7.226 13.547,7.021C13.511,6.691 13.749,6.395 14.078,6.359C14.408,6.323 14.704,6.561 14.74,6.89C14.767,7.139 14.781,7.391 14.781,7.646C14.781,9.534 14.021,11.246 12.79,12.49L15.025,14.725C15.26,14.96 15.26,15.34 15.025,15.574C14.791,15.808 14.411,15.808 14.177,15.574L11.873,13.27C10.748,14.067 9.374,14.536 7.89,14.536C4.085,14.536 1,11.451 1,7.646C1,3.84 4.085,0.755 7.89,0.755C8.381,0.755 8.861,0.807 9.324,0.905C9.648,0.974 9.856,1.292 9.787,1.616C9.718,1.94 9.4,2.148 9.076,2.079C8.694,1.998 8.297,1.955 7.89,1.955Z">
                              
                    <aapt:attr name="android:fillColor">
                                    
                        <gradient android:endX="3.06" android:endY="11.883" android:startX="13.143" android:startY="11.607" android:type="linear">
                                          
                            <item android:color="#FF524EFE" android:offset="0"/>
                                          
                            <item android:color="#FFC15CFF" android:offset="1"/>
                                        
                        </gradient>
                                  
                    </aapt:attr>
                            
                </path>
                      
            </group>
                
        </group>
          
    </group>
    
</vector>
