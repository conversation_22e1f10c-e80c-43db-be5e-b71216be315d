<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="20dp"
    android:viewportWidth="32"
    android:viewportHeight="20">
    <group>
        <clip-path android:pathData="M0,0h32v20h-32z" />
        <path
            android:fillAlpha="0.7"
            android:fillColor="#9CA2FA"
            android:pathData="M9.51,12.859a1.464,6.089 93.958,1 0,12.149 0.841a1.464,6.089 93.958,1 0,-12.149 -0.841z" />
        <path
            android:fillAlpha="0.7"
            android:fillColor="#9CA2FA"
            android:pathData="M0.242,7.777a1.464,6.089 93.958,1 0,12.149 0.841a1.464,6.089 93.958,1 0,-12.149 -0.841z" />
        <group>
            <clip-path android:pathData="M1.076,0.469l18.633,0.326l-0.326,18.633l-18.633,-0.326z" />
            <path
                android:fillColor="#76777A"
                android:pathData="M13.25,4.55C13.25,4.55 14.809,1.99 15.953,2.12C17.306,2.275 18.506,8.289 18.451,11.459" />
            <path
                android:fillColor="#76777A"
                android:pathData="M7.428,4.447C7.428,4.447 5.96,1.834 4.811,1.925C3.453,2.032 2.044,8 1.988,11.17" />
            <path
                android:fillColor="#76777A"
                android:pathData="M18.444,11.417C18.35,16.769 14.649,17.861 10.108,17.782C5.568,17.702 1.909,16.341 2,11.13C2.077,6.713 5.821,3.197 10.362,3.276C14.903,3.356 18.521,7.001 18.444,11.417Z" />
            <path
                android:fillColor="#ffffff"
                android:pathData="M16.128,14.083C13.047,14.029 12.937,11.88 10.193,11.823C7.449,11.784 7.264,13.928 4.183,13.874C2.664,13.848 1.972,12.869 1.957,11.874C1.979,14.426 3.238,17.752 10.087,17.872C16.936,17.992 18.313,14.706 18.423,12.156C18.375,13.153 17.649,14.11 16.128,14.084L16.128,14.083Z" />
            <path
                android:fillColor="#D9D8D6"
                android:pathData="M15.078,4.822L15.774,3.379C15.87,3.178 16.161,3.197 16.232,3.407C16.454,4.07 16.803,5.275 16.988,6.739C16.988,6.739 16.323,5.674 15.078,4.822L15.078,4.822Z" />
            <path android:pathData="M10.192,11.863C12.936,11.919 13.046,14.069 16.127,14.123C17.413,14.145 18.131,13.464 18.351,12.651C18.398,12.289 18.427,11.904 18.434,11.494C18.448,10.714 18.399,9.899 18.191,9.1C17.577,8.681 15.181,7.487 9.955,7.396C4.73,7.305 2.76,8.489 2.167,8.939C2.075,9.669 1.983,10.405 1.969,11.206C1.962,11.624 1.979,12.018 2.017,12.388C2.216,13.199 2.908,13.892 4.183,13.914C7.263,13.968 7.448,11.823 10.193,11.863L10.192,11.863Z">
                <aapt:attr name="android:fillColor">
                    <gradient
                        android:endX="7.723"
                        android:endY="15.178"
                        android:startX="6.114"
                        android:startY="7.798"
                        android:type="linear">
                        <item
                            android:color="#FF2454FF"
                            android:offset="0" />
                        <item
                            android:color="#FF9F79FF"
                            android:offset="0.455" />
                        <item
                            android:color="#FFFFB0DB"
                            android:offset="1" />
                    </gradient>
                </aapt:attr>
            </path>
            <path
                android:fillColor="#00000000"
                android:pathData="M7.622,12.895C8.345,12.42 8.964,12.015 10.131,12.031C11.299,12.055 11.903,12.483 12.609,12.982C12.628,12.995 12.648,13.009 12.668,13.023C13.43,13.562 14.292,14.138 15.919,14.167C17.347,14.192 18.229,13.44 18.498,12.472L18.506,12.444L18.509,12.416C18.558,12.057 18.587,11.678 18.594,11.276C18.595,11.197 18.597,11.119 18.598,11.041L18.599,10.98C18.624,9.662 17.91,8.416 16.601,7.977C15.205,7.51 13.017,7.032 9.913,6.978C6.824,6.924 4.861,7.307 3.636,7.73C2.452,8.14 1.793,9.28 1.722,10.45C1.712,10.624 1.704,10.8 1.701,10.98C1.694,11.392 1.711,11.781 1.751,12.149L1.754,12.178L1.761,12.206C2.006,13.172 2.859,13.939 4.273,13.963C5.899,13.992 6.781,13.446 7.562,12.934C7.582,12.921 7.602,12.908 7.622,12.895Z"
                android:strokeWidth="0.837831">
                <aapt:attr name="android:strokeColor">
                    <gradient
                        android:endX="2.139"
                        android:endY="9.823"
                        android:startX="19.735"
                        android:startY="10.691"
                        android:type="linear">
                        <item
                            android:color="#FFA7EAFF"
                            android:offset="0" />
                        <item
                            android:color="#FFFFFFFF"
                            android:offset="0.542" />
                        <item
                            android:color="#FFA6E9FF"
                            android:offset="1" />
                    </gradient>
                </aapt:attr>
            </path>
            <path
                android:fillColor="#00000000"
                android:pathData="M7.622,12.895C8.345,12.42 8.964,12.015 10.131,12.031C11.299,12.055 11.903,12.483 12.609,12.982C12.628,12.995 12.648,13.009 12.668,13.023C13.43,13.562 14.292,14.138 15.919,14.167C17.347,14.192 18.229,13.44 18.498,12.472L18.506,12.444L18.509,12.416C18.558,12.057 18.587,11.678 18.594,11.276C18.595,11.197 18.597,11.119 18.598,11.041L18.599,10.98C18.624,9.662 17.91,8.416 16.601,7.977C15.205,7.51 13.017,7.032 9.913,6.978C6.824,6.924 4.861,7.307 3.636,7.73C2.452,8.14 1.793,9.28 1.722,10.45C1.712,10.624 1.704,10.8 1.701,10.98C1.694,11.392 1.711,11.781 1.751,12.149L1.754,12.178L1.761,12.206C2.006,13.172 2.859,13.939 4.273,13.963C5.899,13.992 6.781,13.446 7.562,12.934C7.582,12.921 7.602,12.908 7.622,12.895Z"
                android:strokeWidth="0.837831">
                <aapt:attr name="android:strokeColor">
                    <gradient
                        android:endX="2.139"
                        android:endY="9.823"
                        android:startX="19.735"
                        android:startY="10.691"
                        android:type="linear">
                        <item
                            android:color="#FFA7EAFF"
                            android:offset="0" />
                        <item
                            android:color="#FFFFFFFF"
                            android:offset="0.542" />
                        <item
                            android:color="#FFA6E9FF"
                            android:offset="1" />
                    </gradient>
                </aapt:attr>
            </path>
            <path
                android:fillColor="#00000000"
                android:pathData="M7.622,12.895C8.345,12.42 8.964,12.015 10.131,12.031C11.299,12.055 11.903,12.483 12.609,12.982C12.628,12.995 12.648,13.009 12.668,13.023C13.43,13.562 14.292,14.138 15.919,14.167C17.347,14.192 18.229,13.44 18.498,12.472L18.506,12.444L18.509,12.416C18.558,12.057 18.587,11.678 18.594,11.276C18.595,11.197 18.597,11.119 18.598,11.041L18.599,10.98C18.624,9.662 17.91,8.416 16.601,7.977C15.205,7.51 13.017,7.032 9.913,6.978C6.824,6.924 4.861,7.307 3.636,7.73C2.452,8.14 1.793,9.28 1.722,10.45C1.712,10.624 1.704,10.8 1.701,10.98C1.694,11.392 1.711,11.781 1.751,12.149L1.754,12.178L1.761,12.206C2.006,13.172 2.859,13.939 4.273,13.963C5.899,13.992 6.781,13.446 7.562,12.934C7.582,12.921 7.602,12.908 7.622,12.895Z"
                android:strokeWidth="0.837831">
                <aapt:attr name="android:strokeColor">
                    <gradient
                        android:endX="2.139"
                        android:endY="9.823"
                        android:startX="19.735"
                        android:startY="10.691"
                        android:type="linear">
                        <item
                            android:color="#FFA7EAFF"
                            android:offset="0" />
                        <item
                            android:color="#FFFFFFFF"
                            android:offset="0.542" />
                        <item
                            android:color="#FFA6E9FF"
                            android:offset="1" />
                    </gradient>
                </aapt:attr>
            </path>
            <path
                android:fillColor="#00000000"
                android:pathData="M10.138,11.612C12.814,11.667 12.923,13.696 15.926,13.748C17.181,13.77 17.881,13.127 18.094,12.36C18.14,12.018 18.168,11.655 18.175,11.268C18.177,11.169 18.179,11.07 18.18,10.972C18.202,9.805 17.575,8.745 16.468,8.374C15.115,7.921 12.97,7.45 9.905,7.397C6.854,7.343 4.941,7.722 3.773,8.126C2.785,8.468 2.203,9.432 2.14,10.476C2.13,10.644 2.123,10.814 2.12,10.988C2.113,11.382 2.13,11.754 2.168,12.104C2.362,12.869 3.037,13.523 4.28,13.545C7.284,13.597 7.463,11.573 10.139,11.612L10.138,11.612Z"
                android:strokeWidth="0.209458">
                <aapt:attr name="android:strokeColor">
                    <gradient
                        android:endX="2.131"
                        android:endY="10.384"
                        android:startX="18.186"
                        android:startY="10.664"
                        android:type="linear">
                        <item
                            android:color="#FF2CE8FB"
                            android:offset="0" />
                        <item
                            android:color="#FFFFFFFF"
                            android:offset="0.491" />
                        <item
                            android:color="#FF2BE7FA"
                            android:offset="1" />
                    </gradient>
                </aapt:attr>
            </path>
            <path
                android:fillAlpha="0.5"
                android:fillColor="#DADADA"
                android:pathData="M9.163,9.567C9.458,9.25 9.262,8.586 8.724,8.084C8.186,7.583 7.51,7.433 7.214,7.75C6.919,8.067 7.115,8.731 7.653,9.232C8.192,9.734 8.867,9.884 9.163,9.567Z"
                android:strokeAlpha="0.5" />
            <path
                android:fillAlpha="0.5"
                android:fillColor="#DADADA"
                android:pathData="M12.852,9.323C13.408,8.84 13.627,8.184 13.343,7.857C13.059,7.53 12.378,7.656 11.823,8.138C11.267,8.621 11.048,9.277 11.332,9.604C11.616,9.931 12.297,9.805 12.852,9.323Z"
                android:strokeAlpha="0.5" />
            <path
                android:fillColor="#544A90"
                android:pathData="M7.031,12.181C7.634,12.191 8.133,11.6 8.146,10.861C8.159,10.122 7.681,9.514 7.078,9.503C6.475,9.493 5.976,10.084 5.963,10.823C5.95,11.562 6.428,12.17 7.031,12.181Z" />
            <path
                android:fillColor="#544A90"
                android:pathData="M13.364,12.294C13.967,12.304 14.466,11.714 14.479,10.974C14.492,10.235 14.013,9.627 13.41,9.617C12.808,9.606 12.308,10.197 12.295,10.936C12.283,11.676 12.761,12.283 13.364,12.294Z" />
            <path
                android:fillColor="#D9D8D6"
                android:pathData="M5.588,4.657L4.944,3.19C4.855,2.986 4.563,2.994 4.485,3.202C4.24,3.857 3.849,5.049 3.613,6.506C3.613,6.506 4.314,5.464 5.588,4.657L5.588,4.657Z" />
            <group>
                <clip-path android:pathData="M10.193,11.867C12.936,11.923 13.047,14.073 16.127,14.127C17.414,14.149 18.132,13.467 18.351,12.655C18.399,12.293 18.427,11.908 18.434,11.498C18.448,10.717 18.399,9.903 18.191,9.104C17.578,8.685 15.182,7.491 9.956,7.4C4.73,7.309 2.761,8.493 2.167,8.943C2.075,9.673 1.984,10.408 1.97,11.21C1.962,11.628 1.979,12.022 2.018,12.392C2.216,13.203 2.909,13.896 4.183,13.918C7.264,13.972 7.449,11.827 10.193,11.867L10.193,11.867Z" />
                <path
                    android:fillAlpha="0.26"
                    android:fillColor="#B1EDFF"
                    android:pathData="M5.699,13.672C7.992,8.641 9.049,6.412 9.049,6.412L12.653,6.475C12.653,6.475 11.295,8.911 9.218,13.733L5.699,13.672Z" />
                <path
                    android:fillAlpha="0.15"
                    android:fillColor="#ffffff"
                    android:pathData="M10.626,13.344C12.919,8.313 13.976,6.084 13.976,6.084L15.502,6.11C15.502,6.11 14.145,8.546 12.068,13.369L10.626,13.344Z" />
            </group>
            <path
                android:fillColor="#76777A"
                android:pathData="M9.789,17.883C9.892,17.886 9.996,17.889 10.1,17.891C9.995,17.889 9.892,17.886 9.789,17.883Z" />
            <path android:pathData="M12.179,13.553C12.168,14.189 11.271,14.869 10.18,14.85C9.089,14.83 8.217,14.12 8.228,13.484C8.239,12.848 9.129,12.527 10.22,12.546C11.311,12.565 12.19,12.917 12.179,13.553Z">
                <aapt:attr name="android:fillColor">
                    <gradient
                        android:centerX="10.208"
                        android:centerY="13.335"
                        android:gradientRadius="2.163"
                        android:type="radial">
                        <item
                            android:color="#FF6E6E6E"
                            android:offset="0" />
                        <item
                            android:color="#FF222222"
                            android:offset="0.645" />
                    </gradient>
                </aapt:attr>
            </path>
        </group>
    </group>
    <path
        android:fillType="evenOdd"
        android:pathData="M28.346,8.59C28.005,8.59 27.706,8.818 27.615,9.146L26.279,13.985L24.976,9.161C24.889,8.838 24.596,8.613 24.261,8.613C24.237,8.613 24.213,8.614 24.189,8.617C24.177,8.614 24.165,8.613 24.152,8.613H23.342C22.99,8.613 22.669,8.814 22.514,9.13L19.364,15.592C19.215,15.898 19.438,16.254 19.778,16.254H20.171C20.523,16.254 20.844,16.054 20.999,15.737L21.91,13.868L24.761,13.868L25.244,15.583C25.355,15.98 25.717,16.254 26.13,16.254H26.29C26.307,16.254 26.324,16.253 26.34,16.252H26.526C26.94,16.252 27.303,15.975 27.413,15.576L29.077,9.55C29.211,9.067 28.847,8.59 28.346,8.59ZM24.455,12.777L23.718,10.159L22.442,12.777H24.455Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="29.206"
                android:endY="16.073"
                android:startX="24.698"
                android:startY="7.081"
                android:type="linear">
                <item
                    android:color="#FF528DF8"
                    android:offset="0" />
                <item
                    android:color="#FF8064F6"
                    android:offset="0.315" />
                <item
                    android:color="#FFC25BBD"
                    android:offset="0.635" />
                <item
                    android:color="#FFFF6178"
                    android:offset="0.848" />
                <item
                    android:color="#FFFF8965"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M29.41,5.049L29.028,3.939C29.014,3.898 28.988,3.864 28.953,3.839C28.918,3.814 28.876,3.8 28.833,3.801C28.79,3.801 28.748,3.814 28.713,3.839C28.678,3.864 28.652,3.9 28.638,3.941L28.275,5.019C28.265,5.049 28.248,5.076 28.226,5.098C28.204,5.12 28.178,5.137 28.148,5.147L27.052,5.532C27.012,5.546 26.977,5.573 26.952,5.608C26.928,5.643 26.915,5.685 26.915,5.727C26.915,5.77 26.929,5.812 26.954,5.846C26.979,5.881 27.014,5.907 27.055,5.921L28.117,6.284C28.148,6.294 28.177,6.313 28.199,6.336C28.222,6.36 28.239,6.389 28.248,6.421L28.565,7.511C28.577,7.552 28.601,7.588 28.635,7.615C28.669,7.641 28.71,7.657 28.753,7.659C28.796,7.661 28.838,7.649 28.874,7.626C28.91,7.602 28.938,7.568 28.954,7.528L29.379,6.437C29.39,6.407 29.408,6.381 29.431,6.36C29.454,6.339 29.482,6.323 29.512,6.314L30.594,5.994C30.781,5.939 30.793,5.679 30.613,5.606L29.528,5.174C29.501,5.163 29.476,5.146 29.455,5.124C29.435,5.103 29.419,5.077 29.41,5.049Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="31.573"
                android:endY="7.097"
                android:startX="29.075"
                android:startY="3.046"
                android:type="linear">
                <item
                    android:color="#FF528DF8"
                    android:offset="0" />
                <item
                    android:color="#FF8064F6"
                    android:offset="0.315" />
                <item
                    android:color="#FFC25BBD"
                    android:offset="0.635" />
                <item
                    android:color="#FFFF6178"
                    android:offset="0.848" />
                <item
                    android:color="#FFFF8965"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>
