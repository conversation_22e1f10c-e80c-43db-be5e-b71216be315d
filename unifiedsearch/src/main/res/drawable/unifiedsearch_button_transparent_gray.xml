<?xml version="1.0" encoding="utf-8"?>
<selector
    xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="true" android:state_pressed="true">
        <shape>
            <solid android:color="#e5e5e5"/>
        </shape>
    </item>
    <item android:state_enabled="true" android:state_focused="true">
        <shape>
            <solid android:color="#e5e5e5"/>
        </shape>
    </item>
    <item android:state_selected="true">
        <shape>
            <solid android:color="#e5e5e5"/>
        </shape>
    </item>

    <!--<item android:state_enabled="false">-->
        <!--<shape>-->
            <!--<solid android:color="@color/colorWhiteGray"/>-->
        <!--</shape>-->
    <!--</item>-->
    <item>
        <shape>
            <solid android:color="@color/white"/>
        </shape>
    </item>
</selector>

