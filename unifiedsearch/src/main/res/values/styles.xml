<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="UnifiedSearchJoySpaceFilterStyle">
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:button">@null</item>
        <item name="android:textColor">@color/unifiedsearch_color_joyspace_type</item>
        <item name="android:textSize">14sp</item>
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="UnifiedSearchFilterOptionDialogStyle" parent="BottomDialogStyle">
        <item name="android:windowAnimationStyle">@style/UnifiedSearchBottomDialogAnimation</item>
    </style>

    <style name="UnifiedSearchBottomDialogAnimation" parent="Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/unifiedsearch_bottom_dialog_slide_in</item>
        <item name="android:windowExitAnimation">@anim/unifiedsearch_bottom_dialog_slide_out</item>
    </style>

    <style name="UnifiedSearch_customProgressDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
    </style>
</resources>