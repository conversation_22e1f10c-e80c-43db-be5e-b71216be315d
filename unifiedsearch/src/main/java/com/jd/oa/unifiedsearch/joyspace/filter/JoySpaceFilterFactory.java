package com.jd.oa.unifiedsearch.joyspace.filter;

import androidx.collection.ArrayMap;

import java.util.LinkedHashMap;
import java.util.Map;

public class JoySpaceFilterFactory {

    private final ArrayMap<String,JoySpaceFilter<?>> sCache = new ArrayMap<>();

    public Map<String,JoySpaceFilter<?>> getFiltersByType(FilterContext filterContext, @JoySpaceFilter.FilterType String type) {
        Map<String,JoySpaceFilter<?>> filters = new LinkedHashMap<>();
        if (JoySpaceFilter.TYPE_RELATED.equals(type)) {
            filters.put(JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS, getSubType(filterContext, JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS));
            filters.put(JoySpaceFilter.SUBTYPE_SENDER, getSubType(filterContext, JoySpaceFilter.SUBTYPE_SENDER));
            filters.put(JoySpaceFilter.SUBTYPE_RECEIVER, getSubType(filterContext, JoySpaceFilter.SUBTYPE_RECEIVER));
            filters.put(JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE, getSubType(filterContext, JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE));
            filters.put(JoySpaceFilter.SUBTYPE_OPENED_TIME, getSubType(filterContext, JoySpaceFilter.SUBTYPE_OPENED_TIME));
        } else if (JoySpaceFilter.TYPE_RECENT.equals(type)) {
            filters.put(JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS, getSubType(filterContext, JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS));
            filters.put(JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE, getSubType(filterContext, JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE));
            filters.put(JoySpaceFilter.SUBTYPE_OPENED_TIME, getSubType(filterContext, JoySpaceFilter.SUBTYPE_OPENED_TIME));
        } else if (JoySpaceFilter.TYPE_RECEIVED.equals(type)) {
            filters.put(JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS, getSubType(filterContext, JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS));
            filters.put(JoySpaceFilter.SUBTYPE_SENDER, getSubType(filterContext, JoySpaceFilter.SUBTYPE_SENDER));
            filters.put(JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE, getSubType(filterContext, JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE));
            filters.put(JoySpaceFilter.SUBTYPE_OPENED_TIME, getSubType(filterContext, JoySpaceFilter.SUBTYPE_OPENED_TIME));
        } else if (JoySpaceFilter.TYPE_SENT.equals(type)) {
            filters.put(JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS, getSubType(filterContext, JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS));
            filters.put(JoySpaceFilter.SUBTYPE_RECEIVER, getSubType(filterContext, JoySpaceFilter.SUBTYPE_RECEIVER));
            filters.put(JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE, getSubType(filterContext, JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE));
            filters.put(JoySpaceFilter.SUBTYPE_OPENED_TIME, getSubType(filterContext, JoySpaceFilter.SUBTYPE_OPENED_TIME));
        } else if (JoySpaceFilter.TYPE_CREATED.equals(type)) {
            filters.put(JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE, getSubType(filterContext, JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE));
            filters.put(JoySpaceFilter.SUBTYPE_OPENED_TIME, getSubType(filterContext, JoySpaceFilter.SUBTYPE_OPENED_TIME));
        } else if (JoySpaceFilter.TYPE_PUBLIC.equals(type)) {
            filters.put(JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS, getSubType(filterContext, JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS));
            filters.put(JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE, getSubType(filterContext, JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE));
            filters.put(JoySpaceFilter.SUBTYPE_OPENED_TIME, getSubType(filterContext, JoySpaceFilter.SUBTYPE_OPENED_TIME));
        }
        return filters;
    }

    public JoySpaceFilter<?> getSubType(FilterContext filterContext, @JoySpaceFilter.FilterSubType String subType) {
        JoySpaceFilter<?> filter = sCache.get(subType);
        if (filter != null) {
            return filter;
        }
        if (JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS.equals(subType)) {
            filter = new CreateByOthersFilter(filterContext);
        } else if (JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE.equals(subType)) {
            filter = new DocumentTypeFilter(filterContext);
        } else if (JoySpaceFilter.SUBTYPE_OPENED_TIME.equals(subType)) {
            filter = new OpenedTimeFilter(filterContext);
        } else if (JoySpaceFilter.SUBTYPE_SENDER.equals(subType)) {
            filter = new SenderFilter(filterContext);
        } else if (JoySpaceFilter.SUBTYPE_RECEIVER.equals(subType)) {
            filter = new ReceiverFilter(filterContext);
        }
        sCache.put(subType, filter);
        return filter;
    }

    public void clearCache() {
        sCache.clear();
    }

    public ArrayMap<String, JoySpaceFilter<?>> getCache() {
        return sCache;
    }
}