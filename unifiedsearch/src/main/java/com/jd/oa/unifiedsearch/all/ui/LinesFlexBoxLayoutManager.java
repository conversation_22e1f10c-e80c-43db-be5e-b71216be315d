package com.jd.oa.unifiedsearch.all.ui;

import android.content.Context;

import com.google.android.flexbox.FlexLine;
import com.google.android.flexbox.FlexboxLayoutManager;

import java.util.List;

public class LinesFlexBoxLayoutManager extends FlexboxLayoutManager {

    /**
     * 最大行数
     * <p>
     * 小于等于0时，不限制行数
     */
    private int maxLines = 0;

    /**
     * 设置最大显示行数
     *
     * @param maxLines
     */
    public void setMaxLines(int maxLines) {
        this.maxLines = maxLines;
    }

    public LinesFlexBoxLayoutManager(Context context) {
        super(context);
    }

    public LinesFlexBoxLayoutManager(Context context, int flexDirection) {
        super(context, flexDirection);
    }

    public LinesFlexBoxLayoutManager(Context context, int flexDirection, int flexWrap) {
        super(context, flexDirection, flexWrap);
    }

    /**
     * 删掉多余的行数据
     */
    @Override
    public List<FlexLine> getFlexLinesInternal() {
        List<FlexLine> flexLines = super.getFlexLinesInternal();
        int size = flexLines.size();
        if (maxLines > 0 && size > maxLines) {
            flexLines.subList(maxLines, size).clear();
        }
        return flexLines;
    }
}