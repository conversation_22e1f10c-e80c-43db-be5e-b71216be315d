package com.jd.oa.unifiedsearch.all.data;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.List;

/*
 * Time: 2023/10/27
 * Author: qudongshi
 * Description:
 */
public class SearchAIModel implements Serializable {

    public String status; // 是否结束：no 没有数据、finished 结束、loading 还在执行中、running 搜索中
    public String traceId;
    public String erp;
    public boolean finished;
    public List<RelDoc> relDoc;
    public String requestId;
    public String responseAll;
    public String response;
    public String sessionId;
    public String keyword;
    public List<RelApp> relApp;

    public class RelDoc {
        public String name;
        public String pageContent;
        public String pageName;
        public int paragraphIndex;

        public String deeplink;
    }

    public class RelApp {
        public String appAvatar;
        public String appName;
        public String appPin;
        public String appTenant;
        public String appType;
    }

    public boolean isLoading() {
        return "loading".equals(status);
    }

    public boolean isRunning() {
        return "running".equals(status);
    }

    public boolean noResult() {
        return "no".equals(status);
    }

    public boolean finished() {
        return "finished".equals(status);
    }

    public boolean hasTraceId() {
        return !TextUtils.isEmpty(traceId);
    }

    public boolean hasContent() {
        return !TextUtils.isEmpty(response);
    }

    public boolean hasInfo() {
        if (relDoc != null && relDoc.size() > 0) {
            for (RelDoc doc : relDoc) {
                if (!TextUtils.isEmpty(doc.deeplink)) {
                    return true;
                }
            }
        }
        return false;
    }
}
