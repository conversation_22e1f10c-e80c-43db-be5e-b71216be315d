package com.jd.oa.unifiedsearch.process.data;

public class HighlightJoin {
    private Highlight highlight;
    private HighlightSort[] highlightSort;
    private String id;

    public Highlight getHighlight() {
        return highlight;
    }

    public void setHighlight(Highlight highlight) {
        this.highlight = highlight;
    }

    public HighlightSort[] getHighlightSort() {
        return highlightSort;
    }

    public void setHighlightSort(HighlightSort[] highlightSort) {
        this.highlightSort = highlightSort;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public class Highlight {
        private String description;
        private String processDefinitionName;

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getProcessDefinitionName() {
            return processDefinitionName;
        }

        public void setProcessDefinitionName(String processDefinitionName) {
            this.processDefinitionName = processDefinitionName;
        }
    }

    public class HighlightSort {
        private String value;
        private String key;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
}