package com.jd.oa.unifiedsearch.all.helper;

import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.configuration.local.MeAiConfigHelper;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.unifiedsearch.all.callback.IDataLoadedCallback;
import com.jd.oa.unifiedsearch.all.data.SearchAIModel;
import com.jd.oa.unifiedsearch.all.data.SearchApprovalModel;
import com.jd.oa.unifiedsearch.all.data.SearchDiscoveryModel;
import com.jd.oa.unifiedsearch.all.data.SearchModel;
import com.jd.oa.unifiedsearch.all.data.SearchProblemAIModel;
import com.jd.oa.unifiedsearch.all.data.SearchProblemModel;
import com.jd.oa.unifiedsearch.all.data.SearchProcessModel;
import com.jd.oa.unifiedsearch.org.OrgRecord;
import com.jd.oa.unifiedsearch.org.OrgSearchModel;
import com.jd.oa.unifiedsearch.process.data.ProcessInfo;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Time: 2023/10/26
 * Author: qudongshi
 * Description:
 */
public class SearchHelper {
    private static final String TAG = "SearchHelper";

    public static void searchAppInfo(String keyword, IDataLoadedCallback callback, String sessionId, String requestId) {
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.APP.toString(), true);
        NetWorkManagerAppCenter.searchAppInfo(null, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APP.toString(), dataLength + "");
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    List<AppInfo> appList = response.getListData(AppInfo.class, "appList");
                    callback.onResult(SearchFloorType.APP, keyword, appList);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APP.toString(), dataLength + "");
                super.onFailure(exception, info);
                callback.onResult(SearchFloorType.APP, keyword, new ArrayList<>());
            }
        }, keyword);
    }

    public static void searchByFloorType(String keyword, IDataLoadedCallback callback, SearchFloorType floorType, String sessionId, String requestId) {
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, floorType.toString(), true);
        HashMap<String, Object> params = new HashMap<>();
        params.put("keyword", keyword);
        if (floorType == SearchFloorType.APPROVAL) {
            params.put("origin", "AUDIT");
        } else {
            params.put("origin", floorType.toString());
        }
        params.put("start", 0);
        params.put("size", 3);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, floorType.toString(), dataLength + "");
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    String info = response.getData(String.class);
                    if ((SearchFloorType.JOY_SPACE == floorType || SearchFloorType.TASK == floorType) && !TextUtils.isEmpty(info)) {
                        SearchModel data = JsonUtils.getGson().fromJson(info, new TypeToken<SearchModel>() {
                        }.getType());
                        if (data != null && data.docs != null && data.docs.size() > 0) {
                            if (data.docs.get(0).records != null) {
                                callback.onResult(floorType, keyword, data.docs.get(0).records);
                                return;
                            }
                        }
                        callback.onResult(floorType, keyword, new ArrayList<>());
                    } else if (SearchFloorType.ENCYCLO == floorType && !TextUtils.isEmpty(info)) {
                        SearchModel data = JsonUtils.getGson().fromJson(info, new TypeToken<SearchModel>() {
                        }.getType());
                        if (data != null && data.cards != null && data.cards.size() > 0) {
                            if (data.cards != null && data.cards.size() > 0) {
                                callback.onResult(floorType, keyword, data.cards);
                                return;
                            }
                        }
                        callback.onResult(floorType, keyword, new ArrayList<>());
                    } else if (SearchFloorType.PROCESS == floorType && !TextUtils.isEmpty(info)) {
                        SearchProcessModel searchModelResponse = JsonUtils.getGson().fromJson(info, new TypeToken<SearchProcessModel>() {
                        }.getType());
                        if(searchModelResponse == null || searchModelResponse.docs == null
                                || searchModelResponse.docs.isEmpty() || searchModelResponse.docs.get(0).records.isEmpty()) {
                            //搜索结果为空
                            callback.onResult(floorType, keyword, new ArrayList<>());
                        } else {
                            List<ProcessInfo> recordList = searchModelResponse.docs.get(0).records;
                            List<ProcessInfo> processInfoList = new ArrayList<>();
                            for (int i = 0; i < recordList.size(); i++) {
                                if(recordList.get(i).refPro != null) {
                                    processInfoList.add(recordList.get(i));
                                }
                            }
                            callback.onResult(floorType, keyword, processInfoList);
                        }
                    } else if (SearchFloorType.APPROVAL == floorType && !TextUtils.isEmpty(info)) {
                        SearchApprovalModel searchModelResponse = JsonUtils.getGson().fromJson(info, new TypeToken<SearchApprovalModel>() {
                        }.getType());
                        if(searchModelResponse == null || searchModelResponse.docs == null
                                || searchModelResponse.docs.isEmpty() || searchModelResponse.docs.get(0).records.isEmpty()) {
                            //搜索结果为空
                            callback.onResult(floorType, keyword, new ArrayList<>());
                        } else {
                            callback.onResult(floorType, keyword, searchModelResponse.docs.get(0).records);
                        }
                    } else if (SearchFloorType.ORG == floorType && !TextUtils.isEmpty(info)){
                        OrgSearchModel orgSearchModel = JsonUtils.getGson().fromJson(info, new TypeToken<OrgSearchModel>() {}.getType());
                        if(orgSearchModel == null || orgSearchModel.docs == null
                                || orgSearchModel.docs.isEmpty() || orgSearchModel.docs.get(0).records.isEmpty()) {
                            //搜索结果为空
                            callback.onResult(floorType,keyword,new ArrayList<>());
                        } else {
                            List<OrgRecord> recordList = orgSearchModel.docs.get(0).records;
                            List<OrgRecord> orgRecords = new ArrayList<>();
                            for (int i = 0; i < recordList.size(); i++) {
                                if (recordList.get(i).refPro != null) {
                                    orgRecords.add(recordList.get(i));
                                }
                            }
                            //部门做个延迟加载，看之前京ME逻辑，需要等IM那边加载完成再加载其他，因为时序原因有加载不出来的问题
                            new Handler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    callback.onResult(floorType,keyword,orgRecords);
                                }
                            },100);
                        }
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, floorType.toString(), dataLength + "");
                super.onFailure(exception, info);
                callback.onResult(floorType, keyword, new ArrayList<>());
            }
        }, "jdme.search.search");
    }

    public static void searchByFloorType(String keyword, IDataLoadedCallback callback, SearchFloorType floorType, int start, int size) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("keyword", keyword);
        params.put("origin", floorType.toString());
        params.put("start", start);
        params.put("size", size);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    String info = response.getData(String.class);
                    if ((SearchFloorType.JOY_SPACE == floorType || SearchFloorType.TASK == floorType) && !TextUtils.isEmpty(info)) {
                        SearchModel data = JsonUtils.getGson().fromJson(info, new TypeToken<SearchModel>() {
                        }.getType());
                        if (data != null && data.docs != null && data.docs.size() > 0) {
                            if (data.docs.get(0).records != null) {
                                callback.onResult(floorType, keyword, data.docs.get(0).records);
                                return;
                            }
                        }
                        callback.onResult(floorType, keyword, new ArrayList<>());
                    } else if (SearchFloorType.ENCYCLO == floorType && !TextUtils.isEmpty(info)) {
                        SearchModel data = JsonUtils.getGson().fromJson(info, new TypeToken<SearchModel>() {
                        }.getType());
                        if (data != null && data.cards != null && data.cards.size() > 0) {
                            if (data.cards != null && data.cards.size() > 0) {
                                callback.onResult(floorType, keyword, data.cards);
                                return;
                            }
                        }
                        callback.onResult(floorType, keyword, new ArrayList<>());
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onResult(floorType, keyword, new ArrayList<>());
            }
        }, "jdme.search.search");
    }

    public static void searchAI(String keyword, String traceId, IDataLoadedCallback callback, String sessionId, String requestId) {
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.AI.toString(), true);
        HashMap<String, Object> params = new HashMap<>();
        params.put("keyword", keyword);
        params.put("traceId", traceId);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.AI.toString(), dataLength + "");
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    String info = response.getData(String.class);
                    SearchAIModel data = JsonUtils.getGson().fromJson(info, new TypeToken<SearchAIModel>() {
                    }.getType());
                    if (data == null) {
                        return;
                    }
                    if (data.hasTraceId() && data.isLoading()) {
                        SearchConfigHelper.getInstance().getMainHandler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                searchAI(keyword, data.traceId, callback, sessionId, requestId);
                            }
                        }, 300);
                    } else if (data.hasTraceId() && (data.isRunning() || data.finished())) {
                        List<SearchAIModel> listData = new ArrayList<>();
                        listData.add(data);
                        callback.onResult(SearchFloorType.AI, keyword, listData);
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.AI.toString(), dataLength + "");
                super.onFailure(exception, info);
                callback.onResult(SearchFloorType.AI, keyword, new ArrayList<>());
            }
        }, "jdme.search.searchAi");
    }

    public static void searchAIFeedback(int flag, String traceId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("traceId", traceId);
        if (flag == 1) {
            params.put("up", 1);
        } else if (flag == 2) {
            params.put("up", 0);
        } else if (flag == 3) {
            params.put("down", 1);
        } else if (flag == 4) {
            params.put("down", 0);
        }
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, "jdme.search.searchAiFeedback");
    }

    public static void getFrequencyProblems(IDataLoadedCallback callback) {
        HashMap<String, Object> params = new HashMap<>();
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    String info = response.getData(String.class);
                    SearchProblemModel model = JsonUtils.getGson().fromJson(info, new TypeToken<SearchProblemModel>() {
                    }.getType());
                    if (model != null && model.problem != null && model.problem.size() > 0) {
                        callback.onResult(SearchFloorType.RECOMMEND, "", model.problem);
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, "jdme.search.listFrequencyProblem");
    }

    public static void getAIRecommendedQuestions(IDataLoadedCallback callback) {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, String> headers = new HashMap<>();
        params.put("suggestionFromSource", "AI");
        HttpManager.color().post(params, headers, "meai.search.suggestionQuery", new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    String info = response.getData(String.class);
                    SearchProblemAIModel model = JsonUtils.getGson().fromJson(info, new TypeToken<SearchProblemAIModel>() {
                    }.getType());
                    if (model != null && model.suggestionStr != null && !model.suggestionStr.isEmpty()) {
                        callback.onResult(SearchFloorType.AI_RECOMMEND, "", model.suggestionStr);
                    }
                }
            }
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    public static void getDiscoveryItems(IDataLoadedCallback callback) {
        HttpManager.color().post(new HashMap<>(), new HashMap<>(), "jdme.search.searchFind", new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    String info = response.getData(String.class);
                    SearchDiscoveryModel model = JsonUtils.getGson().fromJson(info, new TypeToken<SearchDiscoveryModel>() {
                    }.getType());
                    if (model != null && model.dataItems != null && !model.dataItems.isEmpty()) {
                        callback.onResult(SearchFloorType.DISCOVERY, "", model.dataItems);
                    }
                }
            }
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    public static void openMEAI(Context context, String key) {
        try {
            JSONObject jsonObject = new JSONObject();
            if (!TextUtils.isEmpty(key)) {
                JSONObject kvPair = new JSONObject();
                kvPair.put("keyword", key);
                JSONArray fetchStackArray = new JSONArray();
                fetchStackArray.put(kvPair);
                jsonObject.put("fetchStack", fetchStackArray);
            }
            jsonObject.put("requestID", SearchConfigHelper.getInstance().getRequestID());
            jsonObject.put("searchID", "main");
            jsonObject.put("mode", "continueByFetch");
            jsonObject.put("from", "aiSearchTab");
            Map<String, String> jumpParams = new HashMap<>();
            jumpParams.put("joyparam", jsonObject.toString());
            String deeplink = MeAiConfigHelper.getBrowserUrl(context, jumpParams, "Search_Enter");
            if (!TextUtils.isEmpty(deeplink)) {
                //隐藏输入法
                if (AppBase.getTopActivity() != null) {
                    InputMethodUtils.hideSoftInput(AppBase.getTopActivity());
                }
                Router.build(deeplink).go(context);
            }
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(TAG, "searchInMEAI failed", e);
        }
    }
}
