package com.jd.oa.unifiedsearch.joyday.presenter;

import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.unifiedsearch.joyday.model.Calendar;
import com.jd.oa.unifiedsearch.joyday.model.CalendarList;
import com.jd.oa.unifiedsearch.joyday.model.ConferenceRoom;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;
import com.jd.oa.unifiedsearch.joyday.model.User;
import com.jd.oa.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

//search_request.dart
public class SearchRequest {
    private static final String TAG = "[joyday search]";

    interface Callback {
        void onHandleResult(List<ScheduleModel> scheduleList, List<User> userList,
                            List<Calendar> calendarList, List<ConferenceRoom> rooms, Calendar personalCalendar);
        void onError();
        void onNoNetwork();
    }

    //这里后台要求：如果搜索时间段包括今天，和不包括今天，分开使用不同的接口。。。
    //返回值不同
    //接口，和勇哥一起定的规则，估计是后台这样实现起来方便
    //新搜索，today不在时间段内，不带today，带sort，today在时间段内，带today，不带sort
    //翻页，不带today，带sort，如果today在时间段内，将startTime或endTime改为today
    public static void requestSearchSchedule(Map<String, Object> params, Callback callback) {
        try {
            Log.i(TAG, "request = " + params.toString());
            HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    List<ScheduleModel> scheduleList = new ArrayList<>();
                    List<User> userList = new ArrayList<>();
                    List<Calendar> calendarList = new ArrayList<>();
                    List<ConferenceRoom> conferenceRooms = new ArrayList<>();

                    ApiResponse<SearchScheduleListResult> response = ApiResponse.parse(info.result, new TypeToken<SearchScheduleListResult>() {}.getType());
                    if (response.isSuccessful()) {
                        SearchScheduleListResult result = response.getData();
                        Log.i(TAG, "calendar search response = " + result);
                        scheduleList = result.scheduleList;
                        userList = result.userInfoList;
                        calendarList = result.calendarList;
                        conferenceRooms = result.fixedMeetingRoomList;
                    }

                    if (scheduleList == null) {
                        scheduleList = new ArrayList<>();
                    }
                    if (userList == null) {
                        userList = new ArrayList<>();
                    }
                    if (calendarList == null) {
                        calendarList = new ArrayList<>();
                    }
                    if (conferenceRooms == null) {
                        conferenceRooms = new ArrayList<>();
                    }
                    Calendar personalCalendar = getPersonalCalendar();
                    callback.onHandleResult(scheduleList, userList, calendarList, conferenceRooms, personalCalendar);
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    callback.onError();
                }

                @Override
                public void onNoNetWork() {
                    super.onNoNetWork();
                    callback.onNoNetwork();
                }
            }, NetworkConstant.API_SEARCH_SCHEDULE_COMMON);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //以下逻辑照搬日历逻辑，getScheduleList
    private static Calendar getPersonalCalendar() {
        List<Calendar> localCalendarList = CalendarList.getCalendarListCache();
        Calendar personalCalendar = null;
        for (Calendar calendar : localCalendarList) {
            if (calendar.userType == 1 && calendar.type == 1) {
                personalCalendar = calendar;
            }
        }
        return personalCalendar;
    }

    public static class SearchScheduleListResult {
        @JSONField(name = "appointmentList")
        @SerializedName("appointmentList")
        public List<ScheduleModel> scheduleList;
        public List<User> userInfoList;
        public List<Calendar> calendarList;

        public List<ConferenceRoom> fixedMeetingRoomList;
    }
}
