package com.jd.oa.unifiedsearch.all.section;

import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/19
 * Author: qudongshi
 * Description:
 */
public abstract class BaseSection extends Section {

    public final int RECORD_LIMITE = 3;

    public SearchFloorType floorType;
    public String keyword;

    public BaseSection(SectionParameters sectionParameters, String keyword) {
        super(sectionParameters);
        this.keyword = keyword;
    }

    @Override
    public abstract int getContentItemsTotal();

    @Override
    public abstract RecyclerView.ViewHolder getItemViewHolder(View view);

    @Override
    public abstract void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i);

    public abstract void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword);

}
