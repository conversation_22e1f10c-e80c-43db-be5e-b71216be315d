package com.jd.oa.unifiedsearch.joyspace.view;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;

import java.util.List;
import java.util.Objects;

public class FilterOptionAdapter extends BaseRecyclerAdapter<FilterOption,RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;
    private FilterOption mSelectedOption;

    public FilterOptionAdapter(Context context) {
        super(context);
    }

    public FilterOptionAdapter(Context context, List<FilterOption> data) {
        super(context, data);
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_joyspace_dialog_option_item, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {
        ViewHolder holder = (ViewHolder) viewHolder;
        FilterOption option = getItem(i);
        boolean selected = Objects.equals(option, mSelectedOption);
        holder.text.setText(option.getText());
        holder.text.getPaint().setFakeBoldText(selected);
        holder.itemView.setSelected(selected);

        final int position = i;
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    setSelectedOption(getItem(position));
                    mOnItemClickListener.onItemClick(FilterOptionAdapter.this, v, position);
                }
            }
        });
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setSelectedOption(FilterOption selectedOption) {
        mSelectedOption = selectedOption;
        this.notifyDataSetChanged();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        TextView text;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            text = itemView.findViewById(R.id.tv_text);
        }
    }
}
