package com.jd.oa.unifiedsearch.process.data;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.AppBase;
import com.jd.oa.utils.LocaleUtils;

import java.util.List;
import java.util.Map;

public class ProcessInfo {
    public RefPro refPro;
    public List<HighlightJoin> highlightJoin;
    public String title;

    public static class RefPro {
        public String icon;
        public String processDefinitionNameEn;
        public String processDefinitionName;
        public String linkUrl;
        public String pageViewAuth;
        public String id;
        public String descriptionEn;
        public String processTypeName;
        public String mobileApplyLink;
        public String description;
        public String firstLevelProcessTypeNameEn;
        public String firstLevelProcessTypeName;
        public String mobileApply;
        public String processTypeNameEn;

        public String getDescription() {
            if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")) {
                return description == null ? "" : description;
            } else {
                return descriptionEn == null ? "" : descriptionEn;
            }
        }

        public String getProcessTypeName() {
            if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")) {
                return processTypeName == null ? "" : processTypeName;
            } else {
                return processTypeNameEn == null ? "" : processTypeNameEn;
            }
        }

        public String getFirstLevelProcessTypeName() {
            if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")) {
                return firstLevelProcessTypeName == null ? "" : firstLevelProcessTypeName;
            } else {
                return firstLevelProcessTypeNameEn == null ? "" : firstLevelProcessTypeNameEn;
            }
        }
    }
}