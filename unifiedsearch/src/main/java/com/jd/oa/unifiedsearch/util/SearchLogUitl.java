package com.jd.oa.unifiedsearch.util;

import android.util.Log;

import com.jd.oa.dynamic.BuildConfig;

/*
 * Time: 2023/10/19
 * Author: qudongshi
 * Description:
 */
public class SearchLogUitl {

    private static final String TAG = "SearchLog";
    private static boolean isDebug = BuildConfig.DEBUG;

    public static void LogD(String tag, String msg) {
        if (isDebug) {
            Log.d(TAG, tag + " ---> " + msg);
        }
    }

    public static void LogE(String tag, String msg, Exception e) {
        if (isDebug) {
            Log.e(TAG, tag + " ---> " + msg, e);
            if (e != null) {
                e.printStackTrace();
            }
        } else {
            Log.e(TAG, tag + " ---> " + msg, e);
        }
    }
}
