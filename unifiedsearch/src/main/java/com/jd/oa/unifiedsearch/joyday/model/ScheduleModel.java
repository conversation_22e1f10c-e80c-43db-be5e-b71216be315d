package com.jd.oa.unifiedsearch.joyday.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.jd.ee.librecurparser.BaseAppointment;
import com.jd.oa.unifiedsearch.joyday.utils.DateUtil;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Keep
public class ScheduleModel extends BaseAppointment implements Cloneable {

    public static final int Expired = 0;
    public static final int Ongoing = 1;
    public static final int Future = 2;

    transient public Date _beginDateTime;
    public Date getBeginDateTime() {
        if (_beginDateTime == null) {
            _beginDateTime = new Date(getBeginTime());
        }
        return _beginDateTime;
    }

    transient public Date _endDateTime;
    public Date getEndDateTime() {
        if (_endDateTime == null) {
            _endDateTime = new Date(getEndTime());
        }
        return _endDateTime;
    }

    public String calendarTitle;
    public String canceledScheduleId;

    transient public Highlight highlight;

    public String source;//JOYDAY,EXCHANGE
    public User user;
    public String userId;
    public String teamId;

    transient public boolean firstOfDay = true;  ///是否是当天的第一条日程
    transient public boolean endOfDay = true;  ///是否是当天的最后一条日程
    public int scope = Calendar.PUBLIC;

    public String calendarColor;
    //根据哪个id查询到当前日程
    public String calendarGroupId;
    public List<JoySpaceInfo> joySpaceInfo;//搜索时后台不会返回

    //2022.04新接口格式
    public String groupId;
    public String responseType;
    public int joyspaceLinked;
    public int joymeetingLinked;
    public String account;
    public String email;
    public String organizer;
    public String domain;

    transient public boolean selected;
    transient public boolean disableSelected;

    public List<ConferenceRoom> fixedMeetingRoomList;

    public String getConferenceRoomName() {
        if (fixedMeetingRoomList == null || fixedMeetingRoomList.isEmpty()) return "";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fixedMeetingRoomList.size(); i++) {
            sb.append(fixedMeetingRoomList.get(i).getMeetingName());
            if (i < fixedMeetingRoomList.size() - 1) {
                sb.append("、");
            }
        }
        return sb.toString();
    }

    @Override
    public String toString() {
        return "{id:" + scheduleId + "title: " + subject + ", begin:" + getBeginDateTime() + " end:" + getEndDateTime()  +
        ", repBegin:" + repeatStart + " repEnd:" + repeatEnd + " cancelId:" + canceledScheduleId + " cancel:" + cancelled + "}";
    }

    public int getState() {
        long now = System.currentTimeMillis();
        if (getStart() <= now && getEnd() >= now) {
            return Ongoing;
        } else if (getEnd() < now) {
            return Expired;
        } else {
            return Future;
        }
    }

    // 全天日程，结束时间大于等于开始时间24小时的，就是全天日程
    public boolean isAllDay() {
        return (getEnd() - getStart()) >= 24 * 60 * 60 * 1000;
    }

    //是否是夸天日程
    public boolean isSpanDay() {
        return !DateUtil.isSameDay(getBeginDateTime(), getEndDateTime());
    }

    // 获取跨天日程，所跨的日期
    public List<DateModel> getSpanDays() {
        Date date = new Date(getEnd() - 1);
        return DateUtil.getDateBetween(getStart(), new Date(date.getYear(), date.getMonth(), date.getDate() + 1).getTime());
    }

    public boolean isExpired() {
        return end < java.util.Calendar.getInstance().getTimeInMillis();
    }

    @Override
    public ScheduleModel clone() {
        ScheduleModel model = null;
        try {
            model = (ScheduleModel) super.clone();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (model != null) {
            model._beginDateTime = null;
            model._endDateTime = null;
            model.firstOfDay = false;
        }
        return model;
    }
}
