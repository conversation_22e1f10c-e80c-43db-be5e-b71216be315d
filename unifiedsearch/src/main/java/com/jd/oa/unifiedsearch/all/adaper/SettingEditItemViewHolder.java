package com.jd.oa.unifiedsearch.all.adaper;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.ui.IconFontView;
import com.jd.oa.unifiedsearch.R;

public class SettingEditItemViewHolder extends RecyclerView.ViewHolder {

    private int LAYOUT = R.layout.unifiedsearch_v2_customer_item_content;

    public View vLine;
    //ift_opt_icon
    public IconFontView iftOption;
    //ift_tab_drag
    public IconFontView iftTabDrag;
    //tv_tab_name
    public TextView tvTabName;
    //iv_tab_icon
    public ImageView ivTabIcon;
    public ImageView ivTabNameIcon;

    public RelativeLayout rlContent;
    public TextView tvTitle;

    //add_icon icon_padding_addcircle
    //del_icon icon_padding_minuscircle

    public SettingEditItemViewHolder(View itemView) {
        super(itemView);
        iftOption = itemView.findViewById(R.id.ift_opt_icon);
        iftTabDrag = itemView.findViewById(R.id.ift_tab_drag);
        tvTabName = itemView.findViewById(R.id.tv_tab_name);
        vLine = itemView.findViewById(R.id.v_line);
        rlContent = itemView.findViewById(R.id.rl_content);
        ivTabIcon = itemView.findViewById(R.id.iv_tab_icon);
        ivTabNameIcon = itemView.findViewById(R.id.iv_tab_name_icon);
    }
}