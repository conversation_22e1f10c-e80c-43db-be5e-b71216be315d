package com.jd.oa.unifiedsearch.joyspace.data;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.alibaba.fastjson.annotation.JSONField;
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition;
import com.jd.oa.unifiedsearch.joyspace.utils.JoySpaceSubTitleBuilder;
import com.jd.oa.unifiedsearch.joyspace.utils.JoyspaceDateParser;

import java.util.List;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

/**
 * {
 * "icon_url": "//storage.360buyimg.com/hub-static/icons/Webicon/Line_word_3x.png",
 * "id": "e8u3PCm7xj5qvP9e00PN",
 * "page_id": "e8u3PCm7xj5qvP9e00PN",
 * "origin_id": "TpCqhQCizPgXYjSLtNKu",
 * "team_id": "bnbfteZ18Jf0eykbw1tm",
 * "folder_id": "tmG74zyn5pPnO9dMac5E",
 * "space_id": "mEvd4rZmruQRglBO2ho8",
 * "space_category_id": "Vm9xQ20367j3zQaz48fb",
 * "tenant_id": "00046419",
 * "status": 1,
 * "title": "权限需求（<em>搜索</em>） - 技术方案",
 * "preview_text": "权限需求（<em>搜索</em>） - 技术方案\n\n\n\n\n版本\n撰写人\n说明\nv1.0\n\n初始版本\n\n背景\n描述当前需求背景，主要解决的问题，实现需求需要达到的目标。\n产品设计\n描述当前需求的主要负责人，产品PRD设计，实现需求需要达到的目标。\n负责人：\n\n\n需求名称\n权限升级方案对<em>搜索</em>相关服务的影响\n负责人\n\n产品PRD\n\n\n技术方案\n以下是技术方案主要比较权限方案升级后，对收紧文档的权限改动，如下图所示",
 * "cover_img_url": "",
 * "tags": null,
 * "page_type": 1,
 * "created_by": "AWwV7HRPjaTq1E8FCXJK",
 * "updated_by": "AWwV7HRPjaTq1E8FCXJK",
 * "published_by": "",
 * "page_status": 1,
 * "published_content_id": "",
 * "published_at": "2021-09-22T12:12:09+08:00",
 * "published_at_unix": 1632283929,
 * "created_at": "2021-09-23T10:46:18+08:00",
 * "created_at_unix": 1632365178,
 * "updated_at": "2021-10-18T18:44:17+08:00",
 * "updated_at_unix": 1634553857,
 * "routes": null,
 * "permissions": [
 * "PAGE_UNPUBLISH",
 * "COMMENT_CREATE",
 * "PAGE_EDIT",
 * "PAGE_PARENT_CHANGE",
 * "PAGE_PUBLISH",
 * "SHARE_VIEW",
 * "SHARE_CREATE",
 * "SHARE_REMOVE",
 * "SHARE_EDIT",
 * "PAGE_REMOVE",
 * "PAGE_CREATE",
 * "PAGE_READ",
 * "PAGE_DUPLICATE",
 * "PAGE_DOWNLOAD",
 * "PAGE_EXPORT"
 * ],
 * "space_name": "",
 * "team_name": "",
 * "full_path_names": "04.技术研发$@$技术方案$@$JoySpace技术方案",
 * "full_path_ids": "Oe8xe2RV6MJ9MtxV6aki$@$NtIogcTF6EWFGEYkFoxY$@$tmG74zyn5pPnO9dMac5E"
 * }
 */
@Keep
public class JoySpaceDocument implements JoySpaceListItem {

    private static final String API_HOST = "https://apijoyspace.jd.com";

    public static final int PAGE_TYPE_FOLDER = 0;
    public static final int PAGE_TYPE_DOCUMENT = 1;
    public static final int PAGE_TYPE_REPORTING = 2;
    public static final int PAGE_TYPE_NOTE = 3;
    public static final int PAGE_TYPE_SPREADSHEET = 4;
    public static final int PAGE_TYPE_DOCUMENT_SHORTCUT = 5;
    public static final int PAGE_TYPE_FILE = 6;
    public static final int PAGE_TYPE_WEB_SPREAD = 7;
    public static final int PAGE_TYPE_NEW_SPREAD_SHEET = 8;

    public static final int STATUS_DELETED = 0;

    private static final String PATH_DIVIDER = "\\$@\\$";

    @JSONField(name = "icon_url")
    private String iconUrl;
    @JSONField(name = "page_id")
    private String pageId;
    @JSONField(name = "origin_id")
    private String originId;
    @JSONField(name = "team_id")
    private String teamId;
    @JSONField(name = "folder_id")
    private String folderId;
    @JSONField(name = "space_id")
    private String spaceId;
    @JSONField(name = "space_category_id")
    private String spaceCategoryId;
    @JSONField(name = "tenant_id")
    private String tenantId;
    @JSONField(name = "preview_text")
    private String previewText;
    @JSONField(name = "cover_img_url")
    private String coverImgUrl;
    @JSONField(name = "page_type")
    private int pageType;
    @JSONField(name = "origin_page_type")
    private int originPageType;
    @JSONField(name = "created_by")
    private String createdBy;
    @JSONField(name = "create_erp")
    private String createErp;
    @JSONField(name = "create_name")
    private String createName;
    @JSONField(name = "share_erp")
    private String shareErp;
    @JSONField(name = "share_name")
    private String shareName;
    @JSONField(name = "updated_by")
    private String updatedBy;
    @JSONField(name = "published_by")
    private String publishedBy;
    @JSONField(name = "page_status")
    private int pageStatus;
    @JSONField(name = "published_content_id")
    private String publishedContentId;
    @JSONField(name = "published_at")
    private String publishedAt;
    @JSONField(name = "published_at_unix")
    private long publishedAtUnix;
    @JSONField(name = "created_at")
    private String createdAt;
    @JSONField(name = "created_at_unix")
    private long createdAtUnix;
    @JSONField(name = "updated_at")
    private String updatedAt;
    @JSONField(name = "updated_at_unix")
    private long updatedAtUnix;
    @JSONField(name = "shared_at")
    private String sharedAt;
    @JSONField(name = "opened_at")
    private String openedAt;
    @JSONField(name = "space_name")
    private String spaceName;
    @JSONField(name = "team_name")
    private String teamName;
    @JSONField(name = "full_path_names")
    private String fullPathNames;
    @JSONField(name = "full_path_ids")
    private String fullPathIds;
    private String id;
    private int status;
    private String title;
    private Object tags;
    private Object routes;
    private User author;
    private String subTitle;

    @JSONField(name = "read_amount")
    private int readAmount;
    @JSONField(name = "share_total")
    private int shareTotal;

    //综合搜索里点击云文档item的 埋点url
    @JSONField(name = "log_url")
    private String logUrlMain;
    //新增字段 用于云文档tab列表点击item的埋点url
    private String logUrl;

    private String parentFolderName;

    @JSONField(name = "parent_resource_name")
    private String parentResourceName;

    @JSONField(name = "unread_amount")
    private int unreadAmount;

    private boolean isCollected;

    private boolean unread;

    private String highlightText;

    private String mobileIconUrl;

    public String seqUrl;

    public List<TagInfo> tagInfos;

    public static class TagInfo {
        public String tagName;
        public long tagId;
        public boolean highlighted = false;
    }


    @Override
    public String getTitleText() {
        return getTitle();
    }

    @Override
    public String getSubTitleText(Context context) {

        if (TextUtils.isEmpty(getFullPathNames())) return null;
        return getFullPathNames().replaceAll(PATH_DIVIDER, "/");
    }

    @Override
    public JoySpaceDocument getDocument() {
        return this;
    }

    @Override
    public String getIconUrl() {
        if (!TextUtils.isEmpty(iconUrl) && !iconUrl.startsWith("http")) {
            return "http:" + iconUrl;
        }
        return iconUrl;
    }

    @Override
    public boolean showUnread() {
        return isUnread();
    }

    public String getLogUrlMain() {
        return logUrlMain;
    }

    public void setLogUrlMain(String logUrlMain) {
        this.logUrlMain = logUrlMain;
    }

    public String getLogUrl() {
        return logUrl;
    }

    public void setLogUrl(String logUrl) {
        this.logUrl = logUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getOriginId() {
        return originId;
    }

    public void setOriginId(String originId) {
        this.originId = originId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getFolderId() {
        return folderId;
    }

    public void setFolderId(String folderId) {
        this.folderId = folderId;
    }

    public String getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(String spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceCategoryId() {
        return spaceCategoryId;
    }

    public void setSpaceCategoryId(String spaceCategoryId) {
        this.spaceCategoryId = spaceCategoryId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPreviewText() {
        return previewText;
    }

    public void setPreviewText(String previewText) {
        this.previewText = previewText;
    }

    public String getCoverImgUrl() {
        return coverImgUrl;
    }

    public void setCoverImgUrl(String coverImgUrl) {
        this.coverImgUrl = coverImgUrl;
    }

    public Object getTags() {
        return tags;
    }

    public void setTags(Object tags) {
        this.tags = tags;
    }

    public int getPageType() {
        return pageType;
    }

    public void setPageType(int pageType) {
        this.pageType = pageType;
    }

    public int getOriginPageType() {
        return originPageType;
    }

    public void setOriginPageType(int originPageType) {
        this.originPageType = originPageType;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreateErp() {
        return createErp;
    }

    public void setCreateErp(String createErp) {
        this.createErp = createErp;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getShareErp() {
        return shareErp;
    }

    public void setShareErp(String shareErp) {
        this.shareErp = shareErp;
    }

    public String getShareName() {
        return shareName;
    }

    public void setShareName(String shareName) {
        this.shareName = shareName;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getPublishedBy() {
        return publishedBy;
    }

    public void setPublishedBy(String publishedBy) {
        this.publishedBy = publishedBy;
    }

    public int getPageStatus() {
        return pageStatus;
    }

    public void setPageStatus(int pageStatus) {
        this.pageStatus = pageStatus;
    }

    public String getPublishedContentId() {
        return publishedContentId;
    }

    public void setPublishedContentId(String publishedContentId) {
        this.publishedContentId = publishedContentId;
    }

    public String getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(String publishedAt) {
        this.publishedAt = publishedAt;
    }

    public long getPublishedAtUnix() {
        return publishedAtUnix;
    }

    public void setPublishedAtUnix(long publishedAtUnix) {
        this.publishedAtUnix = publishedAtUnix;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public long getCreatedAtUnix() {
        return createdAtUnix;
    }

    public void setCreatedAtUnix(long createdAtUnix) {
        this.createdAtUnix = createdAtUnix;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public long getUpdatedAtUnix() {
        return updatedAtUnix;
    }

    public void setUpdatedAtUnix(long updatedAtUnix) {
        this.updatedAtUnix = updatedAtUnix;
    }


    public String getSharedAt() {
        return sharedAt;
    }

    public void setSharedAt(String sharedAt) {
        this.sharedAt = sharedAt;
    }

    public String getOpenedAt() {
        return openedAt;
    }

    public void setOpenedAt(String openedAt) {
        this.openedAt = openedAt;
    }

    public Object getRoutes() {
        return routes;
    }

    public void setRoutes(Object routes) {
        this.routes = routes;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getFullPathNames() {
        return fullPathNames;
    }

    public void setFullPathNames(String fullPathNames) {
        this.fullPathNames = fullPathNames;
    }

    public String getFullPathIds() {
        return fullPathIds;
    }

    public void setFullPathIds(String fullPathIds) {
        this.fullPathIds = fullPathIds;
    }

    public User getAuthor() {
        return author;
    }

    public void setAuthor(User author) {
        this.author = author;
    }


    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public int getReadAmount() {
        return readAmount;
    }

    public void setReadAmount(int readAmount) {
        this.readAmount = readAmount;
    }

    public int getShareTotal() {
        return shareTotal;
    }

    public void setShareTotal(int shareTotal) {
        this.shareTotal = shareTotal;
    }


    public String getParentResourceName() {
        return parentResourceName;
    }

    public void setParentResourceName(String parentResourceName) {
        this.parentResourceName = parentResourceName;
    }

    public String getParentFolderName() {
        if (TextUtils.isEmpty(parentResourceName)) {
            return parentFolderName;
        }
        return parentResourceName;
    }

    public void setParentFolderName(String parentFolderName) {
        this.parentFolderName = parentFolderName;
    }

    public int getUnreadAmount() {
        return unreadAmount;
    }

    public void setUnreadAmount(int unreadAmount) {
        this.unreadAmount = unreadAmount;
    }


    public boolean isCollected() {
        return isCollected;
    }

    public void setCollected(boolean collected) {
        isCollected = collected;
    }


    public boolean isUnread() {
        return unread;
    }

    public void setUnread(boolean unread) {
        this.unread = unread;
    }


    public String getHighlightText() {
        return highlightText;
    }

    public void setHighlightText(String highlightText) {
        this.highlightText = highlightText;
    }

    public String getMobileIconUrl() {
        return mobileIconUrl;
    }

    public void setMobileIconUrl(String mobileIconUrl) {
        this.mobileIconUrl = mobileIconUrl;
    }

    /**
     * if (page.page_type === PageType.FILE) {
     * return `${GlobalCache.REACT_APP_API_HOST}/v1/common/icons?${QueryString.stringify({
     * title: page.title,
     * pageType: page.page_type,
     * type: page.type,
     * client,
     * })}`;
     * }
     * return `${GlobalCache.REACT_APP_API_HOST}/v1/common/icons?${QueryString.stringify({
     * pageType: page.page_type,
     * client,
     * })}`;
     */
    public String buildPageIconUrl() {
        Uri.Builder builder = Uri.parse(API_HOST + "/v1/common/icons").buildUpon();
        int type = getPageType();
        if (type == PAGE_TYPE_DOCUMENT_SHORTCUT) {
            type = getOriginPageType();
        }
        if (type == PAGE_TYPE_FILE) {
            builder.appendQueryParameter("pageType", String.valueOf(type));
            String titleText = TextUtils.isEmpty(title) ? "" : title;
            builder.appendQueryParameter("title", titleText.replaceAll("</?em>", ""));
        } else {
            builder.appendQueryParameter("pageType", String.valueOf(type));
        }

        return builder.appendQueryParameter("client", "Android").build().toString();
    }

    public String buildSubTitle(Context context, @JoySpaceFilter.FilterType String type, @Nullable SelectedSortCondition sortCondition) {
        return new JoySpaceSubTitleBuilder(this, new JoyspaceDateParser()).buildSubTitle(context, type, sortCondition);
    }

    /**
     * "id": "sp25wyHeeUDtT6ZqSvT1",
     * "name": "裴东彪",
     * "avatarUrl": "https://m.360buyimg.com/jmeside/jfs/t1/187496/12/2006/132349/609632d9E086c6a19/de5680e2802fdfec.png",
     * "username": "peidongbiao",
     */
    @Keep
    public static class User {

        private String id;
        private String name;
        @JSONField(alternateNames = {"avatarUrl", "avatar_url"})
        private String avatarUrl;
        private String username;


        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }
    }
}
