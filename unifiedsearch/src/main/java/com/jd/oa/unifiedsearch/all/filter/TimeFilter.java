package com.jd.oa.unifiedsearch.all.filter;

import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.jd.me.datetime.picker.DatePickerDialog;
import com.jd.me.datetime.picker.DatePickerView;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.ui.popupwindow.MultiSelectedPopupWindow;
import com.jd.oa.ui.popupwindow.PopupItem;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;
import com.jd.oa.unifiedsearch.joyday.utils.DateUtil;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.filter.FilterContext;
import com.jd.oa.unifiedsearch.joyspace.filter.UnifiedSearchFilter;
import com.jd.oa.unifiedsearch.joyspace.view.FilterArrow;
import com.jd.oa.unifiedsearch.joyspace.view.FilterLayout;
import com.jd.oa.unifiedsearch.util.SelectPopupWindowKt;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function2;

public class TimeFilter extends UnifiedSearchFilter<TimeItemInfo> {
    private FilterOption mOption;
    private final SearchFilterConfig.FilterItem filterItem;
    private final List<SearchFilterConfig.Option> optionConfig;
    private FilterLayout mFilterLayout;
    private TextView mTextView;
    @NonNull
    private FilterOption mDefaultOption;
    private String mDefaultText = ""; //有可能默认筛选名和下拉选项名不一样
    private final String mLabelType;
    private final String TAG = "timeFilter";

    //日期选择器
    private Date mStartDate;
    private Date mEndDate;

    public TimeFilter(FilterContext filterContext, SearchFilterConfig.FilterItem filterItem, @NonNull String labelType) {
        super(filterContext);
        this.filterItem = filterItem;
        this.optionConfig = filterItem.options;
        this.mLabelType = labelType;
        //设置默认过滤器
        if (optionConfig != null && !optionConfig.isEmpty()) {
            for (SearchFilterConfig.Option option : optionConfig) {
                if (option.action.equals("default") && option.defaultTitle != null) {
                    mDefaultText = option.defaultTitle.getText();
                    mDefaultOption = new FilterOption(option.title.getText(), option.action);
                }
                if (option.selected) {
                    mOption = new FilterOption(option.title.getText(), option.action);
                }
            }
        }
    }

    public List<FilterOption> getDropDownOptions() {
        List<FilterOption> result = new ArrayList<>();
        if (optionConfig == null || optionConfig.isEmpty()) {
            MELogUtil.localD(TAG, "时间选择项为空");
            return result;
        }
        for (int i = 0; i < optionConfig.size(); i++) {
            //如果excludeOption项被设为true则不在下拉列表中显示该选项
            if (optionConfig.get(i).excludeOption) {continue;}
            if (optionConfig.get(i).title == null) {continue;}
            String optionName = optionConfig.get(i).title.getText();
            String action;
            if (optionConfig.get(i).action == null || optionConfig.get(i).action.isEmpty()) {
                result.add(new FilterOption(optionName, ""));
            } else if (optionConfig.get(i).action != null && optionConfig.get(i).action.equals("customTime")) { //自定义时间
                action = optionConfig.get(i).action;
                if (mStartDate == null || mEndDate == null) { //没选择自定义时间之前显示 自定义时间
                    result.add(new FilterOption(optionName, action));
                } else { //选择时间后显示 时间范围
                    result.add(new FilterOption(getTimeRangeText(), action));
                }
            } else { //显示选项名
                action = optionConfig.get(i).action;
                result.add(new FilterOption(optionName, action));
            }
        }
        MELogUtil.localD(TAG, "时间选择项：" + result);
        return result;
    }

    @Override
    protected View onCreateView(Context context, ViewGroup container) {
        mFilterLayout = (FilterLayout) LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_filter_item, container, false);
        mTextView = mFilterLayout.findViewById(R.id.tv_text);
        mTextView.setText(mDefaultText);

        FilterArrow arrow = mFilterLayout.findViewById(R.id.arrow);
        arrow.observeTo(mFilterLayout);

        if (mOption != null && !mOption.equals(mDefaultOption)) {
            setSelectedWithoutUIChange(true);
            mTextView.setText(mOption.getText());
            mFilterLayout.select();
            mFilterLayout.close();
        }

        return mFilterLayout;
    }

    private MultiSelectedPopupWindow popupWindow;

    @Override
    public void onFilterClick(AppCompatActivity activity, View view) {
        InputMethodManager im = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        int delayTime = 0;
        if (im.isActive()) {
            im.hideSoftInputFromWindow(view.getWindowToken(), 0);
            delayTime = 100;
        }

        view.postDelayed(new Runnable() {
            @Override
            public void run() {
                List<FilterOption> options = getDropDownOptions();
                final FilterOption selected = mOption == null ? mDefaultOption : mOption;
                popupWindow = new MultiSelectedPopupWindow(
                        activity,
                        SelectPopupWindowKt.filterOptionsToPopupItems(options, selected),
                        false,
                        Collections.singletonList(SelectPopupWindowKt.filterOptionToPopupItem(mDefaultOption, false)), //默认筛选选项
                        new Function2<List<PopupItem>, Boolean, Unit>() {
                            @Override
                            public Unit invoke(List<PopupItem> popupItems, Boolean aBoolean) {
                                //其他时间选项
                                if (popupItems.isEmpty()) {
                                    MELogUtil.localD(TAG, "时间选择默认项");
                                    mOption = mDefaultOption;
                                } else if (popupItems.get(0).getValue().equals("customTime")) { //自定义时间选项
                                    //打开日期选择器，并负责管理筛选项状态
                                    MELogUtil.localD(TAG, "打开日期选择器");
                                    FilterOption customOption = new FilterOption(popupItems.get(0).getText(), popupItems.get(0).getValue());
                                    openDatePickerDialog(customOption);
                                    return null;
                                } else {
                                    MELogUtil.localD(TAG, "时间选择非默认项");
                                    mOption = new FilterOption(popupItems.get(0).getText(), popupItems.get(0).getValue());
                                }

                                if (mOption.equals(selected)) {
                                    MELogUtil.localD(TAG, "时间选择无改变");
                                    return null;
                                }

                                //根据时间选项设置状态
                                if (mOption.equals(mDefaultOption)) {
                                    reset();
                                } else {
                                    //清空选择过的自定义时间
                                    mStartDate = null;
                                    mEndDate = null;
                                    //设置状态
                                    setSelected(true);
                                    mTextView.setText(mOption.getText());
                                    mFilterLayout.select();
                                }

                                if (mOnChangedListener != null) {
                                    mOnChangedListener.onChanged(getTimeStamp());
                                }
                                return null;
                            }
                        },
                        new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                mFilterLayout.close();
                                popupWindow = null;
                                return null;
                            }
                        }
                );
                popupWindow.show(view);
                mFilterLayout.open();
            }
        }, delayTime); //等待键盘隐藏
    }

    @Override
    public void reset() {
        mTextView.setText(mDefaultText);
        mFilterLayout.reset();
        mOption = mDefaultOption;
        mStartDate = null;
        mEndDate = null;
        super.reset();
    }

    @Override
    public TimeItemInfo getValue() {
        return getTimeStamp();
    }

    @Override
    public void onSaveState(String type, Bundle outState) {
        super.onSaveState(type, outState);
        if (mOption != null) {
            outState.putParcelable(type, mOption);
        }
    }

    @Override
    public void onRestoreState(String type, Bundle savedState) {
        super.onRestoreState(type, savedState);
        mOption = savedState.getParcelable(type);
        if (mOption != null) {
            setSelected(true);
            mTextView.setText(mOption.getText());
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.onConfigurationChanged(newConfig);
        }
    }

    private void openDatePickerDialog(final FilterOption customOption) {
        DatePickerDialog dialog = new DatePickerDialog(getContext());
        dialog.setSelectMode(DatePickerView.SelectMode.RANGE);
        if (mEndDate != null) {
            dialog.setEndDate(mEndDate);
        } else {
            dialog.setEndDate(DateUtil.endOfDay(new Date()));
        }
        if (mStartDate != null) {
            dialog.setStartDate(mStartDate);
        } else {
            dialog.setStartDate(DateUtil.startOfDay(new Date()));
        }
        dialog.setStartDateRequired(false);
        dialog.setEndDateRequired(false);
        dialog.setAllRequired(true);
        dialog.setMaxDate(DateUtil.endOfDay(new Date()));

        dialog.setOnCalendarRangeSelectedListener((startDate, endDate) -> {
            Date newStartDate = DateUtil.startOfDay(startDate);
            Date newEndDate = DateUtil.endOfDay(endDate);
            Boolean dataChanged = false;
            if (!newStartDate.equals(mStartDate) || !newEndDate.equals(mEndDate)) {
                mStartDate = newStartDate;
                mEndDate = newEndDate;
                customOption.setText(getTimeRangeText());
                dataChanged = true;
            }
            //设置选项
            mOption = customOption;
            //更改筛选项状态
            setSelected(true);
            mTextView.setText(getTimeRangeText());
            mFilterLayout.select();
            mFilterLayout.close();
            //通知数据变化
            if (mOnChangedListener != null && dataChanged) {
                mOnChangedListener.onChanged(getTimeStamp());
            }
        });

        dialog.setOnCancelListener(dialog1 -> {
        });
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
            }
        });
        dialog.getWindow().setDimAmount(0.2f);
        dialog.show();
    }

    private String getTimeRangeText() {
        Date startDate = mStartDate!= null? mStartDate : new Date();
        Date endDate = mEndDate!= null? mEndDate : new Date();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        String startDateText = dateFormat.format(startDate);
        String endDateText = dateFormat.format(endDate);

        return startDateText + "-" + endDateText;
    }

    private TimeItemInfo getTimeStamp() {
        if (mOption == null) {return null;}
        TimeItemInfo result = new TimeItemInfo();
        result.timeType = mLabelType;
        result.endTime = DateUtil.endOfDay(new Date()).getTime();
        result.selectedOption = getFilterOption();
        result.filterItem = filterItem;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        switch (mOption.getValue()) {
            case "lastWeek":
                // 设置为7天前
                calendar.add(Calendar.DAY_OF_YEAR, -7);
                // 获取7天前的时间戳
                result.startTime = calendar.getTime().getTime();
                break;
            case "lastMonth":
                // 设置为31天前
                calendar.add(Calendar.DAY_OF_YEAR, -31);
                // 获取31天前的时间戳
                result.startTime = calendar.getTime().getTime();
                break;
            case "last3Months":
                // 设置为3月前
                calendar.add(Calendar.DAY_OF_YEAR, -31 * 3);
                // 获取3月前的时间戳
                result.startTime = calendar.getTime().getTime();
                break;
            case "customTime":
                if (mStartDate == null || mEndDate == null) {return null;}
                result.startTime = mStartDate.getTime();
                result.endTime = mEndDate.getTime();
                break;
        }
        return result;
    }

    private SearchFilterConfig.Option getFilterOption() {
        if (mOption == null) {return null;}
        if (optionConfig != null && !optionConfig.isEmpty()) {
            //遍历optionConfig，找到选项对应的option，并获取subFilterItems
            for (SearchFilterConfig.Option option : optionConfig) {
                if (mOption.getValue() != null && mOption.getValue().equals(option.action)) {
                    return option;
                }
            }
        }
        return null;
    }
}
