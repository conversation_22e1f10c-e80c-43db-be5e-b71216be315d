package com.jd.oa.unifiedsearch.joyday.model;

import java.io.Serializable;
import java.lang.Integer;
import java.lang.Object;
import java.lang.String;
import java.util.Map;

public class ConferenceRoom implements Serializable {
  private String districtCode;
  private String districtName;
  private String workplaceName;
  private Object meetingOrderCode;
  private Integer meetingEstimateEtime;
  private String meetingAddress;
  private String meetingName;
  private Object relatedDate;
  private String fullMeetingName;
  private String floorNo;
  private Integer meetingEstimateStime;
  private String scheduleId;
  private String meetingEstimateDate;

  public String getDistrictCode() {
    return this.districtCode;
  }

  public void setDistrictCode(String districtCode) {
    this.districtCode = districtCode;
  }

  public String getDistrictName() {
    return this.districtName;
  }

  public void setDistrictName(String districtName) {
    this.districtName = districtName;
  }

  public String getWorkplaceName() {
    return this.workplaceName;
  }

  public void setWorkplaceName(String workplaceName) {
    this.workplaceName = workplaceName;
  }

  public Object getMeetingOrderCode() {
    return this.meetingOrderCode;
  }

  public void setMeetingOrderCode(Object meetingOrderCode) {
    this.meetingOrderCode = meetingOrderCode;
  }

  public Integer getMeetingEstimateEtime() {
    return this.meetingEstimateEtime;
  }

  public void setMeetingEstimateEtime(Integer meetingEstimateEtime) {
    this.meetingEstimateEtime = meetingEstimateEtime;
  }

  public String getMeetingAddress() {
    return this.meetingAddress;
  }

  public void setMeetingAddress(String meetingAddress) {
    this.meetingAddress = meetingAddress;
  }

  public String getMeetingName() {
    return this.meetingName;
  }

  public void setMeetingName(String meetingName) {
    this.meetingName = meetingName;
  }

  public Object getRelatedDate() {
    return this.relatedDate;
  }

  public void setRelatedDate(Object relatedDate) {
    this.relatedDate = relatedDate;
  }

  public String getFullMeetingName() {
    return this.fullMeetingName;
  }

  public void setFullMeetingName(String fullMeetingName) {
    this.fullMeetingName = fullMeetingName;
  }

  public String getFloorNo() {
    return this.floorNo;
  }

  public void setFloorNo(String floorNo) {
    this.floorNo = floorNo;
  }

  public Integer getMeetingEstimateStime() {
    return this.meetingEstimateStime;
  }

  public void setMeetingEstimateStime(Integer meetingEstimateStime) {
    this.meetingEstimateStime = meetingEstimateStime;
  }

  public String getScheduleId() {
    return this.scheduleId;
  }

  public void setScheduleId(String scheduleId) {
    this.scheduleId = scheduleId;
  }

  public String getMeetingEstimateDate() {
    return this.meetingEstimateDate;
  }

  public void setMeetingEstimateDate(String meetingEstimateDate) {
    this.meetingEstimateDate = meetingEstimateDate;
  }
}
