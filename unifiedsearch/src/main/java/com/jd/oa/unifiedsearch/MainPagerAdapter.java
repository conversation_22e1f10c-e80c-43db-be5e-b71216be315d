package com.jd.oa.unifiedsearch;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.List;

public class MainPagerAdapter extends FragmentStateAdapter {

    private List<Fragment> mList;

    public MainPagerAdapter(@NonNull FragmentActivity fragmentActivity, List<Fragment> list) {
        super(fragmentActivity);
        this.mList = list;
    }

    @NonNull
    @Override
    public Fragment createFragment(int i) {
        return mList.get(i);
    }

    @Override
    public int getItemCount() {
        return mList.size();
    }

}