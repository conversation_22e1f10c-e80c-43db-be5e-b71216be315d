package com.jd.oa.unifiedsearch.joyspace.filter;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.data.SearchFilterQuery;
import com.jd.oa.unifiedsearch.joyspace.view.FilterLayout;

/**
 * 我创建的
 */
@Deprecated
public class CreateByMyselfFilter extends JoySpaceFilter<SearchFilterQuery.Contact>{

    private SearchFilterQuery.Contact mMySelf;

    private FilterLayout mFilterLayout;

    public CreateByMyselfFilter(FilterContext filterContext) {
        super(filterContext);
    }

    @Override
    protected View onCreateView(Context context, ViewGroup container) {
        mFilterLayout = (FilterLayout) LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_filter_item, container, false);
        TextView textView = (TextView) mFilterLayout.findViewById(R.id.tv_text);
        textView.setText(R.string.unifiedsearch_joyspace_filter_created_by_myself);
        return mFilterLayout;
    }

    @Override
    public void onFilterClick(AppCompatActivity activity, View view) {
        setSelected(!isSelected());
        if (isSelected()) {
            mMySelf = new SearchFilterQuery.Contact(PreferenceManager.UserInfo.getTimlineAppID(), PreferenceManager.UserInfo.getUserName());

            //JoySpaceFilter<?> createdByOthers = getFilterByType(JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS);
            //createdByOthers.reset();

        } else {
            mMySelf = null;
        }

        if (mOnChangedListener != null) {
            mOnChangedListener.onChanged(mMySelf);
        }
    }

    @Override
    public SearchFilterQuery.Contact getValue() {
        return mMySelf;
    }

    @Override
    public void reset() {
        mMySelf = null;
        super.reset();
    }

    @Override
    public void onSaveState(String type, Bundle outState) {
        super.onSaveState(type, outState);
        if (mMySelf != null) {
            outState.putSerializable(type, mMySelf);
        }
    }

    @Override
    public void onRestoreState(String type, Bundle savedState) {
        super.onRestoreState(type, savedState);
        mMySelf = (SearchFilterQuery.Contact) savedState.getSerializable(type);
        if (mMySelf != null) {
            setSelected(true);
        }
    }
}
