package com.jd.oa.unifiedsearch.joyday.utils;

import android.content.Context;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyday.model.DateModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtil {

    public static boolean isSameDay(Date day1, Date day2) {
        if (day1 == null || day2 == null) return false;
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
        return fmt.format(day1).equals(fmt.format(day2));
    }

    public static boolean isToday(Date date) {
        return isSameDay(new Date(), date);
    }

    // 获得两个时间之间的日期
    public static List<DateModel> getDateBetween(long beginTime, long endTime) {
        List<DateModel> mDateList = new ArrayList<>();
        //记录往后每一天的时间，用来和最后一天到做对比。这样就能知道什么时候停止了。
        long allTimeEnd = 0;
        //记录当前到个数(相当于天数)
        long currentFlag = 0;

        while (endTime > allTimeEnd) {
            allTimeEnd = beginTime + currentFlag * 24 * 60 * 60 * 1000;
            Date dateTime = new Date(beginTime + currentFlag * 24 * 60 * 60 * 1000);
            long newEndTime = makeNewTimeStamp(dateTime, endTime);
            if (newEndTime < endTime) {
                mDateList.add(DateModel.fromDateTime(dateTime));
            }
            currentFlag++;
        }
        return mDateList;
    }

    // 使用新的day拼接老的的时间
    public static long makeNewTimeStamp(Date day, long time) {
        Date oldDay = new Date(time);
        Date newDay = new Date(day.getYear(), day.getMonth(), day.getDate(), oldDay.getHours(), oldDay.getMinutes(), 0);
        return newDay.getTime();
    }

    public static Date startOfDay(Date time) {
        return new Date(time.getYear(), time.getMonth(), time.getDate(), 0, 0, 0);
    }

    public static Date endOfDay(Date time) {
        return new Date(time.getYear(), time.getMonth(), time.getDate(), 23, 59, 59);
    }

    public static boolean isStartOfDay(Date time) {
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.setTime(time);
        return calendar.get(Calendar.HOUR_OF_DAY) == 0 && calendar.get(Calendar.MINUTE) == 0 && calendar.get(Calendar.SECOND) == 0;
    }

    public static Date subtractMonth(Date time, int num) {
        int year = time.getYear();
        int month = time.getMonth();
        int day = time.getDate();
        int hour = time.getHours();
        int minute = time.getMinutes();
        int second = time.getSeconds();
        double dY = (double) num / 12;
        if (dY < 1){
            // 说明减的月数没有超过一年
            int rangeM = month - num;
            if (rangeM < 0) {
                year = year - 1;
                month = 12 + rangeM;
                return new Date(year, month, day, hour, minute, second);
            } else {
                return new Date(year, rangeM, day, hour, minute, second);
            }
        } else {
            // 减的月数超过了一年
            int cY = (int)Math.ceil(dY);
            int bY = (int)Math.floor(dY);
            int cMonth = num - (12 * bY);
            int rangeM = month - cMonth;
            if (rangeM < 0) {
                month = 12 + rangeM;
                return new Date(year - cY, month, day, hour, minute, second);
            } else {
                return new Date(year - bY, rangeM, day, hour, minute, second);
            }
        }
    }

    public static Date addMonths(Date from, int months) {
        int r = months % 12;
        int q = (int)Math.floor((double)(months - r) / 12);
        int newYear = from.getYear() + q;
        int newMonth = from.getMonth() + r;
        if (newMonth > 11) {//getMonth返回0~11
            newYear++;
            newMonth -= 12;
        }
        int newDay = Math.min(from.getDate(), daysInMonth(newYear, newMonth));
        return new Date(newYear, newMonth, newDay, from.getHours(), from.getMinutes(),
                from.getSeconds());
    }

    public static Date addDay(Date from, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(from);
        calendar.add(Calendar.DATE, day);
        return calendar.getTime();
    }

    private static int daysInMonth(int year, int month) {
    int[] daysInMonthArray = new int[]{31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        int result = daysInMonthArray[month];
        if (month == 1 && isLeapYear(year)) {//2月
            result++;
        }
        return result;
    }

    public static Date firstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date lastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int day = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        return calendar.getTime();
    }

    /**
     * 是否是闰年
     */
    static boolean isLeapYear(int year) {
        year += 1900;
        return ((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0);
    }

    // 获得两个时间之间的日期
    public static List<Date> getTimeBetween(long beginTime, long endTime) {
        List<Date> mDateList = new ArrayList<>();
        if (beginTime == endTime) {
            return mDateList;
        }
        //记录往后每一天的时间，用来和最后一天到做对比。这样就能知道什么时候停止了。
        long allTimeEnd = 0;
        //记录当前到个数(相当于天数)
        long currentFlag = 0;

        while (endTime > allTimeEnd) {
            allTimeEnd = beginTime + currentFlag * 24 * 60 * 60 * 1000;
            Date dateTime = new Date(beginTime + currentFlag * 24 * 60 * 60 * 1000);
            long newEndTime = makeNewTimeStamp(dateTime, endTime);
            if (newEndTime < endTime) {
                mDateList.add(dateTime);
            }
            currentFlag++;
        }
        return mDateList;
    }

    // 两个日期相差的天数
    public static int differenceDay(Date day1, Date day2) {
        long v = day1.getTime() - day2.getTime();
        if (v < 0) v = -v;
        return (int) Math.floor((double) v / 86400000);
    }

    public static String getMonth(Context context, int month) {//0~11
        List<String> list = new ArrayList<>();
        list.add(context.getResources().getString(R.string.schedule_month_january));
        list.add(context.getResources().getString(R.string.schedule_month_february));
        list.add(context.getResources().getString(R.string.schedule_month_march));
        list.add(context.getResources().getString(R.string.schedule_month_april));
        list.add(context.getResources().getString(R.string.schedule_month_may));
        list.add(context.getResources().getString(R.string.schedule_month_june));
        list.add(context.getResources().getString(R.string.schedule_month_july));
        list.add(context.getResources().getString(R.string.schedule_month_august));
        list.add(context.getResources().getString(R.string.schedule_month_september));
        list.add(context.getResources().getString(R.string.schedule_month_october));
        list.add(context.getResources().getString(R.string.schedule_month_november));
        list.add(context.getResources().getString(R.string.schedule_month_december));
        return month > list.size() ? "" : list.get(month - 1);
    }
}
