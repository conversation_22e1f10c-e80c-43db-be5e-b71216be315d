package com.jd.oa.unifiedsearch.all.helper;

import static com.jd.oa.router.DeepLink.JOY_WORK_DETAIL;

import android.net.Uri;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.all.data.SearchModel;
import com.jd.oa.unifiedsearch.all.util.SearchHistoryUtil;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.app.AppSearchAdapter;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.unifiedsearch.approval.ApprovalSearchAdapter;
import com.jd.oa.unifiedsearch.approval.data.ApprovalInfo;
import com.jd.oa.unifiedsearch.joyspace.JoySpaceSearchAdapter;
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument;
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition;
import com.jd.oa.unifiedsearch.joyspace.utils.JoyspaceDetailDeeplinkGenKt;
import com.jd.oa.unifiedsearch.org.OrgRecord;
import com.jd.oa.unifiedsearch.org.OrgSearchAdapter;
import com.jd.oa.unifiedsearch.process.ProcessSearchAdapter;
import com.jd.oa.unifiedsearch.process.data.ProcessInfo;
import com.jd.oa.unifiedsearch.task.TaskModel;
import com.jd.oa.unifiedsearch.task.TaskSearchAdapter;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Time: 2023/10/26
 * Author: qudongshi
 * Description:
 */
public class UIHelper {
    public static void bindUI(RecyclerView recyclerView, SearchFloorType floorType, List<?> data, String keyword, String sessionId, String searchId) {
        if (SearchFloorType.APP == floorType) {
            List<AppInfo> listData = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                if (i > 2) {
                    break;
                }
                if (data.get(i) instanceof AppInfo) {
                    listData.add((AppInfo) data.get(i));
                }
            }
            AppSearchAdapter adapter = new AppSearchAdapter(recyclerView.getContext());

            adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                    AppInfo info = (AppInfo) adapter.getItem(position);
                    getAppDetailAndOpen(info);
                    SearchHistoryUtil.addSearchHistory(keyword);
                }
            });
            recyclerView.setAdapter(adapter);
            adapter.refresh(listData);
        } else if (SearchFloorType.JOY_SPACE == floorType) {
            List<JoySpaceDocument> listData = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i) instanceof SearchModel.Record) {
                    SearchModel.Record record = (SearchModel.Record) data.get(i);
                    String strData = JSON.toJSONString(record.refPro);
                    JoySpaceDocument item = JSON.parseObject(strData, JoySpaceDocument.class);
                    item.setSubTitle(item.buildSubTitle(recyclerView.getContext(), JoySpaceFilter.TYPE_RELATED, new SelectedSortCondition(SortCondition.TYPE_UPDATE_TIME, SortCondition.CONDITION_LATEST_FIRST)));
                    listData.add(item);
                }
            }
            JoySpaceSearchAdapter adapter = new JoySpaceSearchAdapter(recyclerView.getContext());
            recyclerView.setAdapter(adapter);
            adapter.refresh(listData);
            adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                    JoySpaceDocument item = (JoySpaceDocument) adapter.getItem(position);
                    String deeplink = JoyspaceDetailDeeplinkGenKt.generateDetailDeeplink(view.getContext(), item);
                    if (!TextUtils.isEmpty(item.getLogUrl())) {
                        // 埋点请求
                        JoyspaceDetailDeeplinkGenKt.joySpaceLog(item.getLogUrl());
                    }
                    if (!TextUtils.isEmpty(item.seqUrl)) {
                        SearchUtil.sendClickEvent(sessionId, searchId, item.seqUrl);
                    }
                    SearchHistoryUtil.addSearchHistory(keyword);
                    Router.build(deeplink).go(view.getContext());
                }
            });
        } else if (SearchFloorType.TASK == floorType) {
            List<TaskModel> listData = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i) instanceof SearchModel.Record) {
                    SearchModel.Record record = (SearchModel.Record) data.get(i);
                    String strData = JSON.toJSONString(record.refPro);
                    TaskModel item = JSON.parseObject(strData, TaskModel.class);
                    listData.add(item);
                }
            }
            TaskSearchAdapter adapter = new TaskSearchAdapter(recyclerView.getContext(), listData);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                    TaskModel item = (TaskModel) adapter.getItem(position);
                    if (!TextUtils.isEmpty(item.seqUrl)) {
                        SearchUtil.sendClickEvent(sessionId, searchId, item.seqUrl);
                    }
                    SearchHistoryUtil.addSearchHistory(keyword);
                    String deepLink = JOY_WORK_DETAIL;
                    if (!TextUtils.isEmpty(item.taskId)) {
                        deepLink += "?taskId=" + item.taskId;
                        Router.build(deepLink).go(view.getContext());
                    }
                }
            });
        } else if (SearchFloorType.PROCESS == floorType) {
            List<ProcessInfo> listData = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i) instanceof ProcessInfo) {
                    listData.add((ProcessInfo) data.get(i));
                }
            }
            ProcessSearchAdapter adapter = new ProcessSearchAdapter(recyclerView.getContext(), listData);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                    ProcessInfo processInfo = (ProcessInfo) adapter.getItem(position);
                    if (processInfo.refPro.mobileApplyLink != null && !processInfo.refPro.mobileApplyLink.isEmpty()) {
                        JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Process_ck,
                                JDMAConstants.Mobile_Event_UnifiedSearch_Process_ck);
                        SearchHistoryUtil.addSearchHistory(keyword);
                        String deeplink = processInfo.refPro.mobileApplyLink;
                        Router.build(deeplink).go(view.getContext());
                    }
                }
            });
        } else if (SearchFloorType.APPROVAL == floorType) {
            List<ApprovalInfo> listData = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i) instanceof ApprovalInfo) {
                    listData.add((ApprovalInfo) data.get(i));
                }
            }
            ApprovalSearchAdapter adapter = new ApprovalSearchAdapter(recyclerView.getContext(), listData);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                    ApprovalInfo approvalInfo = (ApprovalInfo) adapter.getItem(position);
                    if (approvalInfo.refPro.auditMobileDeeplink != null && !approvalInfo.refPro.auditMobileDeeplink.isEmpty()) {
                        JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Approval_ck,
                                JDMAConstants.Mobile_Event_UnifiedSearch_Approval_ck);
                        SearchHistoryUtil.addSearchHistory(keyword);
                        String deeplink = approvalInfo.refPro.auditMobileDeeplink;
                        Router.build(deeplink).go(view.getContext());
                    }
                }
            });
        } else if (SearchFloorType.ORG == floorType){
            List<OrgRecord> listData = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i) instanceof OrgRecord) {
                    listData.add((OrgRecord) data.get(i));
                }
            }
            OrgSearchAdapter adapter = new OrgSearchAdapter(recyclerView.getContext(),listData);
            recyclerView.setAdapter(adapter);
            adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener(){
                @Override
                public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                    OrgRecord orgRecord = (OrgRecord) adapter.getItem(position);
                    int orgType = orgRecord.refPro.orgType;
                    String relatedOrgId = orgRecord.refPro.relatedOrgId;
                    String organizationCode = orgRecord.refPro.organizationCode;
                    String nodeType = String.valueOf(orgRecord.refPro.nodeType);
                    if(orgType == 1){ //组织架构
                        Map<String,String> params = new HashMap<>();
                        params.put("pageType","organizationStructure");
                        params.put("deptId",organizationCode);
                        params.put("from","search");
                        Router.build(DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam="+ Uri.encode(JSON.toJSONString(params))).go(view.getContext());

                    }else if(orgType == 2){//关联组织
                        Map<String,String> params = new HashMap<>();
                        params.put("pageType","relativeTeam");
                        params.put("relatedOrgId",relatedOrgId);
                        params.put("deptId",organizationCode);
                        params.put("deptType",nodeType);
                        params.put("from","search");
                        Router.build(DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam="+ Uri.encode(JSON.toJSONString(params))).go(view.getContext());
                    }
                }
            });
        }
    }

    private static void getAppDetailAndOpen(AppInfo appInfo) {
        SimpleRequestCallback<String> callback1 = new SimpleRequestCallback<String>(AppBase.getTopActivity(), false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<AppInfo> response = ApiResponse.parse(info.result, AppInfo.class);
                if (response.isSuccessful()) {
                    AppUtils.openFunctionByPlugIn(AppBase.getTopActivity(), response.getData());
                    JDMAUtils.onAppOpenEvent(response.getData().getAppType(), response.getData().getAppID());
                } else {
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };

        NetWorkManagerAppCenter.getAppDetail(AppBase.getTopActivity(), callback1, appInfo.getAppID());
    }

}