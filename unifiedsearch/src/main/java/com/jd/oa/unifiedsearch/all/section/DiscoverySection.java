package com.jd.oa.unifiedsearch.all.section;

import static com.jd.oa.JDMAConstants.Mobile_Event_UnifiedSearch_Discovery_Display_ck;
import static com.jd.oa.JDMAConstants.Mobile_Event_UnifiedSearch_Discovery_Hide_ck;
import static com.jd.oa.JDMAConstants.Mobile_Event_UnifiedSearch_Discovery_ck;
import static com.jd.oa.JDMAConstants.Mobile_Event_UnifiedSearch_Discovery_exposure;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.elliptical.SuperEllipticalImageView;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.callback.ISectionEventCallback;
import com.jd.oa.unifiedsearch.all.data.SearchDiscoveryModel;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.UtilApp;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class DiscoverySection extends BaseSection {

    private Context context;
    LayoutInflater layoutInflater;
    private List<SearchDiscoveryModel.DiscoverDataItem> data; //不要手动添加数据
    private ISectionEventCallback eventCallback;

    public DiscoverySection(Context context, List<SearchDiscoveryModel.DiscoverDataItem> data, ISectionEventCallback callback) {
        super(SectionParameters.builder().itemResourceId(R.layout.unifiedsearch_section_discovery).build(), "");
        this.context = context;
        this.layoutInflater = LayoutInflater.from(context);
        this.data = data;
        this.eventCallback = callback;
    };

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        ContentViewHolder holder = new ContentViewHolder(view);
        if (holder.container != null && holder.container.getChildCount() > 0) {
            holder.container.removeAllViews();
        }
        if (data == null) {
            return holder;
        }
        for (int index = 0; index < data.size(); index++) {
            //初始化view
            View layout = layoutInflater.inflate(R.layout.unifiedsearch_section_discovery_item, holder.container, false);
            holder.container.addView(layout);
        }
        return holder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        if (data == null || viewHolder == null) {
            return;
        }
        ContentViewHolder holder = (ContentViewHolder) viewHolder;
        for (int index = 0; index < data.size(); index++) {
            //获取数据
            SearchDiscoveryModel.DiscoverDataItem discoveryItem = data.get(index);
            //获取view
            View childView = holder.container.getChildAt(index);
            if (childView != null) {
                TextView tvDoc = childView.findViewById(R.id.tv_item);
                SuperEllipticalImageView spIv = childView.findViewById(R.id.discovery_icon);
                if (discoveryItem.iconUrl.isEmpty()) {
                    spIv.setVisibility(View.GONE);
                } else {
                    spIv.setVisibility(View.VISIBLE);
                    ImageLoader.load(context, spIv, discoveryItem.iconUrl);
                }
                tvDoc.setText(discoveryItem.text);
                childView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        SearchConfigHelper.getInstance().getMainHandler().post(new Runnable() {
                            @Override
                            public void run() {
                                //默认跳转，可以通过判断type使用callback传回AllSearchDelegate然后放进输入框
                                if (!TextUtils.isEmpty(discoveryItem.mobileUrl)) {
                                    Router.build(discoveryItem.mobileUrl).go(context);
                                }
                                HashMap<String, String> params = new HashMap<>();
                                params.put("contentId", discoveryItem.contentId);
                                JDMAUtils.clickEvent("", Mobile_Event_UnifiedSearch_Discovery_ck, params);
                            }
                        });
                    }
                });
            }
        }
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {

    }

    private class ContentViewHolder extends RecyclerView.ViewHolder {
        public LinearLayout container;
        public IconFontView iconFontView;

        private boolean isContainerVisible;

        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
            container = itemView.findViewById(R.id.discovery_container);
            iconFontView = itemView.findViewById(R.id.discovery_show);
            iconFontView.setOnClickListener(v -> toggleVisibility());
            //是否显示发现楼层内容
            SimpleDateFormat sdf = new SimpleDateFormat(UtilApp.getAppContext().getString(R.string.date_format_type));
            String currentDate = sdf.format((new Date(System.currentTimeMillis())));
            String timeStamp = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_SHOW_UNIFIEDSEARCH_DISCOVERY);
            isContainerVisible = !timeStamp.equals(currentDate);
            if (isContainerVisible) {
                showContainer();
                JDMAUtils.clickEvent("", Mobile_Event_UnifiedSearch_Discovery_exposure, null);
            } else {
                hideContainer();
            }
        }

        private void toggleVisibility() {
            if (isContainerVisible) {
                hideContainer();
                //将今天的日期记录写进SP，下次载入时通过对比是否是同一天来决定是否隐藏（隐藏之后今天不再展示）
                SimpleDateFormat sdf = new SimpleDateFormat(UtilApp.getAppContext().getString(R.string.date_format_type));
                ToastUtils.showToast(context, context.getString(R.string.unifiedsearch_discovery_item_hide));
                String currentDate = sdf.format((new Date(System.currentTimeMillis())));
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SHOW_UNIFIEDSEARCH_DISCOVERY, currentDate);
                JDMAUtils.clickEvent("", Mobile_Event_UnifiedSearch_Discovery_Hide_ck, null);
            } else {
                showContainer();
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SHOW_UNIFIEDSEARCH_DISCOVERY, "");
                JDMAUtils.clickEvent("", Mobile_Event_UnifiedSearch_Discovery_Display_ck, null);
            }
        }

        private void hideContainer() {
            if (container != null && iconFontView != null) {
                container.setVisibility(View.GONE);
                iconFontView.setText(R.string.icon_general_eyeinvisible);
                isContainerVisible = false;
            }
        }

        private void showContainer() {
            if (container != null && iconFontView != null) {
                container.setVisibility(View.VISIBLE);
                iconFontView.setText(R.string.icon_general_eye);
                isContainerVisible = true;
            }
        }
    }
}
