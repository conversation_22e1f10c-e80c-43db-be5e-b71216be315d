package com.jd.oa.unifiedsearch.joyspace.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;

public class ContactLayout extends LinearLayout {
    private static final int NAME_MAX_LENGTH = 4;
    private TextView mTvText;
    private String mPrefix;
    private String mSuffix;

    public ContactLayout(Context context) {
        this(context, null);
    }

    public ContactLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ContactLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ContactLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        View view = LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_contact, this, true);
        mTvText = view.findViewById(R.id.tv_text);
    }


    public void setText(String text) {
        mTvText.setText(text);
    }

    public void setContacts(String prefix, String suffix, List<MemberEntityJd> contacts) {
        if (CollectionUtil.isEmptyOrNull(contacts)) return;

        if (prefix == null) {
            prefix = "";
        }
        mPrefix = prefix;

        if (suffix == null) {
            suffix = "";
        }
        mSuffix = suffix;

        int totalNum = contacts.size();
        MemberEntityJd first = contacts.get(0);
        String name = first.getName() == null ? "" : first.getName();
        name = name.substring(0, Math.min(NAME_MAX_LENGTH, name.length()));
        String text = totalNum == 1 ? name: getContext().getString(R.string.unifiedsearch_joyspace_contact_select, name, totalNum - 1);
        mTvText.setText(String.format("%s%s%s", prefix, text, suffix));
    }

    public String getPrefix() {
        return mPrefix;
    }

    public String getSuffix() {
        return mSuffix;
    }
}
