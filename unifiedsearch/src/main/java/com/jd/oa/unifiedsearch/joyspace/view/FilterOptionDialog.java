package com.jd.oa.unifiedsearch.joyspace.view;


import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDialog;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;

import java.util.List;


public class FilterOptionDialog extends BaseBottomDialogFragment {

    private TextView mTvTitle;
    private RecyclerView mRecyclerView;

    private String mTitle;
    private List<FilterOption> mOptions;
    private FilterOption mSelectedOption;
    private FilterOption mNewSelectedOption;

    private OnOptionClickListener mOnOptionClickListener;

    private DialogInterface.OnCancelListener mOnCancelListener;

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        AppCompatDialog dialog = new AppCompatDialog(getContext(), R.style.UnifiedSearchFilterOptionDialogStyle);

        View view = LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_joyspace_dialog_option, null);
        initView(view);
        dialog.setContentView(view);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }

        return dialog;
    }

    private void initView(View view) {
        mTvTitle = view.findViewById(R.id.tv_title);
        mRecyclerView = view.findViewById(R.id.recycle_view);

        mTvTitle.setText(mTitle);

        RecyclerView.LayoutManager layoutManager = new GridLayoutManager(getContext(), 2);
        FilterOptionAdapter adapter = new FilterOptionAdapter(getContext(), mOptions);
        if (mSelectedOption == null) {

        } else {
            mNewSelectedOption = mSelectedOption;
            adapter.setSelectedOption(mSelectedOption);
        }

        adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                mNewSelectedOption = (FilterOption) adapter.getItem(position);
                if (mOnOptionClickListener != null) {
                    mOnOptionClickListener.onOptionClick(mNewSelectedOption);
                }
            }
        });

        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(adapter);
    }

    @Override
    public void onCancel(@NonNull DialogInterface dialog) {
        super.onCancel(dialog);
        if (mOnCancelListener != null) {
            mOnCancelListener.onCancel(dialog);
        }
    }

    public void setFilterOptions(List<FilterOption> options) {
        mOptions = options;
    }

    public void setSelectedOption(FilterOption selectedOption) {
        mSelectedOption = selectedOption;
    }

    public void setTitle(String title) {
        mTitle = title;
    }

    public void setOnOptionClickListener(OnOptionClickListener onOptionClickListener) {
        mOnOptionClickListener = onOptionClickListener;
    }

    public void setOnCancelListener(DialogInterface.OnCancelListener onCancelListener) {
        mOnCancelListener = onCancelListener;
    }

    public interface OnOptionClickListener {
        void onOptionClick(FilterOption option);
    }
}