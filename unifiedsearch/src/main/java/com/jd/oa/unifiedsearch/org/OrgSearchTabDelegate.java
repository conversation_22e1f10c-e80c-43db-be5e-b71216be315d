package com.jd.oa.unifiedsearch.org;

import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.chenenyu.router.Router;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.joyspace.view.SearchLoadingIndicator;
import com.jd.oa.utils.CollectionUtil;
import com.pei.pulluploadhelper.PullUpLoad;
import com.pei.pulluploadhelper.PullUpLoadHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class OrgSearchTabDelegate extends UnifiedSearchTabDelegate {
    private static final String STATE_KEYWORD = "keyword";
    private RecyclerView mRecyclerView;
    private View mLayoutPlaceholder;
    private View mLayoutEmpty;
    private View mLayoutLoading;
    private View mLoadingIndicator;
    private View mLayoutError;
    private PullUpLoadHelper mPullUpLoadHelper;
    private Button mBtnRetry;

    private OrgSearchAdapter mOrgSearchAdapter;

    private Animation mLoadingAnimation;
    private String mKeyword;
    private TextView mTvFeedback;
    private TextView mTvEmpty;
    private OrgSearchViewModel mViewModel;

    public OrgSearchTabDelegate(Host host) {super(host);}

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mViewModel = new ViewModelProvider((ViewModelStoreOwner) mHost.getActivity()).get(OrgSearchViewModel.class);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.unifiedsearch_fragment_org, container, false);
        if (savedInstanceState != null) {
            mKeyword = savedInstanceState.getString(STATE_KEYWORD);
        }
        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        this.initView(mHost.requireView(), savedInstanceState);
    }

    private void initView(View view, @Nullable Bundle savedInstanceState) {
        mRecyclerView = view.findViewById(R.id.recycle_view);
        mLayoutPlaceholder = view.findViewById(R.id.layout_placeholder);
        mLayoutEmpty = view.findViewById(R.id.me_pub_empty_view);
        mLayoutLoading = view.findViewById(R.id.layout_loading);
        mLoadingIndicator = view.findViewById(R.id.iv_loading_indicator);
        mLayoutError = view.findViewById(R.id.layout_error);
        mBtnRetry = view.findViewById(R.id.btn_retry);
        mTvEmpty = view.findViewById(R.id.tv_empty);

        // 反馈
        mTvFeedback = view.findViewById(R.id.tv_conent);
        mTvFeedback.setMovementMethod(LinkMovementMethod.getInstance());
        mTvFeedback.setText(SearchUtil.getFeedbackContent());

        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(mHost.getContext());
        mRecyclerView.setLayoutManager(layoutManager);

        mOrgSearchAdapter = new OrgSearchAdapter(mHost.getContext());
        mOrgSearchAdapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                OrgRecord orgRecord = (OrgRecord) adapter.getItem(position);
                int orgType = orgRecord.refPro.orgType;
                String relatedOrgId = orgRecord.refPro.relatedOrgId;
                String organizationCode = orgRecord.refPro.organizationCode;
                String nodeType = String.valueOf(orgRecord.refPro.nodeType);
                if(orgType == 1){ //组织架构
                    Map<String,String> params = new HashMap<>();
                    params.put("pageType","organizationStructure");
                    params.put("deptId",organizationCode);
                    params.put("from","search");
                    Router.build(DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam="+ Uri.encode(JSON.toJSONString(params))).go(mHost.getContext());

                }else if(orgType == 2){//关联组织
                    Map<String,String> params = new HashMap<>();
                    params.put("pageType","relativeTeam");
                    params.put("relatedOrgId",relatedOrgId);
                    params.put("deptId",organizationCode);
                    params.put("deptType",nodeType);
                    params.put("from","search");
                    Router.build(DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam="+ Uri.encode(JSON.toJSONString(params))).go(mHost.getContext());
                }
            }
        });
        mRecyclerView.setAdapter(mOrgSearchAdapter);

        mPullUpLoadHelper = new PullUpLoadHelper(mRecyclerView, new PullUpLoad.OnLoadListener() {
            @Override
            public void onLoad() {
                mViewModel.next(mKeyword, mHost.getSessionId());
            }
        });
        SearchLoadingIndicator endIndicator = new SearchLoadingIndicator(mHost.getContext());
        mPullUpLoadHelper.setEndIndicator(endIndicator);
        mPullUpLoadHelper.setEmpty();

        mLoadingAnimation = AnimationUtils.loadAnimation(mHost.getContext(), R.anim.unifiedsearch_loading_indicator);

        mViewModel.getOrgRecords().observe(mHost.getViewLifecycleOwner(), new Observer<Pair<String, List<OrgRecord>>>() {
            @Override
            public void onChanged(Pair<String, List<OrgRecord>> result) {
                //数据加载成功
                if (CollectionUtil.isEmptyOrNull(result.second)) {
                    if (TextUtils.isEmpty(result.first)) { //返回数据为空
                        mOrgSearchAdapter.clear();
                        mRecyclerView.setVisibility(View.INVISIBLE);
                        mLayoutEmpty.setVisibility(View.GONE);
                        mTvFeedback.setVisibility(View.GONE);
                        mLayoutPlaceholder.setVisibility(result.first == null ? View.INVISIBLE : View.VISIBLE);
                    } else {
                        mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                        mLayoutEmpty.setVisibility(View.VISIBLE);
                        mRecyclerView.setVisibility(View.INVISIBLE);
                        mTvFeedback.setVisibility(View.VISIBLE);
                    }
                } else { //有数据更新
                    mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                    mLayoutEmpty.setVisibility(View.GONE);
                    mTvFeedback.setVisibility(View.GONE);
                    mRecyclerView.setVisibility(View.VISIBLE);
                }
                //adapter更新数据
                mOrgSearchAdapter.refresh(result.second);
                mLayoutError.setVisibility(View.INVISIBLE);
            }
        });

        mViewModel.getLoading().observe(mHost.getViewLifecycleOwner(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                //Loading变化
                Pair<String, List<OrgRecord>> value = mViewModel.getOrgRecords().getValue();
                if (aBoolean && (value == null || CollectionUtil.isEmptyOrNull(value.second))) {
                    showLoading();
                } else {
                    hideLoading();
                }
            }
        });

        mViewModel.getLoadMore().observe(mHost.getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                switch (s) {
                    case OrgSearchViewModel.LOAD_MORE_EMPTY:
                        mPullUpLoadHelper.setEmpty();
                        break;
                    case OrgSearchViewModel.LOAD_MORE_COMPLETE:
                        mPullUpLoadHelper.setComplete();
                        break;
                    case OrgSearchViewModel.LOAD_MORE_LOADING:
                        mPullUpLoadHelper.setLoading();
                        break;
                    case OrgSearchViewModel.LOAD_MORE_LOADED:
                        mPullUpLoadHelper.setLoaded();
                        break;
                }
            }
        });

        mViewModel.getError().observe(mHost.getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                mLayoutEmpty.setVisibility(View.INVISIBLE);
                mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                mRecyclerView.setVisibility(View.INVISIBLE);
                mLayoutError.setVisibility(View.VISIBLE);
            }
        });

        mBtnRetry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //重试按钮
                if (!TextUtils.isEmpty(mKeyword)) {
                    mViewModel.refresh(mKeyword, mHost.getSessionId());
                }
            }
        });
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (outState != null) {
            outState.putString(STATE_KEYWORD, mKeyword);
        }
    }

    @Override
    public void search(String keyword, boolean force) {
        if (!force && Objects.equals(mKeyword, keyword)) return;

        mKeyword = keyword;
        mViewModel.refresh(keyword, mHost.getSessionId());
    }

    @Override
    public void onTextChanged(String keyword) {
        search(keyword, true);
    }

    @Override
    public void tabSelected(String keyword) {
        if (!Objects.equals(keyword, mKeyword)) {
            mViewModel.clear();
        }
        search(keyword,false);
    }

    private void showLoading() {
        mLoadingAnimation.cancel();
        mLoadingAnimation.reset();

        mLoadingIndicator.startAnimation(mLoadingAnimation);
        mLayoutLoading.setVisibility(View.VISIBLE);
    }

    private void hideLoading() {
        mLayoutLoading.setVisibility(View.INVISIBLE);
    }
}
