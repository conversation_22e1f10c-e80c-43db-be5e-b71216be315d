package com.jd.oa.unifiedsearch.approval;

import android.content.Context;
import android.graphics.Color;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.approval.data.ApprovalInfo;
import com.jd.oa.utils.HighlightText;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

import com.jd.oa.ui.recycler.BaseRecyclerAdapter;

public class ApprovalSearchAdapter extends BaseRecyclerAdapter<ApprovalInfo, RecyclerView.ViewHolder> {
    private com.jd.oa.ui.recycler.BaseRecyclerAdapter.OnItemClickListener mOnItemClickListener;
    private final String TYPE_PENDING = "PENDING";
    private final String TYPE_APPROVED = "APPROVED";
    private final String TYPE_INITIATED = "INITIATED";
    private final String TYPE_NOTIFY = "NOTIFY";
    private boolean showFeedback = false;
    private int VIEW_TYPE_ITEM = 0;
    private int VIEW_TYPE_FEEDBACK = 1;
    private final int mHighlightColor;
    private final String mHighlightTag = "em";
    private final String mStartTag = "<" + mHighlightTag + ">";
    private final String mEndTag = "</" + mHighlightTag + ">";
    public void showFeedback() {
        showFeedback = true;
    }
    public ApprovalSearchAdapter(Context context) {
        super(context);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_highlight_color);
    }

    public ApprovalSearchAdapter(Context context, List<ApprovalInfo> data) {
        super(context, data);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_highlight_color);
    }

    @Override
    public int getItemCount() {
        if (super.getItemCount() > 0 && showFeedback) {
            return super.getItemCount() + 1;
        }
        return super.getItemCount();
    }

    @Override
    public int getItemViewType(int index) {
        if (showFeedback) {
            if (index < getItemCount() - 1) {
                return VIEW_TYPE_ITEM;
            } else {
                return VIEW_TYPE_FEEDBACK;
            }
        } else {
            return VIEW_TYPE_ITEM;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (VIEW_TYPE_ITEM == viewType) {
            return new ViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_recycler_item_approval, viewGroup, false));
        } else {
            return new FeedbackViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_pub_feedback_content, viewGroup, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {
        if (viewHolder instanceof  ViewHolder) {
            ViewHolder holder = (ViewHolder) viewHolder;
            ApprovalInfo approvalInfo = getItem(i);
            if (approvalInfo != null && approvalInfo.refPro != null) {
                HighlightText.with(approvalInfo.refPro.auditTitle).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.title);
                if (approvalInfo.getExtContent().isEmpty()) {
                    holder.desc.setVisibility(View.GONE);
                } else {
                    holder.desc.setText(approvalInfo.getExtContent());
                    holder.desc.setVisibility(View.VISIBLE);
                }
                //设置申请人姓名
                String nameTag = approvalInfo.getAuditApplicantName();
                if (nameTag.isEmpty()) {
                    holder.nameTag.setVisibility(View.GONE);
                } else {
                    holder.nameTag.setText(nameTag);
                    holder.nameTag.setVisibility(View.VISIBLE);
                }
                //设置申请时间
                String timeTag = approvalInfo.getAuditApplicationTime(getContext());
                if (timeTag.isEmpty()) {
                    holder.timeTag.setVisibility(View.GONE);
                } else {
                    holder.timeTag.setText(timeTag);
                    holder.timeTag.setVisibility(View.VISIBLE);
                }

                ImageLoader.load(getContext(), holder.image, approvalInfo.refPro.auditIcon, R.drawable.jdme_ic_approval_default);
                if (approvalInfo.refPro.auditType != null) {
                    setType(holder.statusTag, approvalInfo.refPro.auditType);
                }
            }
        } else if (viewHolder instanceof FeedbackViewHolder) {
            FeedbackViewHolder holder = (FeedbackViewHolder) viewHolder;
            holder.tvContent.setVisibility(View.VISIBLE);
            holder.tvContent.setText(SearchUtil.getFeedbackContent());
        }
    }

    private void setType(TextView typeTag, String type) {
        if (type.equals(TYPE_PENDING)) {
            typeTag.setBackground(AppCompatResources.getDrawable(getContext(), R.drawable.unifiedsearch_approval_status_tag_active));
            typeTag.setTextColor(Color.parseColor("#F63218"));
        } else {
            typeTag.setBackground(AppCompatResources.getDrawable(getContext(), R.drawable.unifiedsearch_approval_status_tag_inactive));
            typeTag.setTextColor(Color.parseColor("#6b7280"));
        }
        switch (type) {
            case TYPE_APPROVED:
                typeTag.setText(R.string.unifiedsearch_approval_type_approved);
                break;
            case TYPE_INITIATED:
                typeTag.setText(R.string.unifiedsearch_approval_type_initiated);
                break;
            case TYPE_NOTIFY:
                typeTag.setText(R.string.unifiedsearch_approval_type_notify);
                break;
            case TYPE_PENDING:
                typeTag.setText(R.string.unifiedsearch_approval_type_pending);
                break;
        }
        typeTag.setVisibility(View.VISIBLE);
    }

    public void setOnItemClickListener(com.jd.oa.ui.recycler.BaseRecyclerAdapter.OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        ImageView image;
        TextView title;
        TextView statusTag;
        TextView desc;
        TextView nameTag;
        TextView timeTag;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            image = itemView.findViewById(R.id.iv_image);
            title = itemView.findViewById(R.id.tv_title);
            statusTag = itemView.findViewById(R.id.tv_status_tag);
            desc = itemView.findViewById(R.id.tv_description);
            nameTag = itemView.findViewById(R.id.tv_applicant_name);
            timeTag = itemView.findViewById(R.id.tv_application_time);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        mOnItemClickListener.onItemClick(ApprovalSearchAdapter.this, v, getBindingAdapterPosition());
                    }
                }
            });
        }
    }

    class FeedbackViewHolder extends RecyclerView.ViewHolder {

        public TextView tvContent;

        public FeedbackViewHolder(@NonNull View itemView) {
            super(itemView);
            tvContent = itemView.findViewById(R.id.tv_conent);
            tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }
}
