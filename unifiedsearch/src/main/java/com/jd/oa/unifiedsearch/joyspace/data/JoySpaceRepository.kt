package com.jd.oa.unifiedsearch.joyspace.data;

import android.content.Context
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import io.reactivex.Single

class JoySpaceRepository(context: Context) : JoySpaceDataSource {

    companion object {
        private var sInstance: JoySpaceRepository? = null
        fun get(context: Context): JoySpaceRepository {
            if (sInstance == null) {
                sInstance = JoySpaceRepository(context.applicationContext)
            }
            return sInstance!!
        }
    }

    private val mRemoteDataSource = JoySpaceRemoteDataSource()
    //private val mMockDataSource = JoySpaceMockDataSource(context)

    override fun searchJoySpaceDocuments(
        keyword: String,
        params: SearchFilterQuery,
        start: Int,
        length: Int
    ): Single<List<JoySpaceDocument>> {
        return mRemoteDataSource.searchJoySpaceDocuments(keyword, params, start, length)
    }

    override fun getRelatedDocuments(start: Int, length: Int, query: SearchFilterQuery): Single<List<JoySpaceDocument>> {
        return mRemoteDataSource.getRelatedDocuments(start, length, query)
    }

    override fun getRecentOpenedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return mRemoteDataSource.getRecentOpenedDocuments(start, length, sortCondition)
    }

    override fun getReceivedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return mRemoteDataSource.getReceivedDocuments(start, length, sortCondition)
    }

    override fun getSentDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return mRemoteDataSource.getSentDocuments(start, length, sortCondition)
    }

    override fun getCreatedByMySelfDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return mRemoteDataSource.getCreatedByMySelfDocuments(start, length, sortCondition)
    }
}
