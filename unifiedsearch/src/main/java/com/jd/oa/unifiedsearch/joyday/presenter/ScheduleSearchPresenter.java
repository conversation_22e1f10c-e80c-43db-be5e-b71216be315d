package com.jd.oa.unifiedsearch.joyday.presenter;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.ee.librecurparser.RecurUtils;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.unifiedsearch.joyday.model.Calendar;
import com.jd.oa.unifiedsearch.joyday.model.ConferenceRoom;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;
import com.jd.oa.unifiedsearch.joyday.model.Subcategory;
import com.jd.oa.unifiedsearch.joyday.model.User;
import com.jd.oa.unifiedsearch.joyday.utils.DateUtil;
import com.jd.oa.unifiedsearch.joyday.utils.SubcategoryListParser;
import com.jd.oa.utils.CollectionUtil;
import com.pei.pulluploadhelper.BiDirectionalLoad;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ScheduleSearchPresenter {

    private static final String TAG = "[joyday search]";
    public static final int PAGE_SIZE = 100;

    public interface ViewCallback {
        int ERROR_NO_NETWORK = 0;
        int ERROR_NO_DATA = 1;
        int ERROR_NOT_FOUND = 2;
        void onRefreshData(List<ScheduleModel> list, int startIndex);
        void onSetEmpty(int type, String keyword);
        void onSetComplete(int direction, List<ScheduleModel> data);
        void onSetLoaded(int direction, List<ScheduleModel> data);

        void onSubcategoryLoad(Map<String,List<Subcategory>> map);
    }

    private List<ScheduleModel> mSchedules;

    private HashMap<String, Object> mParams;//当前搜索参数，发起新搜索时更新，上下翻页不要直接修改这个map

    private ViewCallback mView;

    public void setViewCallback(ViewCallback callback) {
        mView = callback;
    }

    //发起搜索
    public void scheduleSearch(ScheduleSearchRequestParams requestParams) {
        mParams = new HashMap<>();
        try {
            if (!TextUtils.isEmpty(requestParams.getKeyword())) {
                mParams.put("keyword", requestParams.getKeyword());
            } else {
                mParams.put("keyword", "");
            }
            if (requestParams.getCalendarIds() != null && !requestParams.getCalendarIds().isEmpty()) {
                mParams.put("calendarGroupIdList", requestParams.getCalendarIds());
            }
            if (requestParams.getContacts() != null && !requestParams.getContacts().isEmpty()) {
                mParams.put("userList", requestParams.getContacts());
            } else {
                mParams.put("userList", Collections.emptyList());
            }
            if (requestParams.getStartTime() > 0 && requestParams.getEndTime() > 0) {
                mParams.put("startTime", requestParams.getStartTime());
                mParams.put("endTime", requestParams.getEndTime());
            }

            if (requestParams.getOrganizer() != null && !requestParams.getOrganizer().isEmpty()) {
                mParams.put("organizerList", requestParams.getOrganizer());
            } else {
                mParams.put("organizerList", Collections.emptyList());
            }

            if (CollectionUtil.notNullOrEmpty(requestParams.getCategoryList())) {
                mParams.put("categoryList", requestParams.getCategoryList());
            } else {
                mParams.put("categoryList", Collections.emptyList());
            }
            if (requestParams.isOnlyAttendeeStrategyUser()) {
                HashMap<String,String> map = new HashMap<>();
                map.put("ddAppId", PreferenceManager.UserInfo.getTimlineAppID());
                map.put("account", PreferenceManager.UserInfo.getUserName());
                mParams.put("onlyAttendeeStrategyUser", map);
            }

            if (requestParams.getFrom() != null) {
                mParams.put("from", requestParams.getFrom());
            }

            if (requestParams.getPermissionCode() != null) {
                mParams.put("permissionCode", requestParams.getPermissionCode());
            }

            SearchRequest.requestSearchSchedule(mParams, new SearchRequest.Callback() {
                @Override
                public void onHandleResult(List<ScheduleModel> scheduleList, List<User> userList, List<Calendar> calendarList, List<ConferenceRoom> rooms, Calendar personalCalendar) {
                    if ((scheduleList == null || scheduleList.isEmpty())) {
                        if (mView != null) {
                            mView.onSetEmpty(TextUtils.isEmpty(requestParams.getKeyword()) ? ViewCallback.ERROR_NO_DATA : ViewCallback.ERROR_NOT_FOUND, requestParams.getKeyword());
                        }
                        return;
                    }
                    try {
                        List<ScheduleModel> result = scheduleFromJson(scheduleList, userList, calendarList, rooms, personalCalendar);
                        mSchedules = result;
                        List<ScheduleModel> parsedScheduleList = ScheduleRuleParser.parseSearchList(mSchedules, requestParams.getStartTime(), requestParams.getEndTime());

                        for (ScheduleModel schedule : parsedScheduleList) {
                            if (schedule.fixedMeetingRoomList == null) {
                                schedule.fixedMeetingRoomList = new ArrayList<>();
                            }
                            for (ConferenceRoom room : rooms) {
                                if (Objects.equals(schedule.getScheduleId(), room.getScheduleId()) &&
                                        Objects.equals(schedule.getRelatedDate(), room.getRelatedDate())) {
                                    schedule.fixedMeetingRoomList.add(room);
                                }
                            }
                        }

                        List<ScheduleModel> splitScheduleList = getSplitSchedule(parsedScheduleList);
                        process(splitScheduleList, !requestParams.isAsc());
                        if (mView != null) {
                            if (splitScheduleList.isEmpty()) {
                                mView.onSetEmpty(TextUtils.isEmpty(requestParams.getKeyword()) ? ViewCallback.ERROR_NO_DATA : ViewCallback.ERROR_NOT_FOUND, requestParams.getKeyword());
                            } else {
                                mView.onRefreshData(splitScheduleList, getStartIndex(splitScheduleList, requestParams.isAsc()));
                                mView.onSetComplete(BiDirectionalLoad.DIRECTION_END, splitScheduleList);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        mSchedules.clear();
                        mParams.clear();
                        if (mView != null) {
                            mView.onSetEmpty(TextUtils.isEmpty(requestParams.getKeyword()) ? ViewCallback.ERROR_NO_DATA : ViewCallback.ERROR_NOT_FOUND, requestParams.getKeyword());
                        }
                    }
                }

                @Override
                public void onError() {
                    if (mView != null) {
                        mView.onSetEmpty(ViewCallback.ERROR_NO_NETWORK, requestParams.getKeyword());
                    }
                    //ToastUtils.showToast(R.string.unifiedsearch_joyday_net_no_network);
                }

                @Override
                public void onNoNetwork() {
                    if (mView != null) {
                        mView.onSetEmpty(ViewCallback.ERROR_NO_NETWORK, requestParams.getKeyword());
                    }
                    //ToastUtils.showToast(R.string.unifiedsearch_joyday_net_no_network);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            mSchedules.clear();
            mParams.clear();
            if (mView != null) {
                mView.onSetEmpty(TextUtils.isEmpty(requestParams.getKeyword()) ? ViewCallback.ERROR_NO_DATA : ViewCallback.ERROR_NOT_FOUND, requestParams.getKeyword());
            }
        }
    }

    private List<ScheduleModel> scheduleFromJson(List<ScheduleModel> scheduleList, List<User> userList,
                                  List<Calendar> calendarList, List<ConferenceRoom> rooms, Calendar personalCalendar) {
        List<ScheduleModel> result = new ArrayList<>();
        Map<String, User> userMap = new HashMap<>();
        for (User user : userList) {
            userMap.put(user.userId + user.teamId, user);
        }
        User quitUser = User.fromParams("quit-employee", "quit-00046419", "", User.TYPE_QUIT, PreferenceManager.UserInfo.getTimlineAppID());

        if (scheduleList != null) {
            for (ScheduleModel schedule : scheduleList) {
                if (schedule.user == null) {
                    schedule.user = userMap.containsKey(schedule.getOrganizerUserId() + schedule.getOrganizerTeamId()) ? userMap.get(schedule.getOrganizerUserId() + schedule.getOrganizerTeamId()) : quitUser;
                }

                for (Calendar calendarItem : calendarList) {
                    if (TextUtils.equals(calendarItem.calendarId, schedule.getCalendarId()) || TextUtils.equals(schedule.calendarGroupId, calendarItem.calendarId)) {
                        if (calendarItem.color == null) {
                            schedule.calendarColor = calendarItem.type == 1 ? "ff295bcc" : "ffEF5B56";
                        } else {
                            schedule.calendarColor = calendarItem.color;
                        }

                        schedule.calendarTitle = calendarItem.title;
                        schedule.setCalendarType(calendarItem.type);
                    }
                }
                if (schedule.calendarColor == null && personalCalendar != null) {
                    if (!TextUtils.equals(schedule.getCalendarId(), personalCalendar.calendarId)) {
                        schedule.calendarColor = personalCalendar.color;
                    }
                }

                result.add(schedule);
            }
        }
        return result;
    }

    private void process(List<ScheduleModel> splitScheduleList, boolean reverse) {
        Collections.sort(splitScheduleList);
        if (reverse) {
            Collections.reverse(splitScheduleList);
        }
        for (int i = 0; i < splitScheduleList.size(); i++) {
            ScheduleModel current = splitScheduleList.get(i);
            if (i == 0) {
                current.firstOfDay = true;
            } else {
                ScheduleModel pre = splitScheduleList.get(i -1);
                boolean notSame = !DateUtil.isSameDay(current.getBeginDateTime(), pre.getBeginDateTime());
                current.firstOfDay = notSame;
                pre.endOfDay = notSame;
            }
        }
    }

    private List<ScheduleModel> getSplitSchedule(List<ScheduleModel> schedules) {
        long todayStart = DateUtil.startOfDay(new Date()).getTime();
        List<ScheduleModel> scheduleList = new ArrayList<>();
        for (ScheduleModel item : schedules) {
            //将跨天日程拆为多个日程
            if (item.isSpanDay()) {
                List<ScheduleModel> list = RecurUtils.splitMultiDayAppointment(item);
                //跨天日程搜索时只返回离今天最近的一天，参照重复日程
                if (!list.isEmpty()) {
                    ScheduleModel first = list.get(0);
                    ScheduleModel last = list.get(list.size() - 1);
                    if (todayStart <= first.getBeginTime()) {
                        scheduleList.add(first);
                    } else if (todayStart >= last.getEndTime()) {
                        scheduleList.add(first);
                    } else {
                        for (ScheduleModel scheduleModel : list) {
                            if (DateUtil.isToday(scheduleModel.getBeginDateTime())) {
                                scheduleList.add(scheduleModel);
                                break;
                            }
                        }
                    }
                }
            } else {
                scheduleList.add(item);
            }
        }
        return scheduleList;
    }

    //规则参照需求文档，优先今天第一条，如果没有今天，往后30天内离今天最近的一条，往后也没有，往前
    private int getStartIndex(List<ScheduleModel> items, boolean asc) {
        int beforeToday = -1;
        int afterToday = -1;
        long todayTimeStamp = DateUtil.startOfDay(new Date()).getTime();
        for (int idx = 0; idx < items.size(); idx++) {
            ScheduleModel item = items.get(idx);
            if (!item.firstOfDay) {
                continue;
            }
            //TYPE_DAY下，item.time都是凌晨整点时间，items已经按时间排序
            long itemTime = DateUtil.startOfDay(item.getBeginDateTime()).getTime();
            if (itemTime == todayTimeStamp) {
                return idx;
            } else if (itemTime < todayTimeStamp) {
                beforeToday = idx;//今天之前最近的一天
                if (!asc) {
                    break;
                }
            } else {
                afterToday = idx;//今天之后的第一天
                if (asc) {
                    break;
                }
            }
        }
        //如果今天没有日程，取前边或后边的第一个有效天数
        return afterToday >= 0 ? afterToday : beforeToday;
    }

    /**
     * var params = <String,dynamic>{
     *       "prefix":["common","mobile"],
     *       "key": []
     *     };
     * var response = await NetManager.post(CalendarApiType.GET_CONFIG, data: params);
     * Map data = response.data is Map ? response.data : json.decode(response.data);
     * var apiResponse = ApiResponse.fromJson(data);
     * return apiResponse;
     */
    public void getJoydayDuccConfig() {
        Map<String,Object> params = new HashMap<>();
        params.put("prefix", Arrays.asList("common", "mobile"));
        //params.put("key", Collections.singletonList("common.scheduleSubcategory"));
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (info.result != null) {
                    ApiResponse<Map<String,Object>> response = ApiResponse.parse(info.result, new TypeToken<Map<String,Object>>(){}.getType());
                    if (response.isSuccessful()) {
                        String subcategory = (String) response.getData().get("common.scheduleSubcategory");
                        mView.onSubcategoryLoad(new SubcategoryListParser().parseConfig(subcategory));
                    }
                }
            }
        }, "joyday.appointment.queryClientConfig");
    }
}
