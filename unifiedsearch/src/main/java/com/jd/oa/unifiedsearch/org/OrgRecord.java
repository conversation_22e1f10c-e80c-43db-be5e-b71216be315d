package com.jd.oa.unifiedsearch.org;

import java.util.List;

public class OrgRecord {
    public List<HighlightJoin> highlightJoin;
    public String indexId;
    public String orgFullCode;
    public String orgFullName;
    public String orgName;
    public String origin;
    public String pin;
    public RefPro refPro;

    public class HighlightJoin{
        public List<HighlightSort> highlightSort;
    }

    public class HighlightSort{
        public String value;
        public String key;
    }

    public class RefPro{
        public String relatedOrgId; //关联组织ID
        public String organizationFullname;//部门 、 关联组织全路径
        public String tenantCode;
        public int nodeType;
        public int orgType;  //部门类型 1-实体部门，2-关联组织
        public List<Member> members;
        public boolean isParent;
        public String organizationName;
        public String organizationType;
        public String organizationCode; //部门ID
        public String teamId;//租户ID
        public String organizationLevel;
        public String organizationFullPath;
        public String status;
    }

    public class Member{
        public String realName;
        public String pin;
        public String teamId;
        public String userId;
    }
}
