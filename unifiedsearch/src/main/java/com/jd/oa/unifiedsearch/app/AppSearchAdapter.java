package com.jd.oa.unifiedsearch.app;

import static com.jd.oa.business.app.adapter.AppRecyclerAdapter.setAppTag;

import android.content.Context;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class AppSearchAdapter extends BaseRecyclerAdapter<AppInfo, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;

    private boolean showFeedback = false;
    private int VIEW_TYPE_ITEM = 0;
    private int VIEW_TYPE_FEEDBACK = 1;

    public AppSearchAdapter(Context context) {
        super(context);
    }

    public AppSearchAdapter(Context context, List<AppInfo> data) {
        super(context, data);
    }

    public void showFeedback() {
        showFeedback = true;
    }

    @Override
    public int getItemCount() {
        if (super.getItemCount() > 0 && showFeedback) {
            return super.getItemCount() + 1;
        }
        return super.getItemCount();
    }

    @Override
    public int getItemViewType(int index) {
        if (showFeedback) {
            if (index < getItemCount() - 1) {
                return VIEW_TYPE_ITEM;
            } else {
                return VIEW_TYPE_FEEDBACK;
            }
        } else {
            return VIEW_TYPE_ITEM;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (VIEW_TYPE_ITEM == viewType) {
            return new ViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_recycler_item_app, viewGroup, false));
        } else {
            return new FeedbackViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_pub_feedback_content, viewGroup, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {
        if (viewHolder instanceof ViewHolder) {
            ViewHolder holder = (ViewHolder) viewHolder;
            AppInfo appInfo = getItem(i);
            ImageLoader.load(getContext(), holder.image, appInfo.getPhotoKey(), R.drawable.jdme_ic_app_default);
            holder.title.setText(appInfo.getAppName());
            holder.desc.setText(appInfo.getAppSubName());
            holder.desc.setVisibility(TextUtils.isEmpty(appInfo.getAppSubName()) ? View.GONE : View.VISIBLE);
            setAppTag(holder.tag, appInfo);
        } else if (viewHolder instanceof FeedbackViewHolder) {
            FeedbackViewHolder holder = (FeedbackViewHolder) viewHolder;
            holder.tvContent.setVisibility(View.VISIBLE);
            holder.tvContent.setText(SearchUtil.getFeedbackContent());
        }
    }


    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        ImageView image;
        TextView title;
        TextView tag;
        TextView desc;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            image = itemView.findViewById(R.id.iv_image);
            title = itemView.findViewById(R.id.tv_title);
            tag = itemView.findViewById(R.id.tv_tag);
            desc = itemView.findViewById(R.id.tv_desc);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        int position = getBindingAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) return;
                        mOnItemClickListener.onItemClick(AppSearchAdapter.this, v, position);
                    }
                }
            });
        }
    }

    class FeedbackViewHolder extends RecyclerView.ViewHolder {

        public TextView tvContent;

        public FeedbackViewHolder(@NonNull View itemView) {
            super(itemView);
            tvContent = itemView.findViewById(R.id.tv_conent);
            tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }
}
