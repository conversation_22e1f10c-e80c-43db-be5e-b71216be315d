package com.jd.oa.unifiedsearch.joyspace;

import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.TranslateAnimation
import android.widget.*
import androidx.core.content.ContextCompat
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.joyspace.data.SearchFilterQuery
import com.jd.oa.unifiedsearch.joyspace.filter.*
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter.FilterType
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortConditionFactory
import com.jd.oa.unifiedsearch.joyspace.view.FiltersContainerLayout
import java.util.*

class JoySpaceSearchTabDelegate(host: Host): JoySpaceSearchDelegate(host), FilterContext {

    companion object {
        const val TAG = "JoySpaceTabDelegate"
        const val STATE_SELECTED_TYPE = "selected_type"
    }

    private val mScrollTypes: HorizontalScrollView by lazy { mHost.requireView().findViewById(R.id.scroll_types) }
    private val mRadioGroup: RadioGroup by lazy { mHost.requireView().findViewById(R.id.radio_group) }
    private val mRadioSent: RadioButton by lazy { mHost.requireView().findViewById(R.id.radio_sent) }
    private val mIvTabIndicator: ImageView by lazy { mHost.requireView().findViewById(R.id.iv_tab_indicator) }
    private val mIvEdge: ImageView by lazy { mHost.requireView().findViewById(R.id.iv_edge)}
    private val mFilterContainer: FiltersContainerLayout by lazy { mHost.requireView().findViewById(R.id.layout_filter_container) }
    private val mSortConditionButton: ImageButton by lazy { mHost.requireView().findViewById(R.id.btn_sort_condition) }

    private lateinit var mCurrentCheckedTab: RadioButton

    @FilterType
    private lateinit var mCurrentSelectedType: String

    private val mFilterFactory: JoySpaceFilterFactory = JoySpaceFilterFactory()

    private lateinit var mSubTypeFilters: Map<String,JoySpaceFilter<*>>

    private var mStartActivityFilter: UnifiedSearchFilter<*>? = null

    private var mSelectedSortCondition: SelectedSortCondition? = null

    private val mSortConditionStorage: SortConditionStorage by lazy { MemorySortConditionStorage() }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        return inflater.inflate(R.layout.unifiedsearch_fragment_joyspace, container, false)
    }

    override fun initView(view: View?, savedInstanceState: Bundle?) {

        mCurrentSelectedType = savedInstanceState?.getString(STATE_SELECTED_TYPE, JoySpaceFilter.TYPE_RELATED) ?: getCurrentSelectedType(mRadioGroup.checkedRadioButtonId)
        mSubTypeFilters = mFilterFactory.getFiltersByType(this, mCurrentSelectedType)

        mFilterContainer.setFilters(mSubTypeFilters.values.toList())

        super.initView(view, savedInstanceState)

        savedInstanceState?.let { mFilterContainer.changeResetState() }

        mCurrentCheckedTab = view!!.findViewById(mRadioGroup.checkedRadioButtonId)
        //mCurrentCheckedTab.post { moveTabIndicator(view, null, mCurrentCheckedTab) }

        mRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            val checkedTab = view.findViewById<RadioButton>(checkedId)
            //moveTabIndicator(view, mCurrentCheckedTab, checkedTab)
            mCurrentCheckedTab = checkedTab

            mCurrentSelectedType = getCurrentSelectedType(checkedId)

            resetTypesEdge()

            mSelectedSortCondition = getSelectedSortCondition()
            mSelectedSortCondition?.let { updateSortConditionIcon(it.condition, it.order) }

            mSubTypeFilters = mFilterFactory.getFiltersByType(this, mCurrentSelectedType)
            mFilterContainer.setFilters(mSubTypeFilters.values.toList())

            if (DocumentTypeFilter.noFolderOption(mCurrentSelectedType)) {
                val documentTypeFilter = mFilterFactory.getSubType(this, JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE) as? DocumentTypeFilter
                documentTypeFilter?.let {
                    if (DocumentTypeFilter.DOCUMENT_TYPE_FOLDER == it.value?.value) {
                        documentTypeFilter.reset()
                    }
                }
            }

            if (checkedTab.isPressed) {
                //人为点击的
                refresh(mKeyword, mCurrentSelectedType)
            }

            if (checkedId == R.id.radio_sent) {
                mScrollTypes.post {
                    val maxScrollX = mRadioGroup.measuredWidth - mScrollTypes.measuredWidth
                    mScrollTypes.scrollTo(maxScrollX, 0)
                }
            } else if (checkedId == R.id.radio_all) {
                mScrollTypes.post { mScrollTypes.scrollTo(0, 0) }
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mScrollTypes.setOnScrollChangeListener { _, _, _, _, _ ->
                resetTypesEdge()
            }
        }

        resetTypesEdge()

        mSortConditionButton.setOnClickListener {
            showSortConditionDialog(it, SortConditionFactory.getSortConditions(mHost.context, mCurrentSelectedType))
        }

        mFilterContainer.mOnFilterChangedListener = {filter, value->
            refresh(mKeyword, mCurrentSelectedType)
        }

        mFilterContainer.mOnResetClickListener =  View.OnClickListener {
            refresh(mKeyword, mCurrentSelectedType)
        }

        mBtnRetry.setOnClickListener {
            refresh(mKeyword, mCurrentSelectedType)
        }

        mSelectedSortCondition = getSelectedSortCondition()
        mSelectedSortCondition?.let { updateSortConditionIcon(it.condition, it.order) }

        if (!TextUtils.isEmpty(mKeyword)) {
            refresh(mKeyword, mCurrentSelectedType)
        }
    }

    override fun onSaveInstanceState(outState: Bundle?) {
        super.onSaveInstanceState(outState)
        outState?.let {
            it.putString(STATE_SELECTED_TYPE, mCurrentSelectedType)

            mSubTypeFilters.forEach { entry ->
                entry.value.onSaveState(entry.key, outState)
            }
        }
    }

    override fun restoreInstanceState(savedState: Bundle) {
        super.restoreInstanceState(savedState)
        mSubTypeFilters.forEach {
            it.value.onRestoreState(it.key, savedState)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mSubTypeFilters.forEach {
            it.value.onConfigurationChanged(newConfig)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val result = mStartActivityFilter?.onActivityResult(requestCode, resultCode, data) ?: false
        mStartActivityFilter = null
        if (result) return
    }

    @FilterType
    private fun getCurrentSelectedType(checkedId: Int): String = when(checkedId) {
        R.id.radio_all -> JoySpaceFilter.TYPE_RELATED
        R.id.radio_recent -> JoySpaceFilter.TYPE_RECENT
        R.id.radio_received -> JoySpaceFilter.TYPE_RECEIVED
        R.id.radio_sent -> JoySpaceFilter.TYPE_SENT
        R.id.radio_created -> JoySpaceFilter.TYPE_CREATED
        R.id.radio_public -> JoySpaceFilter.TYPE_PUBLIC
        else -> throw IllegalArgumentException("Unknown type")
    }

    private fun moveTabIndicator(view: View?, pre: RadioButton?, cur: RadioButton) {
        pre?.let { pre.paint.isFakeBoldText = false }
        cur.paint.isFakeBoldText = true

        fun findIndicatorTranslationX(view: View): Float = ((view.left + view.right) / 2 - mIvTabIndicator.width / 2).toFloat()

        val preTranslateX = if (pre != null) findIndicatorTranslationX(pre) else 0f
        val nextTranslationX = findIndicatorTranslationX(cur)

        val animation = TranslateAnimation(preTranslateX, nextTranslationX,0f, 0f)
        animation.duration = 200
        animation.fillAfter = true
        mIvTabIndicator.startAnimation(animation)
    }

    override fun getSelectedSortCondition(): SelectedSortCondition {
         return mSortConditionStorage.getSelectedSortCondition(mCurrentSelectedType)
             ?: SortCondition.defaultSortCondition(mCurrentSelectedType)
    }

    override fun onSortConditionSelected(@SortCondition.SortType sortType: String, @SortCondition.SortOrder sortOrder: String) {
        super.onSortConditionSelected(sortType, sortOrder)
        mSelectedSortCondition = SelectedSortCondition(sortType, sortOrder)
        mSortConditionStorage.setSelectedSortCondition(mCurrentSelectedType, mSelectedSortCondition)
        updateSortConditionIcon(sortType, sortOrder)

        refresh(mKeyword, mCurrentSelectedType)
    }

    override fun onSortConditionReset() {
        val defaultSortCondition = SortCondition.defaultSortCondition(mCurrentSelectedType)

        onSortConditionSelected(defaultSortCondition.condition, defaultSortCondition.order)
    }

    private fun updateSortConditionIcon(condition: String, option: String) {
        val isDefault = SortCondition.isDefaultSortCondition(mCurrentSelectedType, condition, option)
        val isFirst = when(option) {
            SortCondition.CONDITION_RELATED_FIRST -> true
            SortCondition.CONDITION_LATEST_FIRST -> true
            else -> false
        }
        if (isDefault) {
            mSortConditionButton.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_sort_condition_down_black))
            mSortConditionButton.background = null
        } else if (isFirst) {
            mSortConditionButton.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_sort_condition_down_checked))
            //mSortConditionButton.background = ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_joyspace_sort_condition_button_bg)
        } else {
            mSortConditionButton.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_sort_condition_up_checked))
            //mSortConditionButton.background = ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_joyspace_sort_condition_button_bg)
        }
    }

    private fun resetTypesEdge() {
        mScrollTypes.post {
            try {
                val maxScrollX = mRadioGroup.measuredWidth - mScrollTypes.measuredWidth
                mIvEdge.visibility = if (maxScrollX < 0 || mScrollTypes.scrollX == maxScrollX) {
                    View.INVISIBLE
                } else {
                    View.VISIBLE
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun search(keyword: String?, force: Boolean) {
        Log.d(TAG, "search, $keyword")
        if (!force && Objects.equals(mKeyword, keyword)) return

        mKeyword = keyword

        mHost.view?.let {
            refresh(mKeyword, mCurrentSelectedType)

            resetTypesEdge()
        }
    }

    override fun tabSelected(keyword: String?) {
        search(keyword, false)
    }

    override fun onTextChanged(keyword: String?) {
        search(keyword, true)
    }

    private fun refresh(keyword: String?, @FilterType type: String) {
        mSelectedSortCondition = getSelectedSortCondition()
        val searchFilter = SearchFilterQuery.Factory.createByType(type, mSubTypeFilters, mSelectedSortCondition)
        mViewModel.refresh(keyword, type, searchFilter)
    }

    override fun startActivity(searchFilter: UnifiedSearchFilter<*>, intent: Intent?) {
        mStartActivityFilter = searchFilter
        mHost.startActivity(intent)
    }

    override fun startActivityForResult(searchFilter: UnifiedSearchFilter<*>, intent: Intent?, requestCode: Int) {
        mStartActivityFilter = searchFilter
        mHost.startActivityForResult(intent, requestCode)
    }

    override fun checkedFilterType(): String? = mCurrentSelectedType

    override fun onDestroy() {
        super.onDestroy()
        mFilterFactory.clearCache()
        SortConditionFactory.clearCache()
    }
}