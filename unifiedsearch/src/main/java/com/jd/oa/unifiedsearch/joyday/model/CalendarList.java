package com.jd.oa.unifiedsearch.joyday.model;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.CalendarFlutterPreference;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CalendarList {
    public static List<Calendar> getMyCalendar(List<Calendar> calendars) {
        List<Calendar> list = new ArrayList<>();
        for (Calendar calendar : calendars) {
            if (calendar.type == Calendar.TYPE_PUBLIC || calendar.type == Calendar.TYPE_JOYWORK) {
                continue;
            }
            if (calendar.type == Calendar.TYPE_PERSONAL && calendar.user != null && calendar.user.isMySelf()) {
                list.add(calendar);
                continue;
            }
            if (calendar.userType == Calendar.USER_TYPE_CREATOR ||
                calendar.userType == Calendar.USER_TYPE_MANAGER ||
                calendar.userType == Calendar.USER_TYPE_TEAM_EDITOR) {
                list.add(calendar);
            }
        }
        Collections.sort(list, (o1, o2) -> {
            if (o1.type == Calendar.TYPE_PERSONAL && o2.type != Calendar.TYPE_PERSONAL) return -1;
            if (o1.type != Calendar.TYPE_PERSONAL && o2.type == Calendar.TYPE_PERSONAL) return 1;

            //check的排在前面
            if (o1.checked && !o2.checked) return -1;
            if (!o1.checked && o2.checked) return 1;

            //按时间排序
            if (o1.createTime == 0) return 1;
            return -Long.compare(o1.createTime, o2.createTime);
        });
        return list;
    }

    public static Calendar getMyPersonCalendar(List<Calendar> calendars) {
        if (calendars == null) {
            calendars = CalendarList.getCalendarListCache();
        }
        Calendar myCalendar =  null;
        for (Calendar calendar : calendars) {
            if (calendar.type == Calendar.TYPE_PERSONAL && calendar.user != null && calendar.user.isMySelf()) {
                myCalendar = calendar;
            }
        }
        return myCalendar;
    }

    public static List<Calendar> getSubscribeCalendar(List<Calendar> calendars) {
        List<Calendar> list = new ArrayList<>();
//        for (Calendar calendar : calendars) {
//            if (calendar.type == Calendar.TYPE_PERSONAL && TextUtils.equals(PreferenceManager.UserInfo.getUserName(), calendar.user.account)) {
//                continue;
//            }
//            if (calendar.userType == Calendar.USER_TYPE_TEAM_EDITOR || calendar.userType == Calendar.USER_TYPE_PERSONAL_SUBSCRIBER ||
//                    calendar.userType == Calendar.USER_TYPE_PERSONAL_SUBSCRIBER_PASSIVE || calendar.userType == Calendar.USER_TYPE_TEAM_SUBSCRIBER) {
//                list.add(calendar);
//            }
//        }
//        Collections.sort(list, (o1, o2) -> {
//            //check的排在前面
//            if (o1.checked && !o2.checked) return -1;
//            if (!o1.checked && o2.checked) return 1;
//
//            //按时间排序
//            if (o1.createTime == 0) return 1;
//            return -Long.compare(o1.createTime, o2.createTime);
//        });
        return list;
    }

    public static String checkedCalendarIdsWithCalendarList(List<Calendar> calendarList) {
        StringBuilder ids = new StringBuilder();
        for (Calendar calendar : calendarList) {
            if (calendar.checked) {
                if (ids.length() != 0) {
                    ids.append(",");
                }
                ids.append(calendar.calendarId);
            }
        }
        return ids.length() == 0 ? "" : ids.toString();
    }

    public interface GetCalendarListCallback {
        void onGetCalendarList(List<Calendar> calendars);
    }

    public static class CalendarListResult {
        public List<Calendar> calendarInfoList;
    }

    public static void getCalendarList(GetCalendarListCallback callback) {
        Map<String,Object> params = new HashMap<>();
        params.put("types", Arrays.asList(Calendar.TYPE_PERSONAL, Calendar.TYPE_TEAM, 3));
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                ApiResponse<CalendarListResult> response = ApiResponse.parse(info.result, new TypeToken<CalendarListResult>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onGetCalendarList(response.getData().calendarInfoList);
                } else {
                    callback.onGetCalendarList(new ArrayList<>());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onGetCalendarList(new ArrayList<>());
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                callback.onGetCalendarList(new ArrayList<>());
            }
        }, NetworkConstant.API_GET_CALENDAR_LIST);
    }

    public static List<Calendar> getCalendarListCache() {
        String key = "flutter.calendarList-" + PreferenceManager.UserInfo.getTimlineAppID() + "-" + PreferenceManager.UserInfo.getUserName();
        String calendarListString = CalendarFlutterPreference.getInstance().get(key, "");
        if (TextUtils.isEmpty(calendarListString)) {
            return new ArrayList<>();
        }
        return new Gson().fromJson(calendarListString, new TypeToken<List<Calendar>>() {}.getType());
    }

    public static void saveCalendarListToCache(String val) {
        String key = "flutter.calendarList-" + PreferenceManager.UserInfo.getTimlineAppID() + "-" + PreferenceManager.UserInfo.getUserName();
        CalendarFlutterPreference.getInstance().put(key, val);
    }
}
