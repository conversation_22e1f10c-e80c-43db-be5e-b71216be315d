package com.jd.oa.unifiedsearch.process;

import android.content.Context;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.app.AppSearchAdapter;
import com.jd.oa.unifiedsearch.process.data.ProcessInfo;
import com.jd.oa.utils.HighlightText;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class ProcessSearchAdapter extends BaseRecyclerAdapter<ProcessInfo, RecyclerView.ViewHolder> {
    private OnItemClickListener mOnItemClickListener;
    private boolean showFeedback = false;
    private int VIEW_TYPE_ITEM = 0;
    private int VIEW_TYPE_FEEDBACK = 1;
    private final int mHighlightColor;
    private final String mHighlightTag = "em";
    private final String mStartTag = "<" + mHighlightTag + ">";
    private final String mEndTag = "</" + mHighlightTag + ">";

    public ProcessSearchAdapter(Context context) {
        super(context);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_highlight_color);
    }

    public ProcessSearchAdapter(Context context, List<ProcessInfo> data) {
        super(context, data);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_highlight_color);
    }

    public void showFeedback() {
        showFeedback = true;
    }

    @Override
    public int getItemCount() {
        if (super.getItemCount() > 0 && showFeedback) {
            return super.getItemCount() + 1;
        }
        return super.getItemCount();
    }

    @Override
    public int getItemViewType(int index) {
        if (showFeedback) {
            if (index < getItemCount() - 1) {
                return VIEW_TYPE_ITEM;
            } else {
                return VIEW_TYPE_FEEDBACK;
            }
        } else {
            return VIEW_TYPE_ITEM;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (VIEW_TYPE_ITEM == viewType) {
            return new ViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_recycler_item_process, viewGroup, false));
        } else {
            return new FeedbackViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_pub_feedback_content, viewGroup, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof ViewHolder) {
            ViewHolder vh = (ViewHolder) holder;
            ProcessInfo processInfo = getItem(position);
            //处理title高亮，需要服务端返回<em>xxx</em>
            HighlightText.with(processInfo.title).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(vh.title);
            //副标题为空的话隐藏textview
            if (processInfo.highlightJoin != null && !processInfo.highlightJoin.isEmpty()
                    && processInfo.highlightJoin.get(0).getHighlight() != null) {
                String description = processInfo.highlightJoin.get(0).getHighlight().getDescription();
                if (TextUtils.isEmpty(description)) {
                    vh.desc.setVisibility(View.GONE);
                } else {
                    HighlightText.with(description).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(vh.desc);
                    vh.desc.setVisibility(View.VISIBLE);
                }
            } else {
                vh.desc.setVisibility(View.GONE);
            }
            ImageLoader.load(getContext(), vh.image, processInfo.refPro.icon, false, R.drawable.jdme_ic_process_default);
            String tag = "";
            String first = processInfo.refPro.getFirstLevelProcessTypeName();
            String second = processInfo.refPro.getProcessTypeName();
            if (first.isEmpty() && second.isEmpty()) {
                vh.tag.setVisibility(View.GONE);
            } else {
                if (!first.isEmpty() && second.isEmpty()) {
                    tag = first;
                } else if (first.isEmpty() && !second.isEmpty()) {
                    tag = second;
                } else {
                    tag = first + "/" + second;
                }
                vh.tag.setVisibility(View.VISIBLE);
                vh.tag.setText(tag);
            }
        } else if (holder instanceof FeedbackViewHolder) {
            FeedbackViewHolder vh = (FeedbackViewHolder) holder;
            vh.tvContent.setVisibility(View.VISIBLE);
            vh.tvContent.setText(SearchUtil.getFeedbackContent());
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        ImageView image;
        TextView title;
        TextView desc;
        TextView tag;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            image = itemView.findViewById(R.id.iv_image);
            title = itemView.findViewById(R.id.tv_title);
            desc = itemView.findViewById(R.id.tv_description);
            tag = itemView.findViewById(R.id.tv_tag);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        int position = getBindingAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) return;
                        mOnItemClickListener.onItemClick(ProcessSearchAdapter.this, v, position);
                    }
                }
            });
        }
    }

    class FeedbackViewHolder extends RecyclerView.ViewHolder {

        public TextView tvContent;

        public FeedbackViewHolder(@NonNull View itemView) {
            super(itemView);
            tvContent = itemView.findViewById(R.id.tv_conent);
            tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }
}
