package com.jd.oa.unifiedsearch.all.adaper;

import static com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper.tabNamesMapping;
import static com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper.tab_all;
import static com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper.tab_meai;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchTabModel;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;

import java.util.ArrayList;
import java.util.List;

public class SearchTabEditAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final String TAG = "SearchTabEditAdapter";

    private SearchTabModel data;
    private List<String> customData;
    private List<String> unSeLData;
    private Context mContext;
    private LayoutInflater mLayoutInflater;

    private int VIEW_TYPE_SPLIT = 0;
    private int VIEW_TYPE_CONTENT = 1;

    public SearchTabEditAdapter(Context context) {
        this.mContext = context;
        this.mLayoutInflater = LayoutInflater.from(context);
    }

    @Override
    public int getItemCount() {
        int size = 0;
        if (customData != null) {
            size += customData.size() + 1;
        }
        if (unSeLData != null && unSeLData.size() > 0) {
            size += unSeLData.size() + 1;
        }
        return size;
    }

    public void refresh(SearchTabModel models) {
        this.data = models;
        customData = new ArrayList<>();
        unSeLData = new ArrayList<>();
        //MEAI灰度控制
        if (SearchConfigHelper.getInstance().searchMEAIEnable()) {
            customData.add(tab_meai);
        }
        if (data.customOrder == null || data.customOrder.size() == 0) {
            customData.addAll(data.defaultOrder);
            unSeLData.addAll(data.unSelectedTab);
        } else {
            customData.addAll(data.customOrder);
            unSeLData.addAll(data.unSelectedTab);
        }
        notifyDataSetChanged();
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return VIEW_TYPE_SPLIT;
        } else if (position == customData.size() + 1) {
            return VIEW_TYPE_SPLIT;
        }
        return VIEW_TYPE_CONTENT;
    }

    public SearchTabModel getResult() {
        SearchTabModel model = new SearchTabModel();
        model.unSelectedTab = unSeLData;
        //返回真实的tab选项，MEAI手动加入的所以不返回
        model.customOrder = getTabsMEAIExcluded();
        return model;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(final ViewGroup parent, int viewType) {
        if (viewType == VIEW_TYPE_CONTENT) {
            View viewItem = mLayoutInflater.inflate(R.layout.unifiedsearch_v2_customer_item_content, parent, false);
            SettingEditItemViewHolder editItemViewHolder = new SettingEditItemViewHolder(viewItem);
            return editItemViewHolder;
        } else {
            View viewItem = mLayoutInflater.inflate(R.layout.unifiedsearch_v2_customer_item_header, parent, false);
            SettingHeaderViewHolder headerViewHolder = new SettingHeaderViewHolder(viewItem);
            return headerViewHolder;
        }
    }

    @SuppressLint("RecyclerView")
    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int index) {
        // - icon_padding_minuscircle  gray   bfc1c4
        // - icon_padding_minuscircle  normal EE5A55
        // + icon_padding_addcircle 4c7cff
        // icon_edit_justify
        if (viewHolder instanceof SettingHeaderViewHolder && index == 0) {
            SettingHeaderViewHolder headerViewHolder = (SettingHeaderViewHolder) viewHolder;
            headerViewHolder.tvTitle.setText(R.string.me_search_tab_setting_title);
            viewHolder.itemView.setTag("no");
        } else if (viewHolder instanceof SettingHeaderViewHolder) {
            SettingHeaderViewHolder headerViewHolder = (SettingHeaderViewHolder) viewHolder;
            headerViewHolder.tvTitle.setText(R.string.me_search_tab_setting_title_more);
            viewHolder.itemView.setTag("no");
        } else if (index < customData.size() + 1) {// 已添加
            int relPosition = index - 1;
            SettingEditItemViewHolder itemViewHolder = (SettingEditItemViewHolder) viewHolder;
            String val = customData.get(relPosition);
            itemViewHolder.tvTabName.setText(tabNamesMapping.get(val));
            itemViewHolder.iftOption.setText(R.string.icon_padding_minuscircle);
            if (val.equals(tab_all)) {
                itemViewHolder.iftOption.setTextColor(Color.parseColor("#BFC1C4"));
                itemViewHolder.iftTabDrag.setVisibility(View.GONE);
                itemViewHolder.iftOption.setOnClickListener(null);
                viewHolder.itemView.setTag("no");
            } else if (val.equals(tab_meai)) {
                itemViewHolder.iftOption.setTextColor(Color.parseColor("#BFC1C4"));
                itemViewHolder.iftTabDrag.setVisibility(View.GONE);
                itemViewHolder.iftOption.setOnClickListener(null);
                itemViewHolder.tvTabName.setVisibility(View.GONE);
                itemViewHolder.ivTabNameIcon.setVisibility(View.VISIBLE);
                itemViewHolder.ivTabNameIcon.setImageResource(R.drawable.unifiedsearch_meai_tab);
                viewHolder.itemView.setTag("no");
            } else {
                itemViewHolder.iftOption.setTextColor(Color.parseColor("#EE5A55"));
                itemViewHolder.iftTabDrag.setVisibility(View.VISIBLE);
                itemViewHolder.iftOption.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        removeItem(index, customData.get(relPosition));
                    }
                });
                viewHolder.itemView.setTag("yes");
            }
        } else { // 未添加
            int relPosition = index - customData.size() - 2;
            SettingEditItemViewHolder itemViewHolder = (SettingEditItemViewHolder) viewHolder;
            String val = unSeLData.get(relPosition);
            itemViewHolder.tvTabName.setText(tabNamesMapping.get(val));
            viewHolder.itemView.setTag("no");
            itemViewHolder.iftOption.setText(R.string.icon_padding_addcircle);
            itemViewHolder.iftOption.setTextColor(Color.parseColor("#4c7cff"));
            itemViewHolder.iftOption.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    addItem(index, unSeLData.get(relPosition));
                }
            });
            itemViewHolder.iftTabDrag.setVisibility(View.GONE);
        }
    }

    private void removeItem(int index, String s) {
        SearchLogUitl.LogD(TAG, "tabRemove from_index = " + index + " to_index = " + (getItemCount() - 1));
        customData.remove(s);
        unSeLData.add(s);
        notifyDataSetChanged();
    }

    private void addItem(int index, String s) {
        SearchLogUitl.LogD(TAG, "tabAdd from_index = " + index);
        customData.add(s);
        unSeLData.remove(s);
        notifyDataSetChanged();
    }

    public void onItemMove(int from, int to) {
        SearchLogUitl.LogD(TAG, "onItemMove from_index = " + from + " to_index = " + to);
        if (to > 1 && to < customData.size() + 1) {
            String item = customData.get(from - 1);
            customData.remove(from - 1);
            customData.add(to - 1, item);
            notifyItemMoved(from, to);
        }
    }

    public SearchTabModel getData() {
        return data;
    }

    private ArrayList<String> getTabsMEAIExcluded() {
        ArrayList<String> result = new ArrayList<>();
        for (String tab : customData) {
            if (!tab.equals(tab_meai)) {
                result.add(tab);
            }
        }
        return result;
    }
}
