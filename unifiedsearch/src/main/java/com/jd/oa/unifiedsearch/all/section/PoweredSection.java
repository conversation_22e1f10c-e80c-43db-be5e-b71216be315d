package com.jd.oa.unifiedsearch.all.section;

import android.text.method.LinkMovementMethod;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/24
 * Author: qudongshi
 * Description:
 */
public class PoweredSection extends BaseSection {

    public PoweredSection(String keyword) {
        super(SectionParameters.builder().itemResourceId(R.layout.unifiedsearch_section_powered_content).build(), keyword);
        super.floorType = SearchFloorType.POWERED;

    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new ContentViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ContentViewHolder holder = (ContentViewHolder) viewHolder;
        holder.tvContent.setText(R.string.me_search_power_by);
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {

    }

    private class ContentViewHolder extends RecyclerView.ViewHolder {
        public TextView tvContent;

        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
            tvContent = itemView.findViewById(R.id.tv_conent);
            tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }
}
