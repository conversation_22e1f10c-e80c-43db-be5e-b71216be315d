package com.jd.oa.unifiedsearch.all.section;

import android.content.Context;

import com.jd.oa.unifiedsearch.all.callback.ISectionEventCallback;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;

import java.util.List;

/*
 * Time: 2023/10/16
 * Author: qudongshi
 * Description:
 */
public class SearchSectionFactory {
    public static BaseSection getSection(Context context, SearchFloorType floorType, List<?> data, String keyword, SearchFloorHelper helper, String sessionId, String searchId) {
        switch (floorType) {
            case MESSAGE:
            case CONTACT:
            case GROUP:
            case NOTICE:
            case ROBOT:
            case WAITER:
            case APP:
            case TASK:
            case PROCESS:
            case APPROVAL:
            case JOY_SPACE:
            case ORG:
                return new NormalSection(context, floorType, data, keyword, helper, sessionId, searchId);
            case ENCYCLO:
                return new EncycloSection(context, data, keyword, sessionId, searchId);
            case AI:
                return new AISection(context, data, keyword, helper, sessionId, searchId);
            default:
                return null;
        }
    }

    public static HistorySection getSearchHistorySection(Context context, List<String> data, ISectionEventCallback callback) {
        return new HistorySection(context, data, callback);
    }

    public static RecommendSection getRecommendSection(Context context, List<String> data, ISectionEventCallback callback) {
        return new RecommendSection(context, data, callback);
    }

    public static BaseSection getFeedBackSection(String keyword) {
        return new FeedbackSection(keyword);
    }

    public static BaseSection getEmptySection(String keyword) {
        return new EmptySection(keyword);
    }


    public static BaseSection getPoweredSection(String keyword) {
        return new PoweredSection(keyword);
    }
}
