package com.jd.oa.unifiedsearch.all.section;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchEncycloModel;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.all.util.SearchHistoryUtil;
import com.jd.oa.utils.JsonUtils;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/17
 * Author: qudongshi
 * Description:
 */
public class EncycloSection extends BaseSection {

    private List<?> data;
    private Context context;
    private String keyword;
    private String sessionId;
    private String searchId;

    public EncycloSection(Context context, List<?> data, String keyword, String sessionId, String searchId) {
        super(SectionParameters.builder().itemResourceId(R.layout.unifiedsearch_section_encyclo_content).build(), keyword);
        this.floorType = SearchFloorType.ENCYCLO;
        this.data = data;
        this.context = context;
        this.keyword = keyword;
        this.sessionId = sessionId;
        this.searchId = searchId;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        EncycloContentViewHolder viewHolder = new EncycloContentViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        EncycloContentViewHolder contentViewHolder = (EncycloContentViewHolder) viewHolder;
        if (data.get(0) != null) {
            String jsonData = JsonUtils.getGson().toJson(data.get(0));
            SearchEncycloModel tmp = JsonUtils.getGson().fromJson(jsonData, SearchEncycloModel.class);
            if (tmp == null) {
                return;
            }
            SearchEncycloModel.RefPro model = tmp.refPro;
            if (model == null) {
                return;
            }
            if (!TextUtils.isEmpty(model.title)) {
                contentViewHolder.tvContentTitle.setText(model.title);
            }
            if (model.ext != null && model.ext.aliases != null && model.ext.aliases.size() > 0) {
                SearchEncycloModel.RefPro.Ext ext = model.ext;
                StringBuffer val = new StringBuffer();
                for (int index = 0; index < ext.aliases.size(); index++) {
                    if (index > 0) {
                        val.append("/");
                    }
                    val.append(ext.aliases.get(index));
                }
                contentViewHolder.tvContentSubTitle.setText(val.toString());
            } else {
                contentViewHolder.tvContentSubTitle.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(model.content)) {
                contentViewHolder.tvContentVal.setText(model.content);
            }
            if (model.anchor != null && !TextUtils.isEmpty(model.anchor.deepLink)) {
                contentViewHolder.showBottom();
                contentViewHolder.llBottom.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        SearchHistoryUtil.addSearchHistory(keyword);
                        AppService appService = AppJoint.service(AppService.class);
                        if (appService.isSearchActivity(AppBase.getTopActivity())) {
                            Router.build(model.anchor.deepLink).go(context);
                        }
                    }
                });
            } else {
                contentViewHolder.hideBottom();
            }
        }
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {

    }

    private class EncycloContentViewHolder extends RecyclerView.ViewHolder {
        public LinearLayout llBottom;
        public LinearLayout llContent;
        public TextView tvContentTitle;
        public TextView tvContentSubTitle;
        public TextView tvContentVal;
        public View vSplit;
        public TextView tvMore;

        public EncycloContentViewHolder(@NonNull View itemView) {
            super(itemView);
            llBottom = itemView.findViewById(R.id.section_bottom);
            llContent = itemView.findViewById(R.id.section_content);
            vSplit = itemView.findViewById(R.id.section_bottom_split);
            tvMore = itemView.findViewById(R.id.tv_more);
            tvContentSubTitle = itemView.findViewById(R.id.section_content_sub_title);
            tvContentTitle = itemView.findViewById(R.id.section_content_title);
            tvContentVal = itemView.findViewById(R.id.section_content_val);
        }

        public void showBottom() {
            vSplit.setVisibility(View.VISIBLE);
            llBottom.setVisibility(View.VISIBLE);
        }

        public void hideBottom() {
            vSplit.setVisibility(View.GONE);
            llBottom.setVisibility(View.GONE);
        }
    }
}
