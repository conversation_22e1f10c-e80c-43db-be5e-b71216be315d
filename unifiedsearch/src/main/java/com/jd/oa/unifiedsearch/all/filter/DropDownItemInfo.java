package com.jd.oa.unifiedsearch.all.filter;

import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;

import java.util.HashMap;
import java.util.Map;

public class DropDownItemInfo implements Mappable, FilterNode {
    public String reqKey;
    public Object reqVal;
    public SearchFilterConfig.Option selectedOption;
    public SearchFilterConfig.FilterItem filterItem;

    @Override
    public Map<String, Object> toMap() {
        if (reqKey == null || reqVal == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        result.put(reqKey, reqVal);
        return result;
    }

    @Override
    public SearchFilterConfig.FilterItem getFilterItem() {
        return filterItem;
    }

    @Override
    public SearchFilterConfig.Option getOption() {
        return selectedOption;
    }
}
