package com.jd.oa.unifiedsearch.all;

import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.im.listener.Callback5;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.adaper.SearchTabEditAdapter;
import com.jd.oa.unifiedsearch.all.data.SearchTabModel;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;

/*
 * Time: 2023/10/25
 * Author: qudongshi
 * Description:
 */
@Navigation(hidden = false)
public class SearchTabCustomActivity extends BaseActivity {

    ItemTouchHelper itemTouchHelper;
    RecyclerView mRv;
    SearchTabEditAdapter mAdapter;
    SearchTabModel searchTabModel;

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.unifiedsearch_activity_setting);
        initView();
    }

    private void initView() {
        initActionbar();
        mRv = findViewById(R.id.rc_edit);

        mAdapter = new SearchTabEditAdapter(this);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        mRv.setLayoutManager(layoutManager);
        mRv.setAdapter(mAdapter);
        mRv.setItemAnimator(new DefaultItemAnimator());
        SearchConfigHelper.getInstance().getSearchTabCustomOrder(new Callback5<SearchTabModel>() {
            @Override
            public void onResult(SearchTabModel va1) {
                searchTabModel = va1;
                mAdapter.refresh(va1);
            }
        });

        itemTouchHelper = new ItemTouchHelper(new TouchHelperCallback(new TouchHelperAdapter()));
        itemTouchHelper.attachToRecyclerView(mRv);
    }

    private void initActionbar() {
        ActionBarHelper.init(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        actionBar.setDisplayOptions(ActionBar.DISPLAY_SHOW_CUSTOM);
        actionBar.setCustomView(R.layout.unifiedsearch_activity_setting_actionbar);
        actionBar.setDisplayHomeAsUpEnabled(false);
        ImageView ivBack = actionBar.getCustomView().findViewById(R.id.bar_btn);
        ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        TextView tvSave = actionBar.getCustomView().findViewById(R.id.bar_right_btn);
        tvSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 保存
                SearchTabModel model = mAdapter.getResult();
                if (model != null) {
                    // 数据校验
                    if (model.customOrder.size() + model.unSelectedTab.size() != searchTabModel.defaultOrder.size()) {
                        SearchLogUitl.LogE(TAG, "save data size exception", null);
                        ToastUtils.showToast(R.string.me_save_failed);
                        return;
                    }
                    // 保存数据
                    boolean flag = SearchConfigHelper.getInstance().saveSearchTabConfig(model);
                    if (flag) {
                        setResult(200);
                        finish();
                    } else {
                        ToastUtils.showToast(R.string.me_save_failed);
                    }
                }
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }


    private static class TouchHelperCallback extends ItemTouchHelper.Callback {
        ItemTouchHelperAdapter mAdapter;

        TouchHelperCallback(ItemTouchHelperAdapter adapter) {
            mAdapter = adapter;
        }

        @Override
        public boolean isLongPressDragEnabled() {
            return true;
        }

        @Override
        public int getMovementFlags(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
            String currentTag = (String) viewHolder.itemView.getTag();
            if ("no".equals(currentTag)) {
                return makeMovementFlags(0, 0);
            }
            int dragFlag = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
            int swipeFlag = 0;
            return makeMovementFlags(dragFlag, swipeFlag);
        }

        @Override
        public boolean canDropOver(RecyclerView recyclerView, RecyclerView.ViewHolder current, RecyclerView.ViewHolder target) {
            String targetTag = (String) target.itemView.getTag();
            if ("no".equals(targetTag)) {
                return false;
            } else {
                return true;
            }
        }

        @Override
        public boolean onMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, RecyclerView.ViewHolder target) {
            mAdapter.onItemMove(viewHolder.getAdapterPosition(), target.getAdapterPosition());
            return true;
        }

        @Override
        public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {

        }

        @Override
        public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
            super.clearView(recyclerView, viewHolder);
            try {
                recyclerView.getAdapter().notifyDataSetChanged();
            } catch (Exception e) {
            }
        }
    }

    private class TouchHelperAdapter implements ItemTouchHelperAdapter {

        @Override
        public void onItemMove(int fromPosition, int toPosition) {
            SearchLogUitl.LogD(TAG, "onItemMove start fromPosition = " + fromPosition + " toPosition = " + toPosition);
            if (fromPosition == toPosition) {
                return;
            }
            mAdapter.onItemMove(fromPosition, toPosition);
        }

        @Override
        public void onItemDismiss(int position) {
//            mRecyclerViewAdapter.notifyItemRemoved(position);
        }

    }
}
