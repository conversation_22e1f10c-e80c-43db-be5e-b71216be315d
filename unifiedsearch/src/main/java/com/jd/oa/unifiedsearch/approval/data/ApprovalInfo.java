package com.jd.oa.unifiedsearch.approval.data;

import android.content.Context;

import androidx.core.util.TimeUtils;

import com.jd.me.datetime.picker.util.DatetimeUtil;
import com.jd.oa.AppBase;
import com.jd.oa.unifiedsearch.joyspace.utils.JoyspaceDateParser;
import com.jd.oa.utils.LocaleUtils;

import java.util.List;

public class ApprovalInfo {
    public RefPro refPro;

    public static class RefPro {
        public String auditTitle; //标题
        public String auditType; //类型
        public String auditIcon; //图标
        public String auditStatus; //审批状态
        public String auditApplyRealName; //申请人
        public long auditStartTime; //申请时间
        public String auditMobileDeeplink; //审批链接
        public String auditPcDeeplink; //pc审批链接
        public List<Extension> auditExtField; //动态字段
        public String followCode; //流水单号
    }

    public static class Extension {
        public String fieldKey;
        public String fieldTitle;
        public String fieldValue;

        public String getContent() {
            if (fieldTitle == null || fieldValue == null) {
                return "";
            }
            return fieldTitle + "  " + fieldValue;
        }
    }

    public String getExtContent() {
        if (refPro!= null && refPro.auditExtField!= null &&!refPro.auditExtField.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < refPro.auditExtField.size(); i++) {
                if (refPro.auditExtField.get(i).getContent().isEmpty()) {continue;}
                sb.append(refPro.auditExtField.get(i).getContent());
                sb.append("    ");
            }
            return sb.toString().trim();
        }
        return "";
    }

    public String getAuditApplicantName() {
        if (refPro!= null && refPro.auditApplyRealName!= null && !refPro.auditApplyRealName.isEmpty()) {
            String applicant = refPro.auditApplyRealName;
            String prefix = LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")? "申请人" : "Applicant";
            return prefix + "  " + applicant;
        }
        return "";
    }

    public String getAuditApplicationTime(Context context) {
        if (refPro!= null && refPro.auditStartTime!= 0L) {
            JoyspaceDateParser joyspaceDateParser = new JoyspaceDateParser();
            String time = joyspaceDateParser.parseDateTime(context, refPro.auditStartTime);
            String prefix = LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")? "申请时间" : "Application Time";
            return prefix + "  " + time;
        }
        return "";
    }
}
