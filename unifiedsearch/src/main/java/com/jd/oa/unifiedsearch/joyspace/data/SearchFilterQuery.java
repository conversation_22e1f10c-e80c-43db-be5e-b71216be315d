package com.jd.oa.unifiedsearch.joyspace.data;


import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.filter.CreateByOthersFilter;
import com.jd.oa.unifiedsearch.joyspace.filter.DocumentTypeFilter;
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter;
import com.jd.oa.unifiedsearch.joyspace.filter.OpenedTimeFilter;
import com.jd.oa.unifiedsearch.joyspace.filter.ReceiverFilter;
import com.jd.oa.unifiedsearch.joyspace.filter.SelectContactFilter;
import com.jd.oa.unifiedsearch.joyspace.filter.SenderFilter;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * https://joyspace.jd.com/page/dzpmx6g3mgf3fhMrUf45
 */
@Keep
public class SearchFilterQuery {
    private static final int RECEIVED = 1;
    private static final int SENT = 2;
    private static final int RECENT = 3;
    private static final int CREATE_BY_MYSELF = 4;
    private static final int UNDER_CURRENT_DIRECTORY = 5;
    private static final int PUBLIC = 6;

    @Nullable
    @JoySpaceFilter.FilterType
    private String type;

    /**
     * 文档分类:1我收到的，2 我发出的，3 最近打开
     */
    private List<Integer> classification;

    /**
     * 文档创建人
     */
    private List<String> creators;

    private List<Contact> creatorsFromDD;

    /**
     * 搜索时间周期，1:最近一天；2最近一周；3最近一个月；4:最近三个月
     */
    private int timeRange;

    /**
     * 文件夹：0
     * 文档：1
     * 表单：2
     * 表格：3
     * 文件：4
     * 幻灯片（PPT）：5
     * Word：6
     */
    private List<Integer> pageType;

    /**
     * 发送人列表
     */
    private List<String> sender;

    private List<Contact> sendersFromDD;

    /**
     * 接收人列表
     */
    private List<String> receiver;

    private List<Contact> receiversFromDD;

    /**
     * 排序字段，支持类型：
     * created_at:创建时间，
     * updated_at:更新时间，
     * shared_at:接收时间/发送时间,
     * opened_at:打开时间
     * 不传该参数默认为相关度排序
     */
    private String orderBy;

    /**
     * 排序类型：
     * 0：升序
     * 1：降序
     */
    private int sortOrder;


    /**
     * 搜索类型：
     * page(文档), exploer:(文件夹内)
     */
    private String t;

    private String teamId;

    private String folderId;

    private SelectedSortCondition mSortCondition;

    /**
     * 返回的摘要长度，默认100, 不传为150
     */
    private int digestLength = 150;

    @Nullable
    @JoySpaceFilter.FilterType
    public String getType() {
        return type;
    }

    public void setType(@Nullable String type) {
        this.type = type;
    }

    public List<Integer> getClassification() {
        return classification;
    }

    public List<String> getCreators() {
        return creators;
    }

    public int getTimeRange() {
        return timeRange;
    }

    public List<Integer> getPageType() {
        return pageType;
    }

    public List<String> getSender() {
        return sender;
    }

    public List<String> getReceiver() {
        return receiver;
    }

    public List<Contact> getCreatorsFromDD() {
        return creatorsFromDD;
    }

    public List<Contact> getSendersFromDD() {
        return sendersFromDD;
    }

    public List<Contact> getReceiversFromDD() {
        return receiversFromDD;
    }


    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public int getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getT() {
        return t;
    }

    public void setT(String t) {
        this.t = t;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getFolderId() {
        return folderId;
    }

    public void setFolderId(String folderId) {
        this.folderId = folderId;
    }

    public SelectedSortCondition getSortCondition() {
        return mSortCondition;
    }

    public void setSortCondition(SelectedSortCondition sortCondition) {
        mSortCondition = sortCondition;
    }

    public int getDigestLength() {
        return digestLength;
    }

    public void setDigestLength(int digestLength) {
        this.digestLength = digestLength;
    }

    @Keep
    public static class Contact implements Serializable {
        private String app;
        private String pin;

        public Contact(String app, String pin) {
            this.app = app;
            this.pin = pin;
        }

        public String getApp() {
            return app;
        }

        public void setApp(String app) {
            this.app = app;
        }

        public String getPin() {
            return pin;
        }

        public void setPin(String pin) {
            this.pin = pin;
        }
    }

    public static class Factory {

        public static SearchFilterQuery createByType(@JoySpaceFilter.FilterType String type, Map<String, JoySpaceFilter<?>> subTypes, SelectedSortCondition sortCondition) {

            List<Integer> classification = null;
            if (JoySpaceFilter.TYPE_RELATED.equals(type)) {
                classification = Collections.emptyList();   //默认与我相关
            } else if (JoySpaceFilter.TYPE_RECENT.equals(type)) {
                classification = Collections.singletonList(RECENT);
            } else if (JoySpaceFilter.TYPE_RECEIVED.equals(type)) {
                classification = Collections.singletonList(RECEIVED);
            } else if (JoySpaceFilter.TYPE_SENT.equals(type)) {
                classification = Collections.singletonList(SENT);
            } else if (JoySpaceFilter.TYPE_CREATED.equals(type)) {
                classification = Collections.singletonList(CREATE_BY_MYSELF);
            } else if (JoySpaceFilter.TYPE_PUBLIC.equals(type)) {
                classification = Collections.singletonList(PUBLIC);
            }
            return create(type, classification, subTypes.values(), sortCondition, null, null);
        }

        public static SearchFilterQuery createByFolder(String teamId, String folderId, Collection<JoySpaceFilter<?>> subTypes, SelectedSortCondition sortCondition) {
            return create(null, Collections.singletonList(UNDER_CURRENT_DIRECTORY), subTypes, sortCondition, teamId, folderId);
        }

        private static SearchFilterQuery create(@JoySpaceFilter.FilterType String type, List<Integer> classification, Collection<JoySpaceFilter<?>> subTypes, SelectedSortCondition sortCondition, String teamId, String folderId) {
            SearchFilterQuery query = new SearchFilterQuery();

            query.setType(type);

            query.setSortCondition(sortCondition);

            query.classification = classification;

            for (JoySpaceFilter<?> filter : subTypes) {
                if (filter instanceof CreateByOthersFilter) {
                    query.creatorsFromDD = parseContacts((CreateByOthersFilter)filter);
                } else if (filter instanceof SenderFilter) {
                    query.sendersFromDD = parseContacts((SenderFilter)filter);
                } else if (filter instanceof ReceiverFilter) {
                    query.receiversFromDD = parseContacts((ReceiverFilter)filter);
                } else if (filter instanceof DocumentTypeFilter) {
                    parsePageType(query, (DocumentTypeFilter) filter);
                } else if (filter instanceof OpenedTimeFilter) {
                    parseTimeRange(query, (OpenedTimeFilter)filter);
                }
            }

            parseSortBy(query, sortCondition);

            if (!TextUtils.isEmpty(teamId) && !TextUtils.isEmpty(folderId)) {
                query.t = "explore";
                query.setTeamId(teamId);
                query.setFolderId(folderId);
            }

            return query;
        }

        private static void parseTimeRange(SearchFilterQuery filters, OpenedTimeFilter openedTimeFilter) {
            if (openedTimeFilter == null || openedTimeFilter.getValue() == null) return;

            int timeRange = 0;
            FilterOption option = openedTimeFilter.getValue();
            switch (option.getValue()) {
                case OpenedTimeFilter.OPENED_TIME_ALL:
                    break;
                case OpenedTimeFilter.OPENED_TIME_ONE_DAY:
                    timeRange = 1;
                    break;
                case OpenedTimeFilter.OPENED_TIME_ONE_WEEK:
                    timeRange = 2;
                    break;
                case OpenedTimeFilter.OPENED_TIME_ONE_MONTH:
                    timeRange = 3;
                    break;
                case OpenedTimeFilter.OPENED_TIME_THREE_MONTH:
                    timeRange = 4;
                    break;
            }
            filters.timeRange = timeRange;
        }

        /**
         * @see #pageType
         */
        private static void parsePageType(SearchFilterQuery query, DocumentTypeFilter documentTypeFilter) {
            if (documentTypeFilter == null || documentTypeFilter.getValue() == null) return;
            List<Integer> pageTypes = new ArrayList<>();
            FilterOption option = documentTypeFilter.getValue();
            switch (option.getValue()) {
                case DocumentTypeFilter.DOCUMENT_TYPE_ALL:
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_DOC:
                    pageTypes.add(1);
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_FORM:
                    pageTypes.add(2);
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_TABLE:
                    pageTypes.add(3);
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_FILE:
                    pageTypes.add(4);
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_FOLDER:
                    pageTypes.add(0);
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_PPT:
                    pageTypes.add(5);
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_TRADITIONAL_DOC:
                    pageTypes.add(6);
                    break;
                case DocumentTypeFilter.DOCUMENT_MEETING_DOC:
                    pageTypes.add(7);
                    break;
                case DocumentTypeFilter.DOCUMENT_MIND_MAP:
                    pageTypes.add(8);
                    break;
                case DocumentTypeFilter.DOCUMENT_TYPE_MD_TABLE:
                    pageTypes.add(10);
                    break;
            }
            query.pageType = pageTypes;
        }

        private static List<Contact> parseContacts(SelectContactFilter contactFilter) {
            List<Contact> list = new ArrayList<>();
            if (contactFilter == null || contactFilter.getValue() == null) return list;
            for (MemberEntityJd memberEntityJd : contactFilter.getValue()) {
                Contact contact = new Contact(memberEntityJd.getApp(), memberEntityJd.getId());
                list.add(contact);
            }
            return list;
        }

        private static void parseSortBy(SearchFilterQuery query, SelectedSortCondition sortCondition) {
            if (sortCondition == null) return;
            String condition = sortCondition.getCondition();
            if (SortCondition.TYPE_RELATED.equals(condition)) {
                query.setOrderBy(null); //不传该参数默认为相关度排序
            } else if (SortCondition.TYPE_CREATE_TIME.equals(condition)) {
                query.setOrderBy("created_at");
            } else if (SortCondition.TYPE_UPDATE_TIME.equals(condition)) {
                query.setOrderBy("updated_at");
            } else if (SortCondition.TYPE_SENT_TIME.equals(condition) || SortCondition.TYPE_RECEIVED_TIME.equals(condition)) {
                query.setOrderBy("shared_at");
            } else if (SortCondition.TYPE_OPENED_TIME.equals(condition)) {
                query.setOrderBy("opened_at");
            }

            String order = sortCondition.getOrder();
            if (SortCondition.isAscend(order)) {
                query.setSortOrder(0);
            } else {
                query.setSortOrder(1);
            }
        }
    }
}
