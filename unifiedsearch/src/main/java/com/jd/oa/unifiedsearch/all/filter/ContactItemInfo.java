package com.jd.oa.unifiedsearch.all.filter;

import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ContactItemInfo implements Mappable, FilterNode {
    List<MemberEntityJd> contacts;
    SearchFilterConfig.FilterItem filterItem;

    public ContactItemInfo(List<MemberEntityJd> contacts, SearchFilterConfig.FilterItem filterItem) {
        this.contacts = contacts;
        this.filterItem = filterItem;
    }

    @Override
    public Map<String, Object> toMap() {
        if (contacts == null || contacts.isEmpty()) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> array = new ArrayList<>();
        for (MemberEntityJd contact : contacts) {
            Map<String, Object> contactMap = new HashMap<>();
            contactMap.put("username", contact.mId);
            contactMap.put("app", contact.mApp);
            array.add(contactMap);
        }
        result.put("applyUserList", array);
        return result;
    }

    @Override
    public SearchFilterConfig.FilterItem getFilterItem() {
        return filterItem;
    }

    @Override
    public SearchFilterConfig.Option getOption() {
        return null;
    }
}
