package com.jd.oa.unifiedsearch.joyspace.sortcondition

import android.content.Context
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.joyspace.FilterOption

class SortConditionTextBuilder(
    @SortCondition.SortType
    private val type: String? = null,
    private val condition: FilterOption
) {

    fun buildText(context: Context): String {
        val sortOder = condition.value
        return when(sortOder) {
            SortCondition.CONDITION_RELATED_FIRST -> {
                getTypeText(context)
            }
            SortCondition.CONDITION_LATEST_FIRST -> {
                context.getString(R.string.unifiedsearch_joyspace_sort_latest_first, getTypeText(context))
            }
            SortCondition.CONDITION_LATEST_LAST -> {
                context.getString(R.string.unifiedsearch_joyspace_sort_latest_last, getTypeText(context))
            }
            else -> ""
        }
    }

    private fun getTypeText(context: Context): String = when(type) {
        SortCondition.TYPE_RELATED -> context.getString(R.string.unifiedsearch_joyspace_sort_related)
        SortCondition.TYPE_CREATE_TIME -> context.getString(R.string.unifiedsearch_joyspace_sort_create)
        SortCondition.TYPE_UPDATE_TIME -> context.getString(R.string.unifiedsearch_joyspace_sort_update)
        SortCondition.TYPE_RECEIVED_TIME -> context.getString(R.string.unifiedsearch_joyspace_sort_receive)
        SortCondition.TYPE_OPENED_TIME -> context.getString(R.string.unifiedsearch_joyspace_sort_open)
        SortCondition.TYPE_SENT_TIME -> context.getString(R.string.unifiedsearch_joyspace_sort_sent)
        else -> throw IllegalArgumentException(type)
    }
}