package com.jd.oa.unifiedsearch.joyspace.view

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter

class FiltersContainerLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : FrameLayout(context, attrs, defStyleAttr, defStyleRes) {

    companion object {
        const val TAG = "FiltersLayout"
    }

    private val mSubTypeScrollView: HorizontalScrollView by lazy { findViewById(R.id.layout_subtype_scrollview) }
    private val mSubTypeFilterContainer: LinearLayout by lazy { findViewById(R.id.layout_subtype_container) }
    private val mTvReset: TextView by lazy { findViewById(R.id.tv_reset) }
    private val mIvResetEdge: ImageView by lazy { findViewById(R.id.iv_reset_edge) }
    private var mFilters: List<JoySpaceFilter<*>> = emptyList()

    var mOnResetClickListener: View.OnClickListener? = null
    var mOnFilterChangedListener: ((JoySpaceFilter<*>, Any?) -> Unit)? = null
    var onResetVisibilityChange: ((Int) -> Unit)? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.unifiedsearch_filters_container_layout, this, true)

        mTvReset.setOnClickListener { view ->

            mFilters.forEach { it.reset() }
            changeResetState()

            mOnResetClickListener?.onClick(view)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mSubTypeScrollView.setOnScrollChangeListener { _, _, _, _, _ ->
                changeResetState(false)
            }
        }
    }


    fun setFilters(filters: List<JoySpaceFilter<*>>) {
        mFilters = filters
        mSubTypeFilterContainer.removeAllViews()

        mFilters.forEach { filter ->

            filter.setOnChangedListener {
                changeResetState()
                mOnFilterChangedListener?.invoke(filter, it)
            }

            val subTypeView = filter.getView(context, mSubTypeFilterContainer)
            subTypeView.setOnClickListener {
                filter.onFilterClick(context as AppCompatActivity?, subTypeView)
            }

            if (subTypeView.parent != null) {
                (subTypeView.parent as ViewGroup).removeView(subTypeView)
            }
            mSubTypeFilterContainer.addView(subTypeView)
        }

        changeResetState()
    }

    fun changeResetState(refreshResetVisibility: Boolean = true) {
        if (refreshResetVisibility) {
            val hasNoReset = mFilters.any { it.value != null}
            mTvReset.visibility = if (hasNoReset) View.VISIBLE else View.GONE
            onResetVisibilityChange?.invoke(mTvReset.visibility)
        }

        mSubTypeFilterContainer.postDelayed({
            mSubTypeFilterContainer.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
            val maxScrollX = mSubTypeFilterContainer.measuredWidth - mSubTypeScrollView.measuredWidth
            if (maxScrollX < 0 || mSubTypeScrollView.scrollX == maxScrollX) {
                mIvResetEdge.visibility = View.INVISIBLE
            } else {
                mIvResetEdge.visibility = View.VISIBLE
            }
        }, 50)
    }
}