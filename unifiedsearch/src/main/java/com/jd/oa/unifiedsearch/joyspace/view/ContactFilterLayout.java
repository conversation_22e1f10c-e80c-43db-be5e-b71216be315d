package com.jd.oa.unifiedsearch.joyspace.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class ContactFilterLayout extends FrameLayout {
    private static final int MAX_NUM = 3;

    private TextView mTvText;
    private ViewStub mStubAvatar;
    private ImageView mIvAvatar;
    private TextView mTvNum;

    public ContactFilterLayout(Context context) {
        this(context, null);
    }

    public ContactFilterLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ContactFilterLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ContactFilterLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_filter_contact, this, true);

        mTvText = findViewById(R.id.tv_text);
        mStubAvatar = findViewById(R.id.stub_avatar);
        mTvNum = findViewById(R.id.tv_num);
    }


    public void setText(String text) {
        mTvText.setText(text);
    }

    public void setContacts(List<MemberEntityJd> contacts) {
        if (CollectionUtil.isEmptyOrNull(contacts)) {
            if (mIvAvatar != null) {
                mIvAvatar.setVisibility(View.GONE);
            }
            mTvNum.setVisibility(View.GONE);
            return;
        }

        int totalNum = contacts.size();
        boolean hasMore = contacts.size() > MAX_NUM;
        if (hasMore) {
            contacts = contacts.subList(0, MAX_NUM);
        }

        if (totalNum == 1) {
            if (mIvAvatar == null) {
                mIvAvatar = (ImageView) mStubAvatar.inflate();
            }
            mTvNum.setVisibility(View.GONE);
            mIvAvatar.setVisibility(View.VISIBLE);
            ImageLoader.load(getContext(), mIvAvatar, contacts.get(0).getAvatar(), R.drawable.jdme_pedia_default_avatar);
        } else {
            mTvNum.setVisibility(View.VISIBLE);
            if (mIvAvatar != null) {
                mIvAvatar.setVisibility(View.GONE);
            }

            mTvNum.setText(String.valueOf(totalNum));
        }
    }
}