package com.jd.oa.unifiedsearch.joyday;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.storage.KVMethod;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;
import com.jd.oa.unifiedsearch.joyday.model.Subcategory;
import com.jd.oa.unifiedsearch.joyday.utils.SubcategoryListParser;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class JoydayDuccPreference extends AbsKvEntities {
    private static final String SUBCATEGORY = "flutter.common.scheduleSubcategory";

    private static final KvEntity<String> subcategoryEntity = new KvEntity<>(SUBCATEGORY, "");

    @NonNull
    @Override
    public String getPrefrenceName() {
        return "FlutterDuccConfig";
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public KVMethod getKVMethod() {
        return KVMethod.SP;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    public Map<String,List<Subcategory>> getSubcategoryList() {
        String string = get(subcategoryEntity);
        if (TextUtils.isEmpty(string)) return Collections.emptyMap();
        return new SubcategoryListParser().parseConfig(string);
    }
}
