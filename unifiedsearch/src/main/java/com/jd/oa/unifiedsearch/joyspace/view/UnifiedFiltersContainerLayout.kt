package com.jd.oa.unifiedsearch.joyspace.view

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.all.filter.ContactItemInfo
import com.jd.oa.unifiedsearch.all.filter.DropDownItemInfo
import com.jd.oa.unifiedsearch.all.filter.TimeItemInfo
import com.jd.oa.unifiedsearch.joyspace.filter.UnifiedSearchFilter

class UnifiedFiltersContainerLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : FrameLayout(context, attrs, defStyleAttr, defStyleRes) {
    companion object {
        const val TAG = "UnifiedFiltersLayout"
    }

    private val mSubTypeScrollView: HorizontalScrollView by lazy { findViewById(R.id.layout_subtype_scrollview) }
    private val mSubTypeFilterContainer: LinearLayout by lazy { findViewById(R.id.layout_subtype_container) }
    private val mTvReset: TextView by lazy { findViewById(R.id.tv_reset) }
    private val mIvResetEdge: ImageView by lazy { findViewById(R.id.iv_reset_edge) }
    private var mFilters: List<UnifiedSearchFilter<*>> = emptyList()

    var mOnResetClickListener: View.OnClickListener? = null
    var mOnFilterChangedListener: ((UnifiedSearchFilter<*>, Any?) -> Unit)? = null
    var onResetVisibilityChange: ((Int) -> Unit)? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.unifiedsearch_filters_container_layout, this, true)

        mTvReset.setOnClickListener { view ->

            mFilters.forEach { it.reset() }
            changeResetState()

            mOnResetClickListener?.onClick(view)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mSubTypeScrollView.setOnScrollChangeListener { _, _, _, _, _ ->
                changeResetState(false)
            }
        }
    }


    fun setFilters(filters: List<UnifiedSearchFilter<*>>) {
        mFilters = filters
        mSubTypeFilterContainer.removeAllViews()

        mFilters.forEach { filter ->

            filter.setOnChangedListener {
                changeResetState()
                mOnFilterChangedListener?.invoke(filter, it)
            }

            val subTypeView = filter.getView(context, mSubTypeFilterContainer)
            subTypeView.setOnClickListener {
                filter.onFilterClick(context as AppCompatActivity?, subTypeView)
            }

            if (subTypeView.parent != null) {
                (subTypeView.parent as ViewGroup).removeView(subTypeView)
            }
            mSubTypeFilterContainer.addView(subTypeView)
        }

        changeResetState()
    }

    fun changeResetState(refreshResetVisibility: Boolean = true) {
        if (refreshResetVisibility) {
            //重置按钮可见度根据有没有选择筛选项决定
            val hasNoReset = mFilters.any { filter ->
                when (filter.value) {
                    is DropDownItemInfo -> (filter.value as DropDownItemInfo).reqVal != null //单选判断有无reqVal
                    is TimeItemInfo -> (filter.value as TimeItemInfo).toMap() != null
                    is ContactItemInfo -> (filter.value as ContactItemInfo).toMap() != null
                    else -> filter.value!= null
                }
            }
            mTvReset.visibility = if (hasNoReset) View.VISIBLE else View.GONE
            onResetVisibilityChange?.invoke(mTvReset.visibility)
        }

        mSubTypeFilterContainer.postDelayed({
            mSubTypeFilterContainer.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
            val maxScrollX = mSubTypeFilterContainer.measuredWidth - mSubTypeScrollView.measuredWidth
            if (maxScrollX < 0 || mSubTypeScrollView.scrollX == maxScrollX) {
                mIvResetEdge.visibility = View.INVISIBLE
            } else {
                mIvResetEdge.visibility = View.VISIBLE
            }
        }, 50)
    }
}