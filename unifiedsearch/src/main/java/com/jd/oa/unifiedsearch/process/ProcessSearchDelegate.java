package com.jd.oa.unifiedsearch.process;

import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.joyspace.view.SearchLoadingIndicator;
import com.jd.oa.unifiedsearch.process.data.ProcessInfo;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.pei.pulluploadhelper.PullUpLoad;
import com.pei.pulluploadhelper.PullUpLoadHelper;

import java.util.List;
import java.util.Objects;

public class ProcessSearchDelegate extends UnifiedSearchTabDelegate {
    private static final String STATE_KEYWORD = "keyword";
    private RecyclerView mRecyclerView;
    private View mLayoutPlaceholder;
    private View mLayoutEmpty;
    private View mLayoutLoading;
    private View mLoadingIndicator;
    private View mLayoutError;
    private PullUpLoadHelper mPullUpLoadHelper;
    private Button mBtnRetry;

    private ProcessSearchAdapter mProcessSearchAdapter;

    private Animation mLoadingAnimation;
    private String mKeyword;
    private TextView mTvFeedback;
    private TextView mTvEmpty;
    private ProcessSearchViewModel mViewModel;

    public ProcessSearchDelegate(Host host) {super(host);}

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mViewModel = new ViewModelProvider((ViewModelStoreOwner) mHost.getActivity()).get(ProcessSearchViewModel.class);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.unifiedsearch_fragment_process, container, false);
        if (savedInstanceState != null) {
            mKeyword = savedInstanceState.getString(STATE_KEYWORD);
        }
        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        this.initView(mHost.requireView(), savedInstanceState);
    }

    private void initView(View view, @Nullable Bundle savedInstanceState) {
        mRecyclerView = view.findViewById(R.id.recycle_view);
        mLayoutPlaceholder = view.findViewById(R.id.layout_placeholder);
        mLayoutEmpty = view.findViewById(R.id.me_pub_empty_view);
        mLayoutLoading = view.findViewById(R.id.layout_loading);
        mLoadingIndicator = view.findViewById(R.id.iv_loading_indicator);
        mLayoutError = view.findViewById(R.id.layout_error);
        mBtnRetry = view.findViewById(R.id.btn_retry);
        mTvEmpty = view.findViewById(R.id.tv_empty);

        // 反馈
        mTvFeedback = view.findViewById(R.id.tv_conent);
        mTvFeedback.setMovementMethod(LinkMovementMethod.getInstance());
        mTvFeedback.setText(SearchUtil.getFeedbackContent());

        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(mHost.getContext());
        mRecyclerView.setLayoutManager(layoutManager);

        mProcessSearchAdapter = new ProcessSearchAdapter(mHost.getContext());
        mProcessSearchAdapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                ProcessInfo processInfo = (ProcessInfo) adapter.getItem(position);
                if (processInfo.refPro.mobileApplyLink != null && !processInfo.refPro.mobileApplyLink.isEmpty()) {
                    JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Process_ck,
                            JDMAConstants.Mobile_Event_UnifiedSearch_Process_ck);
                    String deeplink = processInfo.refPro.mobileApplyLink;
                    Router.build(deeplink).go(mHost.getContext());
                }
            }
        });
        mRecyclerView.setAdapter(mProcessSearchAdapter);

        mPullUpLoadHelper = new PullUpLoadHelper(mRecyclerView, new PullUpLoad.OnLoadListener() {
            @Override
            public void onLoad() {
                mViewModel.next(mKeyword, mHost.getSessionId());
            }
        });
        SearchLoadingIndicator endIndicator = new SearchLoadingIndicator(mHost.getContext());
        mPullUpLoadHelper.setEndIndicator(endIndicator);
        mPullUpLoadHelper.setEmpty();

        mLoadingAnimation = AnimationUtils.loadAnimation(mHost.getContext(), R.anim.unifiedsearch_loading_indicator);

        mViewModel.getProcessInfoList().observe(mHost.getViewLifecycleOwner(), new Observer<Pair<String, List<ProcessInfo>>>() {
            @Override
            public void onChanged(Pair<String, List<ProcessInfo>> result) {
                //数据加载成功
                if (CollectionUtil.isEmptyOrNull(result.second)) {
                    if (TextUtils.isEmpty(result.first)) { //返回数据为空
                        mProcessSearchAdapter.clear();
                        mRecyclerView.setVisibility(View.INVISIBLE);
                        mLayoutEmpty.setVisibility(View.GONE);
                        mTvFeedback.setVisibility(View.GONE);
                        mLayoutPlaceholder.setVisibility(result.first == null ? View.INVISIBLE : View.VISIBLE);
                    } else {
                        mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                        mLayoutEmpty.setVisibility(View.VISIBLE);
                        mRecyclerView.setVisibility(View.INVISIBLE);
                        mTvFeedback.setVisibility(View.VISIBLE);
                    }
                } else { //有数据更新
                    mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                    mLayoutEmpty.setVisibility(View.GONE);
                    mTvFeedback.setVisibility(View.GONE);
                    mRecyclerView.setVisibility(View.VISIBLE);
                }
                //adapter更新数据
                mProcessSearchAdapter.refresh(result.second);
                mLayoutError.setVisibility(View.INVISIBLE);
            }
        });

        mViewModel.getLoading().observe(mHost.getViewLifecycleOwner(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                //Loading变化
                Pair<String, List<ProcessInfo>> value = mViewModel.getProcessInfoList().getValue();
                if (aBoolean && (value == null || CollectionUtil.isEmptyOrNull(value.second))) {
                    showLoading();
                } else {
                    hideLoading();
                }
            }
        });

        mViewModel.getLoadMore().observe(mHost.getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                switch (s) {
                    case ProcessSearchViewModel.LOAD_MORE_EMPTY:
                        mPullUpLoadHelper.setEmpty();
                        break;
                    case ProcessSearchViewModel.LOAD_MORE_COMPLETE:
                        mPullUpLoadHelper.setComplete();
                        break;
                    case ProcessSearchViewModel.LOAD_MORE_LOADING:
                        mPullUpLoadHelper.setLoading();
                        break;
                    case ProcessSearchViewModel.LOAD_MORE_LOADED:
                        mPullUpLoadHelper.setLoaded();
                        break;
                }
            }
        });

        mViewModel.getError().observe(mHost.getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                mLayoutEmpty.setVisibility(View.INVISIBLE);
                mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                mRecyclerView.setVisibility(View.INVISIBLE);
                mLayoutError.setVisibility(View.VISIBLE);
            }
        });

        mBtnRetry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //重试按钮
                if (!TextUtils.isEmpty(mKeyword)) {
                    mViewModel.refresh(mKeyword, mHost.getSessionId());
                }
            }
        });
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (outState != null) {
            outState.putString(STATE_KEYWORD, mKeyword);
        }
    }

    @Override
    public void search(String keyword, boolean force) {
        if (!force && Objects.equals(mKeyword, keyword)) return;

        mKeyword = keyword;
        mViewModel.refresh(keyword, mHost.getSessionId());
    }

    @Override
    public void onTextChanged(String keyword) {
        search(keyword, true);
    }

    @Override
    public void tabSelected(String keyword) {
        if (!Objects.equals(keyword, mKeyword)) {
            mViewModel.clear();
        }
        JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Process_Tab_ck, JDMAConstants.Mobile_Event_UnifiedSearch_Process_Tab_ck);
        JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Process_exposure, JDMAConstants.Mobile_Event_UnifiedSearch_Process_exposure);
        search(keyword,false);
    }

    private void showLoading() {
        mLoadingAnimation.cancel();
        mLoadingAnimation.reset();

        mLoadingIndicator.startAnimation(mLoadingAnimation);
        mLayoutLoading.setVisibility(View.VISIBLE);
    }

    private void hideLoading() {
        mLayoutLoading.setVisibility(View.INVISIBLE);
    }
}
