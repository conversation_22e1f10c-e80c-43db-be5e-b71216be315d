package com.jd.oa.unifiedsearch.all;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;

import com.jd.oa.configuration.TenantConfigManager;
import com.jd.oa.unifiedsearch.JDMESectionedRecyclerviewAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.unifiedsearch.all.callback.IDataLoadedCallback;
import com.jd.oa.unifiedsearch.all.callback.ISectionEventCallback;
import com.jd.oa.unifiedsearch.all.data.SearchDiscoveryModel;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.all.helper.SearchHelper;
import com.jd.oa.unifiedsearch.all.section.AIRecommendSection;
import com.jd.oa.unifiedsearch.all.section.BaseSection;
import com.jd.oa.unifiedsearch.all.section.DiscoverySection;
import com.jd.oa.unifiedsearch.all.section.RecommendSection;
import com.jd.oa.unifiedsearch.all.section.SearchSectionFactory;
import com.jd.oa.unifiedsearch.all.ui.WrapContentLinearLayoutManager;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

import static com.jd.oa.configuration.TenantConfigManager.KEY_USEJOYSPACE;
import static com.jd.oa.configuration.TenantConfigManager.KEY_USEJOYWORK;


/*
 * Time: 2023/10/16
 * Author: qudongshi
 * Description:
 */
public class AllSearchDelegate extends UnifiedSearchTabDelegate {

    public static final String TAG = "AllSearchDelegate";

    private LinearLayout mPlaceholder;
    private View mLayoutLoading;
    private View mLoadingIndicator;

    private Animation mLoadingAnimation;

    private View contentView;
    private RecyclerView recyclerView;
    private SectionedRecyclerViewAdapter mSectionAdapter;

    private String currentKeyword;
    private SearchFloorHelper searchFloorHelper;

    private List<SearchFloorType> mIMFloorTypeList;
    private List<BaseSection> delaySections = new ArrayList<>();

    public AllSearchDelegate(Host host) {
        super(host);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        SearchLogUitl.LogD(TAG, "onCreateView " + System.identityHashCode(this));
        contentView = inflater.inflate(R.layout.unifiedsearch_fragment_all, container, false);
        initView(contentView);
        initFloor();
        return contentView;
    }

    private void initFloor() {
        searchFloorHelper = new SearchFloorHelper(new IDataLoadedCallback() {
            @Override
            public void onResult(SearchFloorType floorType, String keyword, List<?> data) {
                synchronized (AllSearchDelegate.class) {
                    if (!currentKeyword.equals(keyword)) {
                        return;
                    }
                    SearchConfigHelper.getInstance().getMainHandler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            // 数据结果处理
                            hideLoading();
                            if (mSectionAdapter.getSection(SearchFloorType.EMPTY.toString()) == null && mSectionAdapter.getItemCount() == 0) {
                                searchFloorHelper.addSection(mSectionAdapter, SearchFloorType.EMPTY, SearchSectionFactory.getEmptySection(keyword));
                            }

                            if (mSectionAdapter.getSection(SearchFloorType.FEED_BACK.toString()) == null) {
                                searchFloorHelper.addSection(mSectionAdapter, SearchFloorType.FEED_BACK, SearchSectionFactory.getFeedBackSection(keyword));
                            }

                            if (mSectionAdapter.getSection(SearchFloorType.POWERED.toString()) == null) {
                                searchFloorHelper.addSection(mSectionAdapter, SearchFloorType.POWERED, SearchSectionFactory.getPoweredSection(keyword));
                            }
                        }
                    }, 2000);

                    if (floorType == null || TextUtils.isEmpty(keyword) || data == null || data.size() == 0) {
                        SearchLogUitl.LogD(TAG, "throw exception result floorType = " + floorType);
                        return;
                    }
                    if (!currentKeyword.equals(keyword)) {
                        SearchLogUitl.LogD(TAG, "keyword changed current = " + currentKeyword + " callback = " + keyword);
                        return;
                    }
                    SearchLogUitl.LogD(TAG, floorType.toString() + "---" + keyword + "---" + data.size());
                    // 数据结果处理
                    hideLoading();
                    mPlaceholder.setVisibility(View.GONE);
                    removeHistoryFloor();
                    removeEmptyFloor();
                    if (mIMFloorTypeList.indexOf(floorType) >= 0 || SearchFloorType.APP == floorType || SearchFloorType.JOY_SPACE == floorType
                            || SearchFloorType.TASK == floorType || SearchFloorType.PROCESS == floorType || SearchFloorType.APPROVAL == floorType) { // IM section logic
                        if (mSectionAdapter.getSection(floorType.toString()) == null) { // new  seciton
                            SearchLogUitl.LogD(TAG, "new section---" + floorType.toString() + "---" + keyword + "---" + data.size());
                            BaseSection tmpSection = SearchSectionFactory.getSection(mHost.getContext(), floorType, data, keyword, searchFloorHelper, mHost.getSessionId(), mHost.getSearchId());
                            if (searchFloorHelper.isImLoaded(keyword)) {
                                searchFloorHelper.addSection(mSectionAdapter, floorType, tmpSection);

                                if (searchFloorHelper.needWaitTypes.contains(floorType)) {
                                    if (searchFloorHelper.isImLoaded(keyword) && !delaySections.isEmpty()) {
                                        for (BaseSection section : delaySections) {
                                            if (section != null && section.keyword.equals(currentKeyword)) {
                                                searchFloorHelper.addSection(mSectionAdapter, section.floorType, section);
                                            }
                                        }
                                        delaySections.clear();
                                    }
                                }
                            } else {
                                delaySections.add(tmpSection);
                            }
                        } else { // 更新数据
                            SearchLogUitl.LogD(TAG, "update section---" + floorType.toString() + "---" + keyword + "---" + data.size());
                            BaseSection section = (BaseSection) mSectionAdapter.getSection(floorType.toString());
                            section.refreshData(mSectionAdapter, data, keyword);
                            mSectionAdapter.notifyItemChangedInSection(floorType.toString(), 0);
                        }
                    } else if (SearchFloorType.ENCYCLO == floorType) {
                        SearchLogUitl.LogD(TAG, "new section---" + floorType.toString() + "---" + keyword + "---" + data.size());
                        BaseSection tmpSection = SearchSectionFactory.getSection(mHost.getContext(), floorType, data, keyword, searchFloorHelper, mHost.getSessionId(), mHost.getSearchId());
                        if (searchFloorHelper.isImLoaded(keyword))
                            searchFloorHelper.addSection(mSectionAdapter, floorType, tmpSection);
                        else delaySections.add(tmpSection);
                    } else if (SearchFloorType.AI == floorType) {
                        if (mSectionAdapter.getSection(floorType.toString()) == null) { // new  seciton
                            SearchLogUitl.LogD(TAG, "new section---" + floorType.toString() + "---" + keyword + "---" + data.size());
                            BaseSection tmpSection = SearchSectionFactory.getSection(mHost.getContext(), floorType, data, keyword, searchFloorHelper, mHost.getSessionId(), mHost.getSearchId());
                            if (searchFloorHelper.isImLoaded(keyword))
                                searchFloorHelper.addSection(mSectionAdapter, floorType, tmpSection);
                            else delaySections.add(tmpSection);
                        } else { // 更新数据
                            SearchLogUitl.LogD(TAG, "update section---" + floorType.toString() + "---" + keyword + "---" + data.size());
                            BaseSection section = (BaseSection) mSectionAdapter.getSection(floorType.toString());
                            section.refreshData(mSectionAdapter, data, keyword);
                        }
                    }
                }
            }
        });
        mIMFloorTypeList = searchFloorHelper.getIMFloorType();
    }

    private void showHistoryFloor() {
        List<String> historyData = searchFloorHelper.getHistory();
        if (historyData != null && !historyData.isEmpty()) {
            BaseSection historySection = SearchSectionFactory.getSearchHistorySection(mHost.getContext(), historyData, keyword -> {
                // 点击
                if (mSectionAdapter.getSection(SearchFloorType.HISTORY.toString()) != null) {
                    searchFloorHelper.removeSection(mSectionAdapter, SearchFloorType.HISTORY);
                }
                sendNotice(keyword);
            });
            hideLoading();
            mPlaceholder.setVisibility(View.GONE);
            searchFloorHelper.removeAllSection(mSectionAdapter);
            searchFloorHelper.addSection(mSectionAdapter, SearchFloorType.HISTORY, historySection);
        } else {
            hideLoading();
            mPlaceholder.setVisibility(View.VISIBLE);
            searchFloorHelper.removeAllSection(mSectionAdapter);
        }
    }

    List<String> recommends = new ArrayList<>();
    List<String> aiRecommends = new ArrayList<>();
    List<SearchDiscoveryModel.DiscoverDataItem> discoveryItems = new ArrayList<>();

    private void showRecommendFloor() {
        if (recommends != null && recommends.size() > 0) {
            if (mSectionAdapter.getSection(SearchFloorType.HISTORY.toString()) == null) {
                mPlaceholder.setVisibility(View.GONE);
            }
            RecommendSection recommendSection = new RecommendSection(mHost.getContext(), recommends, new ISectionEventCallback() {
                @Override
                public void onItemClick(String keyword) {
                    // 点击
                    sendNotice(keyword);
                }
            });
            searchFloorHelper.addSection(mSectionAdapter, SearchFloorType.RECOMMEND, recommendSection);
        } else {
            SearchHelper.getFrequencyProblems(new IDataLoadedCallback() {
                @Override
                public void onResult(SearchFloorType floorType, String keyword, List<?> data) {
                    if (data != null && data.size() > 0) {
                        if (data.get(0) instanceof String) {
                            recommends = (List<String>) data;
                            showRecommendFloor();
                        }
                    }
                }
            });
        }
    }

    private void showAIRecommendFloor() {
        if (aiRecommends != null && !aiRecommends.isEmpty()) {
            if (mSectionAdapter.getSection(SearchFloorType.HISTORY.toString()) == null) {
                mPlaceholder.setVisibility(View.GONE);
            }
            AIRecommendSection aiRecommendSection = new AIRecommendSection(mHost.getContext(), aiRecommends, new ISectionEventCallback() {
                @Override
                public void onItemClick(String keyword) {
                    // 点击
                    sendNotice(keyword);
                }
            });
            searchFloorHelper.addSection(mSectionAdapter, SearchFloorType.AI_RECOMMEND, aiRecommendSection);
        } else {
            SearchHelper.getAIRecommendedQuestions(new IDataLoadedCallback() {
                @Override
                public void onResult(SearchFloorType floorType, String keyword, List<?> data) {
                    if (data != null && !data.isEmpty()) {
                        if (data.get(0) instanceof String) {
                            aiRecommends = (List<String>) data;
                            showAIRecommendFloor();
                        }
                    }
                }
            });
        }
    }

    private void showDiscoveryFloor() {
        if (discoveryItems != null && !discoveryItems.isEmpty()) {
            if (mSectionAdapter.getSection(SearchFloorType.HISTORY.toString()) == null) {
                mPlaceholder.setVisibility(View.GONE);
            }
            BaseSection discoverySection = new DiscoverySection(mHost.getContext(), discoveryItems, new ISectionEventCallback() {
                @Override
                public void onItemClick(String keyword) {
                    // 点击
                }
            });
            searchFloorHelper.addSection(mSectionAdapter, SearchFloorType.DISCOVERY, discoverySection);
        } else {
            SearchHelper.getDiscoveryItems(new IDataLoadedCallback() {
                @Override
                public void onResult(SearchFloorType floorType, String keyword, List<?> data) {
                    if (data != null && !data.isEmpty()) {
                        if (data.get(0) instanceof SearchDiscoveryModel.DiscoverDataItem) {
                            discoveryItems = (List<SearchDiscoveryModel.DiscoverDataItem>) data;
                            showDiscoveryFloor();
                        }
                    }
                }
            });
        }
    }

    private void initView(View view) {
        mPlaceholder = view.findViewById(R.id.layout_placeholder);
        mLayoutLoading = view.findViewById(R.id.layout_loading);
        mLoadingIndicator = view.findViewById(R.id.iv_loading_indicator);
        mLoadingAnimation = AnimationUtils.loadAnimation(mHost.getContext(), R.anim.unifiedsearch_loading_indicator);

        recyclerView = view.findViewById(R.id.recycle_view);
        mSectionAdapter = new JDMESectionedRecyclerviewAdapter();
        WrapContentLinearLayoutManager mLayoutManager = new WrapContentLinearLayoutManager(view.getContext(), LinearLayoutManager.VERTICAL, false);
        mLayoutManager.setInitialPrefetchItemCount(4);
        recyclerView.setLayoutManager(mLayoutManager);
        recyclerView.setAdapter(mSectionAdapter);
        recyclerView.setHasFixedSize(true);
        recyclerView.setItemViewCacheSize(10);
        recyclerView.setNestedScrollingEnabled(true);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        SearchLogUitl.LogD(TAG, "onActivityCreated");
    }


    @Override
    public void search(String s, boolean force) {
        SearchLogUitl.LogD(TAG, "search keyword= " + s);
    }

    @Override
    public void onTextChanged(String s) {
        SearchLogUitl.LogD(TAG, "onTextChanged keyword= " + s);
        if (currentKeyword != null && currentKeyword.equals(s)) {
            return;
        }
        search(s);
    }

    @Override
    public void tabSelected(String keyword) {
        SearchLogUitl.LogD(TAG, "tabSelected keyword= " + keyword);
        if (currentKeyword != null && currentKeyword.equals(keyword)) {
            return;
        }
        search(keyword);
    }

    private void search(String keyword) {
        showLoading();
        currentKeyword = keyword;
        searchFloorHelper.setCurrentKey(keyword);
        String reqId = System.currentTimeMillis() + "";
        //记录requestID, 清除之前的config
        SearchConfigHelper.getInstance().setRequestID(reqId);
        SearchConfigHelper.getInstance().setKeyword(keyword);
        if (TextUtils.isEmpty(keyword)) {
            showHistoryFloor();
            if (SearchConfigHelper.getInstance().searchMEAIEnable()) { //灰度控制
                showAIRecommendFloor();
            } else {
                showRecommendFloor();
            }
            showDiscoveryFloor();
        } else {
            mPlaceholder.setVisibility(View.GONE);
            // 加载楼层数据
            searchFloorHelper.removeAllSection(mSectionAdapter);
            loadSearchFloor(reqId);
        }

    }

    private void loadSearchFloor(String requestId) {
        searchFloorHelper.searchIMFloor(mIMFloorTypeList, currentKeyword, mHost.getSessionId(), requestId);
        searchFloorHelper.searchMEFloor(currentKeyword, SearchFloorType.APP, mHost.getSessionId(), requestId);
        if (TenantConfigManager.getConfigByKey(KEY_USEJOYSPACE)) {
            searchFloorHelper.searchMEFloor(currentKeyword, SearchFloorType.JOY_SPACE, mHost.getSessionId(), requestId);
        }
        if (TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_USEWIKI)) {
            searchFloorHelper.searchMEFloor(currentKeyword, SearchFloorType.ENCYCLO, mHost.getSessionId(), requestId);
        }
        if (SearchConfigHelper.getInstance().searchMEAIEnable()) { //MEAI智能搜索灰度
            //MEAI搜索Call(已移除)
        } else if (searchFloorHelper.searchAIEnable()){ //AI搜索灰度
            searchFloorHelper.searchMEAIFloor(currentKeyword, mHost.getSessionId(), requestId, "");
        }
        if (TenantConfigManager.getConfigByKey(KEY_USEJOYWORK)) {
            searchFloorHelper.searchMEFloor(currentKeyword, SearchFloorType.TASK, mHost.getSessionId(), requestId);
        }
        if (SearchConfigHelper.getInstance().searchProcessEnable()) {
            searchFloorHelper.searchMEFloor(currentKeyword, SearchFloorType.PROCESS, mHost.getSessionId(), requestId);
        }
        if (SearchConfigHelper.getInstance().searchApprovalEnable()) {
            searchFloorHelper.searchMEFloor(currentKeyword, SearchFloorType.APPROVAL, mHost.getSessionId(), requestId);
        }

    }

    private void showLoading() {
        mLoadingAnimation.cancel();
        mLoadingAnimation.reset();

        mLoadingIndicator.startAnimation(mLoadingAnimation);
        mLayoutLoading.setVisibility(View.VISIBLE);
    }

    private void hideLoading() {
        mLayoutLoading.setVisibility(View.INVISIBLE);
    }

    private void removeHistoryFloor() {
        if (mSectionAdapter != null && mSectionAdapter.getSection(SearchFloorType.HISTORY.toString()) != null) {
            mSectionAdapter.removeSection(SearchFloorType.HISTORY.toString());
        }
    }

    private void removeEmptyFloor() {
        if (mSectionAdapter != null && mSectionAdapter.getSection(SearchFloorType.EMPTY.toString()) != null) {
            mSectionAdapter.removeSection(SearchFloorType.EMPTY.toString());
        }
    }

    private void sendNotice(String text) {
        Intent i = new Intent();
        i.setAction("search.action.text.change");
        i.putExtra("text", text);
        LocalBroadcastManager.getInstance(mHost.getActivity()).sendBroadcast(i);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
