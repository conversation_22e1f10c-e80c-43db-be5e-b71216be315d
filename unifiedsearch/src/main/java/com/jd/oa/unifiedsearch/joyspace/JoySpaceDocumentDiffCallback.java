package com.jd.oa.unifiedsearch.joyspace;

import androidx.recyclerview.widget.DiffUtil;

import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument;

import java.util.List;
import java.util.Objects;

public class JoySpaceDocumentDiffCallback extends DiffUtil.Callback {

    private List<JoySpaceDocument> oldList;
    private List<JoySpaceDocument> newList;

    public JoySpaceDocumentDiffCallback(List<JoySpaceDocument> oldList, List<JoySpaceDocument> newList) {
        this.oldList = oldList;
        this.newList = newList;
    }

    @Override
    public int getOldListSize() {
        return oldList == null ? 0 : oldList.size();
    }

    @Override
    public int getNewListSize() {
        return newList == null ? 0 : newList.size();
    }

    @Override
    public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
        return oldList.get(oldItemPosition).getId().equals(newList.get(newItemPosition).getId());
    }

    @Override
    public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
        JoySpaceDocument oldItem = oldList.get(oldItemPosition);
        JoySpaceDocument newItem = newList.get(newItemPosition);
        if (!Objects.equals(oldItem.getIconUrl(), newItem.getIconUrl())) return false;

        if (!Objects.equals(oldItem.getTitle(), newItem.getTitle())) return false;

        if (!Objects.equals(oldItem.getStatus(), newItem.getStatus())) return false;

        return true;
    }
}
