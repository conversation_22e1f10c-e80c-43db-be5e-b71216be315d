package com.jd.oa.unifiedsearch.all.filter;

import static com.jd.oa.router.DeepLink.CONTACTS;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.appcompat.app.AppCompatActivity;

import com.chenenyu.router.Router;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;
import com.jd.oa.unifiedsearch.joyspace.filter.FilterContext;
import com.jd.oa.unifiedsearch.joyspace.filter.UnifiedSearchFilter;
import com.jd.oa.unifiedsearch.joyspace.view.ContactFilterLayout;
import com.jd.oa.unifiedsearch.joyspace.view.FilterArrow;
import com.jd.oa.unifiedsearch.joyspace.view.FilterLayout;
import com.jd.oa.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

public class ContactFilter extends UnifiedSearchFilter<ContactItemInfo> {
    public static final int MAX_CONTACT_NUM = 1;
    private static final int REQUEST_CODE = 103;

    private static final String STATE_CONTACTS = "state.contacts";

    private final String TAG = "contactFilter";

    private String mFilterName = "";
    private SearchFilterConfig.FilterItem mFilterItem;
    private ArrayList<MemberEntityJd> mContactList;

    protected ContactFilterLayout mContactFilterLayout;

    protected FilterLayout mFilterLayout;



    public ContactFilter(FilterContext filterContext, SearchFilterConfig.FilterItem filter, String filterName) {
        super(filterContext);
        mFilterName = filterName;
        mFilterItem = filter;
    }

    @Override
    protected View onCreateView(Context context, ViewGroup container) {
        View view = LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_contact_filter, container, false);
        mContactFilterLayout = view.findViewById(R.id.layout_contact_filter);
        mContactFilterLayout.setText(getTitle());
        mFilterLayout = view.findViewById(R.id.layout_filter);
        FilterArrow arrow = mFilterLayout.findViewById(R.id.arrow);
        arrow.observeTo(mFilterLayout);
        return view;
    }

    @Override
    public void onFilterClick(AppCompatActivity activity, View view) {
        MELogUtil.localD(TAG, "打开联系人选择器");
        selectContacts(activity);
    }

    private void selectContacts(AppCompatActivity activity) {
        ArrayList<String> appIds = new ArrayList<>(getAppIds());
        Intent intent = Router.build(CONTACTS).getIntent(activity);
        intent.putExtra("extra_contact", mContactList);
        //intent.putExtra("extra_alternate_contact", alternate);
        intent.putExtra("title", activity.getString(R.string.unifiedsearch_joyspace_contact_selector_title_prefix) + getTitle());
        intent.putExtra("max", MAX_CONTACT_NUM);
        intent.putExtra("externalContactEnable", false);
        intent.putStringArrayListExtra("extra_specify_appId",appIds);
        startActivityForResult(intent, REQUEST_CODE);
    }

    @Override
    public void reset() {
        if (mContactList != null) {
            mContactList = null;
            mContactFilterLayout.setText(getTitle());
            mContactFilterLayout.setContacts(null);
        }
        super.reset();
    }

    private String getTitle() {
        return mFilterName;
    }

    @Override
    public ContactItemInfo getValue() {
        return new ContactItemInfo(mContactList, mFilterItem);
    }

    protected void onSelectContact(ArrayList<MemberEntityJd> contacts) {
        boolean changed = !CollectionUtil.isEquals(mContactList, contacts);
        mContactList = contacts;
        if (CollectionUtil.notNullOrEmpty(contacts)) {
            MELogUtil.localD(TAG, "已选中联系人: " + contacts);
            setSelected(true);
        } else {
            MELogUtil.localD(TAG, "未选中联系人");
            reset();
        }
        if (mOnChangedListener != null && changed) {
            mOnChangedListener.onChanged(new ContactItemInfo(mContactList, mFilterItem));
        }
        mContactFilterLayout.setContacts(contacts);
    }

    protected List<String> getAppIds() {
        return TenantConfigBiz.INSTANCE.getCollaborativelyApps();
    }

    @Override
    public void onSaveState(String type, Bundle outState) {
        super.onSaveState(type, outState);
        if (mContactList != null) {
            Bundle state = new Bundle();
            state.putParcelableArrayList(STATE_CONTACTS, mContactList);
            outState.putParcelable(type, state);
        }
    }

    @Override
    public void onRestoreState(String type, Bundle savedState) {
        super.onRestoreState(type, savedState);
        Bundle state = savedState.getParcelable(type);
        if (state != null) {
            mContactList = state.getParcelableArrayList(STATE_CONTACTS);
            if (mContactList != null) {
                mContactFilterLayout.setContacts(mContactList);
                setSelected(CollectionUtil.notNullOrEmpty(mContactList));
                onSelectContact(mContactList);
            }
        }
    }

    @Override
    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK) {
                @SuppressWarnings("unchecked")
                ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                onSelectContact(selected);
            }
            return true;
        }
        return false;
    }
}
