package com.jd.oa.unifiedsearch.joyday.model;

import androidx.annotation.Nullable;

import java.util.Date;

public class DateModel implements Comparable<DateModel> {
    public int _year;
    public int _month;
    public int _day = 1;

    private DateModel(int year, int month, int day) {
        _year = year;
        _month = month;
        _day = day;
    }

    //根据DateTime创建对应的model，并初始化农历和传统节日等信息
    public static DateModel fromDateTime(Date dateTime) {
        if (dateTime == null) {
            return null;
        }

        return new DateModel(dateTime.getYear(),  dateTime.getMonth(), dateTime.getDate());
    }

    // 从时间戳
    public static DateModel fromTimestamp(long timeStamp) {
        Date dateTime = new Date(timeStamp);
        return DateModel.fromDateTime(dateTime);
    }

    //转化成DateTime格式
    public Date dateTime() {
        return new Date(_year, _month, _day);
    }

    @Override
    public int compareTo(DateModel other) {
        return Long.compare(new Date(_year, _month, _day).getTime(), new Date(other._year, other._month, other._day).getTime());
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj instanceof DateModel) {
            return this._year == ((DateModel) obj)._year && this._month == ((DateModel) obj)._month && this._day == ((DateModel) obj)._day;
        }
        return false;
    }
}
