package com.jd.oa.unifiedsearch.app;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Pair;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.jd.oa.AppBase;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.httpmanager.CancelTag;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.helper.SearchAnalyzeHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.utils.JDMAUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class AppSearchViewModel extends ViewModel {

    private MutableLiveData<Boolean> mLoading = new MutableLiveData<>(false);
    private MutableLiveData<Pair<String, List<AppInfo>>> mAppInfoList = new MutableLiveData<>();
    private MutableLiveData<String> mError = new MutableLiveData<>();

    private String mKeyword;

    private CancelTag mCancelTag;

    public void refresh(String keyword, String sessionId) {
        mLoading.setValue(true);
        mKeyword = keyword;

        if (TextUtils.isEmpty(keyword)) {
            mAppInfoList.setValue(Pair.create(keyword, Collections.emptyList()));
            mLoading.setValue(false);
            return;
        }

        if (mCancelTag != null) {
            //HttpManager.cancel(mCancelTag);
        }

        mCancelTag = new CancelTag(keyword);
        String requestId = System.currentTimeMillis() + "";
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.APP.toString(),false);
        NetWorkManagerAppCenter.searchAppInfo(mCancelTag, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APP.toString(), dataLength + "");
                super.onSuccess(response);
                mLoading.setValue(false);
                if (response.isSuccessful()) {
                    if (Objects.equals(mKeyword, keyword)) {
                        List<AppInfo> appList = response.getListData(AppInfo.class, "appList");
                        mAppInfoList.setValue(Pair.create(keyword, appList));
                    }
                } else {
                    mError.setValue(response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APP.toString(), dataLength + "");
                super.onFailure(exception, info);
                mLoading.setValue(false);
                mAppInfoList.setValue(Pair.create(keyword, Collections.emptyList()));
                mError.setValue(exception != null ? exception.getMessage() : info != null ? info : AppBase.getAppContext().getString(R.string.me_api_response_unusual));
            }
        }, keyword);
    }

    public void getAppDetailAndOpen(Activity activity, AppInfo appInfo) {
        SimpleRequestCallback<String> callback1 = new SimpleRequestCallback<String>(activity, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<AppInfo> response = ApiResponse.parse(info.result, AppInfo.class);
                if (response.isSuccessful()) {
                    AppUtils.openFunctionByPlugIn(activity, response.getData(), "scene_search");
//                    PageEventUtil.onHomeFunOpenEvent(activity, response.getData().getAppName(), response.getData().getAppType());
                    JDMAUtils.onAppOpenEvent(response.getData().getAppType(), response.getData().getAppID());
                } else {
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                //callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        };

        NetWorkManagerAppCenter.getAppDetail(this, callback1, appInfo.getAppID());
    }

    public LiveData<Boolean> getLoading() {
        return mLoading;
    }

    public LiveData<Pair<String, List<AppInfo>>> getAppInfoList() {
        return mAppInfoList;
    }

    public LiveData<String> getError() {
        return mError;
    }

    public void clear() {
        mAppInfoList.setValue(Pair.create(null, Collections.emptyList()));
    }
}
