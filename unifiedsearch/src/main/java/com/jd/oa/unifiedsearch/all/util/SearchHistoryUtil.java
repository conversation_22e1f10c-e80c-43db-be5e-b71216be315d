package com.jd.oa.unifiedsearch.all.util;

import android.text.TextUtils;

import com.jd.oa.unifiedsearch.all.data.SearchPreference;
import com.jd.oa.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

/*
 * Time: 2023/10/24
 * Author: qudongshi
 * Description:
 */
public class SearchHistoryUtil {

    public static void addSearchHistory(String text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        String history = SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_SEARCH_HISORY);
        List<String> data = JsonUtils.getGson().fromJson(history, List.class);
        if (data == null) {
            data = new ArrayList<>();
        }
        if (data.contains(text)) {
            data.remove(text);
        }
        if (data.size() == 10) {
            data.remove(9);
        }
        data.add(0, text);
        String jsonData = JsonUtils.getGson().toJson(data);
        SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_SEARCH_HISORY, jsonData);
    }
}
