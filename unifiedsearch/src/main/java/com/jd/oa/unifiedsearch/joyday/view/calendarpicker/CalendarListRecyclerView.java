package com.jd.oa.unifiedsearch.joyday.view.calendarpicker;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.utils.DisplayUtils;

public class CalendarListRecyclerView extends RecyclerView {

    public CalendarListRecyclerView(@NonNull Context context) {
        super(context);
    }

    public CalendarListRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CalendarListRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthSpec, int heightSpec) {
        heightSpec = MeasureSpec.makeMeasureSpec(DisplayUtils.dip2px(44 * 10 + 10), MeasureSpec.AT_MOST);
        super.onMeasure(widthSpec, heightSpec);
    }
}
