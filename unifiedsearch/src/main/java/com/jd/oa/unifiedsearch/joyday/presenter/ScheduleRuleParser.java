package com.jd.oa.unifiedsearch.joyday.presenter;

//schedule_rule_parser.dart

import com.jd.ee.librecurparser.RecurParser;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

public class ScheduleRuleParser {
    public static final String TAG = "ScheduleRuleParser";
    public static String cacheMode = "In offline Mode";

    //---------------------------------- 搜索 ----------------------------------//
    public static List<ScheduleModel> parseSearchList(List<ScheduleModel> scheduleList, long beginTime, long endTime) {
        List<ScheduleModel> list = parseScheduleList(scheduleList, beginTime, endTime);

        Iterator<ScheduleModel> iterator = list.iterator();
        while (iterator.hasNext()) {
            ScheduleModel scheduleModel = iterator.next();
            if (scheduleModel.isCancelled()) {
                iterator.remove();
            }
        }

        return list;
    }

    // 从日程规则中，解析出开始时间beginTime，到结束时间的日程
    static List<ScheduleModel> parseScheduleList(List<ScheduleModel> scheduleList, long beginTime, long endTime) {
        RecurParser<ScheduleModel> parser = new RecurParser<>();
        List<ScheduleModel> list = parser.parseAppointmentList(scheduleList, beginTime, endTime, ScheduleModelCreator.instance);
        return list;
    }

    static class ScheduleModelCreator implements RecurParser.AppointmentCreator<ScheduleModel> {

        static ScheduleModelCreator instance = new ScheduleModelCreator();

        @Override
        public ScheduleModel create(ScheduleModel recurrenceAppointment) {
            return recurrenceAppointment.clone();
        }
    }
}