package com.jd.oa.unifiedsearch.joyday.view.calendarpicker;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyday.adapter.CalendarListAdapter;
import com.jd.oa.unifiedsearch.joyday.model.Calendar;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DisplayUtil;

import java.util.List;

public class CalendarPickerDialog extends BottomSheetDialog {

    public interface OnConfirmListener {
        void onConfirm(List<Calendar> myCalendar, List<Calendar> subscribeCalendar);
    }

    private final OnConfirmListener mListener;
    private Calendar mCalendar;
    private List<Calendar> mMyCalendar;
    private List<Calendar> mSubscribeCalendar;

    public CalendarPickerDialog(@NonNull Context context, Calendar calendar, List<Calendar> myCalendar, List<Calendar> subscribeCalendar, OnConfirmListener listener) {
        this(context, 0, calendar, myCalendar, subscribeCalendar, listener);
    }

    public CalendarPickerDialog(@NonNull Context context, int theme, Calendar calendar, List<Calendar> myCalendar, List<Calendar> subscribeCalendar, OnConfirmListener listener) {
        super(context, theme);

        mListener = listener;
        Gson gson = new Gson();//复制一份防止adapter勾选影响外部数据结构，只有点确定返回勾选才有效
        this.mCalendar = calendar;
        mMyCalendar = gson.fromJson(gson.toJson(myCalendar), new TypeToken<List<Calendar>>() {}.getType());
        mSubscribeCalendar = gson.fromJson(gson.toJson(subscribeCalendar), new TypeToken<List<Calendar>>() {}.getType());

        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        View view = LayoutInflater.from(context).inflate(R.layout.unifiedsearch_select_calendar_dialog, null);
        setContentView(view);
        //设置背景
        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            parent.setBackgroundResource(android.R.color.transparent);
        }
        //设置初始高度
        View bottomSheet = findViewById(R.id.design_bottom_sheet);
        if (bottomSheet != null) {
            BottomSheetBehavior behavior = BottomSheetBehavior.from(bottomSheet);
            behavior.setPeekHeight(DisplayUtil.getScreenHeight(context));
        }

        View okButton = findViewById(R.id.ok_btn);
        okButton.setOnClickListener(v -> {
            dismiss();
            if (mListener != null) {
                mListener.onConfirm(mMyCalendar, mSubscribeCalendar);//为了和flutter版保持一致，点空白处返回不保存勾选
            }
        });

        CalendarListAdapter mAdapter = new CalendarListAdapter(context, mCalendar, mMyCalendar, mSubscribeCalendar);
        CalendarListRecyclerView calendarList = findViewById(R.id.calendar_list);
        calendarList.setAdapter(mAdapter);
        calendarList.setLayoutManager(new LinearLayoutManager(context));
    }
}
