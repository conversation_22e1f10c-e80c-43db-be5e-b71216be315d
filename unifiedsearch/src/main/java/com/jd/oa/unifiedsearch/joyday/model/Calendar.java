package com.jd.oa.unifiedsearch.joyday.model;

import java.util.List;

public class Calendar {
    public static final int PRIVACY = 1;
    public static final int BUSY = 2;
    public static final int PUBLIC = 3;

    public static final int VIEW_DAY = 1;
    public static final int VIEW_LIST = 2;
    public static final int VIEW_MONTH = 3;

    public static final int TYPE_PERSONAL = 1;
    public static final int TYPE_TEAM = 2;
    public static final int TYPE_PUBLIC = 3;
    public static final int TYPE_JOYWORK = 4;

    public static final int USER_TYPE_CREATOR = 1;
    public static final int USER_TYPE_PERSONAL_SUBSCRIBER = 2;
    public static final int USER_TYPE_PERSONAL_SUBSCRIBER_PASSIVE = 3;
    public static final int USER_TYPE_MANAGER = 4;
    public static final int USER_TYPE_TEAM_EDITOR = 5;
    public static final int USER_TYPE_TEAM_SUBSCRIBER = 6;

    public int access;
    public String calendarId;
    public String color;
    public String description;
    public int scope;
    ///订阅者
    public List<Subscriber> subscriber;
    public String title;
    ///1个人日历,2团队日历
    public int type;
    ///创建者
    public User user;
    public int view;
    /// 1. 创建者 2. 个人日历订阅者 3.个人日历被动订阅者 4.团队日历管理员 5.团队日历编辑者 6.团队日历订阅者
    public int userType;
    ///是否订阅 1:订阅，0:未订阅
    public int unsubscribe;
    public long createTime;
    ///是否选择
    public boolean checked;
    public int checkedStatus;
    //2022.04新接口数据格式
    public String extended;
    public String diaphaneity;
    public int archive;

    @Override
    public String toString() {
        return "{calendarId: " + calendarId + ", checked: " + checked + "}";
    }
}