package com.jd.oa.unifiedsearch;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewModelStoreOwner;

import com.jd.oa.unifiedsearch.all.AllSearchDelegate;
import com.jd.oa.unifiedsearch.org.OrgSearchTabDelegate;
import com.jd.oa.unifiedsearch.app.AppSearchTabDelegate;
import com.jd.oa.unifiedsearch.approval.ApprovalSearchTabDelegate;
import com.jd.oa.unifiedsearch.joyday.ScheduleSelectorConfig;
import com.jd.oa.unifiedsearch.joyday.view.ScheduleSearchTabDelegate;
import com.jd.oa.unifiedsearch.joyspace.JoySpaceSearchTabDelegate;
import com.jd.oa.unifiedsearch.process.ProcessSearchDelegate;
import com.jd.oa.unifiedsearch.task.TaskSearchTabDelegate;

import java.util.Collections;

public abstract class UnifiedSearchTabDelegate {

    public static final String TYPE_ALL = "all";
    public static final String TYPE_APP = "app";
    public static final String TYPE_JOYSPACE = "joyspace";
    public static final String TYPE_JOYDAY = "joyday";

    public static final String TYPE_MESSAGE = "message";
    public static final String TYPE_GROUP = "group";
    public static final String TYPE_CONTACT = "contact";

    public static final String TYPE_WAITER = "waiter";
    public static final String TYPE_ROBOT = "robot";
    public static final String TYPE_NOTICE = "notice";

    public static final String TYPE_TASK = "task";
    public static final String TYPE_APPROVAL = "approval";
    public static final String TYPE_PROCESS = "process";
    public static final String TYPE_ORG = "org";

    protected Host mHost;

    public UnifiedSearchTabDelegate(Host host) {
        mHost = host;
    }

    public void onAttach(@NonNull Context context) {
    }

    public void onCreate(@Nullable Bundle savedInstanceState) {
    }

    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
    }

    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return null;
    }

    public void onViewCreated(View view, Bundle savedInstanceState) {
    }

    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
    }

    public void onStart() {
    }

    public void onResume() {
    }

    public void onPause() {
    }

    public void onStop() {
    }

    public void onDestroyView() {
    }

    public void onSaveInstanceState(Bundle outState) {
    }

    public void onDetach() {
    }

    public void onDestroy() {
    }

    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
    }

    public void onConfigurationChanged(@NonNull Configuration newConfig) {}

    public abstract void search(String s, boolean force);

    public abstract void onTextChanged(String s);

    public abstract void tabSelected(String keyword);


    public interface Host {

        Context getContext();

        @NonNull
        Context requireContext();

        @Nullable
        Activity getActivity();

        @NonNull
        Lifecycle getLifecycle();

        @NonNull
        View requireView();

        @Nullable
        View getView();

        LifecycleOwner getViewLifecycleOwner();

        ViewModelStoreOwner getViewModelStoreOwner();

        void startActivity(Intent intent);

        void startActivityForResult(@SuppressLint("UnknownNullness") Intent intent, int requestCode);

        FragmentManager getHostFragmentManager();

        void close();

        String getSessionId();

        String getSearchId();
    }

    public interface Factory {
        UnifiedSearchTabDelegate create(Host host, String type);
    }

    public static class DefaultFactory implements Factory {

        @Override
        public UnifiedSearchTabDelegate create(Host host, String type) {
            if (TYPE_APP.equals(type)) {
                return new AppSearchTabDelegate(host);
            } else if (TYPE_JOYSPACE.equals(type)) {
                return new JoySpaceSearchTabDelegate(host);
            } else if (TYPE_JOYDAY.equals(type)) {
                ScheduleSelectorConfig config = new ScheduleSelectorConfig();
                config.setCalendar(true);
                config.setSelectType("none");
                ScheduleSelectorConfig.Category category = new ScheduleSelectorConfig.Category(false, Collections.emptyList(), true);
                config.setCategory(category);
                return new ScheduleSearchTabDelegate(host, config);
            } else if (TYPE_ALL.equals(type)) {
                return new AllSearchDelegate(host);
            } else if (TYPE_TASK.equals(type)) {
                return new TaskSearchTabDelegate(host);
            } else if (TYPE_APPROVAL.equals(type)) {
                return new ApprovalSearchTabDelegate(host);
            } else if (TYPE_PROCESS.equals(type)) {
                return new ProcessSearchDelegate(host);
            } else if (TYPE_ORG.equals(type)){
                return new OrgSearchTabDelegate(host);
            }
            return null;
        }
    }
}
