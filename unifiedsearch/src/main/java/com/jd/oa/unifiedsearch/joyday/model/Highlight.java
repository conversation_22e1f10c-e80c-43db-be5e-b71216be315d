package com.jd.oa.unifiedsearch.joyday.model;

import java.util.HashMap;

public class Highlight {
    public String address;
    public String description;
    public String title;

    public HashMap<String, Object> toJson() {
        HashMap<String, Object> data = new HashMap<>();
        data.put("address", this.address);
        data.put("description", this.description);
        data.put("title", this.title);
        return data;
    }
}
