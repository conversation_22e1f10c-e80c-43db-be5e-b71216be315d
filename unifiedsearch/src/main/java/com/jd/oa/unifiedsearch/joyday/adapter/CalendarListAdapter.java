package com.jd.oa.unifiedsearch.joyday.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyday.model.Calendar;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class CalendarListAdapter extends RecyclerView.Adapter<CalendarListAdapter.ViewHolder> {

    Calendar personalCalendar;
    final private List<Calendar> myCalendar;
    final private List<Calendar> subscribeCalendar;
    final private Context mContext;

    public CalendarListAdapter(Context context, Calendar calendar, List<Calendar> myCalendar, List<Calendar> subscribeCalendar) {
        mContext = context;
        this.personalCalendar = calendar;
        this.myCalendar = myCalendar != null ? myCalendar : new ArrayList<>();
        this.subscribeCalendar = subscribeCalendar != null ? subscribeCalendar : new ArrayList<>();
    }

    @NotNull
    @Override
    public CalendarListAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        View inflate = LayoutInflater.from(mContext).inflate(R.layout.unifiedsearch_select_calendar_list_item, viewGroup, false);
        return new ViewHolder(inflate);
    }

    @Override
    public void onBindViewHolder(@NonNull CalendarListAdapter.ViewHolder viewHolder, int index) {
        if (index < 0 || index >= myCalendar.size() + subscribeCalendar.size()) {
            return;
        }

        final Calendar calendar;
        if (index < myCalendar.size()) {
            calendar = myCalendar.get(index);
            viewHolder.mSeparator.setVisibility(View.GONE);
            viewHolder.mHeader.setVisibility(index == 0 ? View.VISIBLE : View.GONE);
            if (index == 0) {
                viewHolder.mHeaderTextView.setText(mContext.getResources().getString(R.string.schedule_calendar_my_calendar));
            }
        } else {
            index -= myCalendar.size();
            calendar = subscribeCalendar.get(index);
            viewHolder.mSeparator.setVisibility(index == 0 && myCalendar.size() > 0 ? View.VISIBLE : View.GONE);
            viewHolder.mHeader.setVisibility(index == 0 ? View.VISIBLE : View.GONE);
            if (index == 0) {
                viewHolder.mHeaderTextView.setText(mContext.getResources().getString(R.string.schedule_calendar_subscribe_calendar));
            }
        }
        viewHolder.mItem.setVisibility(View.VISIBLE);
        viewHolder.mTextView.setText(calendar.title);
        viewHolder.mTextView.setTypeface(null, calendar.checked ? Typeface.BOLD : Typeface.NORMAL);
        viewHolder.mCheckBox.setChecked(calendar.checked);
        viewHolder.itemView.setOnClickListener(v -> {
            calendar.checked = !calendar.checked;
            viewHolder.mCheckBox.setChecked(calendar.checked);
            viewHolder.mTextView.setTypeface(null, calendar.checked ? Typeface.BOLD : Typeface.NORMAL);
        });
    }

    @Override
    public int getItemCount() {
        return myCalendar.size() + subscribeCalendar.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        final private View mSeparator;
        final private View mHeader;
        final private View mItem;
        final private TextView mHeaderTextView;
        final private CheckBox mCheckBox;
        final private TextView mTextView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mSeparator = itemView.findViewById(R.id.calendar_separator);
            mHeader = itemView.findViewById(R.id.calendar_header);
            mHeaderTextView = itemView.findViewById(R.id.calendar_header_textview);
            mItem = itemView.findViewById(R.id.calendar_item);
            mCheckBox = itemView.findViewById(R.id.calendar_item_checkbox);
            mTextView = itemView.findViewById(R.id.calendar_item_textview);
        }
    }
}
