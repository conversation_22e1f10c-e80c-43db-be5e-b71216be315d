package com.jd.oa.unifiedsearch.joyspace;

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.annotation.StringDef
import androidx.core.content.ContextCompat
import com.chenenyu.router.Router
import com.jd.oa.router.DeepLink
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.joyspace.data.SearchFilterQuery
import com.jd.oa.unifiedsearch.joyspace.filter.*
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortConditionFactory
import com.jd.oa.unifiedsearch.joyspace.view.FiltersContainerLayout
import com.jd.oa.utils.InputMethodUtils
import com.jd.oa.utils.TextWatcherAdapter
import java.util.*


const val FOLDER_TYPE_NORMAL = "normal"
const val FOLDER_TYPE_TEAM = "team"
const val FOLDER_TYPE_PRIVATE = "private"

@StringDef(FOLDER_TYPE_NORMAL, FOLDER_TYPE_TEAM, FOLDER_TYPE_PRIVATE)
@Retention(AnnotationRetention.BINARY)
@Target(AnnotationTarget.FIELD, AnnotationTarget.FUNCTION, AnnotationTarget.VALUE_PARAMETER)
annotation class FolderType

class JoySpaceSearchFolderDelegate(
    host: Host,
    val teamId: String?,
    val folderId: String?,
    @FolderType
    val folderType: String?,
    val folderName: String?,
    val teamName: String?
    ) : JoySpaceSearchDelegate(host), FilterContext {

    companion object {
        const val STATE_KEYWORD = "keyword"
    }

    private val mIvFolderIcon: ImageView by lazy { mHost.requireView().findViewById(R.id.iv_folder_icon) }
    private val mEtSearch: EditText by lazy { mHost.requireView().findViewById(R.id.et_search) }
    private val mLayoutHint: ViewGroup by lazy { mHost.requireView().findViewById(R.id.layout_hint) }
    private val mTvHint: TextView by lazy { mHost.requireView().findViewById(R.id.tv_hint_text) }
    private val mTvClear: TextView by lazy { mHost.requireView().findViewById(R.id.icon_clear) }
    private val mTvCancel: TextView by lazy { mHost.requireView().findViewById(R.id.tv_cancel) }
    private val mFilterContainer: FiltersContainerLayout by lazy { mHost.requireView().findViewById(R.id.layout_filters_container) }
    private val mDivider: View by lazy { mHost.requireView().findViewById(R.id.divider) }
    private val mSortConditionButton: ImageButton by lazy { mHost.requireView().findViewById(R.id.btn_sort_condition) }
    private val mTvSearchAllTips: TextView by lazy { mHost.requireView().findViewById(R.id.tv_search_all_tips) }
    private val mTvSearchAll: TextView by lazy { mHost.requireView().findViewById(R.id.tv_search_all) }

    private val mSearchFiltersMap: Map<String,JoySpaceFilter<*>> by lazy {
        sortedMapOf(
            JoySpaceFilter.SUBTYPE_CREATE_BY_OTHERS to CreateByOthersFilter(this),
            JoySpaceFilter.SUBTYPE_DOCUMENT_TYPE to DocumentTypeFilter(this),
            JoySpaceFilter.SUBTYPE_OPENED_TIME to OpenedTimeFilter(this),
        )
    }

    private val mSortConditions: List<SortCondition> by lazy {
        listOf(SortConditionFactory.getConditionByType(mHost.context, SortCondition.TYPE_UPDATE_TIME), SortConditionFactory.getConditionByType(mHost.context, SortCondition.TYPE_CREATE_TIME))
    }

    private val mDefaultSortCondition: SelectedSortCondition = SelectedSortCondition(SortCondition.TYPE_UPDATE_TIME, SortCondition.CONDITION_LATEST_FIRST)

    private var mStartActivityFilter: UnifiedSearchFilter<*>? = null

    private var mSelectedSortCondition: SelectedSortCondition? = null

    private val mHandler = Handler()

    private val mSortConditionStorage: SortConditionStorage by lazy { MemorySortConditionStorage() }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.unifiedsearch_fragment_joyspace_folder, container, false)
    }

    @SuppressLint("SetTextI18n")
    override fun initView(view: View?, savedInstanceState: Bundle?) {
        super.initView(view, savedInstanceState)

        when(folderType) {
            FOLDER_TYPE_TEAM -> {
                mIvFolderIcon.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_joyspace_folder_team))
                mTvHint.text = "$teamName${mHost.context.getString(R.string.unifiedsearch_joyspace_folder_team)}"
            }
            FOLDER_TYPE_PRIVATE -> {
                mIvFolderIcon.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_joyspace_folder_private))
                mTvHint.text = mHost.context.getString(R.string.unifiedsearch_joyspace_folder_private)
            }
            //FOLDER_TYPE_NORMAL ->
            else -> {
                mIvFolderIcon.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_joyspace_folder_normal))
                mTvHint.text = folderName
            }
        }

        //mTvHint.text = folderName

        mTvCancel.setOnClickListener { mHost.close() }

        mBtnRetry.setOnClickListener { search(mKeyword, true) }

        mEtSearch.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                super.afterTextChanged(s)

                mLayoutHint.visibility = if (TextUtils.isEmpty(s)) View.VISIBLE else View.INVISIBLE
                mTvClear.visibility = if (TextUtils.isEmpty(s)) View.GONE else View.VISIBLE

                mHandler.removeCallbacksAndMessages(null)
                mHandler.postDelayed({
                    <EMAIL>(s.toString())
                }, 200)
            }
        })


        if (mEtSearch.context.getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.P) {
            InputMethodUtils.showSoftInputFromWindow(mEtSearch.context, mEtSearch)
        }
        mEtSearch.setOnEditorActionListener listener@{ v, actionId, event ->
            // Identifier of the action. This will be either the identifier you supplied,
            // or EditorInfo.IME_NULL if being called due to the enter key being pressed.
            if (actionId == EditorInfo.IME_ACTION_SEARCH
                || actionId == EditorInfo.IME_ACTION_DONE
                || event.action == KeyEvent.ACTION_DOWN
                && event.keyCode == KeyEvent.KEYCODE_ENTER) {

                val im = mHost.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                im.hideSoftInputFromWindow(view?.windowToken, 0)

                <EMAIL>(mEtSearch.text.toString(), true)

                return@listener true
            }
            return@listener false
        }

        mTvClear.setOnClickListener {
            mEtSearch.text = null
        }

        mFilterContainer.setFilters(mSearchFiltersMap.values.toList())

        mFilterContainer.mOnResetClickListener = View.OnClickListener {
            search(mKeyword, true)
        }

        mFilterContainer.mOnFilterChangedListener = { _, _ ->
            search(mKeyword, true)
        }

        mFilterContainer.onResetVisibilityChange = {
            mDivider.visibility = it
        }

        mSortConditionButton.setOnClickListener{ showSortConditionDialog(it, mSortConditions)}

        mSelectedSortCondition = getFolderSelectedSortCondition()
        mSelectedSortCondition?.let { updateSortConditionIcon(mSelectedSortCondition!!.condition, mSelectedSortCondition!!.order) }

        mTvSearchAll.setOnClickListener {
            val uri = Uri.parse(DeepLink.UNIFIED_SEARCH)
                .buildUpon()
                .appendQueryParameter("mparam", "{\"defaultTab\": \"9\", \"keyword\": \"${mKeyword ?: ""}\"}")
                .build()
            Router.build(uri).go(mHost.activity)

            mHost.close()
        }
    }

    override fun observeState() {
        super.observeState()

        mViewModel.loadState.observe(mHost.viewLifecycleOwner) {
            if (it is JoySpaceSearchViewModel.LoadState.Refreshed) {
                mTvSearchAllTips.text = mHost.context.getString(R.string.unifiedsearch_joyspace_folder_not_wanted_result)
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle?) {
        super.onSaveInstanceState(outState)
        outState?.let {
            outState.putString(STATE_KEYWORD, mKeyword)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mSearchFiltersMap.values.forEach { it.onConfigurationChanged(newConfig) }
    }

    private fun getFolderSelectedSortCondition(): SelectedSortCondition {
        return mSortConditionStorage.getFolderSelectedSortCondition() ?: mDefaultSortCondition
    }

    override fun getSelectedSortCondition(): SelectedSortCondition? = mSelectedSortCondition

    override fun onSortConditionSelected(@SortCondition.SortType sortType: String, sortOrder: String) {
        mSelectedSortCondition = SelectedSortCondition(sortType, sortOrder)

        mSortConditionStorage.setFolderSelectedSortCondition(mSelectedSortCondition)

        updateSortConditionIcon(sortType, sortOrder)

        search(mKeyword, true)
    }

    override fun onSortConditionReset() {
        onSortConditionSelected(mDefaultSortCondition.condition, mDefaultSortCondition.order)
    }

    private fun updateSortConditionIcon(condition: String, option: String) {
        val isDefault = SortCondition.TYPE_UPDATE_TIME == condition && SortCondition.CONDITION_LATEST_FIRST == option
        val isFirst = when(option) {
            SortCondition.CONDITION_LATEST_FIRST -> true
            else -> false
        }

        if (isDefault) {
            mSortConditionButton.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_sort_condition_down_black))
            mSortConditionButton.background = null
        } else if (isFirst) {
            mSortConditionButton.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_sort_condition_down_checked))
            mSortConditionButton.background = ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_joyspace_sort_condition_button_bg)
        } else {
            mSortConditionButton.setImageDrawable(ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_sort_condition_up_checked))
            mSortConditionButton.background = ContextCompat.getDrawable(mHost.context, R.drawable.unifiedsearch_joyspace_sort_condition_button_bg)
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val result = mStartActivityFilter?.onActivityResult(requestCode, resultCode, data) ?: false
        mStartActivityFilter = null
        if (result) return
    }

    override fun startActivity(searchFilter: UnifiedSearchFilter<*>, intent: Intent?) {
        mStartActivityFilter = searchFilter
        mHost.startActivity(intent)
    }

    override fun startActivityForResult(searchFilter: UnifiedSearchFilter<*>, intent: Intent?, requestCode: Int) {
        mStartActivityFilter = searchFilter
        mHost.startActivityForResult(intent, requestCode)
    }

    override fun checkedFilterType(): String? = ""

    override fun search(keyword: String?, force: Boolean) {
        Log.d(JoySpaceSearchTabDelegate.TAG, "search, $keyword")
        //if (!force && Objects.equals(mKeyword, keyword)) return
        mKeyword = keyword

        if (TextUtils.isEmpty(keyword)) {
            mContent.visibility = View.INVISIBLE
            mTvSearchAllTips.text = mHost.context.getString(R.string.unifiedsearch_joyspace_folder_not_current)
            return
        }
        val query = SearchFilterQuery.Factory.createByFolder(teamId, folderId, mSearchFiltersMap.values, mSelectedSortCondition)
        mViewModel.refresh(keyword, null, query)
    }

    override fun onTextChanged(keyword: String?) {
        search(keyword, true)
    }

    override fun tabSelected(keyword: String?) {}

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
    }
}
