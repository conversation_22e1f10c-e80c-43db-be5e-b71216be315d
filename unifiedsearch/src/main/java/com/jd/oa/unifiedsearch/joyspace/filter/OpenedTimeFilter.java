package com.jd.oa.unifiedsearch.joyspace.filter;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.jd.oa.ui.popupwindow.MultiSelectedPopupWindow;
import com.jd.oa.ui.popupwindow.PopupItem;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.view.FilterArrow;
import com.jd.oa.unifiedsearch.joyspace.view.FilterLayout;
import com.jd.oa.unifiedsearch.util.SelectPopupWindowKt;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function2;

/**
 * 打开时间
 */
public class OpenedTimeFilter extends JoySpaceFilter<FilterOption> {

    public static final String OPENED_TIME_ALL = "all";
    public static final String OPENED_TIME_ONE_DAY = "one_day";
    public static final String OPENED_TIME_ONE_WEEK = "one_week";
    public static final String OPENED_TIME_ONE_MONTH = "one_month";
    public static final String OPENED_TIME_THREE_MONTH = "three_month";

    private FilterOption mOption;

    private FilterLayout mFilterLayout;
    private TextView mTextView;
    private MultiSelectedPopupWindow popupWindow;

    public OpenedTimeFilter(FilterContext filterContext) {
        super(filterContext);
    }

    public static List<FilterOption> openedTimes(Context context) {
        return new ArrayList<FilterOption>(){{
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_time_option_no_limit), OPENED_TIME_ALL));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_time_option_one_day), OPENED_TIME_ONE_DAY));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_time_option_one_week), OPENED_TIME_ONE_WEEK));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_time_option_one_month), OPENED_TIME_ONE_MONTH));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_time_option_three_month), OPENED_TIME_THREE_MONTH));
        }};
    }

    @Override
    protected View onCreateView(Context context, ViewGroup container) {
        mFilterLayout = (FilterLayout) LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_filter_item, container, false);
        mTextView = mFilterLayout.findViewById(R.id.tv_text);
        mTextView.setText(R.string.unifiedsearch_joyspace_filter_opened_time);

        FilterArrow arrow = mFilterLayout.findViewById(R.id.arrow);
        arrow.observeTo(mFilterLayout);

        return mFilterLayout;
    }

    @Override
    public void onFilterClick(AppCompatActivity activity, View view) {

        InputMethodManager im = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (im.isActive()) {
            im.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }

        view.postDelayed(new Runnable() {
            @Override
            public void run() {
                List<FilterOption> options = openedTimes(activity);

                FilterOption selected = mOption;
                if (selected == null) {
                    selected = getOption(options, OPENED_TIME_ALL);
                }

                popupWindow = new MultiSelectedPopupWindow(
                        activity,
                        SelectPopupWindowKt.filterOptionsToPopupItems(options, selected),
                        false,
                        Collections.singletonList(SelectPopupWindowKt.filterOptionToPopupItem(getOption(options, OPENED_TIME_ALL), false)),
                        new Function2<List<PopupItem>, Boolean, Unit>() {
                            @Override
                            public Unit invoke(List<PopupItem> popupItems, Boolean aBoolean) {
                                if (popupItems.isEmpty()) {
                                    mOption = getOption(options, OPENED_TIME_ALL);
                                } else {
                                    mOption = new FilterOption(popupItems.get(0).getText(), popupItems.get(0).getValue());
                                }
                                if (OPENED_TIME_ALL.equals(mOption.getValue())) {
                                    reset();
                                } else {
                                    setSelected(true);
                                    mTextView.setText(mOption.getText());
                                    mFilterLayout.select();
                                }

                                if (mOnChangedListener != null) {
                                    mOnChangedListener.onChanged(mOption);
                                }
                                return null;
                            }
                        },
                        new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                mFilterLayout.close();
                                popupWindow = null;
                                return null;
                            }
                        }
                );
                popupWindow.show(view);

                mFilterLayout.open();
            }
        }, 100);    //等待键盘隐藏
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.onConfigurationChanged(newConfig);
        }
    }

    @Override
    public void reset() {
        mTextView.setText(R.string.unifiedsearch_joyspace_filter_opened_time);

        mOption = null;
        mFilterLayout.reset();
        super.reset();
    }

    @Override
    public FilterOption getValue() {
        return mOption;
    }

    @Override
    public void onSaveState(String type, Bundle outState) {
        super.onSaveState(type, outState);
        if (mOption != null) {
            outState.putParcelable(type, mOption);
        }
    }

    @Override
    public void onRestoreState(String type, Bundle savedState) {
        super.onRestoreState(type, savedState);
        mOption = savedState.getParcelable(type);
        if (mOption != null) {
            setSelected(true);
            mTextView.setText(mOption.getText());
        }
    }

    private FilterOption getOption(List<FilterOption> list, String type) {
        for (int i = 0; i < list.size(); i++) {
            FilterOption option = list.get(i);
            if (option.getValue().equals(type)) {
                return option;
            }
        }
        return null;
    }
}
