package com.jd.oa.unifiedsearch.joyspace.filter;

import androidx.annotation.StringDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

public abstract class JoySpaceFilter<T> extends UnifiedSearchFilter<T> {

    public static final String TYPE_RELATED = "related";    //与我相关
    public static final String TYPE_RECENT = "recent";      //最近打开
    public static final String TYPE_RECEIVED = "received";  //我收到的
    public static final String TYPE_SENT = "sent";          //我发送的
    public static final String TYPE_CREATED = "created";    //我创建的

    public static final String TYPE_PUBLIC = "public";    //全部公开

    @StringDef({TYPE_RELATED, TYPE_RECENT, TYPE_RECEIVED, TYPE_SENT, TYPE_CREATED, TYPE_PUBLIC})
    @Retention(RetentionPolicy.SOURCE)
    @Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD, ElementType.TYPE_PARAMETER})
    public @interface FilterType {}

    @Deprecated
    public static final String SUBTYPE_CREATED_BY_MYSELF = "created_by_myself";
    public static final String SUBTYPE_CREATE_BY_OTHERS = "create_by_others";
    public static final String SUBTYPE_DOCUMENT_TYPE = "document_type";
    public static final String SUBTYPE_OPENED_TIME = "opened_time";
    public static final String SUBTYPE_SENDER = "sender";
    public static final String SUBTYPE_RECEIVER = "receiver";

    @StringDef({SUBTYPE_CREATED_BY_MYSELF,
            SUBTYPE_CREATE_BY_OTHERS,
            SUBTYPE_DOCUMENT_TYPE,
            SUBTYPE_OPENED_TIME,
            SUBTYPE_SENDER,
            SUBTYPE_RECEIVER})
    @Retention(RetentionPolicy.SOURCE)
    @Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
    public @interface FilterSubType{}

    public JoySpaceFilter(FilterContext filterContext) {
        super(filterContext);
    }
}