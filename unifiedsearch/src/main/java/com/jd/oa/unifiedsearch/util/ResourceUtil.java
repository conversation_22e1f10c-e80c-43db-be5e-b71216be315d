package com.jd.oa.unifiedsearch.util;

import android.content.Context;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;

/*
 * Time: 2023/10/19
 * Author: qudongshi
 * Description:
 */
public class ResourceUtil {

    public static String getSectionHeaderRes(Context context, SearchFloorType searchFloorType) {

        switch (searchFloorType) {
            case MESSAGE:
                return context.getResources().getString(R.string.me_all_search_section_title_chat_history);
            case CONTACT:
                return context.getResources().getString(R.string.me_all_search_section_title_contact);
            case ROBOT:
                return context.getResources().getString(R.string.me_all_search_section_title_robot);
            case GROUP:
                return context.getResources().getString(R.string.me_all_search_section_title_group);
            case NOTICE:
                return context.getResources().getString(R.string.me_all_search_section_title_notice);
            case WAITER:
                return context.getResources().getString(R.string.me_all_search_section_title_waiter);
            case APP:
                return context.getResources().getString(R.string.me_all_search_section_title_app);
            case TASK:
                return context.getResources().getString(R.string.me_all_search_section_title_task);
            case JOY_SPACE:
                return context.getResources().getString(R.string.me_all_search_section_title_docs);
            case PROCESS:
                return context.getString(R.string.unifiedsearch_tab_process);
            case APPROVAL:
                return context.getString(R.string.unifiedsearch_tab_approval);
            case ORG:
                return context.getString(R.string.me_search_tab_org);
            default:
                return "";
        }
    }
}
