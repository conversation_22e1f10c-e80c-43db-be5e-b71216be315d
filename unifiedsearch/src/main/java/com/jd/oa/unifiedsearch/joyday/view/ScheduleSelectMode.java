package com.jd.oa.unifiedsearch.joyday.view;

public enum ScheduleSelectMode {
    NONE, SINGLE, MULTIPLE;

    public static ScheduleSelectMode of(String value) {
        if (NONE.toString().equalsIgnoreCase(value)) {
            return NONE;
        } else if (SINGLE.toString().equalsIgnoreCase(value)) {
            return SINGLE;
        } else if (MULTIPLE.toString().equalsIgnoreCase(value)) {
            return MULTIPLE;
        } else {
            throw new IllegalArgumentException("Unknown mode: " + value);
        }
    }
}