package com.jd.oa.unifiedsearch.joyspace;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;
import com.jd.oa.unifiedsearch.joyday.utils.TextUtil;
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition;

public class JoySpaceSearchPreference extends AbsKvEntities implements SortConditionStorage {

    private static final String NAME = "JoySpaceSearch";

    private static volatile JoySpaceSearchPreference sInstance;

    public static KvEntity<String> sFolderSelectedCondition = new KvEntity<>("joyspace_folder_selected_sort_condition", "");

    public static JoySpaceSearchPreference getInstance() {
        if (sInstance == null) {
            synchronized (JoySpaceSearchPreference.class) {
                if (sInstance == null) {
                    sInstance = new JoySpaceSearchPreference();
                }
            }
        }
        return sInstance;
    }

    @Override
    public String getPrefrenceName() {
        return NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }


    public void setSelectedSortCondition(@JoySpaceFilter.FilterType String type, SelectedSortCondition condition) {
        KvEntity<String> entity = new KvEntity<>(type, "");
        put(entity, JSON.toJSONString(condition));
    }

    @Nullable
    public SelectedSortCondition getSelectedSortCondition(@JoySpaceFilter.FilterType String type) {
        KvEntity<String> entity = new KvEntity<>(type, "");
        String selected = get(entity);
        if (TextUtils.isEmpty(selected)) return null;
        try {
            SelectedSortCondition condition = JSON.parseObject(selected, SelectedSortCondition.class);
            if (TextUtils.isEmpty(condition.getCondition()) || TextUtils.isEmpty(condition.getOrder())) {
                return null;
            } else {
                return condition;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public void setFolderSelectedSortCondition(SelectedSortCondition condition) {
        put(sFolderSelectedCondition, JSON.toJSONString(condition));
    }

    @Nullable
    public SelectedSortCondition getFolderSelectedSortCondition() {
        String folderSelected = get(sFolderSelectedCondition);
        if (TextUtils.isEmpty(folderSelected)) return null;
        return JSON.parseObject(folderSelected, SelectedSortCondition.class);
    }
}
