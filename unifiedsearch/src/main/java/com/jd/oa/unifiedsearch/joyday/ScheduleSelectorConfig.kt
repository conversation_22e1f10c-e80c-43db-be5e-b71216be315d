package com.jd.oa.unifiedsearch.joyday

import androidx.annotation.Keep
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel
import java.util.Objects

@Keep
class ScheduleSelectorConfig(
    val title: String? = null,
    val dateRange: DateRange? = null,
    var calendar: Boolean = false,
    var members: Boolean = false,
    val timeDivider: Boolean = true,
    var selectType: String? = "single",
    val selected: MutableList<ScheduleModel>? = null,
    val disableSelected: Boolean = false,
    val organizerList: List<UserEntity>? = null,
    val user: User? = null,
    val maxNum: Int = 0,
    val sortOrder: String = ORDER_ASC,
    var category: Category? = null,
    var properties: List<String>? = null,
    val showCreate: Boolean = false,
    val showSearch: Boolean = true,
    var from: String? = null,
    val permissionCode: List<String>? = null,
) {
    companion object {
        const val ORDER_ASC = "asc"
        const val ORDER_DESC = "desc"

        const val PROPERTY_ORGANIZER = "organizer"
        const val PROPERTY_LOCATION = "location"
        const val PROPERTY_CALENDAR = "calendar"
        const val PROPERTY_ROOM = "rooms"
        const val PROPERTY_SUBCATEGORY = "subcategory"
    }

    init {
        if (properties != null && properties!!.size > 2) {
            properties = properties!!.subList(0, 2)
        }
    }

    fun isOrganizerEnable(): Boolean {
        if (properties != null && properties!!.isNotEmpty()) {
            return properties!!.contains(PROPERTY_ORGANIZER)
        }
        return true
    }

    fun isCalendarEnable(): Boolean {
        if (properties != null && properties!!.isNotEmpty()) {
            return properties!!.contains(PROPERTY_CALENDAR)
        }
        if (calendar) {
            return true
        }
        return false
    }

    fun isLocationEnable(): Boolean {
        if (properties != null && properties!!.isNotEmpty()) {
            return properties!!.contains(PROPERTY_LOCATION)
        }
        if (calendar) {
            return false
        }
        return true
    }

    fun isRoomEnable(): Boolean {
        if (properties != null && properties!!.isNotEmpty()) {
            return properties!!.contains(PROPERTY_ROOM)
        }
        return false
    }

    fun isSubcategoryEnable(): Boolean {
        if (properties != null && properties!!.isNotEmpty()) {
            return properties!!.contains(PROPERTY_SUBCATEGORY)
        }
        return false
    }

    @Keep
    class DateRange {
        val readOnly: Boolean = false
        val startTime: Long? = null
        val endTime: Long? = null
        val minStartTime: Long? = null
        val maxEndTime: Long? = null
        val maxDayRange: Long? = null
    }
    @Keep
    class User {
        val readOnly: Boolean = false
        val userList: List<UserEntity>? = null
    }
    @Keep
    class UserEntity {
        val emplAccount: String? = null
        val ddAppId: String? = null
        val realName: String? = null
        val headImg: String? = null
        fun toMemberEntityJd(): MemberEntityJd {
            val entityJd = MemberEntityJd()
            entityJd.id = emplAccount
            entityJd.app = ddAppId
            entityJd.name = realName
            entityJd.avatar = headImg
            return entityJd
        }

        override fun equals(other: Any?): Boolean {
            if (other is UserEntity) {
                return this.emplAccount == other.emplAccount && this.ddAppId == other.ddAppId
            } else if (other is MemberEntityJd) {
                return this.emplAccount == other.id && this.ddAppId == other.app
            }
            return false
        }

        override fun hashCode(): Int = Objects.hash(this.ddAppId, this.emplAccount)
    }

    @Keep
    class Category(
        val readOnly: Boolean = false,
        val categoryList: List<String> = emptyList(),
        val show: Boolean = false
    ) {
        companion object {
            const val APPOINTMENT = "APPOINTMENT"
            const val MEETING = "MEETING"
        }

        val isAll get() = categoryList.contains(APPOINTMENT) && categoryList.contains(MEETING)
        val hasAppointment get() = categoryList.contains(APPOINTMENT)
        val hasMeeting get() = categoryList.contains(MEETING)
    }
}