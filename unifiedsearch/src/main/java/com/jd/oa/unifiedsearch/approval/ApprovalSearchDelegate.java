package com.jd.oa.unifiedsearch.approval;

import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfigWrapper;
import com.jd.oa.unifiedsearch.all.helper.SearchFilterConfigHelper;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.approval.data.ApprovalInfo;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.List;

public abstract class ApprovalSearchDelegate extends UnifiedSearchTabDelegate {
    protected static final String STATE_KEYWORD = "keyword";
    protected RecyclerView mRecyclerView;
    protected View mLayoutPlaceholder;
    protected View mLayoutEmpty;
    protected View mLayoutLoading;
    protected View mLoadingIndicator;
    protected View mLayoutError;
    protected Button mBtnRetry;
    protected TextView mTvEmpty;

    protected ApprovalSearchAdapter mApprovalSearchAdapter;
    protected ApprovalSearchViewModel mViewModel;
    protected Animation mLoadingAnimation;

    protected String mKeyword;

    protected TextView mTvFeedback;

    public ApprovalSearchDelegate(Host host) {super(host);}

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mViewModel = new ViewModelProvider((ViewModelStoreOwner) mHost.getActivity()).get(ApprovalSearchViewModel.class);
        SearchFilterConfigHelper.getInstance().initSearchConfig(mHost.getContext());
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.unifiedsearch_fragment_approval,container,false);
        if (savedInstanceState != null) {
            mKeyword = savedInstanceState.getString(STATE_KEYWORD);
        }
        return view;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        this.initView(mHost.requireView(), savedInstanceState);
    }

    protected void initView(View view, @Nullable Bundle savedInstanceState) {
        mRecyclerView = view.findViewById(R.id.recycle_view);
        mLayoutPlaceholder = view.findViewById(R.id.layout_placeholder);
        mLayoutEmpty = view.findViewById(R.id.me_pub_empty_view);
        mLayoutLoading = view.findViewById(R.id.layout_loading);
        mLoadingIndicator = view.findViewById(R.id.iv_loading_indicator);
        mLayoutError = view.findViewById(R.id.layout_error);
        mBtnRetry = view.findViewById(R.id.btn_retry);
        mTvEmpty = view.findViewById(R.id.tv_empty);

        // 反馈
        mTvFeedback = view.findViewById(R.id.tv_conent);
        mTvFeedback.setMovementMethod(LinkMovementMethod.getInstance());
        mTvFeedback.setText(SearchUtil.getFeedbackContent());

        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(mHost.getContext());
        mRecyclerView.setLayoutManager(layoutManager);

        mApprovalSearchAdapter = new ApprovalSearchAdapter(mHost.getContext());
        mApprovalSearchAdapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                ApprovalInfo approvalInfo = (ApprovalInfo) adapter.getItem(position);
                if (approvalInfo.refPro.auditMobileDeeplink != null && !approvalInfo.refPro.auditMobileDeeplink.isEmpty()) {
                    JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Approval_ck,
                            JDMAConstants.Mobile_Event_UnifiedSearch_Approval_ck);
                    String deeplink = approvalInfo.refPro.auditMobileDeeplink;
                    Router.build(deeplink).go(mHost.getContext());
                }
            }
        });
        mRecyclerView.setAdapter(mApprovalSearchAdapter);

        mLoadingAnimation = AnimationUtils.loadAnimation(mHost.getContext(), R.anim.unifiedsearch_loading_indicator);
        mViewModel.getApprovalInfoList().observe(mHost.getViewLifecycleOwner(), new Observer<Pair<String, List<ApprovalInfo>>>() {
            @Override
            public void onChanged(Pair<String, List<ApprovalInfo>> result) {
                if (CollectionUtil.isEmptyOrNull(result.second)) {
                    mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                    mLayoutEmpty.setVisibility(View.VISIBLE);
                    mRecyclerView.setVisibility(View.INVISIBLE);
                    mTvFeedback.setVisibility(View.VISIBLE);
                } else {
                    mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                    mLayoutEmpty.setVisibility(View.GONE);
                    mTvFeedback.setVisibility(View.GONE);
                    mRecyclerView.setVisibility(View.VISIBLE);
                }
                mApprovalSearchAdapter.refresh(result.second);
                mLayoutError.setVisibility(View.INVISIBLE);
            }
        });
        mViewModel.getLoading().observe(mHost.getViewLifecycleOwner(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                Pair<String, List<ApprovalInfo>> value = mViewModel.getApprovalInfoList().getValue();
                if (aBoolean && (value == null || CollectionUtil.isEmptyOrNull(value.second))) {
                    showLoading();
                } else {
                    hideLoading();
                }
            }
        });

        mViewModel.getError().observe(mHost.getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                mLayoutEmpty.setVisibility(View.INVISIBLE);
                mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                mRecyclerView.setVisibility(View.INVISIBLE);
                mLayoutError.setVisibility(View.VISIBLE);
            }
        });
    }

    protected void restoreInstanceState(Bundle savedState) {}

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (outState != null) {
            outState.putString(STATE_KEYWORD, mKeyword);
        }
    }

    private void showLoading() {
        mLoadingAnimation.cancel();
        mLoadingAnimation.reset();

        mLoadingIndicator.startAnimation(mLoadingAnimation);
        mLayoutLoading.setVisibility(View.VISIBLE);
    }

    private void hideLoading() {
        mLayoutLoading.setVisibility(View.INVISIBLE);
    }
}
