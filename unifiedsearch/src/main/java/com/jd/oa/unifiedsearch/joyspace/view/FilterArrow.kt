package com.jd.oa.unifiedsearch.joyspace.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import android.widget.ImageView
import com.jd.oa.unifiedsearch.R

class FilterArrow @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0): FrameLayout(context, attrs, defStyleAttr, defStyleRes) {

    private val mIvArrow: ImageView by lazy { findViewById(R.id.iv_arrow) }
    private var mState: State = State.DOWN

    init {
        LayoutInflater.from(context).inflate(R.layout.unifiedsearch_filter_arrow, this)
    }

    fun down() {
        if (mState == State.DOWN) return

        val rotateAnimation = AnimationUtils.loadAnimation(context, R.anim.unifiedsearch_filter_arrow_down)
        mIvArrow.startAnimation(rotateAnimation)

        this.mState = State.DOWN
    }

    fun up() {
        if (mState == State.UP) return

        val rotateAnimation = AnimationUtils.loadAnimation(context, R.anim.unifiedsearch_filter_arrow_up)
        mIvArrow.startAnimation(rotateAnimation)

        this.mState = State.UP
    }

    fun observeTo(filterLayout: FilterLayout) {
        filterLayout.stateChangeListener = {
            when(it) {
                FilterState.Opened -> {
                    up()
                }
                else -> {
                    down()
                }
            }
        }
    }

    enum class State {
        UP, DOWN
    }
}