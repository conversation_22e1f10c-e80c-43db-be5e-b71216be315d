package com.jd.oa.unifiedsearch.joyday.view;

import android.content.Context;
import android.text.method.LinkMovementMethod;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.pei.pulluploadhelper.LoadingIndicator;

public class ScheduleSearchIndicator extends FrameLayout implements LoadingIndicator {

    private View mView;
    private TextView mTextView;

    public ScheduleSearchIndicator(Context context) {
        this(context, null);
    }

    public ScheduleSearchIndicator(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScheduleSearchIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mView = LayoutInflater.from(context).inflate(R.layout.unifiedsearch_schedule_search_list_footer, this);
        mTextView = findViewById(R.id.tv_feedback);
        mTextView.setVisibility(INVISIBLE);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        if (layoutParams != null) {
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            setLayoutParams(layoutParams);
        }
    }

    @Override
    public void setEmpty() {
        mTextView.setVisibility(GONE);
    }

    @Override
    public void setLoading() {
        mTextView.setVisibility(GONE);
    }

    @Override
    public void setLoaded() {
        mTextView.setVisibility(GONE);
    }

    @Override
    public void setComplete() {
        mTextView.setVisibility(VISIBLE);
        mTextView.setText(SearchUtil.getFeedbackContent());
        mTextView.setMovementMethod(LinkMovementMethod.getInstance());
    }
}
