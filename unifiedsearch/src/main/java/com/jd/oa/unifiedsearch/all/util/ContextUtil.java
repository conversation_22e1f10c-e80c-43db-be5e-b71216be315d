package com.jd.oa.unifiedsearch.all.util;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.DisplayMetrics;

import com.jd.oa.unifiedsearch.util.SearchLogUitl;
import com.jd.oa.utils.LocaleUtils;

import java.util.Locale;

/*
 * Time: 2023/12/4
 * Author: qudongshi
 * Description:
 */
public class ContextUtil {

    public static Context applyLanguage(Context context) {
        try {
            Locale locale = LocaleUtils.getUserSetLocale(context) == null ? LocaleUtils.getSystemLocale() : LocaleUtils.getUserSetLocale(context);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {//v24或以上，需要将指定的语言绑定到上下文然后再attachBaseContext（）
                Configuration configuration = context.getResources().getConfiguration();
                configuration.setLocale(locale);
                Context myContext = context.createConfigurationContext(configuration);
                return myContext;
            } else {//v24以下，直接将指定的语言绑定到资源不需要attachBaseContext（）
                Resources resource = context.getResources();
                Configuration configuration = resource.getConfiguration();
                configuration.locale = locale;
                DisplayMetrics dm = resource.getDisplayMetrics();
                resource.updateConfiguration(configuration, dm);
                return context;
            }
        } catch (Exception e) {
            SearchLogUitl.LogE("ContextUtil", "applyLanguage exception", e);
        }
        return context;
    }
}
