package com.jd.oa.unifiedsearch.all.viewholder;

import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;

/*
 * Time: 2023/10/26
 * Author: qudongshi
 * Description:
 */
public class HeaderViewHolder extends RecyclerView.ViewHolder {
    public TextView tvTitle;

    public HeaderViewHolder(@NonNull View itemView) {
        super(itemView);
        tvTitle = itemView.findViewById(R.id.section_title);
    }
}