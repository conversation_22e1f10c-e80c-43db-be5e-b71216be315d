package com.jd.oa.unifiedsearch.all.data;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.AppBase;
import com.jd.oa.utils.LocaleUtils;

import java.util.List;
import java.util.Map;

public class SearchFilterConfig {
    @SerializedName("Approval")
    public Approval approval;

    public static class Approval {
        @SerializedName("filter")
        public List<String> filter;
        @SerializedName("filterItems")
        public Map<String, FilterItem> filterItems;
    }

    public static class FilterItem {
        public String filterItemKey;
        @SerializedName("labelType")
        public String labelType;
        @SerializedName("reqKey")
        public String reqKey;
        @SerializedName("options")
        public List<Option> options;
    }

    public static class Option {
        @SerializedName("defaultTitle")
        public DefaultTitle defaultTitle;
        @SerializedName("title")
        public Title title;
        @SerializedName("reqValue")
        public Object reqValue;
        @SerializedName("action")
        public String action;
        @SerializedName("selected")
        public boolean selected;
        @SerializedName("excludeOption")
        public boolean excludeOption;
        @SerializedName("subFilterItems")
        public List<String> subFilterItems;
    }

    public static class DefaultTitle {
        @SerializedName("zh")
        public String zh;
        @SerializedName("en")
        public String en;

        public String getText() {
            if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")) {
                return zh == null ? "" : zh;
            } else {
                return en == null ? "" : en;
            }
        }
    }

    public static class Title {
        @SerializedName("zh")
        public String zh;
        @SerializedName("en")
        public String en;

        public String getText() {
            if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")) {
                return zh == null ? "" : zh;
            } else {
                return en == null ? "" : en;
            }
        }
    }
}
