package com.jd.oa.unifiedsearch;

import android.os.Bundle;
import android.text.Editable;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.jd.oa.BaseActivity;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.TextWatcherAdapter;

import java.util.Arrays;
import java.util.List;

public class UnifiedSearchActivity extends BaseActivity {

    private EditText mEditText;
    private TabLayout mTabLayout;
    private ViewPager2 mViewPager;

    private List<Fragment> mFragments;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.unifiedsearch_activity_main);

        mEditText = findViewById(R.id.et_search);
        mTabLayout = findViewById(R.id.tab_layout);
        mViewPager = findViewById(R.id.viewPager);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }

        ImDdService imDdService = AppJoint.service(ImDdService.class);

        mFragments = Arrays.asList(imDdService.getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_APP), imDdService.getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_JOYSPACE));
        MainPagerAdapter adapter = new MainPagerAdapter(this, mFragments);

        mViewPager.setAdapter(adapter);

        TabLayoutMediator mediator = new TabLayoutMediator(mTabLayout, mViewPager, new TabLayoutMediator.TabConfigurationStrategy() {
            @Override
            public void onConfigureTab(@NonNull TabLayout.Tab tab, int i) {
                UnifiedSearchTab searchTab = (UnifiedSearchTab) mFragments.get(i);
                tab.setText(searchTab.getTitle());
            }
        });
        mediator.attach();

        mEditText.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        mEditText.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    Fragment fragment = mFragments.get(mTabLayout.getSelectedTabPosition());
                    if (fragment instanceof UnifiedSearchTab) {
                        ((UnifiedSearchTab) fragment).search(mEditText.getText().toString());
                    }
                    return true;
                }
                return false;
            }
        });

        findViewById(R.id.btn_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
}