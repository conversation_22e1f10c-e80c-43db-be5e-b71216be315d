package com.jd.oa.unifiedsearch.all.section;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.callback.ISectionEventCallback;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchHelper;
import com.jd.oa.unifiedsearch.all.util.SearchHistoryUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class AIRecommendSection extends BaseSection {
    private Context mContext;
    LayoutInflater layoutInflater;
    private List<String> data;
    private ISectionEventCallback eventCallback;
    public AIRecommendSection(Context context, List<String> data, ISectionEventCallback callback) {
        super(SectionParameters.builder().itemResourceId(R.layout.unifiedsearch_section_ai_recommend).build(), "");
        this.mContext = context;
        this.layoutInflater = LayoutInflater.from(mContext);
        this.data = data;
        this.eventCallback = callback;
    }
    @Override
    public int getContentItemsTotal() {
        return 1;
    }
    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new ContentViewHolder(view);
    }
    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        if (data == null) {
            return;
        }
        AIRecommendSection.ContentViewHolder holder = (AIRecommendSection.ContentViewHolder) viewHolder;
        if (holder.container != null && holder.container.getChildCount() > 0) {
            holder.container.removeAllViews();
        }
        for (int index = 0; index < data.size(); index++) {
            String str = data.get(index);
            TextView tvDoc = (TextView) layoutInflater.inflate(R.layout.unifiedsearch_section_recommend_content_info_ai_item, holder.container, false);
            tvDoc.setText(str);
            final int indexCopy = index;
            tvDoc.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    SearchConfigHelper.getInstance().getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            eventCallback.onItemClick(str);
                            SearchHistoryUtil.addSearchHistory(str);
                            HashMap<String, String> params = new HashMap<>();
                            params.put("pos", "" + indexCopy);
                            JDMAUtils.clickEvent(JDMAConstants.Mobile_Page_MEAI_Main_Home, JDMAConstants.Mobile_Event_MEAI_Search_Main_QA_ck, params);
                            // 打开MEAI
                            if (mContext != null) {
                                SearchHelper.openMEAI(mContext, str);
                            }
                        }
                    });
                }
            });
            holder.container.addView(tvDoc);
        }
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {
    }
    private class ContentViewHolder extends RecyclerView.ViewHolder {
        public LinearLayout container;
        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
            container = itemView.findViewById(R.id.ai_recommendation_container);
        }
    }
}