package com.jd.oa.unifiedsearch.joyspace.utils;

import android.content.Context
import android.net.Uri
import android.text.TextUtils
import android.util.Log
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.router.DeepLink.JDME
import com.jd.oa.unifiedsearch.joyspace.JoySpaceSearchDelegate
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument
import com.jd.oa.utils.LocaleUtils
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

const val JOYSPACE_APP_ID = "201909020601"

fun generateDetailDeeplink(context: Context, document: JoySpaceDocument): String {
//    val routeTag = when(document.pageType) {
//        JoySpaceDocument.PAGE_TYPE_REPORTING -> "report_view"
//        JoySpaceDocument.PAGE_TYPE_NOTE, JoySpaceDocument.PAGE_TYPE_DOCUMENT -> "document_edit"
//        JoySpaceDocument.PAGE_TYPE_SPREADSHEET -> "spreadsheet_edit"
//        JoySpaceDocument.PAGE_TYPE_WEB_SPREAD -> "websheet_edit"
//        JoySpaceDocument.PAGE_TYPE_FILE -> "file_view"
//        JoySpaceDocument.PAGE_TYPE_NEW_SPREAD_SHEET -> "sheet_edit"
//        else -> ""
//    }

    val isFolder = document.pageType == JoySpaceDocument.PAGE_TYPE_FOLDER

    val routeTag = if (isFolder) {
        if (document.teamId != null && document.teamId.startsWith("$")) "my_space_folder" else  "team_space_folder"
    } else {
        "doc_edit_type_${if (document.pageType == JoySpaceDocument.PAGE_TYPE_DOCUMENT_SHORTCUT) document.originPageType else document.pageType}"
    }

    //isMaster iPad分栏模式下，当前打开的页面是否需要在左侧master push显示，'0'->否，'1'->是
    val isMater = "0"

    val pageId = if (document.pageType == JoySpaceDocument.PAGE_TYPE_DOCUMENT_SHORTCUT) {
        document.originId
    } else {
        if (!TextUtils.isEmpty(document.pageId)) document.pageId else document.id
    }

    val builder = Uri.parse(JDME + "rn/$JOYSPACE_APP_ID")
        .buildUpon()
        .appendQueryParameter("routeTag", routeTag)
        .appendQueryParameter("isMaster", isMater)
        .appendQueryParameter("page_id", pageId)
        .appendQueryParameter("language", LocaleUtils.getUserSetLocaleStr(context))
        .appendQueryParameter("standalone", "true")

    if (isFolder) {
        builder.appendQueryParameter("folderId", document.folderId)
        builder.appendQueryParameter("teamId", document.teamId)
    }

    return builder.build().toString()
}

fun joySpaceLog(url: String) {
    if (TextUtils.isEmpty(url)) {
        return
    }
    val request = Request.Builder().url(url).get().build()
    //只支持get请求 没有办法
    val call = HttpManager.getHttpClient().newCall(request)
    call.enqueue(object : Callback {
        override fun onFailure(call: Call, e: IOException) {
        }

        override fun onResponse(call: Call, response: Response) {
            Log.e(JoySpaceSearchDelegate.TAG, "onResponse: " + response.body())
        }
    })
}