package com.jd.oa.unifiedsearch.task;

import android.content.Context;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.utils.HighlightText;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class TaskSearchAdapter extends BaseRecyclerAdapter<TaskModel, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;
    private int mHighlightColor;
    private final String mHighlightTag = "em";
    private final String mStartTag = "<" + mHighlightTag + ">";
    private final String mEndTag = "</" + mHighlightTag + ">";

    private boolean showFeedback = false;

    private int VIEW_TYPE_ITEM = 0;
    private int VIEW_TYPE_FEEDBACK = 1;

    public TaskSearchAdapter(Context context) {
        super(context);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_color_1869F5);
    }

    public TaskSearchAdapter(Context context, List<TaskModel> data) {
        super(context, data);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_color_1869F5);
    }

    @Override
    public int getItemCount() {
        if (super.getItemCount() > 0 && showFeedback) {
            return super.getItemCount() + 1;
        }
        return super.getItemCount();
    }

    @Override
    public int getItemViewType(int index) {
        if (showFeedback) {
            if (index < getItemCount() - 1) {
                return VIEW_TYPE_ITEM;
            } else {
                return VIEW_TYPE_FEEDBACK;
            }
        } else {
            return VIEW_TYPE_ITEM;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (VIEW_TYPE_ITEM == viewType) {
            return new ViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_recycler_item_task, viewGroup, false));
        } else {
            return new FeedbackViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_pub_feedback_content, viewGroup, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {
        if (viewHolder instanceof ViewHolder) {
            ViewHolder holder = (ViewHolder) viewHolder;
            TaskModel taskInfo = getItem(i);
            ImageLoader.load(getContext(), holder.image, taskInfo.taskIcon, R.drawable.jdme_ic_app_default);
            HighlightText.with(taskInfo.title).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.title);
            if (taskInfo.remark == null || TextUtils.isEmpty(taskInfo.remark.trim())) {
                holder.remark.setVisibility(View.GONE);
            } else {
                HighlightText.with(taskInfo.remark).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.remark);
            }
            String desc = taskInfo.buildDec(getContext());
            HighlightText.with(desc).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.desc);
        } else if (viewHolder instanceof FeedbackViewHolder) {
            FeedbackViewHolder holder = (FeedbackViewHolder) viewHolder;
            holder.tvContent.setVisibility(View.VISIBLE);
            holder.tvContent.setText(SearchUtil.getFeedbackContent());
        }
    }

    public void showFeedback() {
        showFeedback = true;
    }


    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        ImageView image;
        TextView title;
        TextView remark;
        TextView desc;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            image = itemView.findViewById(R.id.iv_image);
            title = itemView.findViewById(R.id.tv_title);
            remark = itemView.findViewById(R.id.tv_remark);
            desc = itemView.findViewById(R.id.tv_desc);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        int position = getBindingAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) return;
                        mOnItemClickListener.onItemClick(TaskSearchAdapter.this, v, position);
                    }
                }
            });
        }
    }

    class FeedbackViewHolder extends RecyclerView.ViewHolder {

        public TextView tvContent;

        public FeedbackViewHolder(@NonNull View itemView) {
            super(itemView);
            tvContent = itemView.findViewById(R.id.tv_conent);
            tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }
}
