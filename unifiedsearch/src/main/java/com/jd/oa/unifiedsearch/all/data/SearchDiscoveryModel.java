package com.jd.oa.unifiedsearch.all.data;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.util.List;

@Keep
public class SearchDiscoveryModel {
    @SerializedName("list")
    public List<DiscoverDataItem> dataItems;

    public class DiscoverDataItem {
        public String actionType;
        public String pcUrl;
        public String contentId;
        public String mobileUrl;
        public String iconUrl;
        public String text;
    }
}
