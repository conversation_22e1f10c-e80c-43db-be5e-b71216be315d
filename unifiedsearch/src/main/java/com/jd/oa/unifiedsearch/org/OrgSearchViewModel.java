package com.jd.oa.unifiedsearch.org;

import android.text.TextUtils;
import android.util.Pair;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.unifiedsearch.all.helper.SearchAnalyzeHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.utils.JsonUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class OrgSearchViewModel extends ViewModel {
    private MutableLiveData<Boolean> mLoading = new MutableLiveData<>(false);
    private MutableLiveData<String> mLoadMore = new MutableLiveData<>();
    private MutableLiveData<Pair<String, List<OrgRecord>>> mOrgRecords = new MutableLiveData<>();
    private MutableLiveData<String> mError = new MutableLiveData<>();

    public static final String LOAD_MORE_LOADING = "loading";
    public static final String LOAD_MORE_COMPLETE = "complete";
    public static final String LOAD_MORE_LOADED = "loaded";
    public static final String LOAD_MORE_EMPTY = "empty";
    private final int PAGE_SIZE = 20;
    private int mPageOffset = 0;

    private String mKeyword;

    public void refresh(String keyword, String sessionId) {
        mLoading.setValue(true);
        mKeyword = keyword;

        //搜索关键字为空
        if (TextUtils.isEmpty(keyword)) {
            mOrgRecords.setValue(Pair.create(keyword, Collections.emptyList()));
            mLoading.setValue(false);
            return;
        }

        String requestId = System.currentTimeMillis() + "";
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.ORG.toString(), false);
        NetWorkManager.getProcessSearchResult(null, sessionId, keyword,0, PAGE_SIZE, Arrays.asList(SearchFloorType.ORG.toString()), new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.ORG.toString(), dataLength + "");
                super.onFailure(exception, info);
                mLoading.setValue(false);
                mOrgRecords.setValue(Pair.create(keyword, Collections.emptyList()));
                mError.setValue(exception != null ? exception.getMessage() : info != null ? info : "network issue, please try again");
                mLoadMore.setValue(LOAD_MORE_EMPTY);
            }

            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.ORG.toString(), dataLength + "");
                super.onSuccess(response);
                mLoading.setValue(false);
                if (response.isSuccessful()) {
                    //接口返回成功
                    if (Objects.equals(mKeyword, keyword)) {
                        String info = response.getData(String.class);
                        OrgSearchModel orgSearchModel = JsonUtils.getGson().fromJson(info, new TypeToken<OrgSearchModel>() {}.getType());
                        if(orgSearchModel == null || orgSearchModel.docs == null
                                || orgSearchModel.docs.isEmpty() || orgSearchModel.docs.get(0).records.isEmpty()) {
                            //搜索结果为空
                            mOrgRecords.setValue(Pair.create(keyword, Collections.emptyList()));
                            mLoadMore.setValue(LOAD_MORE_EMPTY);
                        } else {
                            List<OrgRecord> recordList = orgSearchModel.docs.get(0).records;
                            List<OrgRecord> orgRecords = new ArrayList<>();
                            for (int i = 0; i < recordList.size(); i++) {
                                if (recordList.get(i).refPro != null) {
                                    orgRecords.add(recordList.get(i));
                                }
                            }
                            mPageOffset = PAGE_SIZE;
                            mOrgRecords.setValue(Pair.create(keyword, orgRecords));
                            if (orgRecords.size() < PAGE_SIZE) {
                                mLoadMore.setValue(LOAD_MORE_COMPLETE);
                            } else {
                                mLoadMore.setValue(LOAD_MORE_LOADED);
                            }
                        }
                    } else {
                        mLoadMore.setValue(LOAD_MORE_EMPTY);
                    }
                } else {
                    mError.setValue(response.getErrorMessage());
                    mLoadMore.setValue(LOAD_MORE_EMPTY);
                }
            }
        });
    }

    public void next(String keyword, String sessionId) {
        mLoadMore.setValue(LOAD_MORE_LOADING);
        String requestId = System.currentTimeMillis() + "";
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.ORG.toString(), false);
        NetWorkManager.getProcessSearchResult(null, sessionId, keyword,mPageOffset, PAGE_SIZE, Arrays.asList(SearchFloorType.ORG.toString()), new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.ORG.toString(), dataLength + "");
                super.onFailure(exception, info);
                mLoadMore.setValue(LOAD_MORE_LOADED);
            }

            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.ORG.toString(), dataLength + "");
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    //接口返回成功
                    if (Objects.equals(mKeyword, keyword)) {
                        String info = response.getData(String.class);
                        OrgSearchModel orgSearchModel = JsonUtils.getGson().fromJson(info, new TypeToken<OrgSearchModel>() {
                        }.getType());
                        if(orgSearchModel == null || orgSearchModel.docs == null
                                || orgSearchModel.docs.isEmpty() || orgSearchModel.docs.get(0).records.isEmpty()) {
                            //搜索结果为空
                            mLoadMore.setValue(LOAD_MORE_COMPLETE);
                        } else {
                            List<OrgRecord> recordList = orgSearchModel.docs.get(0).records;
                            List<OrgRecord> orgRecords = new ArrayList<>();
                            int countNew = 0;
                            if (mOrgRecords.getValue() != null && mOrgRecords.getValue().second != null) {
                                orgRecords.addAll(mOrgRecords.getValue().second);
                            }
                            for (int i = 0; i < recordList.size(); i++) {
                                if (recordList.get(i).refPro != null) {
                                    orgRecords.add(recordList.get(i));
                                    countNew++;
                                }
                            }
                            mPageOffset += PAGE_SIZE;
                            mOrgRecords.setValue(Pair.create(keyword, orgRecords));
                            if (countNew < PAGE_SIZE) {
                                mLoadMore.setValue(LOAD_MORE_COMPLETE);
                            } else {
                                mLoadMore.setValue(LOAD_MORE_LOADED);
                            }
                        }
                    } else {
                        mLoadMore.setValue(LOAD_MORE_LOADED);
                    }
                } else {
                    mLoadMore.setValue(LOAD_MORE_LOADED);
                }
            }
        });
    }

    public MutableLiveData<Boolean> getLoading() {return mLoading;}

    public MutableLiveData<String> getLoadMore() {return mLoadMore;}

    public MutableLiveData<Pair<String, List<OrgRecord>>> getOrgRecords() {return mOrgRecords;}

    public MutableLiveData<String> getError() {return mError;}

    public void clear() {
        mOrgRecords.setValue(Pair.create(null, Collections.emptyList()));
    }
}
