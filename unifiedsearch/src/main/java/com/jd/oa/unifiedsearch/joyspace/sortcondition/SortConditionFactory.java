package com.jd.oa.unifiedsearch.joyspace.sortcondition;

import android.content.Context;

import androidx.collection.ArrayMap;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class SortConditionFactory {

    private static final ArrayMap<String,SortCondition> sCache = new ArrayMap<>();

    public static List<SortCondition> getSortConditions(Context context, @JoySpaceFilter.FilterType String type) {
        if (JoySpaceFilter.TYPE_RELATED.equals(type)) {
            return Arrays.asList(
                    getConditionByType(context, SortCondition.TYPE_RELATED),
                    getConditionByType(context, SortCondition.TYPE_UPDATE_TIME),
                    getConditionByType(context, SortCondition.TYPE_CREATE_TIME)
            );
        } else if (JoySpaceFilter.TYPE_RECEIVED.equals(type)) {
            return  Arrays.asList(
                    getConditionByType(context, SortCondition.TYPE_RELATED),
                    getConditionByType(context, SortCondition.TYPE_RECEIVED_TIME),
                    getConditionByType(context, SortCondition.TYPE_UPDATE_TIME)
            );
        } else if (JoySpaceFilter.TYPE_RECENT.equals(type)) {
            return Arrays.asList(
                    getConditionByType(context, SortCondition.TYPE_RELATED),
                    getConditionByType(context, SortCondition.TYPE_OPENED_TIME),
                    getConditionByType(context, SortCondition.TYPE_UPDATE_TIME)
            );
        } else if (JoySpaceFilter.TYPE_CREATED.equals(type)) {
            return Arrays.asList(
                    getConditionByType(context, SortCondition.TYPE_RELATED),
                    getConditionByType(context, SortCondition.TYPE_CREATE_TIME),
                    getConditionByType(context, SortCondition.TYPE_UPDATE_TIME)
            );
        } else if (JoySpaceFilter.TYPE_SENT.equals(type)) {
            return Arrays.asList(
                    getConditionByType(context, SortCondition.TYPE_RELATED),
                    getConditionByType(context, SortCondition.TYPE_SENT_TIME),
                    getConditionByType(context, SortCondition.TYPE_UPDATE_TIME)
            );
        } else if (JoySpaceFilter.TYPE_PUBLIC.equals(type)) {
            return Arrays.asList(
                    getConditionByType(context, SortCondition.TYPE_RELATED),
                    getConditionByType(context, SortCondition.TYPE_CREATE_TIME),
                    getConditionByType(context, SortCondition.TYPE_UPDATE_TIME)
            );
        } else {
            throw new IllegalArgumentException("Wrong type: " + type);
        }
    }

    public static SortCondition getConditionByType(Context context, @SortCondition.SortType String type) {

        SortCondition condition = sCache.get(type);
        if (condition != null) return condition;

        FilterOption relatedFirst = new FilterOption(context.getString(R.string.unifiedsearch_joyspace_sort_condition_related_first), SortCondition.CONDITION_RELATED_FIRST);
        FilterOption recentFirst = new FilterOption(context.getString(R.string.unifiedsearch_joyspace_sort_condition_latest_first), SortCondition.CONDITION_LATEST_FIRST);
        FilterOption recentLast = new FilterOption(context.getString(R.string.unifiedsearch_joyspace_sort_condition_latest_last), SortCondition.CONDITION_LATEST_LAST);
        FilterOption latestFirst = new FilterOption(context.getString(R.string.unifiedsearch_joyspace_sort_condition_latest_first), SortCondition.CONDITION_LATEST_FIRST);
        FilterOption latestLast = new FilterOption(context.getString(R.string.unifiedsearch_joyspace_sort_condition_latest_last), SortCondition.CONDITION_LATEST_LAST);

        if (SortCondition.TYPE_RELATED.equals(type)) {
            condition = new SortCondition(context.getString(R.string.unifiedsearch_joyspace_sort_condition_related), SortCondition.TYPE_RELATED, Collections.singletonList(relatedFirst));
        } else if (SortCondition.TYPE_UPDATE_TIME.equals(type)) {
            condition = new SortCondition(context.getString(R.string.unifiedsearch_joyspace_sort_condition_update_time), SortCondition.TYPE_UPDATE_TIME, Arrays.asList(recentFirst,recentLast));
        } else if (SortCondition.TYPE_CREATE_TIME.equals(type)) {
            condition = new SortCondition(context.getString(R.string.unifiedsearch_joyspace_sort_condition_create_time), SortCondition.TYPE_CREATE_TIME, Arrays.asList(latestFirst, latestLast));
        } else if (SortCondition.TYPE_RECEIVED_TIME.equals(type)) {
            condition = new SortCondition(context.getString(R.string.unifiedsearch_joyspace_sort_condition_received_time), SortCondition.TYPE_RECEIVED_TIME, Arrays.asList(recentFirst, recentLast));
        } else if (SortCondition.TYPE_OPENED_TIME.equals(type)) {
            condition = new SortCondition(context.getString(R.string.unifiedsearch_joyspace_sort_condition_opened_time), SortCondition.TYPE_OPENED_TIME, Arrays.asList(recentFirst, recentLast));
        } else if (SortCondition.TYPE_SENT_TIME.equals(type)) {
            condition = new SortCondition(context.getString(R.string.unifiedsearch_joyspace_sort_condition_sent_time), SortCondition.TYPE_SENT_TIME, Arrays.asList(recentFirst, recentLast));
        } else {
            throw new IllegalArgumentException("Wrong type: " + type);
        }

        sCache.put(type, condition);
        
        return condition;
    }

    public static void clearCache() {
        sCache.clear();
    }
}
