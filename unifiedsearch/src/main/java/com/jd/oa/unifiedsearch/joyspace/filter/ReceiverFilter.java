package com.jd.oa.unifiedsearch.joyspace.filter;

import android.content.Context;

import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.view.ContactFilterLayout;

import java.util.ArrayList;

/**
 * 接收人
 */
public class ReceiverFilter extends SelectContactFilter {
    private static final String TAG = "ReceiverFilter";

    public ReceiverFilter(FilterContext filterContext) {
        super(filterContext);
    }

    @Override
    protected String getTitle(Context context) {
        return context.getString(R.string.unifiedsearch_joyspace_filter_receiver);
    }

    @Override
    protected void onSelectContact(ArrayList<MemberEntityJd> contacts) {
        super.onSelectContact(contacts);
        mContactFilterLayout.setContacts(contacts);
    }
}
