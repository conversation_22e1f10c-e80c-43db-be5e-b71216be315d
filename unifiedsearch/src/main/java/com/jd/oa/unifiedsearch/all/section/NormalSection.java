package com.jd.oa.unifiedsearch.all.section;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.all.helper.UIHelper;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.all.viewholder.ContentViewHolder;
import com.jd.oa.unifiedsearch.all.viewholder.HeaderViewHolder;
import com.jd.oa.unifiedsearch.util.ResourceUtil;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/17
 * Author: qudongshi
 * Description:
 */
public class NormalSection extends BaseSection {

    private List<?> data;
    private String keyword;
    private String sessionId;
    private String searchId;
    private final String TAG = "IMSection";

    private SearchFloorHelper searchFloorHelper;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public NormalSection(Context context, SearchFloorType floorType, List<?> data, String keyword, SearchFloorHelper searchFloorHelper, String sessionId, String searchId) {
        super(SectionParameters.builder().headerResourceId(R.layout.unifiedsearch_section_header).itemResourceId(R.layout.unifiedsearch_section_content).build(), keyword);
        this.data = data;
        this.keyword = keyword;
        super.floorType = floorType;
        this.searchFloorHelper = searchFloorHelper;
        this.sessionId = sessionId;
        this.searchId = searchId;

    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ContentViewHolder contentViewHolder = (ContentViewHolder) viewHolder;
        contentViewHolder.llContent.removeAllViews();
        SearchLogUitl.LogD(TAG, "onBindItemViewHolder data size = " + data.size() + " floorType = " + floorType.toString() + " keyword = " + keyword);
        Context context = contentViewHolder.itemView.getContext();
        if (data.size() < RECORD_LIMITE) {
            contentViewHolder.hideBottom();
        } else {
            if (context != null) {
                contentViewHolder.showBottom(context, floorType);
                contentViewHolder.llBottom.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        SearchLogUitl.LogD(TAG, floorType.toString() + "  show more");
                        SearchUtil.showMore(context, floorType);
                    }
                });
            }
        }
        try {
            for (int index = 0; index < data.size(); index++) {
                if (index > RECORD_LIMITE - 1) {
                    return;
                }
                if (searchFloorHelper.getIMFloorType().contains(floorType)) {
                    imDdService.searchBindUI(contentViewHolder.llContent, floorType.toString(), data.get(index), keyword, sessionId, searchId);
                } else {
                    if (context != null) {
                        contentViewHolder.recyclerView.setVisibility(View.VISIBLE);
                        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(context);
                        contentViewHolder.recyclerView.setLayoutManager(layoutManager);
                        UIHelper.bindUI(contentViewHolder.recyclerView, floorType, data, keyword, sessionId, searchId);
                    }
                }
            }
        } catch (Exception e) {
            SearchLogUitl.LogE(TAG, "imDdService.searchBindUI execption", e);
        }
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {
        // 搜索内容不一致，舍弃
        if (!this.keyword.equals(keyword)) {
            return;
        }
        this.keyword = keyword;
        this.data = data;

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        ContentViewHolder contentViewHolder = new ContentViewHolder(view);
        return contentViewHolder;
    }

    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        HeaderViewHolder headerViewHolder = new HeaderViewHolder(view);
        headerViewHolder.tvTitle.setText(ResourceUtil.getSectionHeaderRes(view.getContext(), floorType));
        return headerViewHolder;
    }
}
