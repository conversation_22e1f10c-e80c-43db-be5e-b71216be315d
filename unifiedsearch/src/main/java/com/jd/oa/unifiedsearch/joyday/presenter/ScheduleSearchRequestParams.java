package com.jd.oa.unifiedsearch.joyday.presenter;

import java.util.List;
import java.util.Map;

public class ScheduleSearchRequestParams {

    String keyword;
    long startTime;
    long endTime;
    List<String> calendarIds;
    List<Map<String, String>> contacts;
    List<Map<String, String>> organizer;
    boolean asc;
    List<String> categoryList;
    boolean onlyAttendeeStrategyUser;

    String from;

    List<String> permissionCode;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public List<String> getCalendarIds() {
        return calendarIds;
    }

    public void setCalendarIds(List<String> calendarIds) {
        this.calendarIds = calendarIds;
    }

    public List<Map<String, String>> getContacts() {
        return contacts;
    }

    public void setContacts(List<Map<String, String>> contacts) {
        this.contacts = contacts;
    }

    public List<Map<String, String>> getOrganizer() {
        return organizer;
    }

    public void setOrganizer(List<Map<String, String>> organizer) {
        this.organizer = organizer;
    }

    public boolean isAsc() {
        return asc;
    }

    public void setAsc(boolean asc) {
        this.asc = asc;
    }

    public List<String> getCategoryList() {
        return categoryList;
    }

    public void setCategoryList(List<String> categoryList) {
        this.categoryList = categoryList;
    }

    public boolean isOnlyAttendeeStrategyUser() {
        return onlyAttendeeStrategyUser;
    }

    public void setOnlyAttendeeStrategyUser(boolean onlyAttendeeStrategyUser) {
        this.onlyAttendeeStrategyUser = onlyAttendeeStrategyUser;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public List<String> getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(List<String> permissionCode) {
        this.permissionCode = permissionCode;
    }
}
