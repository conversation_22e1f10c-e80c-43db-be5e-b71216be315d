package com.jd.oa.unifiedsearch.all.adaper;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.callback.ISectionEventCallback;

import java.util.List;

/*
 * Time: 2023/11/7
 * Author: qudongshi
 * Description:
 */
public class HistoryAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    public List<String> mData;

    public ISectionEventCallback mCallBack;

    public HistoryAdapter(List<String> data, ISectionEventCallback eventCallback) {
        mData = data;
        mCallBack = eventCallback;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View viewItem = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.unifiedsearch_section_history_content_item, viewGroup, false);
        return new ItemViewHolder(viewItem);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int index) {
        if (viewHolder instanceof ItemViewHolder) {
            ItemViewHolder holder = (ItemViewHolder) viewHolder;
            holder.tvTitle.setText(mData.get(index));
            holder.tvTitle.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mCallBack.onItemClick(mData.get(index));
                }
            });
        }

    }

    @Override
    public int getItemCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }


    public class ItemViewHolder extends RecyclerView.ViewHolder {
        public TextView tvTitle;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_item);
        }
    }
}
