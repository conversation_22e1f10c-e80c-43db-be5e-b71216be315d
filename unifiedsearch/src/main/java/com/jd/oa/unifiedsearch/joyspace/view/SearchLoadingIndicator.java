package com.jd.oa.unifiedsearch.joyspace.view;

import android.content.Context;
import android.text.method.LinkMovementMethod;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.pei.pulluploadhelper.LoadingIndicator;

import androidx.annotation.Nullable;

import com.jd.oa.unifiedsearch.R;

public class SearchLoadingIndicator extends FrameLayout implements LoadingIndicator {

    protected View mView;
    protected ProgressBar mPbLoading;
    protected TextView mTvText;

    private ViewGroup mLayoutComplete;
    private TextView mTvFeedBack;

    public SearchLoadingIndicator(Context context) {
        this(context, (AttributeSet)null);
    }

    public SearchLoadingIndicator(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SearchLoadingIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mView = LayoutInflater.from(context).inflate(R.layout.unifiedsearch_loading_indicator, this);
        this.mPbLoading = (ProgressBar)this.findViewById(R.id.pb_loading);
        this.mTvText = (TextView)this.findViewById(R.id.tv_state);
        this.mLayoutComplete = findViewById(R.id.layout_complete);
        this.mTvFeedBack = findViewById(R.id.tv_feedback);

        this.setVisibility(INVISIBLE);
    }

    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        ViewGroup.LayoutParams layoutParams = this.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            this.setLayoutParams(layoutParams);
        }
    }

    @Override
    public void setEmpty() {
        this.setVisibility(INVISIBLE);
        this.mLayoutComplete.setVisibility(View.GONE);
    }

    @Override
    public void setLoading() {
        this.setVisibility(View.VISIBLE);
        this.mPbLoading.setVisibility(View.VISIBLE);
        this.mLayoutComplete.setVisibility(View.GONE);
        this.mTvText.setVisibility(View.VISIBLE);
        this.mTvText.setText(R.string.loading);
    }

    @Override
    public void setLoaded() {
        this.setVisibility(VISIBLE);
        this.mPbLoading.setVisibility(GONE);
        this.mLayoutComplete.setVisibility(View.GONE);
        this.mTvText.setVisibility(View.VISIBLE);
        this.mTvText.setText(R.string.loaded);
    }

    @Override
    public void setComplete() {
        this.setVisibility(View.VISIBLE);
        this.mPbLoading.setVisibility(GONE);
        this.mTvText.setVisibility(View.GONE);
        this.mLayoutComplete.setVisibility(View.VISIBLE);

        this.mTvFeedBack.setText(SearchUtil.getFeedbackContent());
        this.mTvFeedBack.setMovementMethod(LinkMovementMethod.getInstance());
    }
}