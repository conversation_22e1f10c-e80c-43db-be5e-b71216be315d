package com.jd.oa.unifiedsearch.all.section;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayout;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.adaper.HistoryAdapter;
import com.jd.oa.unifiedsearch.all.callback.ISectionEventCallback;
import com.jd.oa.unifiedsearch.all.data.SearchPreference;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.all.ui.LinesFlexBoxLayoutManager;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/17
 * Author: qudongshi
 * Description:
 */
public class HistorySection extends BaseSection {

    private final String TAG = "HistorySection";

    private Context context;
    LayoutInflater layoutInflater;
    private List<String> data;

    private ISectionEventCallback eventCallback;

    public HistorySection(Context context, List<String> data, ISectionEventCallback callback) {
        super(SectionParameters.builder()
                .itemResourceId(R.layout.unifiedsearch_section_history_content)
                .build(),"");
        this.context = context;
        this.layoutInflater = LayoutInflater.from(context);
        this.data = data;
        this.eventCallback = callback;
        super.floorType = SearchFloorType.HISTORY;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        ContentViewHolder contentViewHolder = new ContentViewHolder(view);
        // 清空搜索历史记录
        contentViewHolder.iftCleanHistory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SearchLogUitl.LogD(TAG, "history clean");
                final Dialog dialog = new Dialog(context, R.style.UnifiedSearch_customProgressDialog);
                dialog.setContentView(R.layout.unifiedsearch_dialog_style2);
                dialog.setCanceledOnTouchOutside(true);
                dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
                TextView tvCancel = dialog.findViewById(R.id.jd_dialog_pos_button);
                tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
                TextView tvClean = dialog.findViewById(R.id.jd_dialog_neg_button);
                tvClean.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        SearchPreference.getInstance().remove(SearchPreference.KV_ENTITY_SEARCH_HISORY);
                        eventCallback.onItemClick("");
                        dialog.dismiss();
                    }
                });
                dialog.show();
            }
        });
        return contentViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ContentViewHolder contentViewHolder = (ContentViewHolder) viewHolder;
        LinesFlexBoxLayoutManager layoutManager = new LinesFlexBoxLayoutManager(context);
        layoutManager.setFlexDirection(FlexDirection.ROW);
        layoutManager.setFlexWrap(FlexWrap.WRAP);
        layoutManager.setMaxLines(2);
        contentViewHolder.recyclerView.setLayoutManager(layoutManager);
        HistoryAdapter adapter = new HistoryAdapter(data, eventCallback);
        contentViewHolder.recyclerView.setAdapter(adapter);
//        if (contentViewHolder.flexboxLayout.getChildCount() > 0) {
//            contentViewHolder.flexboxLayout.removeAllViews();
//        }
//        if (data != null && data.size() > 0) {
//            for (String s : data) {
//                View tempView = layoutInflater.inflate(R.layout.unifiedsearch_section_history_content_item, contentViewHolder.flexboxLayout, false);
//                TextView tvItem = tempView.findViewById(R.id.tv_item);
//                tvItem.setText(s);
//                tvItem.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        eventCallback.onItemClick(tvItem.getText().toString());
//                    }
//                });
//                contentViewHolder.flexboxLayout.addView(tvItem);
//            }
//        }
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {

    }

    private class ContentViewHolder extends RecyclerView.ViewHolder {
        public RecyclerView recyclerView;
        public IconFontView iftCleanHistory;

        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
            recyclerView = itemView.findViewById(R.id.recycle_view);
            iftCleanHistory = itemView.findViewById(R.id.header_clean);
        }
    }
}