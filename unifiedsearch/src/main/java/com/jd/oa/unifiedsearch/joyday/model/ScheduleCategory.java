package com.jd.oa.unifiedsearch.joyday.model;

import android.content.Context;

import com.jd.oa.ui.dialog.BottomSelectDialog;

public class ScheduleCategory implements BottomSelectDialog.SelectableItem {

    public static final String CATEGORY_ALL = "ALL";
    public static final String CATEGORY_MEETING = "MEETING";
    public static final String CATEGORY_APPOINTMENT = "APPOINTMENT";

    String category;
    String text;
    boolean selected;

    public ScheduleCategory(String category, String text, boolean selected) {
        this.category = category;
        this.text = text;
        this.selected = selected;
    }

    @Override
    public boolean isSelected() {
        return selected;
    }

    @Override
    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    @Override
    public String getTitle(Context context) {
        return text;
    }

    public String getCategory() {
        return category;
    }
}
