package com.jd.oa.unifiedsearch.util

import com.jd.oa.ui.popupwindow.PopupItem
import com.jd.oa.unifiedsearch.joyspace.FilterOption

fun filterOptionsToPopupItems(options: List<FilterOption>, selected: FilterOption?): List<PopupItem> {
    val items: MutableList<PopupItem> = ArrayList()
    for (i in options.indices) {
        val option: FilterOption = options[i]
        items.add(filterOptionToPopupItem(option, option == selected))
    }
    return items
}

fun filterOptionToPopupItem(option: FilterOption, check: Boolean): PopupItem {
    return PopupItem(option.text, option.value, check)
}