package com.jd.oa.unifiedsearch.joyspace.view;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyday.utils.TextUtil;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition;
import com.jd.oa.utils.CollectionUtil;

public class SortConditionLayout extends LinearLayout {

    private TextView mTvTitle;
    private TextView mTvLeft;
    private TextView mTvRight;
    private View mDivider;

    private SortCondition mCondition;
    private FilterOption mSelected;

    private OnConditionClickListener mOnLeftClickListener;
    private OnConditionClickListener mOnRightClickListener;

    public SortConditionLayout(Context context) {
        this(context, null);
    }

    public SortConditionLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SortConditionLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public SortConditionLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        View view = LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_layout_sort_conditon, this, true);

        setOrientation(LinearLayout.VERTICAL);
        setBackgroundColor(Color.WHITE);

        mTvTitle = view.findViewById(R.id.tv_title);
        mTvLeft = view.findViewById(R.id.tv_left);
        mTvRight = view.findViewById(R.id.tv_right);
        mDivider = view.findViewById(R.id.divider);

        mTvLeft.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnLeftClickListener != null) {
                    mOnLeftClickListener.onClick(SortConditionLayout.this, (TextView) v, mCondition.getOptions().get(0));
                }
            }
        });

        mTvRight.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnRightClickListener != null) {
                    mOnRightClickListener.onClick(SortConditionLayout.this, (TextView) v, mCondition.getOptions().get(1));
                }
            }
        });
    }

    public void setCondition(SortCondition condition) {
        mCondition = condition;
        mTvTitle.setText(condition.getTitle());

        if (CollectionUtil.isEmptyOrNull(condition.getOptions())) return;

        if (condition.getOptions().size() == 1) {
            mDivider.setVisibility(View.GONE);
            mTvRight.setVisibility(View.GONE);
            mTvLeft.setText(condition.getOptions().get(0).getText());
        } else {
            mDivider.setVisibility(View.VISIBLE);
            mTvRight.setVisibility(View.VISIBLE);
            mTvLeft.setText(condition.getOptions().get(0).getText());
            mTvRight.setText(condition.getOptions().get(1).getText());
        }
    }

    public void setOnLeftClickListener(OnConditionClickListener onLeftClickListener) {
        mOnLeftClickListener = onLeftClickListener;
    }

    public void setOnRightClickListener(OnConditionClickListener onRightClickListener) {
        mOnRightClickListener = onRightClickListener;
    }

    public SortCondition getCondition() {
        return mCondition;
    }

    public TextView setSelected(String optionType) {
        if (mCondition == null || mCondition.getOptions().isEmpty() || TextUtils.isEmpty(optionType)) return null;

        for (int i = 0; i < mCondition.getOptions().size(); i++) {
            if (mCondition.getOptions().get(i).getValue().equals(optionType)) {
                if(i == 0) {
                    mTvLeft.setSelected(true);
                    mTvLeft.getPaint().setFakeBoldText(true);
                    mTvRight.setSelected(false);
                    mTvRight.getPaint().setFakeBoldText(false);
                    return mTvLeft;
                } else if (i == 1) {
                    mTvLeft.setSelected(false);
                    mTvLeft.getPaint().setFakeBoldText(false);
                    mTvRight.setSelected(true);
                    mTvRight.getPaint().setFakeBoldText(true);
                    return mTvRight;
                }
            }
        }
        return null;
    }

    public void unselected() {
        mTvLeft.setSelected(false);
        mTvLeft.getPaint().setFakeBoldText(false);
        mTvRight.setSelected(false);
        mTvRight.getPaint().setFakeBoldText(false);
    }

    public interface OnConditionClickListener {

        void onClick(SortConditionLayout layout, TextView view, FilterOption option);
    }
}