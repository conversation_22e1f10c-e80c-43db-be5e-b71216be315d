package com.jd.oa.unifiedsearch.joyspace

import android.app.Application
import android.text.TextUtils
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceRepository
import com.jd.oa.unifiedsearch.joyspace.data.JoyspaceSearchException
import com.jd.oa.unifiedsearch.joyspace.data.SearchFilterQuery
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition
import io.reactivex.Observable
import io.reactivex.Single

import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import java.util.Collections

class JoySpaceSearchViewModel(application: Application): AndroidViewModel(application) {

    companion object {
        const val TAG = "JoySpaceSearchViewModel"
        const val PAGE_SIZE = 20
    }

    private val mDocumentList: MutableLiveData<List<JoySpaceDocument>> = MutableLiveData()
    val documentList: LiveData<List<JoySpaceDocument>> get() = mDocumentList
    private val mLoadState: MutableLiveData<LoadState> = MutableLiveData()
    val loadState: LiveData<LoadState> get() = mLoadState

    private val mRepository: JoySpaceRepository = JoySpaceRepository.get(application)
    private val mCompositeDisposable: CompositeDisposable = CompositeDisposable()

    private var mKeyWord: String? = null
    private var mPageOffset: Int = 0
    private var mFilterQuery: SearchFilterQuery? = null
    private var mType: String? = null

    fun refresh(keyword: String?, @JoySpaceFilter.FilterType type: String?, params: SearchFilterQuery) {
        mKeyWord = keyword
        mType = type
        mFilterQuery = params

        val refreshResult = search(keyword ?: "", params, 0, PAGE_SIZE)

        val disposable = refreshResult.observeOn(AndroidSchedulers.mainThread())
            .doOnSubscribe {
                mLoadState.value = LoadState.Refreshing
            }
            .subscribe({
                mPageOffset = PAGE_SIZE
                mDocumentList.value = it
                mLoadState.value = LoadState.Refreshed(loadResult(it, true))
            }, {
                Log.e(TAG, it.message, it)
                val errCode = if (it is JoyspaceSearchException) {
                    it.code
                } else ""
                mLoadState.value = LoadState.Refreshed(LoadResult.Failed(it.message ?: "", errCode))
            })
        mCompositeDisposable.add(disposable)
    }

    fun next() {
        val nextPage = search(mKeyWord ?: "", mFilterQuery!!, mPageOffset, PAGE_SIZE)
        val disposable = nextPage.observeOn(AndroidSchedulers.mainThread())
            .doOnSubscribe {
                mLoadState.value = LoadState.Loading
            }
            .subscribe({
                mPageOffset += PAGE_SIZE
                val list = mutableListOf<JoySpaceDocument>()
                list.addAll(mDocumentList.value ?: emptyList())
                list.addAll(it)
                mDocumentList.value = list
                mLoadState.value = LoadState.Loaded(loadResult(it, false))
            }, {
                mLoadState.value = LoadState.Loaded(LoadResult.Failed(it.message ?: ""))
            })
        mCompositeDisposable.add(disposable)
    }

    private fun loadResult(result: List<JoySpaceDocument>, refresh: Boolean): LoadResult = when {
        result.isEmpty() && refresh -> LoadResult.Empty
        result.size < PAGE_SIZE -> LoadResult.Complete
        else -> LoadResult.Success
    }

    private fun getDocumentsByType(@JoySpaceFilter.FilterType type: String?, start: Int, pageSize: Int, query: SearchFilterQuery): Single<List<JoySpaceDocument>> {
        return when (type) {
            JoySpaceFilter.TYPE_RELATED -> mRepository.getRelatedDocuments(start, pageSize, query)
            JoySpaceFilter.TYPE_RECENT -> mRepository.getRecentOpenedDocuments(start, pageSize, query.sortCondition)
            JoySpaceFilter.TYPE_RECEIVED -> mRepository.getReceivedDocuments(start, pageSize, query.sortCondition)
            JoySpaceFilter.TYPE_SENT -> mRepository.getSentDocuments(start, pageSize, query.sortCondition)
            JoySpaceFilter.TYPE_CREATED -> mRepository.getCreatedByMySelfDocuments(start, pageSize, query.sortCondition)
            JoySpaceFilter.TYPE_PUBLIC -> Single.just(Collections.emptyList())
            else -> throw IllegalArgumentException("Unknown type $type")
        }.toObservable()
            .flatMap { Observable.fromIterable(it) }
            .map { it.apply { subTitle = it.buildSubTitle(getApplication(), query.type, query.sortCondition) } }
            .toList()
    }

    private fun search(keyword: String, params: SearchFilterQuery, start: Int, length: Int): Single<List<JoySpaceDocument>> {
        return mRepository.searchJoySpaceDocuments(keyword, params, start, length)
            .toObservable()
            .flatMap { Observable.fromIterable(it) }
            .map { it.apply { subTitle = it.buildSubTitle(getApplication(), params.type, params.sortCondition) } }
            .toList()
    }

    override fun onCleared() {
        super.onCleared()
        mCompositeDisposable.clear()
    }

    sealed class LoadState {
        object Init: LoadState()
        object Refreshing: LoadState()
        class Refreshed(val result: LoadResult): LoadState()
        object Loading: LoadState()
        class Loaded(val result: LoadResult): LoadState()
    }

    sealed class LoadResult {
        object Complete: LoadResult()
        object Empty: LoadResult()
        object Success: LoadResult()
        class Failed(val message: String, val code: String? = null): LoadResult()
    }
}