package com.jd.oa.unifiedsearch.approval

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import com.jd.oa.JDMAConstants
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.all.filter.Mappable
import com.jd.oa.unifiedsearch.all.helper.SearchFilterConfigHelper
import com.jd.oa.unifiedsearch.approval.filter.ApprovalFilterFactory
import com.jd.oa.unifiedsearch.joyspace.filter.FilterContext
import com.jd.oa.unifiedsearch.joyspace.filter.UnifiedSearchFilter
import com.jd.oa.unifiedsearch.joyspace.view.SearchLoadingIndicator
import com.jd.oa.unifiedsearch.joyspace.view.UnifiedFiltersContainerLayout
import com.jd.oa.unifiedsearch.process.ProcessSearchViewModel
import com.jd.oa.utils.JDMAUtils
import com.pei.pulluploadhelper.PullUpLoadHelper

class ApprovalSearchTabDelegate(host: Host): ApprovalSearchDelegate(host), FilterContext {
    companion object {
        const val TAG = "ApprovalSearchTabDelegate"
    }

    private val mFilterContainer: UnifiedFiltersContainerLayout by lazy { mHost.requireView().findViewById(R.id.layout_filter_container) }
    private val mFilterFactory: ApprovalFilterFactory = ApprovalFilterFactory()
    private lateinit var mSubTypeFilters: List<UnifiedSearchFilter<*>>
    private var mStartActivityFilter: UnifiedSearchFilter<*>? = null
    private var mPullUpLoadHelper: PullUpLoadHelper? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        return inflater.inflate(R.layout.unifiedsearch_fragment_approval, container, false)
    }

    override fun initView(view: View?, savedInstanceState: Bundle?) {
        super.initView(view, savedInstanceState)
        //获取默认过滤条件
        mSubTypeFilters = SearchFilterConfigHelper.getInstance().getFilters(this, mHost.context, mFilterFactory)
        mPullUpLoadHelper = PullUpLoadHelper(mRecyclerView) {
            mViewModel.next(mKeyword, mHost.sessionId, getFilterParams())
        }
        val endIndicator = SearchLoadingIndicator(mHost.context)
        mPullUpLoadHelper?.setEndIndicator(endIndicator)
        mPullUpLoadHelper?.setEmpty()
        //设置过滤器
        mFilterContainer.setFilters(mSubTypeFilters)
        savedInstanceState?.let { mFilterContainer.changeResetState() }

        mFilterContainer.mOnFilterChangedListener = {_, value-> //过滤条件发生变化
            MELogUtil.localD(TAG, "过滤条件发生变化" + value.toString())
            mSubTypeFilters = SearchFilterConfigHelper.getInstance().getFilters(this, mHost.context, mFilterFactory)
            mFilterContainer.setFilters(mSubTypeFilters)
            refresh(mKeyword)
        }

        mFilterContainer.mOnResetClickListener =  View.OnClickListener {
            MELogUtil.localD(TAG, "点击重置按钮")
            //重置按钮点击后的逻辑
            mFilterFactory.resetAll()
            mSubTypeFilters = SearchFilterConfigHelper.getInstance().getFilters(this, mHost.context, mFilterFactory)
            mFilterContainer.setFilters(mSubTypeFilters)
            refresh(mKeyword)
        }

        mBtnRetry.setOnClickListener {
            MELogUtil.localD(TAG, "点击重置重试按钮")
            refresh(mKeyword)
        }

        if (!TextUtils.isEmpty(mKeyword)) {
            refresh(mKeyword)
        }

        mViewModel.loadMore.observe(mHost.viewLifecycleOwner, Observer {
            when (it) {
                ProcessSearchViewModel.LOAD_MORE_EMPTY -> mPullUpLoadHelper!!.setEmpty()
                ProcessSearchViewModel.LOAD_MORE_COMPLETE -> mPullUpLoadHelper!!.setComplete()
                ProcessSearchViewModel.LOAD_MORE_LOADING -> mPullUpLoadHelper!!.setLoading()
                ProcessSearchViewModel.LOAD_MORE_LOADED -> mPullUpLoadHelper!!.setLoaded()
            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val result = mStartActivityFilter?.onActivityResult(requestCode, resultCode, data) ?: false
        mStartActivityFilter = null
        if (result) return
    }

    override fun search(keyword: String?, force: Boolean) {
        if (!force && mKeyword == keyword) return
        mKeyword = keyword
        refresh(mKeyword)
    }

    override fun tabSelected(keyword: String?) {
        JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Approval_Tab_ck,
            JDMAConstants.Mobile_Event_UnifiedSearch_Approval_Tab_ck)
        JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_UnifiedSearch_Approval_exposure,
            JDMAConstants.Mobile_Event_UnifiedSearch_Approval_exposure)
        search(keyword, false)
    }

    override fun onTextChanged(keyword: String?) {
        search(keyword, true)
    }

    private fun refresh(keyword: String?) {
        //获取选中的过滤条件
        val params = getFilterParams()
        MELogUtil.localD(TAG, "审批搜索Params过滤条件: " + params.toString())
        mViewModel.refresh(keyword, mHost.sessionId, params)
    }

    override fun startActivity(searchFilter: UnifiedSearchFilter<*>?, intent: Intent?) {
        mStartActivityFilter = searchFilter
        mHost.startActivity(intent)
    }

    override fun startActivityForResult(searchFilter: UnifiedSearchFilter<*>?, intent: Intent?, requestCode: Int) {
        mStartActivityFilter = searchFilter
        mHost.startActivityForResult(intent, requestCode)
    }

    override fun checkedFilterType(): String? { return "" }

    override fun onDestroy() {
        super.onDestroy()
    }

    private fun getFilterParams(): HashMap<String, Any>? {
        //获取选中的过滤条件
        val params = HashMap<String, Any>()
        for (filter in mSubTypeFilters) {
            val filterValue = filter.value
            if (filterValue != null && filterValue is Mappable) {
                filterValue.toMap()?.forEach { (key, value) ->
                    params[key] = value
                }
            }
        }
        if (params.isEmpty()) {
            return null
        }
        return params
    }
}