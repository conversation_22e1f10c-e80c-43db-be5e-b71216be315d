package com.jd.oa.unifiedsearch.joyday.utils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.unifiedsearch.joyday.model.Subcategory;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class SubcategoryListParser {

    public Map<String,List<Subcategory>> parseConfig(String config) {
        try {
            Map<String,List<Subcategory>> map = new HashMap<>();
            JSONObject object = new JSONObject(config);
            for (Iterator<String> it = object.keys(); it.hasNext(); ) {
                String key = it.next();
                List<Subcategory> subcategories = new Gson().fromJson(object.getString(key), new TypeToken<List<Subcategory>>(){}.getType());
                map.put(key, subcategories);
            }
            return map;
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }
}