package com.jd.oa.unifiedsearch.joyspace.sortcondition;

import androidx.annotation.Keep;

import com.alibaba.fastjson.annotation.JSONField;

@Keep
public class SelectedSortCondition {
    @SortCondition.SortType
    private String condition;
    @SortCondition.SortOrder
    @JSONField(name = "order", alternateNames = {"option"})
    private String order;

    public SelectedSortCondition() {
    }

    public SelectedSortCondition(@SortCondition.SortType String condition, @SortCondition.SortOrder String option) {
        this.condition = condition;
        this.order = option;
    }

    @SortCondition.SortType
    public String getCondition() {
        return condition;
    }

    @SortCondition.SortOrder
    public String getOrder() {
        return order;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}