package com.jd.oa.unifiedsearch.joyday.utils;

import android.content.Context;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class ScheduleDateFormatter {

    private static final SimpleDateFormat sTimeFormat = new SimpleDateFormat("HH:mm");
    private static final SimpleDateFormat sEndTimeFormat = new SimpleDateFormat("HH:mm");
    private static SimpleDateFormat sDateFormat;
    private static SimpleDateFormat sFullDateFormat;

    private static String formatTime(Context context, ScheduleModel schedule) {
        String timeText;
        boolean isAllDay = schedule.isAllDay();

        if (isAllDay && DateUtil.isStartOfDay(schedule.getBeginDateTime()) && DateUtil.isStartOfDay(schedule.getEndDateTime())) {
            timeText = context.getResources().getString(R.string.schedule_all_day);
        } else if (DateUtil.isStartOfDay(schedule.getEndDateTime())) {
            timeText = sTimeFormat.format(schedule.getBeginDateTime()) + "-" + "24:00";
        } else {
            timeText = sTimeFormat.format(schedule.getBeginDateTime()) + "-" + sTimeFormat.format(schedule.getEndDateTime());
        }
        return timeText;
    }

    private static String formatDate(Context context, ScheduleModel schedule) {
        Calendar date = Calendar.getInstance();
        date.setTimeInMillis(schedule.getBeginTime());

        Calendar yesterday = Calendar.getInstance();
        yesterday.add(Calendar.DATE, -1);

        Calendar tomorrow = Calendar.getInstance();
        tomorrow.add(Calendar.DATE, 1);

        String dateText;
        if (DateUtil.isToday(date.getTime())) {
            dateText = context.getString(R.string.joyday_today);
        } else if (DateUtil.isSameDay(date.getTime(), yesterday.getTime())) {
            dateText = context.getString(R.string.joyday_yesterday);
        } else if (DateUtil.isSameDay(date.getTime(), tomorrow.getTime())) {
            dateText = context.getString(R.string.joyday_tomorrow);
        } else if (DateUtil.isSameDay(date.getTime(), Calendar.getInstance().getTime())) {
            if (sDateFormat == null) {
                sDateFormat = new SimpleDateFormat(context.getString(R.string.calendar_schedule_selector_month_format), Locale.getDefault());
            }
            dateText = sDateFormat.format(date.getTime());
        } else {
            if (sFullDateFormat == null) {
                sFullDateFormat = new SimpleDateFormat(context.getString(R.string.calendar_schedule_selector_year_month_format), Locale.getDefault());
            }
            dateText = sFullDateFormat.format(date.getTime());
        }
        return  dateText;
    }

    public static String format(Context context, ScheduleModel schedule, boolean formatDate) {
        String time = formatTime(context, schedule);
        if (!formatDate) {
            return time;
        }
        String date = formatDate(context, schedule);
        return date + " " + time;
    }
}