package com.jd.oa.unifiedsearch.all.section;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/11/22
 * Author: qudongshi
 * Description:
 */
public class EmptySection extends BaseSection {

    public EmptySection(String keyword) {
        super(SectionParameters.builder().itemResourceId(R.layout.unifiedsearch_layout_no_result).build(), keyword);
        super.floorType = SearchFloorType.EMPTY;

    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new ContentViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {

    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {

    }

    private class ContentViewHolder extends RecyclerView.ViewHolder {

        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
        }
    }
}