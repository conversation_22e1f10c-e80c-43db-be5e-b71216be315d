package com.jd.oa.unifiedsearch.all.helper;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.configuration.local.model.SearchFloorConfig;
import com.jd.oa.im.listener.Callback5;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchPreference;
import com.jd.oa.unifiedsearch.all.data.SearchTabModel;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.LocaleUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Time: 2023/10/28
 * Author: qudongshi
 * Description:
 */
public class SearchConfigHelper {


    private List<String> localDefaultOrder = new ArrayList<>();
    public static Map<String, Integer> tabNamesMapping = new HashMap<>();

    public static Map<String, SearchFloorConfig> tabTitleMapping = new HashMap<>();
    public static Map<SearchFloorType, SearchFloorConfig> floorTitleMapping = new HashMap<>();
    public static final String tab_meai = "meai";
    public static final String tab_all = "all";
    public static final String tab_contact = "contact";
    public static final String tab_app = "app";
    public static final String tab_joyspace = "joyspace";
    public static final String tab_joyday = "joyday";
    public static final String tab_waiter = "waiter";
    public static final String tab_robot = "robot";
    public static final String tab_notice = "notice";
    public static final String tab_message = "message";
    public static final String tab_group = "group";
    public static final String tab_task = "task";
    public static final String tab_approval = "approval";
    public static final String tab_process = "process";
    public static final String tab_org = "org";

    static {
        tabNamesMapping.put(tab_meai, R.string.me_search_tab_meai);
        tabNamesMapping.put(tab_all, R.string.me_search_tab_all);
        tabNamesMapping.put(tab_app, R.string.me_search_tab_app);
        tabNamesMapping.put(tab_task, R.string.me_search_tab_task);
        tabNamesMapping.put(tab_joyday, R.string.me_search_tab_schedule);
        tabNamesMapping.put(tab_joyspace, R.string.me_search_tab_joyspace);
        tabNamesMapping.put(tab_approval, R.string.unifiedsearch_tab_approval);
        tabNamesMapping.put(tab_process, R.string.unifiedsearch_tab_process);
        // IM
        tabNamesMapping.put(tab_contact, R.string.me_search_tab_contact);
        tabNamesMapping.put(tab_group, R.string.me_search_tab_group);
        tabNamesMapping.put(tab_message, R.string.me_search_tab_message);
        tabNamesMapping.put(tab_notice, R.string.me_search_tab_notice);
        tabNamesMapping.put(tab_waiter, R.string.me_search_tab_shop);
        tabNamesMapping.put(tab_robot, R.string.me_search_tab_robot);
        //部门
        tabNamesMapping.put(tab_org,R.string.me_search_tab_org);
    }

    private static SearchConfigHelper helper;

    private List<SearchFloorConfig> searchFloorConfigs;
    private SearchTabModel searchTabConfigs;

    private boolean isChinese;
    private static final String TAG = "SearchConfigHelper";

    ImDdService imDdService = AppJoint.service(ImDdService.class);
    private String requestID;
    private String keyword;

    private SearchConfigHelper() {
        isChinese = LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh");
        initSearchConfig();
        initTabDefaultOrder(); // 初始化默认排序
    }

    private void initTabDefaultOrder() {
        localDefaultOrder = new ArrayList<>();
        localDefaultOrder.add(tab_all);
        localDefaultOrder.add(tab_contact);
        localDefaultOrder.add(tab_group);
        localDefaultOrder.add(tab_waiter);
        localDefaultOrder.add(tab_org);
        localDefaultOrder.add(tab_robot);
        localDefaultOrder.add(tab_message);
        localDefaultOrder.add(tab_notice);
        localDefaultOrder.add(tab_app);
        localDefaultOrder.add(tab_task);
        localDefaultOrder.add(tab_joyspace);
        localDefaultOrder.add(tab_joyday);
        localDefaultOrder.add(tab_process);
        localDefaultOrder.add(tab_approval);
    }

    public void getSearchTabCustomOrder(Callback5<SearchTabModel> callback) {
        // 已同步直接返回
        if (!SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_TAB_IMPORTED)) {
            imDdService.getSearchTabConfig(new Callback5<String>() {
                @Override
                public void onResult(String va1) {
                    getLocalCustomOrder(callback, va1);
                }
            });
        } else {
            getLocalCustomOrder(callback, "");
        }
    }

    private void getLocalCustomOrder(Callback5<SearchTabModel> callback, String imRecord) {
        SearchTabModel searchTabModel = new SearchTabModel();
        searchTabModel.defaultOrder = localDefaultOrder;
        searchTabModel.customOrder = new ArrayList<>();
        searchTabModel.unSelectedTab = new ArrayList<>();
        if (!TextUtils.isEmpty(imRecord) && !imRecord.equals("[]")) {
            //加工数据
            try {
                List<SearchTabModel.SearchTab> tabs = JsonUtils.getGson().fromJson(imRecord, new TypeToken<List<SearchTabModel.SearchTab>>() {
                }.getType());
                for (SearchTabModel.SearchTab tabItem : tabs) {
                    SearchTabModel.SearchTab convertItem = convertTabItem(tabItem);
                    if (convertItem == null) {
                        continue;
                    }
                    if (tabItem.seq == -1) {
                        searchTabModel.unSelectedTab.add(convertItem.type);
                    } else {
                        searchTabModel.customOrder.add(convertItem.type);
                    }
                }
                // 标记已加工
                SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_TAB_IMPORTED, true);
                SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_TAB_CUSTOM, JsonUtils.getGson().toJson(searchTabModel.customOrder));
                SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_TAB_UNSEL, JsonUtils.getGson().toJson(searchTabModel.unSelectedTab));
            } catch (Exception e) {
                getLocalCustomOrder(callback, "[]");
            }
        } else {
            if (!SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_TAB_IMPORTED)) {
                SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_TAB_IMPORTED, true);
            }
            String strUnSel = SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_TAB_UNSEL);
            String strCustom = SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_TAB_CUSTOM);
            searchTabModel.customOrder = JsonUtils.getGson().fromJson(strCustom, new TypeToken<List<String>>() {
            }.getType());
            searchTabModel.unSelectedTab = JsonUtils.getGson().fromJson(strUnSel, new TypeToken<List<String>>() {
            }.getType());
        }
        searchTabConfigs = permissionsFilter(searchTabModel);
        new Handler(Looper.getMainLooper()).post(() -> callback.onResult(searchTabConfigs));
    }

    public SearchTabModel permissionsFilter(SearchTabModel model) {
        // 容错处理
        if (model.customOrder != null && model.customOrder.size() > 0) {
            // 保证综合排在首位
            if (model.customOrder.indexOf(tab_all) > 0 || model.customOrder.indexOf(tab_all) == -1) {
                model.customOrder.remove(tab_all);
                model.customOrder.add(0, tab_all);
                SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_TAB_CUSTOM, JsonUtils.getGson().toJson(model.customOrder));
            }
        }
        //日历
        if (!TenantConfigBiz.INSTANCE.isJoyDayEnable()) {
            model.defaultOrder.remove(tab_joyday);
            model.unSelectedTab.remove(tab_joyday);
            model.customOrder.remove(tab_joyday);
        } else if (TenantConfigBiz.INSTANCE.isJoyDayEnable() && model.customOrder.size() > 0) {
            if (!model.unSelectedTab.contains(tab_joyday) && !model.customOrder.contains(tab_joyday)) {
                model.customOrder.add(tab_joyday);
            }
        }
        //文档
        if (!TenantConfigBiz.INSTANCE.isJoySpaceEnable()) {
            model.defaultOrder.remove(tab_joyspace);
            model.unSelectedTab.remove(tab_joyspace);
            model.customOrder.remove(tab_joyspace);
        } else if (TenantConfigBiz.INSTANCE.isJoySpaceEnable() && model.customOrder.size() > 0) {
            if (!model.unSelectedTab.contains(tab_joyspace) && !model.customOrder.contains(tab_joyspace)) {
                model.customOrder.add(tab_joyspace);
            }
        }
        //任务
        if (!TenantConfigBiz.INSTANCE.isJoyWorkEnable()) {
            model.defaultOrder.remove(tab_task);
            model.unSelectedTab.remove(tab_task);
            model.customOrder.remove(tab_task);
        } else if (TenantConfigBiz.INSTANCE.isJoyWorkEnable() && model.customOrder.size() > 0) {
            if (!model.unSelectedTab.contains(tab_task) && !model.customOrder.contains(tab_task)) {
                model.customOrder.add(tab_task);
            }
        }
        //商家
        if (!imDdService.hasWaiterPermission()) {
            model.defaultOrder.remove(tab_waiter);
            model.unSelectedTab.remove(tab_waiter);
            model.customOrder.remove(tab_waiter);
        } else if (imDdService.hasWaiterPermission() && model.customOrder.size() > 0) {
            if (!model.unSelectedTab.contains(tab_waiter) && !model.customOrder.contains(tab_waiter)) {
                model.customOrder.add(tab_waiter);
            }
        }
        //流程
        if (!SearchConfigHelper.getInstance().searchProcessEnable()) {
            model.defaultOrder.remove(tab_process);
            model.unSelectedTab.remove(tab_process);
            model.customOrder.remove(tab_process);
        } else if (SearchConfigHelper.getInstance().searchProcessEnable() && model.customOrder.size() > 0) {
            if (!model.unSelectedTab.contains(tab_process) && !model.customOrder.contains(tab_process)) {
                model.customOrder.add(tab_process);
            }
        }
        //审批
        if (!SearchConfigHelper.getInstance().searchApprovalEnable()) {
            model.defaultOrder.remove(tab_approval);
            model.unSelectedTab.remove(tab_approval);
            model.customOrder.remove(tab_approval);
        } else if (SearchConfigHelper.getInstance().searchApprovalEnable() && model.customOrder.size() > 0) {
            if (!model.unSelectedTab.contains(tab_approval) && !model.customOrder.contains(tab_approval)) {
                model.customOrder.add(tab_approval);
            }
        }
        //部门
        if (!SearchConfigHelper.getInstance().searchOrgEnable()){
            model.defaultOrder.remove(tab_org);
            model.unSelectedTab.remove(tab_org);
            model.customOrder.remove(tab_org);
        } else if (SearchConfigHelper.getInstance().searchOrgEnable() && model.customOrder.size() > 0) {
            if(!model.unSelectedTab.contains(tab_org) && !model.customOrder.contains(tab_org)){
                model.customOrder.add(tab_org);
            }
        }
        //通知
        if (!SearchConfigHelper.getInstance().searchNoticeEnable()){
            model.defaultOrder.remove(tab_notice);
            model.unSelectedTab.remove(tab_notice);
            model.customOrder.remove(tab_notice);
        } else if (SearchConfigHelper.getInstance().searchNoticeEnable() && model.customOrder.size() > 0) {
            if(!model.unSelectedTab.contains(tab_notice) && !model.customOrder.contains(tab_notice)){
                model.customOrder.add(tab_notice);
            }
        }
        return model;
    }

    private void initSearchConfig() {
//        String remoteConfig = TenantConfigManager.getConfigStringByKey(TenantConfigManager.KEY_SEARCH_GROUP);
//        if (!TextUtils.isEmpty(remoteConfig)) {
//            searchFloorConfigs = JsonUtils.getGson().fromJson(remoteConfig, new TypeToken<ArrayList<SearchFloorConfig>>() {
//            }.getType());
//        } else {
        searchFloorConfigs = LocalConfigHelper.getInstance(AppBase.getAppContext()).getSearchFloorConfig();
//        }
        if (searchFloorConfigs != null) {
            for (SearchFloorConfig config : searchFloorConfigs) {
                if (config.groupName == SearchFloorType.JOY_SPACE.toString()) {

                }
            }
        }
    }

    public static SearchConfigHelper getInstance() {
        synchronized (SearchConfigHelper.class) {
            if (helper == null) {
                helper = new SearchConfigHelper();
            }
            return helper;
        }
    }

    public List<SearchFloorConfig> getSearchFloorConfigs() {
        if (searchFloorConfigs == null) {
            initSearchConfig();
        }
        return searchFloorConfigs;
    }

    public void getCustomSearchFilterConfig() {
        NetWorkManager.getCustomSearchFilterConfig(new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    String info = response.getData(String.class);
                    // 配置json获取成功，存入preference
                    SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_SEARCH_FILTER_CONFIG, info);
                }
            }
        });
    }

    public boolean saveSearchTabConfig(SearchTabModel model) {
        if (model == null) {
            return false;
        }
        SearchLogUitl.LogD(TAG, "saveSearchTabConfig");
        SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_TAB_CUSTOM, JsonUtils.getGson().toJson(model.customOrder));
        SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_TAB_UNSEL, JsonUtils.getGson().toJson(model.unSelectedTab));
        searchTabConfigs.customOrder = model.customOrder;
        searchTabConfigs.unSelectedTab = model.unSelectedTab;
        return true;
    }

    public String getFloorTitle(SearchFloorType floorType) {
        return "";
    }

    public String getTabTitle(String tabName) {
        if (tabName.equals(tab_all)) {

        }
        return "";
    }

    private String getLocalTitle(SearchFloorConfig config, String defaultVal) {
        if (config == null) {
            return defaultVal;
        }
        if (isChinese) {
            return TextUtils.isEmpty(config.groupTitle) ? defaultVal : config.groupTitle;
        } else {
            return TextUtils.isEmpty(config.groupTitleEn) ? defaultVal : config.groupTitle;
        }
    }

    private SearchTabModel.SearchTab convertTabItem(SearchTabModel.SearchTab item) {
        if (item == null) {
            return null;
        }
        String type = getRealTabType(item.type);
        if (type.equals("secret_single") || type.equals("secret_group")) {
            return null;
        }
        SearchTabModel.SearchTab result = new SearchTabModel.SearchTab();
        result.type = type;
        return result;
    }

    public String getRealTabType(String val) {
        String type = val.toLowerCase();
        if (type.equals("joy_space")) {
            type = SearchConfigHelper.tab_joyspace;
        } else if (type.equals("schedule")) {
            type = SearchConfigHelper.tab_joyday;
        }
        return type;
    }

    private Handler handler;

    public Handler getMainHandler() {
        synchronized (SearchHelper.class) {
            if (handler == null) {
                handler = new Handler(Looper.getMainLooper());
            }
            return handler;
        }
    }

    public boolean searchMEAIEnable() {
        String flag = ABTestManager.getInstance().getConfigByKey("mobile.ai.chat.enable", "0");
        String searchFlag = ABTestManager.getInstance().getConfigByKey("me.search.ai.android.enable", "0");
        if (flag.equals("0") || searchFlag.equals("0")) {
            return false;
        }
        return true;
    }

    public boolean searchProcessEnable() {
        String flag = ABTestManager.getInstance().getConfigByKey("mobile.search.process.enable", "0");
        if (flag.equals("0")) {
            return false;
        }
        return true;
    }

    public boolean searchApprovalEnable() {
//        String flag = ABTestManager.getInstance().getConfigByKey("mobile.search.approval.enable", "0");
//        if (flag.equals("0")) {
//            return false;
//        }
//        return true;
        //7.7版本暂时不上审批功能，灰度暂时不开放
        return false;
    }

    public boolean searchOrgEnable(){
        return TenantConfigBiz.INSTANCE.isOrganizationEnable() || TenantConfigBiz.INSTANCE.isRelatedOrganizationEnable();
    }

    public boolean searchNoticeEnable(){
        ModuleModel.UnifiedSearchModel unifiedSearchModel = LocalConfigHelper.getInstance().getUnifiedSearchModel();
        return unifiedSearchModel != null && unifiedSearchModel.noticeEnable;
    }

    public String getRequestID() {
        if (requestID == null) {
            return "";
        }
        return requestID;
    }

    public void setRequestID(String requestID) {
        this.requestID = requestID;
    }

    public String getKeyword() {
        if (keyword == null) {
            return "";
        }
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
