package com.jd.oa.unifiedsearch.joyday.adapter;

import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyday.ScheduleSelectorConfig;
import com.jd.oa.unifiedsearch.joyday.model.Calendar;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;
import com.jd.oa.unifiedsearch.joyday.model.Subcategory;
import com.jd.oa.unifiedsearch.joyday.utils.DateUtil;
import com.jd.oa.unifiedsearch.joyday.utils.ScheduleDateFormatter;
import com.jd.oa.unifiedsearch.joyday.utils.TextUtil;
import com.jd.oa.unifiedsearch.joyday.view.ScheduleSearchTabDelegate;
import com.jd.oa.unifiedsearch.joyday.view.ScheduleSelectMode;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DrawableEx;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ToastUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ScheduleSearchAdapter extends RecyclerView.Adapter<ScheduleSearchAdapter.ViewHolder> {

    private List<ScheduleModel> scheduleList = new ArrayList<>();
    final private Context mContext;
    private ScheduleSearchTabDelegate.OnSingleSelectChangeListener mOnSingleSelectChangeListener;

    private ScheduleSearchTabDelegate.OnMultiSelectChangeListener mOnMultiSelectChangeListener;
    ScheduleSelectMode mSelectMode;

    private boolean mTimeDivider;

    private int mMaxNum;

    private List<ScheduleModel> mSelectedList;
    java.util.Calendar yesterday = java.util.Calendar.getInstance();

    java.util.Calendar tomorrow = java.util.Calendar.getInstance();

    ScheduleSelectorConfig mConfig;

    Map<String,List<Subcategory>> mSubcategories = new HashMap<>();

    public ScheduleSearchAdapter(Context context, ScheduleSelectMode selectMode, ScheduleSearchTabDelegate.OnSingleSelectChangeListener onSingleSelectChangeListener, ScheduleSelectorConfig config) {
        this.mContext = context;
        this.mSelectMode = selectMode;
        this.mOnSingleSelectChangeListener = onSingleSelectChangeListener;
        this.mConfig = config;
        this.mTimeDivider = config.getTimeDivider();
        this.mMaxNum = config.getMaxNum();

        yesterday.add(java.util.Calendar.DATE, -1);
        tomorrow.add(java.util.Calendar.DATE, 1);
    }

    public boolean refreshData(final List<ScheduleModel> scheduleList, List<ScheduleModel> configSelectedList, boolean disableSelect) {
        this.scheduleList = (scheduleList != null ? scheduleList : new ArrayList<>());
        boolean changed = false;
        if (mSelectedList == null) {
            mSelectedList = new ArrayList<>();
        }
        changed = initSelectedList(configSelectedList, disableSelect);

        boolean result = setSelected(mSelectedList);
        notifyDataSetChanged();
        return changed || result;
    }

    private boolean initSelectedList(List<ScheduleModel> configSelectedList, boolean disableSelect) {
        if (configSelectedList == null) {
            return false;
        }
        boolean changed = false;
        Iterator<ScheduleModel> iterator = configSelectedList.iterator();
        while (iterator.hasNext()) {
            ScheduleModel s1 = iterator.next();
            s1.disableSelected = disableSelect;
            boolean find = false;
            for (int i1 = 0; i1 < this.scheduleList.size(); i1++) {
                ScheduleModel s2 = this.scheduleList.get(i1);
                if (s1.equals(s2)) {
                    s2.disableSelected = disableSelect;
                    mSelectedList.add(s2);
                    iterator.remove();
                    find = true;
                    changed = true;
                    break;
                }
            }
//            if (!find) {
//                mSelectedList.add(s1);
//            }
        }
        return changed;
    }
    private boolean setSelected(List<ScheduleModel> selected) {
        if (CollectionUtil.isEmptyOrNull(selected)) {
            return false;
        }
        boolean selectedChange = false;
        for (int i = 0; i < scheduleList.size(); i++) {
            ScheduleModel schedule = scheduleList.get(i);
            for (int j = 0; j < selected.size(); j++) {
                ScheduleModel selectedSchedule = selected.get(j);
                if (schedule.equals(selectedSchedule)) {
                    schedule.selected = true;
                    schedule.disableSelected = selectedSchedule.disableSelected;
                    selectedChange = true;
                }
            }
        }
        return selectedChange;
    }

    @NotNull
    @Override
    public ScheduleSearchAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        View inflate = LayoutInflater.from(mContext).inflate(R.layout.unifiedsearch_schedule_search_list_item, viewGroup, false);
        return new ScheduleSearchAdapter.ViewHolder(inflate);
    }

    @Override
    public void onBindViewHolder(@NonNull ScheduleSearchAdapter.ViewHolder viewHolder, int index) {
        if (index < 0 || index >= scheduleList.size()) {
            return;
        }
        ScheduleModel schedule = scheduleList.get(index);
        if (schedule == null) {
            return;
        }
        boolean isExpired = schedule.isExpired();

        if (mTimeDivider) {
            viewHolder.mHeader.setVisibility(schedule.firstOfDay ? View.VISIBLE : View.GONE);
            if (schedule.firstOfDay) {
                viewHolder.mHeaderTextView.setText(makeHeaderText(DateUtil.startOfDay(schedule.getBeginDateTime())));
                if (isExpired) {
                    viewHolder.mItemTitle.setTextColor(mContext.getColor(R.color.unifiedsearch_sub_title_text_color));
                } else {
                    viewHolder.mItemTitle.setTextColor(mContext.getColor(R.color.unifiedsearch_schedule_date_color));
                }
            }
            viewHolder.mDaySeparator.setVisibility(schedule.endOfDay && index < scheduleList.size() - 1 ? View.VISIBLE : View.GONE);
        } else {
            viewHolder.mHeader.setVisibility(View.GONE);
            viewHolder.mDaySeparator.setVisibility(View.GONE);
        }

        viewHolder.mItemCircle.setImageDrawable(DrawableEx.INSTANCE.solidOval(getColor(schedule)));
        viewHolder.mItemCircle.setAlpha(isExpired ? 0.3f : 1);

        String title = schedule.highlight != null && !TextUtils.isEmpty(schedule.highlight.title) ? schedule.highlight.title : schedule.getSubject();
        if (TextUtils.isEmpty(title)) {
            title = mContext.getString(R.string.schedule_no_title);
        }
        viewHolder.mItemTitle.setTextColor(isExpired ? mContext.getColor(R.color.unifiedsearch_sub_title_text_color) : mContext.getColor(R.color.unifiedsearch_title_text_color));
        viewHolder.mItemTitle.setText(TextUtil.getSpanText(title.replaceAll("\n", " "), mContext.getResources().getColor(R.color.unifiedsearch_highlight_color)));

        if (ScheduleModel.APPOINTMENT_TYPE_MASTER.equals(schedule.getAppointmentType())) {
            Drawable repeat = schedule.isExpired() ? ContextCompat.getDrawable(mContext, R.drawable.unifiedsearch_repeat_expired):
                        ContextCompat.getDrawable(mContext, R.drawable.unifiedsearch_ic_repeat);
            if (repeat != null) {
                repeat.setBounds(0, 0, repeat.getIntrinsicWidth(), repeat.getIntrinsicHeight());
            }
            viewHolder.mItemTitle.setCompoundDrawables(null , null, repeat, null);
        } else if (ScheduleModel.APPOINTMENT_TYPE_EXCEPTION.equals(schedule.getAppointmentType())) {
            Drawable repeat = schedule.isExpired() ? ContextCompat.getDrawable(mContext, R.drawable.unifiedsearch_repeat_exception_expired):
                    ContextCompat.getDrawable(mContext, R.drawable.unifiedsearch_ic_repeat_exception);
            if (repeat != null) {
                repeat.setBounds(0, 0, repeat.getIntrinsicWidth(), repeat.getIntrinsicHeight());
            }
            viewHolder.mItemTitle.setCompoundDrawables(null , null, repeat, null);
        } else {
            viewHolder.mItemTitle.setCompoundDrawables(null , null, null, null);
        }
        viewHolder.mItemTimeText.setText(mContext.getString(R.string.calendar_schedule_selector_time, makeTimeText(schedule)));

        if (mConfig.isOrganizerEnable()) {
            viewHolder.mItemFromText.setVisibility(View.VISIBLE);
            viewHolder.mItemFromText.setText(mContext.getString(R.string.calendar_schedule_selector_creator, makeFromText(schedule)));
        } else {
            viewHolder.mItemFromText.setVisibility(View.GONE);
        }

        if (mConfig.isCalendarEnable()) {
            viewHolder.mItemCalendarText.setVisibility(View.VISIBLE);
            viewHolder.mItemCalendarText.setText(mContext.getString(R.string.calendar_schedule_selector_calendar, schedule.calendarTitle));
        } else {
            viewHolder.mItemCalendarText.setVisibility(View.GONE);
        }

        if (mConfig.isRoomEnable()) {
            if (schedule.fixedMeetingRoomList != null && schedule.fixedMeetingRoomList.size() != 0) {
                viewHolder.mItemLocationText.setVisibility(View.VISIBLE);
                viewHolder.mItemLocationText.setText(mContext.getString(R.string.calendar_schedule_selector_conference_room, schedule.getConferenceRoomName()));
            } else {
                viewHolder.mItemLocationText.setVisibility(View.GONE);
            }
        } else if (mConfig.isLocationEnable()) {
            viewHolder.mItemLocationText.setVisibility(!TextUtils.isEmpty(schedule.getLocation()) ? View.VISIBLE : View.GONE);
            String address = schedule.highlight != null && !TextUtils.isEmpty(schedule.highlight.address) ? schedule.highlight.address : schedule.getLocation();
            if (!TextUtils.isEmpty(address)) {
                CharSequence addressSpan = TextUtil.getSpanText(address.replaceAll("\n", " "), mContext.getResources().getColor(R.color.unifiedsearch_highlight_color));
                viewHolder.mItemLocationText.setText(mContext.getString(R.string.calendar_schedule_selector_location, addressSpan));
            }
        } else {
            viewHolder.mItemLocationText.setVisibility(View.GONE);
        }

        if (mConfig.isSubcategoryEnable()) {
            String subcategory = getSubcategoryText(schedule.getCategory(), schedule.getSubcategory());
            if (TextUtils.isEmpty(subcategory)) {
                viewHolder.mSubcategoryText.setVisibility(View.GONE);
            } else {
                viewHolder.mSubcategoryText.setVisibility(View.VISIBLE);
                viewHolder.mSubcategoryText.setText(mContext.getString(R.string.unifiedsearch_schedule_selector_subcategory, subcategory));
            }
        } else {
            viewHolder.mSubcategoryText.setVisibility(View.GONE);
        }

        if (mSelectMode == ScheduleSelectMode.MULTIPLE) {
            //viewHolder.mItemCircle.setVisibility(View.GONE);
            viewHolder.mItemCheck.setVisibility(View.VISIBLE);
            if (schedule.selected) {
                viewHolder.mItemCheck.setText(R.string.icon_padding_checkcircle);
                if (schedule.disableSelected) {
                    viewHolder.mItemCheck.setTextColor(ContextCompat.getColor(mContext, R.color.unifiedsearch_joyday_unselected_color));
                } else {
                    viewHolder.mItemCheck.setTextColor(ContextCompat.getColor(mContext, R.color.unifiedsearch_joyday_check_color));
                }
            } else {
                viewHolder.mItemCheck.setText(R.string.icon_prompt_circle);
                viewHolder.mItemCheck.setTextColor(ContextCompat.getColor(mContext, R.color.unifiedsearch_joyday_uncheck_color));
            }
        } else {
            //viewHolder.mItemCircle.setVisibility(View.VISIBLE);
            viewHolder.mItemCheck.setVisibility(View.GONE);
        }
    }

    private String getSubcategoryText(String category, String subcategory) {
        if (mSubcategories == null) return null;
        List<Subcategory> list = mSubcategories.get(category);
        for (Subcategory item : list) {
            if (TextUtils.equals(item.getSubcategory(), subcategory)) {
                return item.getText();
            }
        }
        return null;
    }

    @Override
    public int getItemCount() {
        return scheduleList == null ? 0 : scheduleList.size();
    }

    public void setSelectedState(ScheduleModel scheduleModel, boolean selected) {
        if (scheduleList == null) {
            return;
        }
        for (int i = 0; i < scheduleList.size(); i++) {
            ScheduleModel s = scheduleList.get(i);
            if (s.equals(scheduleModel)) {
                s.selected = selected;
                if (selected) {
                    mSelectedList.add(s);
                } else {
                    mSelectedList.remove(s);
                }
                this.notifyItemChanged(i);
                break;
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setSubcategories(Map<String, List<Subcategory>> subcategories) {
        mSubcategories = subcategories;
        this.notifyDataSetChanged();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        final public View mHeader;
        final public TextView mHeaderTextView;
        final public View mDaySeparator;
        final public ImageView mItemCircle;
        final public TextView mItemTitle;
        final public TextView mItemTimeText;
        final public TextView mItemFromText;
        final public TextView mItemLocationText;
        final public TextView mItemCheck;
        final public TextView mItemCalendarText;

        final public TextView mSubcategoryText;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);

            mHeader = itemView.findViewById(R.id.schedule_header);
            mHeaderTextView = itemView.findViewById(R.id.schedule_header_textview);
            mDaySeparator = itemView.findViewById(R.id.schedule_day_separator);
            mItemCircle = itemView.findViewById(R.id.schedule_item_circle);
            mItemTitle = itemView.findViewById(R.id.schedule_item_title);
            mItemTimeText = itemView.findViewById(R.id.schedule_item_time_text);
            mItemFromText = itemView.findViewById(R.id.schedule_item_from_text);
            mItemLocationText = itemView.findViewById(R.id.schedule_item_location_text);
            mItemCheck = itemView.findViewById(R.id.schedule_item_check);
            mItemCalendarText = itemView.findViewById(R.id.schedule_item_calendar_text);
            mSubcategoryText = itemView.findViewById(R.id.schedule_item_subcategory);

            itemView.setOnClickListener(v -> {
                int position = getBindingAdapterPosition(); //减headerCount
                if (position < 0) {
                    return;
                }
                ScheduleModel schedule = scheduleList.get(position);

                if (mSelectMode == ScheduleSelectMode.SINGLE && mOnSingleSelectChangeListener != null) {
                    if (schedule.disableSelected) {
                        ToastUtils.showToast(mContext, R.string.calendar_schedule_selector_disable_select_click);
                    } else {
                        mOnSingleSelectChangeListener.onItemClick(schedule);
                    }
                } else if (mSelectMode == ScheduleSelectMode.MULTIPLE) {
                    if (schedule.disableSelected) {
                        ToastUtils.showToast(mContext, R.string.calendar_schedule_selector_disable_select_click_multiple);
                    } else {
                        int selectedNum = mSelectedList.size();
                        if (!schedule.selected && mMaxNum > 0 && selectedNum >= mMaxNum) {
                            ToastUtils.showToast(mContext, R.string.calendar_schedule_selector_max_num_limit);
                        } else {
                            schedule.selected = !schedule.selected;
                            if (schedule.selected) {
                                mSelectedList.add(schedule);
                            } else {
                                mSelectedList.remove(schedule);
                            }
                            notifyItemChanged(position);
                            if (mOnMultiSelectChangeListener != null) {
                                mOnMultiSelectChangeListener.onChange(getSelectedList());
                            }
                        }
                    }
                } else if (mSelectMode == ScheduleSelectMode.NONE) {
                    openScheduleDetail(schedule);
                }
            });
        }
    }

    public List<ScheduleModel> getSelectedList() {
        if (mSelectedList == null) return new ArrayList<>();
        return mSelectedList;
    }

    private String makeTimeText(@NotNull ScheduleModel schedule) {
        return ScheduleDateFormatter.format(mContext, schedule, !mTimeDivider);
    }

    private String makeFromText(@NotNull ScheduleModel schedule) {
        if (schedule.user == null) {
            return "";
        }
        if (!TextUtils.isEmpty(schedule.user.realName)) {
            return schedule.user.realName;
        } else if (!TextUtils.isEmpty(schedule.user.email)) {
            return schedule.user.email;
        } else if (!TextUtils.isEmpty(schedule.organizer)) {
            return schedule.organizer;
        } else {
            return mContext.getResources().getString(R.string.schedule_quit_user);
        }
    }

    private String makeHeaderText(Date date) {
        String dateText;
        if (DateUtil.isToday(date)) {
            dateText = mContext.getString(R.string.joyday_today);
        } else if (DateUtil.isSameDay(date, yesterday.getTime())) {
            dateText = mContext.getString(R.string.joyday_yesterday);
        } else if (DateUtil.isSameDay(date, tomorrow.getTime())) {
            dateText = mContext.getString(R.string.joyday_tomorrow);
        } else {
            if (TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN")) {
                dateText = (date.getYear() + 1900) + "年" + (date.getMonth() + 1) + "月" + date.getDate() + "日";
            } else {
                dateText = DateUtil.getMonth(mContext, date.getMonth() + 1) + " " + date.getDate() + " " + (date.getYear() + 1900);
            }
        }
        return dateText;
    }

    private int getColor(ScheduleModel schedule) {
        int color;
        String colorString = !TextUtils.isEmpty(schedule.getColor()) ? schedule.getColor() : schedule.calendarColor;
        if (!TextUtils.isEmpty(colorString)) {
            if (colorString.startsWith("#")) {
                colorString = colorString.replaceAll("#", "#ff");
            } else if ((colorString.startsWith("ff") || colorString.startsWith("FF")) && colorString.length() == 8) {
                colorString = "#" + colorString;
            }
            if (!colorString.contains("#") && !colorString.contains("0x") && colorString.length() == 6) {
                colorString = "#ff" + colorString;
            }
        }

        try {
            color = Color.parseColor(colorString);
        } catch (Exception e) {
            e.printStackTrace();
            color = schedule.getCalendarType() == Calendar.TYPE_PERSONAL ?
                    Color.parseColor("#ff295bcc") : Color.parseColor("#ffEF5B56");
        }
        return color;
    }

    private void openScheduleDetail(ScheduleModel schedule) {
        String json = new Gson().toJson(schedule);
        String scheduleDetail = CALENDER_SCHEDULE + "/detail?mparam=" + Uri.encode(json);
        Router.build(scheduleDetail).go(mContext);
    }

    public void setOnMultiSelectChangeListener(ScheduleSearchTabDelegate.OnMultiSelectChangeListener onMultiSelectChangeListener) {
        mOnMultiSelectChangeListener = onMultiSelectChangeListener;
    }
}
