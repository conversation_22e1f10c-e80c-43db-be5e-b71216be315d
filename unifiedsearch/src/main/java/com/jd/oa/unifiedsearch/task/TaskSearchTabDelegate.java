package com.jd.oa.unifiedsearch.task;

import static com.jd.oa.router.DeepLink.JOY_WORK_DETAIL;

import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.unifiedsearch.all.util.SearchHistoryUtil;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;
import java.util.Objects;

public class TaskSearchTabDelegate extends UnifiedSearchTabDelegate {
    private static final String STATE_KEYWORD = "keyword";

    private RecyclerView mRecyclerView;
    private View mLayoutPlaceholder;
    private View mLayoutEmpty;
    private View mLayoutLoading;
    private View mLoadingIndicator;
    private View mLayoutError;
    private Button mBtnRetry;

    private TaskSearchAdapter mTaskSearchAdapter;

    private TaskSearchViewModel mViewModel;

    private Animation mLoadingAnimation;

    private String mKeyword;

    private TextView mTvFeedback;

    public TaskSearchTabDelegate(Host host) {
        super(host);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mViewModel = new ViewModelProvider((ViewModelStoreOwner) mHost.getActivity()).get(TaskSearchViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.unifiedsearch_fragment_task, container, false);

        if (savedInstanceState != null) {
            mKeyword = savedInstanceState.getString(STATE_KEYWORD);
        }

        return view;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        this.initView(mHost.requireView(), savedInstanceState);
    }

    private void initView(View view, @Nullable Bundle savedInstanceState) {
        mRecyclerView = view.findViewById(R.id.recycle_view);
        mLayoutPlaceholder = view.findViewById(R.id.layout_placeholder);
        mLayoutEmpty = view.findViewById(R.id.me_pub_empty_view);
        mLayoutLoading = view.findViewById(R.id.layout_loading);
        mLoadingIndicator = view.findViewById(R.id.iv_loading_indicator);
        mLayoutError = view.findViewById(R.id.layout_error);
        mBtnRetry = view.findViewById(R.id.btn_retry);
        // 反馈
        mTvFeedback = view.findViewById(R.id.tv_conent);
        mTvFeedback.setMovementMethod(LinkMovementMethod.getInstance());
        mTvFeedback.setText(SearchUtil.getFeedbackContent());

        //HorizontalDividerDecoration decoration = new HorizontalDividerDecoration(DensityUtil.dp2px(mHost.requireContext(), 0.8f), Color.parseColor("#FFF0F3F3"));
        //decoration.setDividerPaddingLeft((int)(64f * DisplayUtils.getDensity()));
        //mRecyclerView.addItemDecoration(decoration);

        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(mHost.getContext());
        mRecyclerView.setLayoutManager(layoutManager);

        mTaskSearchAdapter = new TaskSearchAdapter(mHost.getContext());
        mTaskSearchAdapter.showFeedback();
        mRecyclerView.setAdapter(mTaskSearchAdapter);

        mLoadingAnimation = AnimationUtils.loadAnimation(mHost.getContext(), R.anim.unifiedsearch_loading_indicator);

        mViewModel.getTaskInfoList().observe(mHost.getViewLifecycleOwner(), new Observer<Pair<String, List<TaskModel>>>() {
            @Override
            public void onChanged(Pair<String, List<TaskModel>> result) {
                if (CollectionUtil.isEmptyOrNull(result.second)) {
                    if (TextUtils.isEmpty(result.first)) {
                        mTaskSearchAdapter.clear();
                        mRecyclerView.setVisibility(View.INVISIBLE);
                        mLayoutEmpty.setVisibility(View.GONE);
                        mTvFeedback.setVisibility(View.GONE);
                        mLayoutPlaceholder.setVisibility(result.first == null ? View.INVISIBLE : View.VISIBLE);
                    } else {
                        mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                        mLayoutEmpty.setVisibility(View.VISIBLE);
                        mRecyclerView.setVisibility(View.INVISIBLE);
                        mTvFeedback.setVisibility(View.VISIBLE);
                    }
                } else {
                    mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                    mLayoutEmpty.setVisibility(View.GONE);
                    mTvFeedback.setVisibility(View.GONE);
                    mRecyclerView.setVisibility(View.VISIBLE);
                }
                mTaskSearchAdapter.refresh(result.second);
                mLayoutError.setVisibility(View.INVISIBLE);
            }
        });

        mViewModel.getLoading().observe(mHost.getViewLifecycleOwner(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                Pair<String, List<TaskModel>> value = mViewModel.getTaskInfoList().getValue();
                if (aBoolean && (value == null || CollectionUtil.isEmptyOrNull(value.second))) {
                    showLoading();
                } else {
                    hideLoading();
                }
            }
        });

        mViewModel.getError().observe(mHost.getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String s) {
                mLayoutEmpty.setVisibility(View.INVISIBLE);
                mLayoutPlaceholder.setVisibility(View.INVISIBLE);
                mRecyclerView.setVisibility(View.INVISIBLE);
                mLayoutError.setVisibility(View.VISIBLE);
            }
        });

        mBtnRetry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(mKeyword)) {
                    mViewModel.refresh(mKeyword, mHost.getSessionId());
                }
            }
        });

        mTaskSearchAdapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                TaskModel item = (TaskModel) adapter.getItem(position);
                String deepLink = JOY_WORK_DETAIL;
                if (!TextUtils.isEmpty(item.taskId)) {
                    deepLink += "?taskId=" + item.taskId;
                    Router.build(deepLink).go(view.getContext());
                    SearchHistoryUtil.addSearchHistory(mKeyword);
                }
            }
        });

        if (!TextUtils.isEmpty(mKeyword) && savedInstanceState == null) {
            //mViewModel.refresh(mKeyword);
        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (outState != null) {
            outState.putString(STATE_KEYWORD, mKeyword);
        }
    }

    @Override
    public void search(String keyword, boolean force) {
        if (!force && Objects.equals(mKeyword, keyword)) return;

        mKeyword = keyword;
        mViewModel.refresh(keyword,mHost.getSessionId());
    }

    @Override
    public void tabSelected(String keyword) {
        if (!Objects.equals(keyword, mKeyword)) {
            mViewModel.clear();
        }
        search(keyword, false);
    }

    @Override
    public void onTextChanged(String keyword) {
        search(keyword, true);
    }

    private void showLoading() {
        mLoadingAnimation.cancel();
        mLoadingAnimation.reset();

        mLoadingIndicator.startAnimation(mLoadingAnimation);
        mLayoutLoading.setVisibility(View.VISIBLE);
    }

    private void hideLoading() {
        mLayoutLoading.setVisibility(View.INVISIBLE);
    }
}