package com.jd.oa.unifiedsearch.all.filter;

import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;
import com.jd.oa.unifiedsearch.all.helper.SearchFilterConfigHelper;

import java.util.HashMap;
import java.util.Map;

public class TimeItemInfo implements Mappable, FilterNode {
    public String timeType;
    public long startTime;
    public long endTime;
    public SearchFilterConfig.Option selectedOption;
    public SearchFilterConfig.FilterItem filterItem;

    @Override
    public Map<String, Object> toMap() {
        if (startTime == 0 || endTime == 0) { return null; }
        Map<String, Object> result = new HashMap<>();
        switch (timeType) {
            case SearchFilterConfigHelper.TYPE_APPROVAL_APPLICATION_TIME:
                result.put("applicationStartTime", startTime);
                result.put("applicationEndTime", endTime);
                break;
            case SearchFilterConfigHelper.TYPE_APPROVAL_NOTIFY_TIME:
                result.put("notifyStartTime", startTime);
                result.put("notifyEndTime", endTime);
                break;
            case SearchFilterConfigHelper.TYPE_APPROVAL_COMPLETION_TIME:
                result.put("completionStartTime", startTime);
                result.put("completionEndTime", endTime);
                break;
            case SearchFilterConfigHelper.TYPE_APPROVAL_TASK_ARRIVAL_TIME:
                result.put("taskArrivalStartTime", startTime);
                result.put("taskArrivalEndTime", endTime);
                break;
        }
        return result;
    }

    @Override
    public SearchFilterConfig.FilterItem getFilterItem() {
        return filterItem;
    }

    @Override
    public SearchFilterConfig.Option getOption() {
        return selectedOption;
    }
}