package com.jd.oa.unifiedsearch.all.helper;

import static com.jd.oa.unifiedsearch.all.util.SearchUtil.EVENT_ID_SEARCH_SEARCH_ONCE;
import static com.jd.oa.unifiedsearch.all.util.SearchUtil.PAGE_ID;

import android.text.TextUtils;

import com.jd.oa.preference.JDMEUserPreference;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.Map;

/*
 * Time: 2024/4/15
 * Author: qudongshi
 * Description:
 */
public class SearchAnalyzeHelper {

//
//    erp,//用户名，必选
//    keyword,//搜索关键字，必选
//    session_id,//搜索的唯一标识，必选
//    search_type,//搜索类型TASK/ROBOT/GROUP/CONTACT/JOY_SPACE/ENCYCLO/….增加类型：MESSAGE(聊天记录)，WAITER(商家)，必选
//    data_lenght,//数据长度字节数据，单位byte
//    network_time,//网络返回数据时间,单位ms，有则必选
//    local_time,//本地搜素返回数据时间，单位ms，有则必选
//    display_time,//获取数据后逻辑处理及显示时间，单位ms，必选
//    增加requestId，每次触发搜索生成唯一标识，综合统一生成，各业务tab独立生成
//    增加触发搜索位置字段location， UNIFIED：综合；TAB：单独tab

    private static SearchAnalyzeHelper helper = null;

    private static Map<String, Map<String, String>> params;

    public String KEY_START_TIME = "START_TIME";

    private SearchAnalyzeHelper() {

    }

    public static synchronized SearchAnalyzeHelper getInstance() {
        if (helper == null) {
            helper = new SearchAnalyzeHelper();
            params = new HashMap<>();
        }
        return helper;
    }

    public void searchStart(String keyword, String sessionId, String requestId, String searchType, boolean isUnified) {
        if (paramsIsNull(requestId, keyword, searchType)) {
            return;
        }
        // 添加 erp、keyword、session_id、search_type
        String id = generateId(requestId, searchType);
        Map<String, String> param = new HashMap<>();
        param.put(KEY_START_TIME, System.currentTimeMillis() + "");
        param.put("erp", JDMEUserPreference.getInstance().get(JDMEUserPreference.KV_ENTITY_USER_NAME));
        param.put("session_id", sessionId);
        param.put("search_type", searchType);
        param.put("requestId", requestId);
        param.put("location", isUnified ? "UNIFIED" : searchType);
        param.put("keyword", keyword);
        params.put(id, param);
    }

    public void searchEnd(String requestId, String searchType, String dataLength) {
        if (paramsIsNull(requestId, searchType)) {
            return;
        }
        long endTime = System.currentTimeMillis();
        Map<String, String> param = getParam(requestId, searchType);
        if (param == null) {
            return;
        }
        if (param.get(KEY_START_TIME) == null) {
            return;
        }
        long startTime = Long.valueOf(param.get(KEY_START_TIME));
        param.put("network_time", endTime - startTime + "");
        if (!paramsIsNull(dataLength)) {
            param.put("data_length", dataLength);
        }
        param.remove(KEY_START_TIME);
        JDMAUtils.clickEvent(PAGE_ID, EVENT_ID_SEARCH_SEARCH_ONCE, param);
        params.remove(generateId(requestId, searchType));
    }


    public void onDestroy() {
        helper = null;
        params.clear();
        params = null;
    }

    public String generateId(String requestId, String searchType) {
        if (paramsIsNull(requestId, searchType)) {
            return "";
        }
        return requestId.hashCode() + searchType.hashCode() + "";
    }

    public boolean paramsIsNull(String... values) {
        if (values == null) {
            return true;
        }
        for (String val : values) {
            if (TextUtils.isEmpty(val)) {
                return true;
            }
        }
        return false;
    }

    public Map<String, String> getParam(String requestId, String searchType) {
        if (paramsIsNull(requestId, searchType) || params == null) {
            return null;
        }
        String id = generateId(requestId, searchType);
        return params.get(id);
    }
}
