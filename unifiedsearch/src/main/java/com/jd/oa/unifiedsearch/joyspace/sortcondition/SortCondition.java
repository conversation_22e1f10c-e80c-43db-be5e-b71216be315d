package com.jd.oa.unifiedsearch.joyspace.sortcondition;

import androidx.annotation.Keep;
import androidx.annotation.StringDef;

import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Keep
public class SortCondition {
    public static final String TYPE_RELATED = "related";
    public static final String TYPE_UPDATE_TIME = "update_time";
    public static final String TYPE_CREATE_TIME = "create_time";
    public static final String TYPE_RECEIVED_TIME = "received_time";
    public static final String TYPE_OPENED_TIME = "opened_time";
    public static final String TYPE_SENT_TIME = "sent_time";
    @StringDef({TYPE_RELATED, TYPE_UPDATE_TIME, TYPE_CREATE_TIME, TYPE_RECEIVED_TIME, TYPE_OPENED_TIME, TYPE_SENT_TIME})
    @Retention(RetentionPolicy.CLASS)
    @Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
    public @interface SortType {}

    public static final String CONDITION_RELATED_FIRST = "related_first";
    //public static final String CONDITION_RECENT_FIRST = "recent_first";
    //public static final String CONDITION_RECENT_LAST = "recent_last";
    public static final String CONDITION_LATEST_FIRST = "latest_first";
    public static final String CONDITION_LATEST_LAST = "latest_last";
    @StringDef({CONDITION_RELATED_FIRST, CONDITION_LATEST_FIRST, CONDITION_LATEST_LAST})
    @Retention(RetentionPolicy.CLASS)
    @Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
    public @interface SortOrder {}

    private static final Map<String,SelectedSortCondition> sDefaultCondition = new HashMap<>();

    static {
        sDefaultCondition.put(JoySpaceFilter.TYPE_RELATED, new SelectedSortCondition(TYPE_RELATED, CONDITION_RELATED_FIRST));
        sDefaultCondition.put(JoySpaceFilter.TYPE_RECEIVED, new SelectedSortCondition(TYPE_RELATED, CONDITION_RELATED_FIRST));
        sDefaultCondition.put(JoySpaceFilter.TYPE_RECENT, new SelectedSortCondition(TYPE_RELATED, CONDITION_RELATED_FIRST));
        sDefaultCondition.put(JoySpaceFilter.TYPE_SENT, new SelectedSortCondition(TYPE_RELATED, CONDITION_RELATED_FIRST));
        sDefaultCondition.put(JoySpaceFilter.TYPE_CREATED, new SelectedSortCondition(TYPE_RELATED, CONDITION_RELATED_FIRST));
        sDefaultCondition.put(JoySpaceFilter.TYPE_PUBLIC, new SelectedSortCondition(TYPE_RELATED, CONDITION_RELATED_FIRST));
    }

    private String title;
    @SortType
    private String type;
    private List<FilterOption> options;

    public SortCondition(String title, @SortType String type, List<FilterOption> options) {
        this.title = title;
        this.type = type;
        this.options = options;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<FilterOption> getOptions() {
        return options;
    }

    public void setOptions(List<FilterOption> options) {
        this.options = options;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    @Override
    public String toString() {
        return "SortCondition{" +
                "title='" + title + '\'' +
                ", type='" + type + '\'' +
                '}';
    }

    public static boolean isDefaultSortCondition(@JoySpaceFilter.FilterType String filterType, @SortType String condition, @SortOrder String order) {
        SelectedSortCondition defaultCondition = SortCondition.defaultSortCondition(filterType);
        return defaultCondition.getCondition().equals(condition) && defaultCondition.getOrder().equals(order);
    }

    public static SelectedSortCondition defaultSortCondition(@JoySpaceFilter.FilterType String filterType) {
        return sDefaultCondition.get(filterType);
    }

    public static boolean isAscend(@SortOrder String order) {
        return CONDITION_LATEST_LAST.equals(order);
    }

    public static boolean isDescend(@SortOrder String order) {
        return !isAscend(order);
    }
}
