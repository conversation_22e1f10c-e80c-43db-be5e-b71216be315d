package com.jd.oa.unifiedsearch.joyspace

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelStoreOwner
import com.chenenyu.router.annotation.Route
import com.jd.oa.AppBase
import com.jd.oa.BaseActivity
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jd.oa.abilities.apm.BuglyProLoader
import com.jd.oa.router.DeepLink
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate
import com.jd.oa.utils.InputMethodUtils
import org.json.JSONObject


@Route(DeepLink.UNIFIED_SEARCH_FOLDER)
class JoySpaceSearchFolderActivity : BaseActivity(), UnifiedSearchTabDelegate.Host {

    private var mDelegate: UnifiedSearchTabDelegate? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        supportActionBar?.apply { hide() }

        val mparam = intent.getStringExtra("mparam") ?: ""
        try {
            val param = JSONObject(mparam)
            val teamId = param.optString("teamId")
            val folderId = param.optString("folderId")
            val folderType = param.optString("folderType", null) ?: FOLDER_TYPE_NORMAL
            val folderName = param.optString("folderName")
            val teamName = param.optString("teamName")

            mDelegate = JoySpaceSearchFolderDelegate(this, teamId, folderId, folderType, folderName, teamName)

            mDelegate?.onAttach(context)

            mDelegate?.onCreate(savedInstanceState)

            val view = mDelegate?.onCreateView(layoutInflater, findViewById(android.R.id.content), savedInstanceState)
            view?.run { setContentView(view) }
            mDelegate?.onViewCreated(view, savedInstanceState)

        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, getString(R.string.me_params_error), Toast.LENGTH_SHORT).show()
            val buglyLoader = BuglyProLoader(
                AppBase.getAppContext(),
                ApmLoaderHepler.getInstance(AppBase.getAppContext()).configModel
            )
            buglyLoader.upLoadException(e)
            finish()
            return
        }
    }

    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
        mDelegate?.onActivityCreated(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mDelegate?.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        mDelegate?.onViewStateRestored(savedInstanceState)
    }

    override fun onStart() {
        super.onStart()
        mDelegate?.onStart()
    }

    override fun onResume() {
        super.onResume()
        mDelegate?.onResume()
    }

    override fun onPause() {
        super.onPause()
        mDelegate?.onPause()
    }

    override fun onStop() {
        super.onStop()
        mDelegate?.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        mDelegate?.onDetach()
        mDelegate?.onDestroyView()
        mDelegate?.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mDelegate?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String?>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        mDelegate?.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        mDelegate?.onConfigurationChanged(newConfig)
        super.onConfigurationChanged(newConfig)
    }

    override fun getContext(): Context = this

    override fun requireContext(): Context = context

    override fun getActivity(): Activity? = this

    override fun requireView(): View = findViewById(android.R.id.content)

    override fun getView(): View? = findViewById(android.R.id.content);

    override fun getViewLifecycleOwner(): LifecycleOwner = this

    override fun getViewModelStoreOwner(): ViewModelStoreOwner = this

    override fun getHostFragmentManager(): FragmentManager = supportFragmentManager

    override fun close() {
        InputMethodUtils.hideSoftInput(this)
        finish()
    }

    override fun getSessionId(): String {
       return ""
    }

    override fun getSearchId(): String {
        return ""
    }
}