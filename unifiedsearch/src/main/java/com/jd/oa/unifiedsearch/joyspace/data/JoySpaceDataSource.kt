package com.jd.oa.unifiedsearch.joyspace.data;

import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import io.reactivex.Single

interface JoySpaceDataSource {

    fun searchJoySpaceDocuments(keyword: String, params: SearchFilterQuery, start: Int, length: Int): Single<List<JoySpaceDocument>>

    fun getRelatedDocuments(start: Int, length: Int, query: SearchFilterQuery): Single<List<JoySpaceDocument>>

    fun getRecentOpenedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>>

    fun getReceivedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>>

    fun getSentDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>>

    fun getCreatedByMySelfDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>>

}
