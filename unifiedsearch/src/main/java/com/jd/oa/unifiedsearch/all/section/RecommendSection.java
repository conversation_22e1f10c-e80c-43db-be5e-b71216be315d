package com.jd.oa.unifiedsearch.all.section;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.callback.ISectionEventCallback;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.all.util.SearchHistoryUtil;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/31
 * Author: qudongshi
 * Description:
 */
public class RecommendSection extends BaseSection {

    private Context context;
    LayoutInflater layoutInflater;
    private List<String> data;

    private ISectionEventCallback eventCallback;

    public RecommendSection(Context context, List<String> data, ISectionEventCallback callback) {
        super(SectionParameters.builder().itemResourceId(R.layout.unifiedsearch_section_recommend).build(), "");
        this.context = context;
        this.layoutInflater = LayoutInflater.from(context);
        this.data = data;
        this.eventCallback = callback;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        ContentViewHolder viewHolder = new ContentViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        if (data == null) {
            return;
        }
        ContentViewHolder holder = (ContentViewHolder) viewHolder;
        if (holder.container != null && holder.container.getChildCount() > 0) {
            holder.container.removeAllViews();
        }
        for (String str : data) {
            TextView tvDoc = (TextView) layoutInflater.inflate(R.layout.unifiedsearch_section_recommend_content_info_item, holder.container, false);
            tvDoc.setText(str);
            tvDoc.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    SearchConfigHelper.getInstance().getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            eventCallback.onItemClick(str);
                            SearchHistoryUtil.addSearchHistory(str);
                        }
                    });
                }
            });
            holder.container.addView(tvDoc);
        }
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {

    }

    private class ContentViewHolder extends RecyclerView.ViewHolder {
        public LinearLayout container;

        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
            container = itemView.findViewById(R.id.section_content_info_container);
        }
    }
}
