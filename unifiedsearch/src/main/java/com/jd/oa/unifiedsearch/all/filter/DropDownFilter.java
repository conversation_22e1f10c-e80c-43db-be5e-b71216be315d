package com.jd.oa.unifiedsearch.all.filter;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.ui.popupwindow.MultiSelectedPopupWindow;
import com.jd.oa.ui.popupwindow.PopupItem;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.filter.FilterContext;
import com.jd.oa.unifiedsearch.joyspace.filter.UnifiedSearchFilter;
import com.jd.oa.unifiedsearch.joyspace.view.FilterArrow;
import com.jd.oa.unifiedsearch.joyspace.view.FilterLayout;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.util.SelectPopupWindowKt;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function2;

public class DropDownFilter extends UnifiedSearchFilter<DropDownItemInfo> {
    private FilterOption mOption;
    private SearchFilterConfig.FilterItem filterConfig;
    private List<SearchFilterConfig.Option> optionConfig;
    private FilterLayout mFilterLayout;
    private TextView mTextView;
    private final String TAG = "dropDownFilter";
    @NonNull
    private FilterOption mDefaultOption;
    private String mDefaultText = ""; //有可能默认筛选名和下拉选项名不一样
    private List<String> mDropDownItemIdList = new ArrayList<>();
    private List<SearchFilterConfig.Option> mDropDownItemOption = new ArrayList<>();

    public DropDownFilter(FilterContext filterContext, @NonNull SearchFilterConfig.FilterItem filterConfig) {
        super(filterContext);
        this.filterConfig = filterConfig;
        this.optionConfig = filterConfig.options;
        //设置默认过滤器
        if (optionConfig != null && !optionConfig.isEmpty()) {
            for (SearchFilterConfig.Option option : filterConfig.options) {
                String optionId = option.hashCode() + "";
                mDropDownItemIdList.add(optionId);
                mDropDownItemOption.add(option);
                if (option.action.equals("default") && option.defaultTitle != null) {
                    mDefaultText = option.defaultTitle.getText();
                    mDefaultOption = new FilterOption(option.title.getText(), optionId);
                }
                if (option.selected) {
                    mOption = new FilterOption(option.title.getText(), optionId);
                }
            }
        }
    }

    public List<FilterOption> getDropDownOptions() {
        List<FilterOption> result = new ArrayList<>();
        if (optionConfig == null || optionConfig.isEmpty()) {
            MELogUtil.localD(TAG, "下拉选项为空");
            return result;
        }
        for (int i = 0; i < optionConfig.size(); i++) {
            //如果excludeOption项被设为true则不在下拉列表中显示该选项
            if (optionConfig.get(i).excludeOption) {
                continue;
            }
            String optionId = optionConfig.get(i).hashCode() + "";
            result.add(new FilterOption(optionConfig.get(i).title.getText(), optionId));
        }
        MELogUtil.localD(TAG, "下拉选项: " + result);
        return result;
    }

    @Override
    protected View onCreateView(Context context, ViewGroup container) {
        mFilterLayout = (FilterLayout) LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_filter_item, container, false);
        mTextView = mFilterLayout.findViewById(R.id.tv_text);
        mTextView.setText(mDefaultText);

        FilterArrow arrow = mFilterLayout.findViewById(R.id.arrow);
        arrow.observeTo(mFilterLayout);

        if (mOption != null && !mOption.equals(mDefaultOption)) {
            setSelectedWithoutUIChange(true);
            mTextView.setText(mOption.getText());
            mFilterLayout.select();
            mFilterLayout.close();
        }

        return mFilterLayout;

    }

    private MultiSelectedPopupWindow popupWindow;

    @Override
    public void onFilterClick(AppCompatActivity activity, View view) {
        InputMethodManager im = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        int delayTime = 0;
        if (im.isActive()) {
            im.hideSoftInputFromWindow(view.getWindowToken(), 0);
            delayTime = 100;
        }

        view.postDelayed(new Runnable() {
            @Override
            public void run() {
                List<FilterOption> options = getDropDownOptions();
                final FilterOption selected = mOption == null ? mDefaultOption: mOption;
                MELogUtil.localD(TAG, "单选下拉菜单已选中: " + selected);
                popupWindow = new MultiSelectedPopupWindow(
                        activity,
                        SelectPopupWindowKt.filterOptionsToPopupItems(options, selected),
                        false,
                        Collections.singletonList(SelectPopupWindowKt.filterOptionToPopupItem(mDefaultOption, false)), //默认筛选选项
                        new Function2<List<PopupItem>, Boolean, Unit>() {
                            @Override
                            public Unit invoke(List<PopupItem> popupItems, Boolean aBoolean) {
                                MELogUtil.localD(TAG, "单选下拉菜单触发点击事件");
                                if (popupItems.isEmpty()) {
                                    MELogUtil.localD(TAG, "单选下拉菜单通过点击恢复默认选项返回默认");
                                    mOption = mDefaultOption;
                                } else {
                                    mOption = new FilterOption(popupItems.get(0).getText(), popupItems.get(0).getValue());
                                }

                                //如果没有发生变化则不做操作
                                if (mOption.equals(selected)) {
                                    MELogUtil.localD(TAG, "单选下拉选项没有发生变化");
                                    return null;
                                }

                                if (mOption.equals(mDefaultOption)) {
                                    MELogUtil.localD(TAG, "单选下拉选择默认");
                                    if (mOnChangedListener != null) {
                                        mOnChangedListener.onChanged(getValue());
                                    }
                                    reset();
                                    return null;
                                } else {
                                    MELogUtil.localD(TAG, "单选下拉选择非默认");
                                    setSelected(true);
                                    mTextView.setText(mOption.getText());
                                    mFilterLayout.select();
                                }

                                if (mOnChangedListener != null) {
                                    mOnChangedListener.onChanged(getValue());
                                }
                                return null;
                            }
                        },
                        new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                mFilterLayout.close();
                                popupWindow = null;
                                return null;
                            }
                        }
                );
                popupWindow.show(view);
                mFilterLayout.open();
            }
        }, delayTime); //等待键盘隐藏
    }

    @Override
    public void reset() {
        mTextView.setText(mDefaultText);
        mFilterLayout.reset();
        mOption = mDefaultOption;
        super.reset();
    }

    @Override
    public DropDownItemInfo getValue() {
        DropDownItemInfo result = new DropDownItemInfo();
        if (filterConfig != null) {
            result.reqKey = filterConfig.reqKey;
            result.filterItem = filterConfig;
        }
        SearchFilterConfig.Option option = getOptionConfig();
        if (option != null) {
            result.reqVal = option.reqValue;
            result.selectedOption = option;
        }
        return result;
    }

    @Override
    public void onSaveState(String type, Bundle outState) {
        super.onSaveState(type, outState);
        if (mOption != null) {
            outState.putParcelable(type, mOption);
        }
    }

    @Override
    public void onRestoreState(String type, Bundle savedState) {
        super.onRestoreState(type, savedState);
        mOption = savedState.getParcelable(type);
        if (mOption != null) {
            setSelected(true);
            mTextView.setText(mOption.getText());
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.onConfigurationChanged(newConfig);
        }
    }

    private SearchFilterConfig.Option getOptionConfig() {
        if (mOption != null) {
            for (int i = 0; i< mDropDownItemIdList.size(); i++) {
                if (mOption.getValue().equals(mDropDownItemIdList.get(i))) {
                    return mDropDownItemOption.get(i);
                }
            }
        }
        return null;
    }
}
