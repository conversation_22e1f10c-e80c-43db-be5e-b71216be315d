package com.jd.oa.unifiedsearch.joyspace;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.HighlightText;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.LocaleUtils;
import com.tencent.smtt.sdk.CookieManager;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

public class JoySpaceSearchAdapter extends BaseRecyclerAdapter<JoySpaceDocument, RecyclerView.ViewHolder> {
    private static final String TAG = "JoySpaceSearchAdapter";
    private static final String ELLIPSIS = "…";
    private OnItemClickListener mOnItemClickListener;
    private int mHighlightColor;
    private final String mHighlightTag = "em";
    private final String mStartTag = "<" + mHighlightTag + ">";
    private final String mEndTag = "</" + mHighlightTag + ">";

    private final float mPreviewTextMaxWidth;

    public JoySpaceSearchAdapter(Context context) {
        super(context);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_highlight_color);
        mPreviewTextMaxWidth = DisplayUtil.getScreenWidth(context) -
                context.getResources().getDimension(R.dimen.unifiedsearch_joyspace_item_padding_horizontal) * 2 -
                context.getResources().getDimension(R.dimen.unifiedsearch_joyspace_item_icon_size) -
                context.getResources().getDimension(R.dimen.unifiedsearch_joyspace_item_preview_text_padding);
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_recycler_item_joyspace, viewGroup, false));
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {
        ViewHolder holder = (ViewHolder) viewHolder;
        JoySpaceDocument document = getItem(i);
        if (document.getPageType() == JoySpaceDocument.PAGE_TYPE_FOLDER) {
            ImageLoader.load(getContext(), holder.image, R.drawable.unifiedsearch_joysapce_icon_folder);
        } else {
            ImageLoader.load(getContext(), holder.image, document.getMobileIconUrl(), R.drawable.unifiedsearch_joyspace_icon_default);
        }

        holder.shortcut.setVisibility(document.getPageType() == JoySpaceDocument.PAGE_TYPE_DOCUMENT_SHORTCUT ? View.VISIBLE : View.INVISIBLE);

        if (TextUtils.isEmpty(document.getTitleText())) {
            if (document.getStatus() == JoySpaceDocument.STATUS_DELETED) {
                holder.title.setText(getContext().getString(R.string.unifiedsearch_joyspace_document_deleted) + getContext().getString(R.string.unifiedsearch_joyspace_document_no_title));
            } else {
                holder.title.setText(getContext().getString(R.string.unifiedsearch_joyspace_document_no_title));
            }
        } else {
            HighlightText.with(document.getTitleText()).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.title);
        }

        if (document.getHighlightText() == null) {
            String highlightText;
            try {
                highlightText = computePreviewHighlightText(document.getPreviewText(), holder.preview.getPaint(), mStartTag, mEndTag, mPreviewTextMaxWidth, 2);
                Log.d(TAG, "onBindViewHolder, " + document.getTitle() + ", " + highlightText);
            } catch (Exception e) {
                highlightText = document.getPreviewText();
            }
            document.setHighlightText(highlightText);
        }

        HighlightText.with(document.getHighlightText()).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.preview);
        holder.preview.setVisibility(TextUtils.isEmpty(document.getHighlightText()) ? View.GONE : View.VISIBLE);

        holder.desc.setText(document.getSubTitle());
        holder.unread.setVisibility(document.showUnread() ? View.VISIBLE : View.INVISIBLE);

        if (document.tagInfos != null && !document.tagInfos.isEmpty()) {
            holder.lLTag.setVisibility(View.VISIBLE);
            holder.tag1.setVisibility(View.VISIBLE);
            holder.tag1.setText(document.tagInfos.get(0).tagName);
            setTagHighLight(holder.tag1, document.tagInfos.get(0).highlighted);
            if (document.tagInfos.size() >= 2) {
                holder.tag2.setVisibility(View.VISIBLE);
                holder.tag2.setText(document.tagInfos.get(1).tagName);
                setTagHighLight(holder.tag2, document.tagInfos.get(1).highlighted);
            }
            if (document.tagInfos.size() >= 3) {
                holder.tagMore.setVisibility(View.VISIBLE);
                holder.tagMore.setText("+" + (document.tagInfos.size() - 2));
            }
            holder.tag1.setOnClickListener(v->{});
            holder.tag2.setOnClickListener(v -> {});
            holder.tagMore.setOnClickListener(v -> jumpToTagPage(document.getPageId()));
        } else {
            holder.lLTag.setVisibility(View.GONE);
            holder.tag1.setVisibility(View.GONE);
            holder.tag2.setVisibility(View.GONE);
            holder.tagMore.setVisibility(View.GONE);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    private String computePreviewHighlightText(@NonNull String text, TextPaint paint, String startTag, String endTag, float availableWidth, int lineNum) {
        if (TextUtils.isEmpty(text)) return "";

        String pureText = text.replaceAll("</?" + mHighlightTag + ">", "");
        float textTotalWidth = paint.measureText(pureText);
        if (textTotalWidth <= availableWidth * lineNum) { //2行
            return text;
        }

        int highlightStartIndex = text.indexOf(startTag);
        if (highlightStartIndex == -1) return text;

        int highlightEndIndex = text.indexOf(endTag) + endTag.length();
        if (highlightEndIndex < highlightStartIndex) return text;

        String highlight = text.substring(highlightStartIndex, highlightEndIndex);
        String highlightText = highlight.replaceAll("</?" + mHighlightTag + ">", "");
        if (TextUtils.isEmpty(highlightText)) return text;

        float frontTextWidth = paint.measureText(text, 0, highlightStartIndex);
        float highlightWidth = paint.measureText(highlightText);
        float ellipsisWidth = paint.measureText(ELLIPSIS);

        if (textTotalWidth > availableWidth * lineNum) {
            //后面会显示"…"
            if (frontTextWidth + highlightWidth < availableWidth * lineNum - ellipsisWidth) return text;
        } else {
            if (frontTextWidth + highlightWidth < availableWidth * lineNum) return text;
        }

        availableWidth -= ellipsisWidth;

        int index = findCutOffIndex(pureText, paint, availableWidth, highlightWidth, ellipsisWidth, highlightStartIndex);

        return ELLIPSIS + text.substring(index);
    }

    private int findCutOffIndex(String text, TextPaint paint, float availableWidth, float highlightWidth, float ellipsisWidth, int start) {
        int index = start - 1;
        float frontWidth, resultWidth;
        while (index >= 0) {
            frontWidth = paint.measureText(text, index, start);
            resultWidth = paint.measureText(text, index, text.length());
            float availWidth = availableWidth;
            if (resultWidth > availableWidth) {
                //减去尾部"…"的宽度
                availWidth -= ellipsisWidth;
            }
            if (frontWidth > (availWidth - highlightWidth) / 2) {
                if (resultWidth >= availWidth) {
                    index++;//多移一位，不显示尾部"…"
                    break;
                }
            }
            index--;
        }
        return index;
    }

    private void jumpToTagPage(String pageId) {
        String domain = "joyspace.jd.com";
        if (TextUtils.isEmpty(CookieManager.getInstance().getCookie(domain))) {
            CookieManager cookieManager = CookieManager.getInstance();
            cookieManager.setCookie(domain, "focus-token=" + TokenManager.getInstance().getAccessToken());
            cookieManager.setCookie(domain, "focus-team-id=" + PreferenceManager.UserInfo.getTenantCode());
            cookieManager.setCookie(domain, "focus-client=Android");
            cookieManager.setCookie(domain, "focus-lang=" + LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
            CookieManager.getInstance().flush();
        }
//        if (NetEnvironmentConfigModel.PREV.equalsIgnoreCase(PreferenceManager.UserInfo.getNetEnvironment())) {
//            String url = String.format("https://joyspace-pre2.jd.com/h5/tags/selector/%s", pageId);
//            String deepLink = DeepLink.webUrl("201906270537",url, 0);
//            Router.build(deepLink).go(getContext());
//        }
//        else {
        String url = "https://joyspace.jd.com/h5/tags/selector/" + pageId;
        String deepLink = DeepLink.webUrl(url, 0);
        Router.build(deepLink).go(getContext());
//        }
    }

    private void setTagHighLight(TextView tv, boolean isHighLight) {
        if (isHighLight) {
            tv.setBackgroundResource(R.drawable.unifiedsearch_search_bg_highlight);
            tv.setTextColor(Color.parseColor("#EDEFFD"));
        } else {
            tv.setBackgroundResource(R.drawable.unifiedsearch_search_bg_normal);
            tv.setTextColor(Color.parseColor("#4A5FE8"));
        }
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        ImageView image;
        TextView title;
        TextView preview;
        TextView desc;
        ImageView unread;
        ImageView shortcut;
        TextView tag1, tag2, tagMore;
        LinearLayout lLTag ;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            image = itemView.findViewById(R.id.iv_image);
            title = itemView.findViewById(R.id.tv_title);
            preview = itemView.findViewById(R.id.tv_preview);
            desc = itemView.findViewById(R.id.tv_subtitle);
            unread = itemView.findViewById(R.id.iv_unread);
            shortcut = itemView.findViewById(R.id.iv_shortcut);
            tag1 = itemView.findViewById(R.id.tv_tag1);
            tag2 = itemView.findViewById(R.id.tv_tag2);
            tagMore = itemView.findViewById(R.id.tv_tag_more);
            lLTag = itemView.findViewById(R.id.ll_tag);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        mOnItemClickListener.onItemClick(JoySpaceSearchAdapter.this, v, getBindingAdapterPosition());
                    }
                }
            });
        }
    }
}