package com.jd.oa.unifiedsearch.joyspace.view;

import android.app.Dialog;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDialog;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition;

import java.util.List;

public class SortConditionDialog extends BaseBottomDialogFragment implements SortConditionLayout.OnConditionClickListener {

    private LinearLayout mLayoutContainer;

    private List<SortCondition> mList;

    private SortCondition mSelectedCondition;
    private TextView mSelectedView;

    private OnConditionSelected mOnConditionSelected;

    private @SortCondition.SortType String mSelectedConditionType;
    private @SortCondition.SortOrder String mSelectedOptionType;

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        AppCompatDialog dialog = new AppCompatDialog(getContext(), R.style.UnifiedSearchFilterOptionDialogStyle);

        View view = LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_joyspace_dialog_sort_condition, null);
        initView(view);
        dialog.setContentView(view);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }

        return dialog;
    }

    private void initView(View view) {
        mLayoutContainer = view.findViewById(R.id.layout_container);

        if (mList != null) {
            mLayoutContainer.removeAllViews();
            for (SortCondition sortCondition : mList) {
                SortConditionLayout layout = new SortConditionLayout(getContext());
                layout.setCondition(sortCondition);
                layout.setOnLeftClickListener(this);
                layout.setOnRightClickListener(this);
                mLayoutContainer.addView(layout);

                if (sortCondition.getType().equals(mSelectedConditionType)) {
                    mSelectedCondition = sortCondition;
                    mSelectedView = layout.setSelected(mSelectedOptionType);
                }
            }
        }
    }

    @Override
    public void onClick(SortConditionLayout layout, TextView view, FilterOption option) {
        if (mSelectedView != null) {
            mSelectedView.setSelected(false);
            mSelectedView.getPaint().setFakeBoldText(false);
        }
        mSelectedCondition = layout.getCondition();
        mSelectedView = view;

        mSelectedView.setSelected(true);
        mSelectedView.getPaint().setFakeBoldText(true);

        if (mOnConditionSelected != null) {
            mOnConditionSelected.onSelect(mSelectedCondition, option);
        }

        view.postDelayed(this::dismiss, 32);
    }

    public void setSortConditions(List<SortCondition> list) {
        mList = list;
    }

    public void setSelectedCondition(@SortCondition.SortType String conditionType, String optionType) {
        this.mSelectedConditionType = conditionType;
        this.mSelectedOptionType = optionType;
    }


    public void setOnConditionSelected(OnConditionSelected onConditionSelected) {
        mOnConditionSelected = onConditionSelected;
    }

    public interface OnConditionSelected {

        void onSelect(SortCondition condition, FilterOption option);
    }
}