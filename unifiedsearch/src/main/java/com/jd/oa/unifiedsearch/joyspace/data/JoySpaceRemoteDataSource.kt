package com.jd.oa.unifiedsearch.joyspace.data;

import android.text.TextUtils
import com.alibaba.fastjson.JSON
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition
import com.jd.oa.utils.CollectionUtil
import io.reactivex.Single
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

class JoySpaceRemoteDataSource: JoySpaceDataSource {

    override fun searchJoySpaceDocuments(
        keyword: String,
        params: SearchFilterQuery,
        start: Int,
        length: Int
    ): Single<List<JoySpaceDocument>> {
        return Single.create {
            val queryParams = mutableMapOf<String,Any>(
                "search" to keyword,
                "start" to start,
                "length" to length,
                "classiFication" to params.classification,
                "orderBy" to params.orderBy,
                "sortOrder" to params.sortOrder,
                "digestLength" to params.digestLength,
                "scene" to "me_joyspace_global"
            )

            if (!TextUtils.isEmpty(params.t)) {
                queryParams["t"] = params.t
            }

            if (!TextUtils.isEmpty(params.teamId) && !TextUtils.isEmpty(params.folderId)) {
                queryParams["teamId"] = params.teamId
                queryParams["folderId"] = params.folderId
            }

            if (params.timeRange != 0) {
                queryParams["timeRange"] = params.timeRange
            }

            if (CollectionUtil.notNullOrEmpty(params.pageType)) {
                queryParams["pageType"] = params.pageType
            }

            if (CollectionUtil.notNullOrEmpty(params.creatorsFromDD)) {
                queryParams["creatorsFromDD"] = params.creatorsFromDD
            }

            if (CollectionUtil.notNullOrEmpty(params.sendersFromDD)) {
                queryParams["sendersFromDD"] = params.sendersFromDD
            }

            if (CollectionUtil.notNullOrEmpty(params.receiversFromDD)) {
                queryParams["receiversFromDD"] = params.receiversFromDD
            }

            HttpManager.post(null, queryParams, object : SimpleRequestCallback<String>() {

                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    val jsonObject = JSONObject(info?.result ?: "")
                    val status = jsonObject.getString("status")
                    if ("success" == status) {
                        val dataString = jsonObject.getJSONObject("data").getString("data")
                        val data = JSON.parseArray(dataString, JoySpaceDocument::class.java)
                        it.onSuccess(data)
                    } else {
                        val errCode = jsonObject.optString("errCode")
                        val errMsg = jsonObject.optString("errMsg")
                        it.onError(JoyspaceSearchException(errCode, errMsg))
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.onError(Exception(exception?.message ?: info ?: "Request failed"))
                }
            }, "joyspace.search.global.v2")
        }
    }

    override fun getRelatedDocuments(start: Int, length: Int, query: SearchFilterQuery): Single<List<JoySpaceDocument>> {
        return searchJoySpaceDocuments("", query, start, length)
    }

    override fun getRecentOpenedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return Single.create {
            val headers: MutableMap<String, String> = HashMap()
            headers[HttpManager.HEADER_KEY_METHOD_GET] = "true"

            val params: MutableMap<String, Any> = HashMap()
            params["start"] = start
            params["length"] = length

//            val sort = parseSortCondition(sortCondition)
//            if (!TextUtils.isEmpty(sort)) {
//                params["sort"] = sort
//            }

            HttpManager.v2().post(null, headers, params, object : SimpleRequestCallback<String?>(null) {

                    override fun onSuccess(info: ResponseInfo<String?>) {
                        super.onSuccess(info)
                        val jsonObject = JSONObject(info?.result ?: "")
                        val status = jsonObject.getString("status")
                        if ("success" == status) {
                            val list = mutableListOf<JoySpaceDocument>()
                            val pages = jsonObject.getJSONObject("data").optJSONArray("pages") ?: JSONArray()
                            for (index in 0 until pages.length()) {
                                val pageObject = pages.getJSONObject(index)
                                val opened = JSON.parseObject(pages.getString(index), JoySpaceDocument::class.java)
                                opened.createName = pageObject.optJSONObject("author")?.optString("name")
                                opened.openedAt = opened.createdAt
                                list.add(opened)
                            }
                            it.onSuccess(list)
                        } else {
                            it.onError(Exception("Request failed"))
                        }
                    }

                    override fun onFailure(exception: HttpException, info: String) {
                        super.onFailure(exception, info)
                        it.onError(Exception(exception?.message ?: info ?: "Request failed"))
                    }

                }, "joyspace.pages.recent")
        }
    }

    override fun getReceivedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return Single.create {
            val headers: MutableMap<String, String> = HashMap()
            headers[HttpManager.HEADER_KEY_METHOD_GET] = "true"

            val params: MutableMap<String, Any> = HashMap()
            params["start"] = start
            params["length"] = length

//            val sort = parseSortCondition(sortCondition)
//            if (!TextUtils.isEmpty(sort)) {
//                params["sort"] = sort
//            }

            HttpManager.v2().post(null, headers, params, object : SimpleRequestCallback<String?>(null) {

                override fun onSuccess(info: ResponseInfo<String?>) {
                    super.onSuccess(info)
                    val result = JSONObject(info.result ?: "")
                    val status = result.getString("status")
                    if ("success" == status) {
                        val list = mutableListOf<JoySpaceDocument>()
                        val jsonArray = result.getJSONObject("data").optJSONArray("shares") ?: JSONArray()
                        for (index in 0 until jsonArray.length()) {
                            val jsonObject = jsonArray.getJSONObject(index)
                            if (jsonObject.has("page")) {
                                val received = JSON.parseObject(jsonObject.getString("page"), JoySpaceDocument::class.java)
                                val share = jsonObject.optJSONObject("share")
                                received.shareName = share?.optString("name")
                                received.sharedAt = jsonObject.optString("shared_at")
                                received.status = jsonObject.optInt("status")
                                received.isUnread = jsonObject.optInt("read_amount", 0) == 0
                                list.add(received)
                            }
                        }
                        it.onSuccess(list)
                    } else {
                        it.onError(Exception("Request failed"))
                    }
                }

                override fun onFailure(exception: HttpException, info: String) {
                    super.onFailure(exception, info)
                    it.onError(Exception(exception?.message ?: info ?: "Request failed"))
                }

            }, "joyspace.pages.share")
        }
    }

    override fun getSentDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return Single.create {
            val headers: MutableMap<String, String> = HashMap()
            headers[HttpManager.HEADER_KEY_METHOD_GET] = "true"

            val params: MutableMap<String, Any> = HashMap()
            params["start"] = start
            params["length"] = length

//            val sort = parseSortCondition(sortCondition)
//            if (!TextUtils.isEmpty(sort)) {
//                params["sort"] = sort
//            }

            HttpManager.v2().post(null, headers, params, object : SimpleRequestCallback<String?>(null) {

                override fun onSuccess(info: ResponseInfo<String?>) {
                    super.onSuccess(info)
                    val result = JSONObject(info?.result ?: "")
                    val status = result.getString("status")
                    if ("success" == status) {
                        val list = mutableListOf<JoySpaceDocument>()
                        val jsonArray = result.getJSONObject("data").getJSONArray("pages")
                        for (index in 0 until jsonArray.length()) {
                            val jsonObject = jsonArray.getJSONObject(index)
                            if (jsonObject.has("page")) {
                                val sent = JSON.parseObject(jsonObject.getString("page"), JoySpaceDocument::class.java)
                                sent.sharedAt = sent.createdAt
                                sent.unreadAmount = jsonObject.getInt("unreadCount")
                                list.add(sent)
                            }
                        }
                        it.onSuccess(list)
                    } else {
                        it.onError(Exception("Request failed"))
                    }
                }

                override fun onFailure(exception: HttpException, info: String) {
                    super.onFailure(exception, info)
                    it.onError(Exception(exception?.message ?: info ?: "Request failed"))
                }

            }, "joyspace.pages.shareBy")
        }
    }

    override fun getCreatedByMySelfDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        return Single.create {
            val headers: MutableMap<String, String> = HashMap()
            headers[HttpManager.HEADER_KEY_METHOD_GET] = "true"
            val params: MutableMap<String, Any> = HashMap()
            params["start"] = start
            params["length"] = length

//            val sort = parseSortCondition(sortCondition)
//            if (!TextUtils.isEmpty(sort)) {
//                params["sort"] = sort
//            }

            HttpManager.v2().post(null, headers, params, object : SimpleRequestCallback<String?>(null) {

                override fun onSuccess(info: ResponseInfo<String?>?) {
                    super.onSuccess(info)
                    val result = JSONObject(info?.result ?: "")
                    val status = result.getString("status")
                    if ("success" == status) {
                        val jsonArray = result.getJSONObject("data").getString("pages")
                        val data = JSON.parseArray(jsonArray, JoySpaceDocument::class.java)
                        it.onSuccess(data)
                    } else {
                        it.onError(Exception("Request failed"))
                    }
                }

                override fun onFailure(exception: HttpException, info: String) {
                    super.onFailure(exception, info)
                    it.onError(Exception(exception?.message ?: info ?: "Request failed"))
                }

            }, "joyspace.pages.mycreate")
        }
    }


    private fun parseSortCondition(sortCondition: SelectedSortCondition?):String {
        if (sortCondition == null) return ""
        val sort = when(sortCondition.condition) {
            SortCondition.TYPE_CREATE_TIME -> "created_at"
            SortCondition.TYPE_UPDATE_TIME -> "updated_at"
            SortCondition.TYPE_RECEIVED_TIME -> "received_at"
            SortCondition.TYPE_SENT_TIME -> "created_at"
            SortCondition.TYPE_OPENED_TIME -> "created_at"
            else -> ""
        }
        if(TextUtils.isEmpty(sort)) return sort
        return if (SortCondition.isDescend(sortCondition.order)) "-$sort" else sort
    }
}

class JoyspaceSearchException(val code: String?, val msg: String?): Exception("code: $code, msg: $msg") {
    companion object {
        const val ERROR_SENSITIVE_WORD = "80001"
    }
}