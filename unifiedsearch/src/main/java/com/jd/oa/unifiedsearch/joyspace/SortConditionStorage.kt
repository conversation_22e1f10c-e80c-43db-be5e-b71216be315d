package com.jd.oa.unifiedsearch.joyspace

import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition

interface SortConditionStorage {

    fun setSelectedSortCondition(
        @JoySpaceFilter.FilterType type: String?,
        condition: SelectedSortCondition?,
    )

    fun getSelectedSortCondition(@JoySpaceFilter.FilterType type: String?): SelectedSortCondition?

    fun setFolderSelectedSortCondition(condition: SelectedSortCondition?)

    fun getFolderSelectedSortCondition(): SelectedSortCondition?
}

class MemorySortConditionStorage() : SortConditionStorage {

    companion object {
        private const val FOLDER_KEY = "joyspace_folder_selected_sort_condition"
    }

    private val map: MutableMap<String?,SelectedSortCondition?> = mutableMapOf()

    override fun setSelectedSortCondition(type: String?, condition: SelectedSortCondition?) {
        map[type] = condition
    }

    override fun getSelectedSortCondition(type: String?): SelectedSortCondition? {
        return map[type]
    }

    override fun setFolderSelectedSortCondition(condition: SelectedSortCondition?) {
        map[FOLDER_KEY] = condition
    }

    override fun getFolderSelectedSortCondition(): SelectedSortCondition? {
        return map[FOLDER_KEY]
    }
}