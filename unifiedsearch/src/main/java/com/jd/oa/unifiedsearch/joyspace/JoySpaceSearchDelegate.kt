package com.jd.oa.unifiedsearch.joyspace

import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.util.Log
import android.view.View
import android.view.ViewStub
import android.view.animation.AnimationUtils
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.CallSuper
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.Router
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.ui.popupwindow.MultiSelectedPopupWindow
import com.jd.oa.ui.popupwindow.PopupItem
import com.jd.oa.ui.recycler.HorizontalDividerDecoration
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate
import com.jd.oa.unifiedsearch.all.util.SearchUtil
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument
import com.jd.oa.unifiedsearch.joyspace.data.JoyspaceSearchException
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortConditionTextBuilder
import com.jd.oa.unifiedsearch.joyspace.utils.generateDetailDeeplink
import com.jd.oa.unifiedsearch.joyspace.view.SearchLoadingIndicator
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.DisplayUtils
import com.pei.pulluploadhelper.PullUpLoadHelper
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

abstract class JoySpaceSearchDelegate(host: Host) : UnifiedSearchTabDelegate(host) {

    companion object {
        const val TAG = "JoySpaceSearchDelegate"
        const val STATE_KEYWORD = "keyword"
    }

    protected val mRecyclerView: RecyclerView by lazy {
        mHost.requireView().findViewById(R.id.recycle_view)
    }
    protected val mStubEmpty: ViewStub by lazy { mHost.requireView().findViewById(R.id.stub_empty) }
    protected val mLayoutLoading: View by lazy {
        mHost.requireView().findViewById(R.id.layout_loading)
    }
    protected val mIvLoadingIndicator: ImageView by lazy {
        mHost.requireView().findViewById(R.id.iv_loading_indicator)
    }
    protected val mLayoutError: View by lazy { mHost.requireView().findViewById(R.id.layout_error) }
    protected val mBtnRetry: Button by lazy { mHost.requireView().findViewById(R.id.btn_retry) }
    protected val mLayoutSensitive: View? by lazy { mHost.requireView().findViewById(R.id.layout_sensitive) }
    protected val mContent: View by lazy { mHost.requireView().findViewById(R.id.content) }

    protected val mAdapter: JoySpaceSearchAdapter by lazy { JoySpaceSearchAdapter(mHost.context) }
    protected lateinit var mPullUpLoadHelper: PullUpLoadHelper
    protected val mViewModel: JoySpaceSearchViewModel by lazy {
        ViewModelProvider(mHost.viewModelStoreOwner).get(
            JoySpaceSearchViewModel::class.java
        )
    }
    protected var mKeyword: String? = null
    protected var sortPopup: MultiSelectedPopupWindow? = null

    //protected val mSortConditionDialog: SortConditionDialog by lazy { SortConditionDialog() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        savedInstanceState?.run { mKeyword = getString(STATE_KEYWORD) }
    }

    override fun onViewCreated(view: View?, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.initView(view, savedInstanceState)
    }

    @CallSuper
    protected open fun initView(view: View?, savedInstanceState: Bundle?) {

        val layoutManager = LinearLayoutManager(mHost.context)
        mRecyclerView.adapter = mAdapter
        mRecyclerView.layoutManager = layoutManager

        val decoration = HorizontalDividerDecoration(
            DensityUtil.dp2px(mHost.requireContext(), 0.8f),
            Color.parseColor("#FFF0F3F3")
        )
        decoration.setDividerPaddingLeft((66f * DisplayUtils.getDensity()).toInt())
        mRecyclerView.addItemDecoration(decoration)

        mPullUpLoadHelper = PullUpLoadHelper(mRecyclerView) {
            mViewModel.next()
        }

        val endIndicator = SearchLoadingIndicator(mHost.context)
        mPullUpLoadHelper.setEndIndicator(endIndicator)

        mPullUpLoadHelper.setEmpty()

        savedInstanceState?.let { restoreInstanceState(it) }

        mAdapter.setOnItemClickListener { adapter, _, position ->
            val document = adapter.getItem(position) as JoySpaceDocument
            val deeplink = generateDetailDeeplink(mHost.requireContext(), document)
            if (!TextUtils.isEmpty(document.logUrl)) {
            //  2022/8/23  埋点请求
                joySpaceLog(document.logUrl)
            }
            Router.build(deeplink).go(mHost.activity)
        }

        //mSortConditionDialog.setOnConditionSelected(this::onSortConditionSelected)

        mStubEmpty.setOnInflateListener(ViewStub.OnInflateListener { stub, inflated ->
            val feedback = inflated.findViewById<TextView>(R.id.tv_feedback)
            feedback.text = SearchUtil.getFeedbackContent()
            feedback.movementMethod = LinkMovementMethod.getInstance()
        })

        observeState()
    }

    private fun joySpaceLog(url: String) {
        if (TextUtils.isEmpty(url)) {
            return
        }
        val request = Request.Builder().url(url).get().build()
        //只支持get请求 没有办法
        val call = HttpManager.getHttpClient().newCall(request)
        call.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
            }

            override fun onResponse(call: Call, response: Response) {
                Log.e(TAG, "onResponse: " + response.body())
            }
        })
        //        val headers: MutableMap<String, String> = HashMap()
//        headers[HttpManager.HEADER_KEY_METHOD_GET] = "true"
//        HttpManager.post(null, null, object : SimpleRequestCallback<String>() {
//            override fun onSuccess(info: ResponseInfo<String>?) {
//                super.onSuccess(info)
//            }
//
//            override fun onFailure(exception: HttpException?, info: String?) {
//                super.onFailure(exception, info)
//            }
//        }, url)
    }


    protected open fun observeState() {
        mViewModel.documentList.observe(mHost.viewLifecycleOwner) {
            //val diffResult = DiffUtil.calculateDiff(JoySpaceDocumentDiffCallback(mAdapter.data, it))
            //diffResult.dispatchUpdatesTo(mAdapter)
            mAdapter.refresh(it)
            mPullUpLoadHelper.setLoaded()
        }

        mViewModel.loadState.observe(mHost.viewLifecycleOwner) { state ->
            mContent.visibility = View.VISIBLE
            when (state) {
                JoySpaceSearchViewModel.LoadState.Refreshing -> {
                    mStubEmpty.visibility = View.GONE
                    mLayoutError.visibility = View.GONE
                    mLayoutSensitive?.visibility = View.GONE
                    if (mAdapter.data.isEmpty()) {
                        mLayoutLoading.visibility = View.VISIBLE
                        val animation = AnimationUtils.loadAnimation(
                            mHost.context,
                            R.anim.unifiedsearch_loading_indicator
                        )
                        mIvLoadingIndicator.startAnimation(animation)
                    }
                }
                is JoySpaceSearchViewModel.LoadState.Refreshed -> {
                    when (state.result) {
                        JoySpaceSearchViewModel.LoadResult.Empty -> {
                            showEmpty()
                        }
                        JoySpaceSearchViewModel.LoadResult.Complete -> {
                            mLayoutLoading.visibility = View.INVISIBLE
                            mLayoutError.visibility = View.INVISIBLE
                            mLayoutSensitive?.visibility = View.GONE
                            mStubEmpty.visibility = View.GONE
                            mRecyclerView.visibility = View.VISIBLE
                            mPullUpLoadHelper.setComplete()
                        }
                        JoySpaceSearchViewModel.LoadResult.Success -> {
                            mLayoutLoading.visibility = View.INVISIBLE
                            mLayoutError.visibility = View.INVISIBLE
                            mLayoutSensitive?.visibility = View.GONE
                            mStubEmpty.visibility = View.GONE
                            mRecyclerView.visibility = View.VISIBLE
                            mRecyclerView.scrollToPosition(0)
                            mPullUpLoadHelper.setLoaded()
                        }
                        is JoySpaceSearchViewModel.LoadResult.Failed -> {
                            mLayoutLoading.visibility = View.INVISIBLE
                            mStubEmpty.visibility = View.GONE
                            mRecyclerView.visibility = View.INVISIBLE
                            mPullUpLoadHelper.setEmpty()
                            if (state.result.code == JoyspaceSearchException.ERROR_SENSITIVE_WORD) {
                                //mLayoutSensitive.inflate()
                                mLayoutSensitive?.visibility = View.VISIBLE
                            } else {
                                mLayoutError.visibility = View.VISIBLE
                            }
                        }
                    }
                }
                JoySpaceSearchViewModel.LoadState.Loading -> {
                    mLayoutLoading.visibility = View.INVISIBLE
                    mPullUpLoadHelper.setLoading()
                }
                is JoySpaceSearchViewModel.LoadState.Loaded -> {
                    when (state.result) {
                        JoySpaceSearchViewModel.LoadResult.Complete -> {
                            mLayoutLoading.visibility = View.INVISIBLE
                            mPullUpLoadHelper.setComplete()
                        }
                        JoySpaceSearchViewModel.LoadResult.Success -> {
                            mLayoutLoading.visibility = View.INVISIBLE
                            mPullUpLoadHelper.setLoaded()
                        }
                        is JoySpaceSearchViewModel.LoadResult.Failed -> {
                            mLayoutLoading.visibility = View.INVISIBLE
                            mPullUpLoadHelper.setLoaded()
                        }
                    }
                }
                else -> {}
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle?) {
        super.onSaveInstanceState(outState)
        outState?.let { it.putString(STATE_KEYWORD, mKeyword) }
    }

    protected open fun restoreInstanceState(savedState: Bundle) {}

    protected open fun showSortConditionDialog(view: View, conditions: List<SortCondition>) {
        val im = mHost.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        if (im.isActive) {
            im.hideSoftInputFromWindow(view.windowToken, 0)
        }
        val selected = getSelectedSortCondition()
        val items = conditions.flatMap<SortCondition, PopupItem> { sortCondition ->
                val type = sortCondition.type
                val items = sortCondition.options.map {
                    PopupItem(
                        SortConditionTextBuilder(type, it).buildText(view.context),
                        it.value,
                        type == selected?.condition && it.value == selected?.order,
                        data = type
                    )
                }
                return@flatMap items
            }
        sortPopup = MultiSelectedPopupWindow(
            mHost.context,
            items,
            false,
            mutableListOf(),
            { popupItems, multi ->
                if (popupItems.isNotEmpty()) {
                    val type = popupItems.first().data as String
                    val order = popupItems.first().value
                    onSortConditionSelected(type, order)
                } else {
                    onSortConditionReset()
                }
            }, {
                sortPopup = null
            }
        )

        view.postDelayed({
            sortPopup?.show(view)
        }, 100)
    }
    open fun getSelectedSortCondition(): SelectedSortCondition? = null

    protected open fun onSortConditionSelected(@SortCondition.SortType sortType: String, @SortCondition.SortOrder sortOrder: String) {
        Log.d(TAG, "onSortConditionSelected, $sortType, $sortOrder")
    }

    protected open fun onSortConditionReset() {

    }

    protected open fun showEmpty() {
        mLayoutLoading.visibility = View.INVISIBLE
        mLayoutError.visibility = View.INVISIBLE
        mLayoutSensitive?.visibility = View.GONE
        mRecyclerView.visibility = View.VISIBLE

        mStubEmpty.visibility = View.VISIBLE
        mPullUpLoadHelper.setEmpty()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (sortPopup != null && sortPopup!!.isShowing()) {
            sortPopup?.onConfigurationChanged(newConfig)
        }
    }
}