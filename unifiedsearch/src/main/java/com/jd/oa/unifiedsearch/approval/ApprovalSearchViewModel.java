package com.jd.oa.unifiedsearch.approval;

import android.text.TextUtils;
import android.util.Pair;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.unifiedsearch.all.data.SearchApprovalModel;
import com.jd.oa.unifiedsearch.all.data.SearchProcessModel;
import com.jd.oa.unifiedsearch.all.helper.SearchAnalyzeHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.approval.data.ApprovalInfo;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ApprovalSearchViewModel extends ViewModel {
    private MutableLiveData<Boolean> mLoading = new MutableLiveData<>(false);
    private MutableLiveData<String> mLoadMore = new MutableLiveData<>();
    private MutableLiveData<Pair<String, List<ApprovalInfo>>> mApprovalInfoList = new MutableLiveData<>();
    private MutableLiveData<String> mError = new MutableLiveData<>();
    private String mKeyword;
    public static final String LOAD_MORE_LOADING = "loading";
    public static final String LOAD_MORE_COMPLETE = "complete";
    public static final String LOAD_MORE_LOADED = "loaded";
    public static final String LOAD_MORE_EMPTY = "empty";
    private final int PAGE_SIZE = 20;
    private int mPageOffset = 0;

    public void refresh(String keyword, String sessionId, Map<String, Object> params) {
        mLoading.setValue(true);
        mKeyword = keyword;
        String requestId = System.currentTimeMillis() + "";
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.APPROVAL.toString(),false);
        NetWorkManager.getApprovalSearchResult(null, sessionId, keyword, params,0, PAGE_SIZE, Arrays.asList("AUDIT"), new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APPROVAL.toString(), dataLength + "");
                super.onFailure(exception, info);
                mLoading.setValue(false);
                mApprovalInfoList.setValue(Pair.create(keyword, Collections.emptyList()));
                mError.setValue(exception != null ? exception.getMessage() : info != null ? info : "network issue, please try again");
                mLoadMore.setValue(LOAD_MORE_EMPTY);
            }

            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APPROVAL.toString(), dataLength + "");
                super.onSuccess(response);
                mLoading.setValue(false);
                if (response.isSuccessful()) {
                    //接口返回成功
                    if (Objects.equals(mKeyword, keyword)) {
                        String info = response.getData(String.class);
                        SearchApprovalModel searchApprovalResponse = JsonUtils.getGson().fromJson(info, new TypeToken<SearchApprovalModel>() {
                        }.getType());
                        if (searchApprovalResponse == null || searchApprovalResponse.docs == null
                        || searchApprovalResponse.docs.isEmpty() || searchApprovalResponse.docs.get(0).records.isEmpty()) {
                            //搜索结果为空
                            mApprovalInfoList.setValue(Pair.create(keyword, Collections.emptyList()));
                            mLoadMore.setValue(LOAD_MORE_EMPTY);
                        } else {
                            mPageOffset = PAGE_SIZE;
                            mApprovalInfoList.setValue(Pair.create(keyword, searchApprovalResponse.docs.get(0).records));
                            if (searchApprovalResponse.docs.get(0).records.size() < PAGE_SIZE) {
                                mLoadMore.setValue(LOAD_MORE_COMPLETE);
                            } else {
                                mLoadMore.setValue(LOAD_MORE_LOADED);
                            }
                        }
                    } else {
                        mLoadMore.setValue(LOAD_MORE_EMPTY);
                    }
                } else {
                    mError.setValue(response.getErrorMessage());
                    mLoadMore.setValue(LOAD_MORE_EMPTY);
                }
            }
        });
    }

    public void next(String keyword, String sessionId, Map<String, Object> params) {
        mLoadMore.setValue(LOAD_MORE_LOADING);
        String requestId = System.currentTimeMillis() + "";
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.APPROVAL.toString(),false);
        NetWorkManager.getApprovalSearchResult(null, sessionId, keyword, params, mPageOffset, PAGE_SIZE, Arrays.asList("AUDIT"), new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                int dataLength = info == null ? 0 : info.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APPROVAL.toString(), dataLength + "");
                super.onFailure(exception, info);
                mLoadMore.setValue(LOAD_MORE_LOADED);
            }

            @Override
            public void onSuccess(ResponseInfo<String> response) {
                int dataLength = response.result == null ? 0 : response.result.length();
                SearchAnalyzeHelper.getInstance().searchEnd(requestId, SearchFloorType.APPROVAL.toString(), dataLength + "");
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    //接口返回成功
                    if (Objects.equals(mKeyword, keyword)) {
                        String info = response.getData(String.class);
                        SearchApprovalModel searchApprovalResponse = JsonUtils.getGson().fromJson(info, new TypeToken<SearchApprovalModel>() {
                        }.getType());
                        if (searchApprovalResponse == null || searchApprovalResponse.docs == null
                                || searchApprovalResponse.docs.isEmpty() || searchApprovalResponse.docs.get(0).records.isEmpty()) {
                            //搜索结果为空
                            mLoadMore.setValue(LOAD_MORE_COMPLETE);
                        } else {
                            List<ApprovalInfo> recordList = searchApprovalResponse.docs.get(0).records;
                            List<ApprovalInfo> approvalInfoList = new ArrayList<>();
                            int countNew = 0;
                            if (mApprovalInfoList.getValue() != null && mApprovalInfoList.getValue().second != null) {
                                approvalInfoList.addAll(mApprovalInfoList.getValue().second);
                            }
                            for (int i = 0; i < recordList.size(); i++) {
                                if (recordList.get(i).refPro != null) {
                                    approvalInfoList.add(recordList.get(i));
                                    countNew++;
                                }
                            }
                            mPageOffset += PAGE_SIZE;
                            mApprovalInfoList.setValue(Pair.create(keyword, approvalInfoList));
                            if (countNew < PAGE_SIZE) {
                                mLoadMore.setValue(LOAD_MORE_COMPLETE);
                            } else {
                                mLoadMore.setValue(LOAD_MORE_LOADED);
                            }
                        }
                    } else {
                        mLoadMore.setValue(LOAD_MORE_LOADED);
                    }
                } else {
                    mLoadMore.setValue(LOAD_MORE_LOADED);
                }
            }
        });
    }

    public MutableLiveData<Boolean> getLoading() {return mLoading;}
    public MutableLiveData<String> getLoadMore() {return mLoadMore;}

    public MutableLiveData<Pair<String, List<ApprovalInfo>>> getApprovalInfoList() {return mApprovalInfoList;}

    public MutableLiveData<String> getError() {return mError;}

    public void clear() {
        mApprovalInfoList.setValue(Pair.create(null, Collections.emptyList()));
    }
}
