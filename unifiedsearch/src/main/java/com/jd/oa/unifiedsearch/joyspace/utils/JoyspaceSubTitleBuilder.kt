package com.jd.oa.unifiedsearch.joyspace.utils

import android.content.Context
import android.text.TextUtils
import com.jd.oa.unifiedsearch.R
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument
import com.jd.oa.unifiedsearch.joyspace.filter.JoySpaceFilter
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SortCondition

class JoySpaceSubTitleBuilder(
    private val joySpaceDocument: JoySpaceDocument,
    private val dateParser: JoyspaceDateParser
    ) {

    fun buildSubTitle(context: Context, @JoySpaceFilter.FilterType type: String?, sortCondition: SelectedSortCondition?): String {
        var part1 = ""
        if (JoySpaceFilter.TYPE_RELATED == type) {
            if (!TextUtils.isEmpty(joySpaceDocument.createName)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createName)
            } else if (!TextUtils.isEmpty(joySpaceDocument.createErp)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createErp)
            }
        } else if (JoySpaceFilter.TYPE_RECEIVED == type) {
            if (!TextUtils.isEmpty(joySpaceDocument.shareName)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_sender, joySpaceDocument.shareName)
            } else if (!TextUtils.isEmpty(joySpaceDocument.shareErp)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_sender, joySpaceDocument.shareErp)
            }
        } else if (JoySpaceFilter.TYPE_RECENT == type) {
            if (!TextUtils.isEmpty(joySpaceDocument.createName)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createName)
            } else if (!TextUtils.isEmpty(joySpaceDocument.createErp)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createErp)
            }
        } else if (JoySpaceFilter.TYPE_CREATED == type) {
            //part1 = if (TextUtils.isEmpty(joySpaceDocument.parentFolderName)) "" else joySpaceDocument.parentFolderName
            if (!TextUtils.isEmpty(joySpaceDocument.createName)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createName)
            } else if (!TextUtils.isEmpty(joySpaceDocument.createErp)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createErp)
            }
        } else if (JoySpaceFilter.TYPE_SENT == type) {
            part1 = if (joySpaceDocument.unreadAmount == 0) {
                context.getString(R.string.unifiedsearch_joyspace_sent_all_read)
            } else {
                context.getString(
                    R.string.unifiedsearch_joyspace_sent_unread_count,
                    joySpaceDocument.unreadAmount
                )
            }
        } else if (JoySpaceFilter.TYPE_PUBLIC == type) {
            if (!TextUtils.isEmpty(joySpaceDocument.createName)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createName)
            } else if (!TextUtils.isEmpty(joySpaceDocument.createErp)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createErp)
            }
        } else if (TextUtils.isEmpty(type)) {
            //文件夹搜索
            if (!TextUtils.isEmpty(joySpaceDocument.createName)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createName)
            } else if (!TextUtils.isEmpty(joySpaceDocument.createErp)) {
                part1 = context.getString(R.string.unifiedsearch_joyspace_creator, joySpaceDocument.createErp)
            }
        }

        var time = ""
        if (sortCondition != null) {
            val condition = sortCondition.condition
            if (SortCondition.TYPE_RELATED == condition) {
                time = relatedTime(context, type)
            } else if (SortCondition.TYPE_UPDATE_TIME == condition) {
                time = context.getString(
                    R.string.unifiedsearch_joyspace_sort_condition_update_at,
                    dateParser.parseDateTime(context, joySpaceDocument.updatedAt)
                )
            } else if (SortCondition.TYPE_CREATE_TIME == condition) {
                time = context.getString(
                    R.string.unifiedsearch_joyspace_sort_condition_create_at,
                    dateParser.parseDateTime(context, joySpaceDocument.createdAt)
                )
            } else if (SortCondition.TYPE_RECEIVED_TIME == condition) {
                time = context.getString(
                    R.string.unifiedsearch_joyspace_sort_condition_received_at,
                    dateParser.parseDateTime(context, joySpaceDocument.sharedAt)
                )
            } else if (SortCondition.TYPE_SENT_TIME == condition) {
                time = context.getString(
                    R.string.unifiedsearch_joyspace_sort_condition_sent_at,
                    dateParser.parseDateTime(context, joySpaceDocument.sharedAt)
                )
            } else if (SortCondition.TYPE_OPENED_TIME == condition) {
                time = context.getString(
                    R.string.unifiedsearch_joyspace_sort_condition_opened_at,
                    dateParser.parseDateTime(context, joySpaceDocument.openedAt)
                )
            }
        }
        return if (TextUtils.isEmpty(part1)) time else "$part1  $time"
    }

    private fun relatedTime(context: Context, @JoySpaceFilter.FilterType type: String?): String {
        return when (type) {
            JoySpaceFilter.TYPE_RELATED -> context.getString(
                R.string.unifiedsearch_joyspace_sort_condition_update_at,
                dateParser.parseDateTime(context, joySpaceDocument.updatedAt)
            )
            JoySpaceFilter.TYPE_RECEIVED -> context.getString(
                R.string.unifiedsearch_joyspace_sort_condition_received_at,
                dateParser.parseDateTime(context, joySpaceDocument.sharedAt)
            )
            JoySpaceFilter.TYPE_RECENT -> context.getString(
                R.string.unifiedsearch_joyspace_sort_condition_opened_at,
                dateParser.parseDateTime(context, joySpaceDocument.openedAt)
            )
            JoySpaceFilter.TYPE_CREATED -> context.getString(
                R.string.unifiedsearch_joyspace_sort_condition_create_at,
                dateParser.parseDateTime(context, joySpaceDocument.createdAt)
            )
            JoySpaceFilter.TYPE_SENT -> context.getString(
                R.string.unifiedsearch_joyspace_sort_condition_sent_at,
                dateParser.parseDateTime(context, joySpaceDocument.sharedAt)
            )
            else -> context.getString(
                R.string.unifiedsearch_joyspace_sort_condition_update_at,
                dateParser.parseDateTime(context, joySpaceDocument.updatedAt)
            )
        }
    }
}