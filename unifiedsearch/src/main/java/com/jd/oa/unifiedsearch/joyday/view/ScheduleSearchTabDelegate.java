package com.jd.oa.unifiedsearch.joyday.view;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.DisplayMetrics;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.me.datetime.picker.DatePickerDialog;
import com.jd.me.datetime.picker.DatePickerView;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.timezone.HolidayHelperKt;
import com.jd.oa.ui.CircleImageViewWithGap;
import com.jd.oa.ui.popupwindow.MultiSelectedPopupWindow;
import com.jd.oa.ui.popupwindow.PopupItem;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.joyday.JoydayDuccPreference;
import com.jd.oa.unifiedsearch.joyday.ScheduleSelectorConfig;
import com.jd.oa.unifiedsearch.joyday.adapter.ScheduleSearchAdapter;
import com.jd.oa.unifiedsearch.joyday.model.Calendar;
import com.jd.oa.unifiedsearch.joyday.model.CalendarList;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleCategory;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;
import com.jd.oa.unifiedsearch.joyday.model.Subcategory;
import com.jd.oa.unifiedsearch.joyday.presenter.ScheduleSearchPresenter;
import com.jd.oa.unifiedsearch.joyday.presenter.ScheduleSearchRequestParams;
import com.jd.oa.unifiedsearch.joyday.utils.DateUtil;
import com.jd.oa.unifiedsearch.joyspace.view.FilterArrow;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;
import com.pei.pulluploadhelper.PullUpLoadHelper;

import org.jetbrains.annotations.NotNull;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function2;

public class ScheduleSearchTabDelegate extends UnifiedSearchTabDelegate implements ScheduleSearchPresenter.ViewCallback {

    private static final int MAX_NUM = 50;
    private static final int REQUEST_CODE = 1;
    private static final int REQUEST_CODE_CONTACT_SELECT = 2;

    private static final int MAX_MONTH_RANGE = 3;

    private String mKeyword;
    private Date mStartDate;
    private Date mEndDate;
    private ArrayList<MemberEntityJd> inviteContacts = new ArrayList<>();//联系人列表
    private ArrayList<String> selectedCalendarIds = new ArrayList<>();//已选择日历id列表
    private Calendar mMyPersonalCalendar;
    private List<Calendar> mMyCalendar;//只读，不要修改
    private List<Calendar> mSubscribeCalendar;//只读，不要修改

    private View calendarBtn;
    private TextView calendarTextView;
    private View datePickerBtn;
    private TextView datePickerTextView;
    private View selectContactBtn;
    private TextView selectContactTextView;
    private View moreTip;
    private TextView moreTextView;
    private CircleImageViewWithGap firstAvatar;
    private View mEmptyView;
    private View mEmptyViewImage;
    private TextView mEmptyViewTextStart;
    private TextView mEmptyViewTextMiddle;
    private TextView mEmptyViewTextEnd;
    private Button mRetryButton;
    private RecyclerView mSearchList;
    private View mBtnCategory;
    private TextView mTvCategory;
    private View mBtnReset;
    private View mResetMask;
    private ScheduleSearchAdapter mAdapter;
    private PullUpLoadHelper mLoadHelper;
    private LinearLayoutManager mLayoutManager;
    private RecyclerView.SmoothScroller mSmoothScroller;
    private Handler mHandler;
    private ScheduleSearchPresenter mPresenter;
    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            scheduleSearch();
        }
    };

    private ScheduleSelectMode mSelectMode;

    private OnSingleSelectChangeListener mOnSingleSelectChangeListener;
    private OnMultiSelectChangeListener mOnMultiSelectChangeListener;

    private ScheduleSelectorConfig mSelectorConfig;

    private String mSelectedCategory;

    private FilterArrow mCalendarArrow;
    private FilterArrow mParticipantsArrow;
    private FilterArrow mDateArrow;
    private FilterArrow mCategoryArrow;

    private ViewStub mEmptyStub;

    private View mCategoryBottomPadding;

    private View mCalendarBottomPadding;
    private MultiSelectedPopupWindow popupWindow;

    private ScheduleModel mScrollTarget;

    private JoydayDuccPreference mDuccPreference;

    public ScheduleSearchTabDelegate(Host host, ScheduleSelectorConfig config) {
        super(host);
        this.mSelectorConfig = config;
        if (!TextUtils.isEmpty(mSelectorConfig.getSelectType())) {
            mSelectMode = ScheduleSelectMode.of(mSelectorConfig.getSelectType());
        } else {
            mSelectMode = ScheduleSelectMode.NONE;
        }
        mHandler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(@NotNull Message msg) {
                super.handleMessage(msg);
                if (msg.what == 88) {
                    scheduleSearch();
                }
            }
        };
        mPresenter = new ScheduleSearchPresenter();
        mPresenter.setViewCallback(this);
        mKeyword = "";

        mDuccPreference = new JoydayDuccPreference();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LocalBroadcastManager.getInstance(mHost.getContext()).registerReceiver(mRefreshReceiver, new IntentFilter("schedule_deleted"));
    }

    @Override
    public void onDestroy() {
        LocalBroadcastManager.getInstance(mHost.getContext()).unregisterReceiver(mRefreshReceiver);
        super.onDestroy();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.unifiedsearch_fragment_schedule, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initData();
        initView(view);
        if (CalendarList.getCalendarListCache().isEmpty()) {//首次进入没有缓存
            CalendarList.getCalendarList(calendars -> {
                if (calendars != null && !calendars.isEmpty()) {
                    resetCalendar(calendars);
                    updateCalendarButtonUI();
                }
                mHandler.post(this::scheduleSearch);
            });
        } else {
            mHandler.postDelayed(this::scheduleSearch, 100);
        }
        pageEvent("mobile_Joyday_searchview");
    }

    private void initView(View view) {
        mEmptyStub = view.findViewById(R.id.stub_empty);
        mEmptyStub.setOnInflateListener(new ViewStub.OnInflateListener() {
            @Override
            public void onInflate(ViewStub stub, View inflated) {
                TextView feedback = inflated.findViewById(R.id.tv_feedback);
                feedback.setText(SearchUtil.getFeedbackContent());
                feedback.setMovementMethod(LinkMovementMethod.getInstance());
            }
        });

        //calendar picker dialog
        calendarBtn = view.findViewById(R.id.calendar_dialog_btn);
        calendarTextView = view.findViewById(R.id.calendar_dialog_btn_tv);
        mCalendarArrow = view.findViewById(R.id.arrow_calendar);
        mCalendarBottomPadding = view.findViewById(R.id.view_calendar_bottom_padding);
        updateCalendarButtonUI();
        calendarBtn.setOnClickListener(v -> {
            clickEvent("mobile_Joyday_searchview_calendar_click");
            InputMethodManager im = (InputMethodManager) mHost.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            if (im.isActive()) {
                im.hideSoftInputFromWindow(view.getWindowToken(), 0);
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        openCalendarPickerDialog(v);
                    }
                }, 200);
            } else {
                openCalendarPickerDialog(v);
            }
        });

        if (mSelectorConfig.getCalendar()) {
            calendarBtn.setVisibility(View.VISIBLE);
        } else {
            calendarBtn.setVisibility(View.GONE);
        }

        //contact selector
        selectContactBtn = view.findViewById(R.id.select_contact_dialog_btn);
        selectContactTextView = view.findViewById(R.id.select_contact_btn_tv);
        moreTip = view.findViewById(R.id.more_tip);
        moreTextView = view.findViewById(R.id.more_textview);
        firstAvatar = view.findViewById(R.id.first_avatar);
        mParticipantsArrow = view.findViewById(R.id.arrow_participants);
        updateSelectContactButtonUI();
        selectContactBtn.setOnClickListener(v -> {
            clickEvent("mobile_Joyday_searchview_participants_click");
            if (mSelectorConfig.getUser() != null && mSelectorConfig.getUser().getReadOnly()) {
                ToastUtils.showToast(mHost.getContext(), R.string.calendar_schedule_selector_user_click);
            } else {
                openContactSelector();
            }
        });

        //date picker dialog
        datePickerBtn = view.findViewById(R.id.date_picker_dialog_btn);
        datePickerTextView = view.findViewById(R.id.date_picker_btn_tv);
        mDateArrow = view.findViewById(R.id.arrow_date);
        updateDatePickerButtonUI(true);
        datePickerBtn.setOnClickListener(v -> {
            ScheduleSelectorConfig.DateRange dateRange = mSelectorConfig.getDateRange();
            if (dateRange != null && dateRange.getReadOnly()) {
                ToastUtils.showToast(mHost.getContext(), R.string.calendar_schedule_selector_date_click_tips);
            } else {
                clickEvent("mobile_Joyday_searchview_date_click");
                InputMethodManager im = (InputMethodManager) mHost.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                if (im.isActive()) {
                    im.hideSoftInputFromWindow(view.getWindowToken(), 0);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            openDatePickerDialog();
                        }
                    }, 200);
                } else {
                    openDatePickerDialog();
                }
            }
        });

        mBtnCategory = view.findViewById(R.id.btn_category);
        mTvCategory = view.findViewById(R.id.tv_category);
        mCategoryArrow = view.findViewById(R.id.arrow_category);
        mCategoryBottomPadding = view.findViewById(R.id.view_category_bottom_padding);
        updateCategoryUI(getDefaultCategory());
        mBtnCategory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                InputMethodManager im = (InputMethodManager) mHost.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                im.hideSoftInputFromWindow(view.getWindowToken(), 0);
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        showCategoryDialog(v);
                    }
                }, 200);
            }
        });

        //重置
        mBtnReset = view.findViewById(R.id.reset_btn);
        mResetMask = view.findViewById(R.id.mask);
        mBtnReset.setOnClickListener(v -> {
            InputMethodManager im = (InputMethodManager) mHost.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            im.hideSoftInputFromWindow(view.getWindowToken(), 0);
            resetSelectDate();
            resetCalendar(null);
            inviteContacts.clear();
            resetCategory();

            updateCalendarButtonUI();
            updateSelectContactButtonUI();
            updateDatePickerButtonUI(true);
            updateCategoryUI(getDefaultCategory());
            updateResetButtonUI();
            scheduleSearch();
        });
        updateResetButtonUI();

        mEmptyView = view.findViewById(R.id.empty_view);
        mEmptyViewImage = view.findViewById(R.id.empty_view_image);
        mEmptyViewTextStart = view.findViewById(R.id.empty_view_text_start);
        mEmptyViewTextMiddle = view.findViewById(R.id.empty_view_text_middle);
        mEmptyViewTextEnd = view.findViewById(R.id.empty_view_text_end);
        mRetryButton = view.findViewById(R.id.btn_retry);
        mRetryButton.setOnClickListener(v -> {
            scheduleSearch();
        });

        //搜索列表
        mSearchList = view.findViewById(R.id.search_list);
        mAdapter = new ScheduleSearchAdapter(
                mHost.getContext(),
                mSelectMode,
                mOnSingleSelectChangeListener,
                mSelectorConfig
        );
        mSearchList.setAdapter(mAdapter);
        mLayoutManager = new LinearLayoutManager(mHost.getContext(), LinearLayoutManager.VERTICAL, false);
        mSearchList.setLayoutManager(mLayoutManager);
        mLoadHelper = new PullUpLoadHelper(mSearchList, () -> {

        });
        mSmoothScroller = new LinearSmoothScroller(mHost.getContext()) {
            @Override
            protected int getVerticalSnapPreference() {
                return LinearSmoothScroller.SNAP_TO_START;
            }

            @Nullable
            @Override
            public PointF computeScrollVectorForPosition(int targetPosition) {
                return mLayoutManager.computeScrollVectorForPosition(targetPosition);
            }

            @Override
            protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                return 0.01f / displayMetrics.density;
            }

        };
        mLoadHelper.setEndIndicator(new ScheduleSearchIndicator(mHost.getContext()));

        mAdapter.setOnMultiSelectChangeListener(mOnMultiSelectChangeListener);

        if (mSelectorConfig.isSubcategoryEnable()) {
            getCategoryList();
        }
    }

    private void initData() {
        resetSelectDate();
        resetCalendar(null);
        inviteContacts.clear();
        if (mSelectorConfig.getUser() != null && mSelectorConfig.getUser().getUserList() != null) {
            List<MemberEntityJd> list = new ArrayList<>();
            for (ScheduleSelectorConfig.UserEntity userEntity : mSelectorConfig.getUser().getUserList()) {
                list.add(userEntity.toMemberEntityJd());
            }
            inviteContacts.addAll(list);
        }
        resetCategory();
    }

    @Override
    public void search(String keyword, boolean force) {
        newSearch(keyword, force);
    }

    @Override
    public void onTextChanged(String keyword) {
        newSearch(keyword, false);
    }

    private void newSearch(String keyword, boolean force) {
        keyword = keyword.trim();
        if (!force && TextUtils.equals(mKeyword, keyword)) {
            return;
        }

        mKeyword = keyword;
        if (mHandler.hasMessages(88)) {
            mHandler.removeMessages(88);
        }
        mHandler.sendEmptyMessageDelayed(88, 200);
    }

    private void scheduleSearch() {
        ScheduleSearchRequestParams requestParams = new ScheduleSearchRequestParams();
        List<Map<String, String>> contacts = new ArrayList<>();
        for (MemberEntityJd entity : inviteContacts) {
            Map<String, String> contact = new HashMap<>();
            contact.put("ddAppId", entity.mApp);
            contact.put("account", entity.mId);
            contacts.add(contact);
        }

        List<ScheduleSelectorConfig.UserEntity> list = mSelectorConfig.getOrganizerList();
        List<Map<String, String>> organizer = new ArrayList<>();
        if (list != null) {
            for (ScheduleSelectorConfig.UserEntity entity : list) {
                Map<String, String> contact = new HashMap<>();
                contact.put("ddAppId", entity.getDdAppId());
                contact.put("account", entity.getEmplAccount());
                organizer.add(contact);
            }
        }
        List<String> categoryList = new ArrayList<>();
        if (ScheduleCategory.CATEGORY_APPOINTMENT.equals(mSelectedCategory)) {
            categoryList.add(ScheduleCategory.CATEGORY_APPOINTMENT);
        } else if (ScheduleCategory.CATEGORY_MEETING.equals(mSelectedCategory)) {
            categoryList.add(ScheduleCategory.CATEGORY_MEETING);
        } else {
            categoryList.add(ScheduleCategory.CATEGORY_APPOINTMENT);
            categoryList.add(ScheduleCategory.CATEGORY_MEETING);
        }
        requestParams.setKeyword(mKeyword);
        requestParams.setStartTime(mStartDate.getTime());
        requestParams.setEndTime(mEndDate.getTime());
        requestParams.setCalendarIds(selectedCalendarIds);
        requestParams.setContacts(contacts);
        requestParams.setOrganizer(organizer);
        requestParams.setAsc(ScheduleSelectorConfig.ORDER_ASC.equals(mSelectorConfig.getSortOrder()));
        requestParams.setCategoryList(categoryList);

        if (!mSelectorConfig.getCalendar()) {
            requestParams.setOnlyAttendeeStrategyUser(true);
        }

        if (mSelectorConfig.getFrom() != null) {
            requestParams.setFrom(mSelectorConfig.getFrom());
        }
        requestParams.setPermissionCode(mSelectorConfig.getPermissionCode());

        mPresenter.scheduleSearch(requestParams);
    }

    private void updateResetButtonUI() {
        if (isDefaultCalendars() && isDefaultContact() && isDefaultDate(mStartDate, mEndDate) && isDefaultCategory(mSelectedCategory)) {
            mBtnReset.setVisibility(View.GONE);
            mResetMask.setVisibility(View.GONE);
        } else {
            mBtnReset.setVisibility(View.VISIBLE);
            mResetMask.setVisibility(View.VISIBLE);
        }
    }

    private void updateCalendarButtonUI() {
        int num = selectedCalendarIds.size();
        calendarBtn.setSelected(num > 0);
        String text = mHost.getContext().getString(R.string.schedule_search_option_calendar) + (num <= 0 ? "" : " " + num);
        calendarTextView.setText(text);

        boolean isDefault = isDefaultCalendars();
        int color = isDefault ? R.color.unifiedsearch_joyday_filter_text_color_default : R.color.unifiedsearch_filter_text_color;
        calendarTextView.setTextColor(ContextCompat.getColor(mHost.getContext(), color));
        calendarBtn.setSelected(!isDefault);
        mCalendarArrow.setSelected(!isDefault);
        mCalendarBottomPadding.setSelected(!isDefault);
    }

    private boolean isDefaultCalendars() {
        boolean isDefault = true;
        Calendar calendar = CalendarList.getMyPersonCalendar(null);
        if (calendar != null) {
            isDefault = selectedCalendarIds.size() == 1 && selectedCalendarIds.contains(calendar.calendarId);
        }
        return isDefault;
    }

    private void updateSelectContactButtonUI() {
        if (mHost.getActivity() == null) return;
        if (inviteContacts.size() == 0) {
            firstAvatar.setVisibility(View.GONE);
            moreTip.setVisibility(View.GONE);
            moreTextView.setText(null);
        } else if (inviteContacts.size() == 1) {
            firstAvatar.setVisibility(View.VISIBLE);
            moreTip.setVisibility(View.GONE);
            moreTextView.setText(null);
            ImageLoader.load(AppBase.getAppContext(), firstAvatar, inviteContacts.get(0).mAvatar, false, R.drawable.unifiedsearch_avatar_default, R.drawable.unifiedsearch_avatar_default);
        } else {
            firstAvatar.setVisibility(View.GONE);
            moreTip.setVisibility(View.VISIBLE);
            String text = String.valueOf(inviteContacts.size());
            moreTextView.setText(text);
        }
        boolean isDefault = isDefaultContact();
        int color = isDefault ? R.color.unifiedsearch_joyday_filter_text_color_default : R.color.unifiedsearch_filter_text_color;
        selectContactTextView.setTextColor(ContextCompat.getColor(mHost.getContext(), color));
        selectContactBtn.setSelected(!isDefault);
        mParticipantsArrow.setSelected(!isDefault);

        if (mSelectorConfig.getUser() != null && mSelectorConfig.getUser().getReadOnly()) {
            mParticipantsArrow.setVisibility(View.GONE);
        } else {
            mParticipantsArrow.setVisibility(View.VISIBLE);
        }
    }

    private boolean isDefaultContact() {
        boolean isDefault = true;
        if (mSelectorConfig.getUser() != null) {
            if (mSelectorConfig.getUser().getUserList() != null)  {
                List<ScheduleSelectorConfig.UserEntity> list = mSelectorConfig.getUser().getUserList();
                isDefault = CollectionUtil.isEquals(list, inviteContacts);
            }
        } else {
            isDefault = CollectionUtil.isEmptyOrNull(inviteContacts);
        }
        return isDefault;
    }

    private void updateDatePickerButtonUI(boolean isDefault) {
        if (mStartDate != null && mEndDate != null) {
            datePickerBtn.setSelected(true);
            String text = new SimpleDateFormat("yyyy/M/d").format(mStartDate) + "-" + new SimpleDateFormat("yyyy/M/d").format(mEndDate);
            datePickerTextView.setText(text);
            int color = isDefault ? R.color.unifiedsearch_joyday_filter_text_color_default : R.color.unifiedsearch_filter_text_color;
            datePickerTextView.setTextColor(ContextCompat.getColor(mHost.getContext(), color));
        }
        datePickerBtn.setSelected(!isDefault);
        mDateArrow.setSelected(!isDefault);

        ScheduleSelectorConfig.DateRange dateRange = mSelectorConfig.getDateRange();
        if (dateRange != null && dateRange.getReadOnly()) {
            mDateArrow.setVisibility(View.GONE);
        } else {
            mParticipantsArrow.setVisibility(View.VISIBLE);
        }
    }

    private void resetCategory() {
        ScheduleCategory category = getDefaultCategory();
        mSelectedCategory = category.getCategory();
    }

    private void updateCategoryUI(ScheduleCategory category) {
        if (mSelectorConfig.getCategory() == null || !mSelectorConfig.getCategory().getShow()) {
            mBtnCategory.setVisibility(View.GONE);
            return;
        }
        mBtnCategory.setVisibility(View.VISIBLE);
        mTvCategory.setText(category.getTitle(mHost.getContext()));
        boolean isDefault = isDefaultCategory(category.getCategory());
        int color = isDefault ? R.color.unifiedsearch_joyday_filter_text_color_default : R.color.unifiedsearch_filter_text_color;
        mTvCategory.setTextColor(ContextCompat.getColor(mHost.getContext(), color));
        mBtnCategory.setSelected(!isDefault);
        mCategoryArrow.setSelected(!isDefault);
        mCategoryBottomPadding.setSelected(!isDefault);
    }

    private boolean isDefaultCategory(String category) {
        ScheduleCategory defaultCategory = getDefaultCategory();
        boolean isDefault = defaultCategory.getCategory().equals(category);
        return isDefault;
    }

    private ScheduleCategory getDefaultCategory() {
        ScheduleCategory defaultCategory = null;
        if (mSelectorConfig.getCategory() != null) {
            ScheduleSelectorConfig.Category categoryConfig = mSelectorConfig.getCategory();
            if (categoryConfig.isAll()) {
                defaultCategory = new ScheduleCategory(ScheduleCategory.CATEGORY_ALL, mHost.getContext().getString(R.string.calendar_schedule_category_all), true);
            } else if (categoryConfig.getHasAppointment()) {
                defaultCategory = new ScheduleCategory(ScheduleCategory.CATEGORY_APPOINTMENT, mHost.getContext().getString(R.string.calendar_schedule_category_appointment), true);
            } else if (categoryConfig.getHasMeeting()) {
                defaultCategory = new ScheduleCategory(ScheduleCategory.CATEGORY_MEETING, mHost.getContext().getString(R.string.calendar_schedule_category_meeting), true);
            }
        }
        if (defaultCategory == null) {
            defaultCategory = new ScheduleCategory(ScheduleCategory.CATEGORY_ALL, mHost.getContext().getString(R.string.calendar_schedule_category_all), true);;
        }
        return defaultCategory;
    }

    private void openCalendarPickerDialog(View view) {
        if (mMyPersonalCalendar == null) {
            this.resetCalendar(null);
            return;
        }
        if (mMyCalendar != null) {
            for (Calendar calendar : mMyCalendar) {
                calendar.checked = selectedCalendarIds.contains(calendar.calendarId);
            }
        }
        if (mSubscribeCalendar != null) {
            for (Calendar calendar : mSubscribeCalendar) {
                calendar.checked = selectedCalendarIds.contains(calendar.calendarId);
            }
        }

        List<PopupItem> items = new ArrayList<>();
        if (mMyCalendar != null) {
            for (Calendar calendar : mMyCalendar) {
                items.add(new PopupItem(calendar.title, calendar.calendarId, calendar.checked, null));
            }
        }

        popupWindow = new MultiSelectedPopupWindow(
                mHost.getActivity(),
                items,
                true,
                Collections.singletonList(new PopupItem(mMyPersonalCalendar.title, mMyPersonalCalendar.calendarId, false, null)),
                new Function2<List<PopupItem>, Boolean, Unit>() {
                    @Override
                    public Unit invoke(List<PopupItem> popupItems, Boolean aBoolean) {
                        ArrayList<String> newCalendarIds = new ArrayList<>();
                        if (popupItems.isEmpty()) {
                            newCalendarIds.add(mMyPersonalCalendar.calendarId);
                        } else {
                            for (PopupItem item : popupItems) {
                                if (item.getChecked()) {
                                    newCalendarIds.add(item.getValue());
                                }
                            }
                        }
                        if (!CollectionUtil.isEquals(newCalendarIds, selectedCalendarIds)) {
                            selectedCalendarIds = newCalendarIds;
                            updateCalendarButtonUI();
                            updateResetButtonUI();
                            scheduleSearch();
                        }
                        return null;
                    }
                },
                new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        mCalendarArrow.down();
                        view.setBackground(ContextCompat.getDrawable(mHost.getContext(), R.drawable.unifiedsearch_filter_layout_bg));
                        mCalendarBottomPadding.setBackgroundColor(Color.TRANSPARENT);
                        popupWindow = null;
                        return null;
                    }
                }
        );
        popupWindow.show(view, 0, mHost.getContext().getResources().getDimensionPixelOffset(R.dimen.unifiedsearch_joyspace_filter_margin_bottom));
        mCalendarArrow.up();

        view.setBackground(ContextCompat.getDrawable(mHost.getContext(), R.drawable.unifiedsearch_filter_layout_opened_bg));
        mCalendarBottomPadding.setBackground(ContextCompat.getDrawable(mHost.getContext(), R.drawable.unifiedsearch_filter_color));
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.onConfigurationChanged(newConfig);
        }
    }

    private void openContactSelector() {
        if (!inviteContacts.isEmpty()) {//打开中间页
            ArrayList<String> appIds = new ArrayList<>(getAppIds());
            Intent intent = Router.build(DeepLink.CONTACTS).getIntent(mHost.getActivity());
            intent.putExtra("extra_contact", inviteContacts);
            intent.putExtra("extra_alternate_contact", new ArrayList<>());
            intent.putExtra("title", "");
            intent.putExtra("max", MAX_NUM);
            intent.putStringArrayListExtra("extra_specify_appId",appIds);
            mHost.startActivityForResult(intent, REQUEST_CODE_CONTACT_SELECT);
        } else {
            ImDdService imDdService = AppJoint.service(ImDdService.class);

            MemberListEntityJd entity = new MemberListEntityJd();
            entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER)
                    .setShowConstantFilter(true)
                    .setShowSelf(true)
                    .setMaxNum(MAX_NUM)
                    .setSpecifyAppId(getAppIds());

            imDdService.gotoMemberList(mHost.getActivity(), REQUEST_CODE, entity, new Callback<ArrayList<MemberEntityJd>>() {
                @Override
                public void onSuccess(ArrayList<MemberEntityJd> bean) {
                    if (!CollectionUtil.isEquals(inviteContacts, bean)) {
                        inviteContacts = bean;
                        updateSelectContactButtonUI();
                        updateResetButtonUI();
                        scheduleSearch();
                    }
                }

                @Override
                public void onFail() {

                }
            });
        }
    }

    private void openDatePickerDialog() {
        if (mHost.getContext() == null) return;

        DatePickerDialog dialog = new DatePickerDialog(mHost.requireContext());
        dialog.setSelectMode(DatePickerView.SelectMode.RANGE);
        dialog.setEndDate(mEndDate);
        dialog.setStartDate(mStartDate);
        dialog.setStartDateRequired(false);
        dialog.setEndDateRequired(false);
        dialog.setAllRequired(true);
        HolidayHelperKt.holidayFetcher(dialog);
        ScheduleSelectorConfig.DateRange dateRange = mSelectorConfig.getDateRange();
        if (dateRange != null) {
            if (dateRange.getMinStartTime() != null && dateRange.getMinStartTime() != 0) {
                dialog.setMinDate(new Date(dateRange.getMinStartTime()));
            }
            if (dateRange.getMaxEndTime() != null && dateRange.getMaxEndTime() != 0) {
                dialog.setMaxDate(new Date(dateRange.getMaxEndTime()));
            }

            if (dateRange.getMaxDayRange() != null) {
                dialog.setMaxSelectedDayRange(Math.toIntExact(dateRange.getMaxDayRange()));
            } else {
                dialog.setMaxSelectedMonthRange(MAX_MONTH_RANGE);
            }
        } else {
            dialog.setMaxSelectedMonthRange(MAX_MONTH_RANGE);
        }

        dialog.setOnCalendarRangeSelectedListener((startDate, endDate) -> {
            Date newStartDate = DateUtil.startOfDay(startDate);
            Date newEndDate = DateUtil.endOfDay(endDate);
            if (!newStartDate.equals(mStartDate) || !newEndDate.equals(mEndDate)) {
                mStartDate = newStartDate;
                mEndDate = newEndDate;
                boolean isDefault = isDefaultDate(mStartDate, mEndDate);
                updateDatePickerButtonUI(isDefault);
                updateResetButtonUI();
                scheduleSearch();
            }
        });

        dialog.setOnCancelListener(dialog1 -> {

        });
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                mDateArrow.down();
            }
        });
        dialog.getWindow().setDimAmount(0.2f);
        dialog.show();
        mDateArrow.up();
    }

    private void showCategoryDialog(View view) {
        boolean all = ScheduleCategory.CATEGORY_ALL.equals(mSelectedCategory);
        boolean meeting = ScheduleCategory.CATEGORY_MEETING.equals(mSelectedCategory);
        boolean appointment = ScheduleCategory.CATEGORY_APPOINTMENT.equals(mSelectedCategory);

        List<ScheduleCategory> categories = Arrays.asList(
                new ScheduleCategory(ScheduleCategory.CATEGORY_ALL, mHost.getContext().getString(R.string.calendar_schedule_category_all), all),
                new ScheduleCategory(ScheduleCategory.CATEGORY_MEETING, mHost.getContext().getString(R.string.calendar_schedule_category_meeting), meeting),
                new ScheduleCategory(ScheduleCategory.CATEGORY_APPOINTMENT, mHost.getContext().getString(R.string.calendar_schedule_category_appointment), appointment)
        );

        List<PopupItem> items = new ArrayList<>();
        for (ScheduleCategory category : categories) {
            items.add(new PopupItem(category.getTitle(mHost.getContext()), category.getCategory(), category.isSelected(), null));
        }

        popupWindow = new MultiSelectedPopupWindow(
                mHost.getActivity(),
                items,
                false,
                Collections.singletonList(new PopupItem(mHost.getContext().getString(R.string.calendar_schedule_category_all), ScheduleCategory.CATEGORY_ALL, false, null)),
                new Function2<List<PopupItem>, Boolean, Unit>() {
                    @Override
                    public Unit invoke(List<PopupItem> popupItems, Boolean aBoolean) {
                        ScheduleCategory category;
                        if (popupItems.isEmpty()) {
                            category = new ScheduleCategory(ScheduleCategory.CATEGORY_ALL, mHost.getContext().getString(R.string.calendar_schedule_category_all), true);
                        } else {
                            category = new ScheduleCategory(popupItems.get(0).getValue(), popupItems.get(0).getText(), true);
                        }
                        mSelectedCategory = category.getCategory();
                        updateCategoryUI(category);
                        mCategoryBottomPadding.setBackgroundColor(Color.TRANSPARENT);
                        updateResetButtonUI();
                        scheduleSearch();
                        if (TextUtils.equals(ScheduleCategory.CATEGORY_MEETING, mSelectedCategory)) {
                            clickEvent("Search_1684144583068|1");
                        } else if (TextUtils.equals(ScheduleCategory.CATEGORY_APPOINTMENT, mSelectedCategory)) {
                            clickEvent("Search_1684144583068|2");
                        }
                        return null;
                    }
                },
                new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        mCategoryArrow.down();
                        view.setBackground(ContextCompat.getDrawable(mHost.getContext(), R.drawable.unifiedsearch_filter_layout_bg));
                        mCategoryBottomPadding.setBackgroundColor(Color.TRANSPARENT);
                        popupWindow = null;
                        return null;
                    }
                }
        );
        view.setBackground(ContextCompat.getDrawable(mHost.getContext(), R.drawable.unifiedsearch_filter_layout_opened_bg));
        mCategoryBottomPadding.setBackground(ContextCompat.getDrawable(mHost.getContext(), R.drawable.unifiedsearch_filter_color));
        popupWindow.show(view, 0, mHost.getContext().getResources().getDimensionPixelOffset(R.dimen.unifiedsearch_joyspace_filter_margin_bottom));

        mCategoryArrow.up();
    }

    //产品说进入搜索页默认选择前后三个月，日历控件不会出现不选择情况，点击重置按钮，时间段重置到前后三个月
    private void resetSelectDate() {
        Pair<Date, Date> datePair = getDefaultDate();
        mStartDate = datePair.first;
        mEndDate = datePair.second;
    }

    private Pair<Date, Date> getDefaultDate() {
        Date startDate = null, endDate = null;
        ScheduleSelectorConfig.DateRange dateRange = mSelectorConfig.getDateRange();
        if (dateRange != null) {
            if (dateRange.getStartTime() != null && dateRange.getEndTime() != null) {
                startDate = new Date(dateRange.getStartTime());
                long maxDateRange = dateRange.getMaxDayRange() != null ? dateRange.getMaxDayRange() : 0;
                endDate = checkDateRange(dateRange.getStartTime(), dateRange.getEndTime(), maxDateRange);
            } else if (dateRange.getMaxDayRange() != null) {
                Date today = new Date();
                startDate = DateUtil.startOfDay(today);
                endDate = DateUtil.endOfDay(DateUtil.addDay(today, Math.toIntExact(dateRange.getMaxDayRange())));
            }
        }
        if (startDate == null || endDate == null) {
            Date today = new Date();
            startDate = DateUtil.startOfDay(DateUtil.subtractMonth(DateUtil.firstDayOfMonth(today), 1));
            endDate = DateUtil.endOfDay(DateUtil.addMonths(DateUtil.lastDayOfMonth(today), 1));
        }
        return Pair.create(startDate, endDate);
    }

    private boolean isDefaultDate(Date start, Date end) {
        Pair<Date,Date> defaultDate = getDefaultDate();
        return DateUtil.isSameDay(defaultDate.first, start) && DateUtil.isSameDay(defaultDate.second, end);
    }

    private Date checkDateRange(long start, long end, long maxDateRange) {
        java.util.Calendar startDate = java.util.Calendar.getInstance();
        startDate.setTimeInMillis(start);

        java.util.Calendar endDate = java.util.Calendar.getInstance();
        endDate.setTimeInMillis(end);

        int diff = DateUtil.differenceDay(startDate.getTime(), endDate.getTime());
        if (maxDateRange > 0 && diff > maxDateRange) {
             startDate.add(java.util.Calendar.DATE, Math.toIntExact(maxDateRange));
             return startDate.getTime();
        }

        startDate.add(java.util.Calendar.MONTH, MAX_MONTH_RANGE);
        if (endDate.after(startDate)) {
            return startDate.getTime();
        }
        return endDate.getTime();
    }

    private void resetCalendar(List<Calendar> calendarList) {
        List<Calendar> calendars = null;
        if (calendarList == null) {
            calendars = CalendarList.getCalendarListCache();
        } else {
            calendars = calendarList;
        }
        mMyCalendar = CalendarList.getMyCalendar(calendars);
        mSubscribeCalendar = CalendarList.getSubscribeCalendar(calendars);
        mMyPersonalCalendar = CalendarList.getMyPersonCalendar(calendars);
        selectedCalendarIds.clear();

        if (mMyPersonalCalendar != null) {
            selectedCalendarIds.add(mMyPersonalCalendar.calendarId);
        }
    }

    private List<String> getAppIds() {
        return TenantConfigBiz.INSTANCE.getCollaborativelyApps();
    }

    private void getCategoryList() {
        Map<String,List<Subcategory>> map = mDuccPreference.getSubcategoryList();
        mAdapter.setSubcategories(map);

        if (map == null || map.isEmpty()) {
            mPresenter.getJoydayDuccConfig();
        }
    }

    @Override
    public void onSubcategoryLoad(Map<String, List<Subcategory>> map) {
        mAdapter.setSubcategories(map);
    }

    private void clickEvent(String eventId) {
        Map<String,String> params = new HashMap<>();
        String name = PreferenceManager.UserInfo.getUserName();
        params.put("erp", name);
        params.put("name", eventId);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId, name, "", "", "", "", new HashMap<>(params));
    }

    private void pageEvent(String pageName) {
        Map<String,String> params = new HashMap<>();
        String name = PreferenceManager.UserInfo.getUserName();
        params.put("erp", name);
        params.put("name", pageName);
        JDMAUtils.onEventPagePV(AppBase.getAppContext(), pageName, "", name,  pageName, new HashMap<>(params));
    }

    @Override
    public void onRefreshData(List<ScheduleModel> list, int startIndex) {
        mSearchList.setVisibility(View.VISIBLE);
        mEmptyView.setVisibility(View.INVISIBLE);
        mEmptyStub.setVisibility(View.GONE);

        boolean changed = mAdapter.refreshData(list, mSelectorConfig.getSelected(), mSelectorConfig.getDisableSelected());
        if (changed && mOnMultiSelectChangeListener != null) {
            mOnMultiSelectChangeListener.onChange(mAdapter.getSelectedList());
        }

        int scrollIndex;
        if (mScrollTarget != null) {
            scrollIndex = findScrollTargetIndex(list, mScrollTarget);
            mScrollTarget = null;
        } else {
            scrollIndex = startIndex;
        }

        mHandler.post(() -> {
            if (scrollIndex >= 0 && scrollIndex < list.size()) {
                mLayoutManager.scrollToPositionWithOffset(scrollIndex, 0);
            }
        });
    }

    private int findScrollTargetIndex(List<ScheduleModel> list, ScheduleModel scheduleModel) {
        if (scheduleModel == null) return -1;
        for (int i = 0; i < list.size(); i++) {
            if (Objects.equals(list.get(i), scheduleModel)) {
                return i;
            }
        }
        return -1;
    }

    @Override
    public void onSetEmpty(int type, String keyword) {
        mLoadHelper.setEmpty();
        mSearchList.setVisibility(View.INVISIBLE);
        mEmptyView.setVisibility(View.VISIBLE);
        mRetryButton.setVisibility(View.INVISIBLE);
//        if (type == ERROR_NO_DATA) {
//            mEmptyViewImage.setBackgroundResource(R.drawable.unifiedsearch_error_no_data);
//            mEmptyViewTextStart.setText("");
//            mEmptyViewTextMiddle.setText(R.string.calendar_schedule_search_no_data);
//            mEmptyViewTextEnd.setText("");
//        } else if (type == ERROR_NOT_FOUND) {
//            mEmptyViewImage.setBackgroundResource(R.drawable.unifiedsearch_error_no_data);
//            if (TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN")) {
//                mEmptyViewTextStart.setText("未查询到\"");
//                mEmptyViewTextMiddle.setText(TextUtil.getSpanText("<em>" + keyword + "</em>", mHost.getActivity().getResources().getColor(R.color.unifiedsearch_highlight_color)));
//                mEmptyViewTextEnd.setText("\"相关的日程");
//            } else {
//                mEmptyViewTextStart.setText("No schedule matching with '");
//                mEmptyViewTextMiddle.setText(TextUtil.getSpanText("<em>" + keyword + "</em>", mHost.getActivity().getResources().getColor(R.color.unifiedsearch_highlight_color)));
//                mEmptyViewTextEnd.setText("'");
//            }
//        }
        if (type == ERROR_NO_DATA || type == ERROR_NOT_FOUND) {
            mEmptyStub.setVisibility(View.VISIBLE);
        } else if (type == ERROR_NO_NETWORK) {
            mEmptyStub.setVisibility(View.GONE);
            mRetryButton.setVisibility(View.VISIBLE);
            mEmptyViewImage.setBackgroundResource(R.drawable.unifiedsearch_error);
            mEmptyViewTextStart.setText("");
            mEmptyViewTextMiddle.setText(R.string.unifiedsearch_joyspace_error_text);
            mEmptyViewTextEnd.setText("");
        }
    }

    @Override
    public void onSetComplete(int direction, List<ScheduleModel> data) {
        mLoadHelper.setComplete();
    }

    @Override
    public void onSetLoaded(int direction, List<ScheduleModel> data) {
        mLoadHelper.setLoaded();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE_CONTACT_SELECT) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                if (selected != null) {
                    if (!CollectionUtil.isEquals(inviteContacts, selected)) {
                        inviteContacts = selected;
                        updateSelectContactButtonUI();
                        updateResetButtonUI();
                        scheduleSearch();
                    }
                }
            }
        }
    }

    @Override
    public void tabSelected(String keyword) {
        search(keyword, false);
    }

    public void setOnSingleSelectChangeListener(OnSingleSelectChangeListener onSingleSelectChangeListener) {
        mOnSingleSelectChangeListener = onSingleSelectChangeListener;
    }
    public void setOnMultiSelectChangeListener(ScheduleSearchTabDelegate.OnMultiSelectChangeListener onMultiSelectChangeListener) {
        mOnMultiSelectChangeListener = onMultiSelectChangeListener;
        if (mAdapter != null) {
            mAdapter.setOnMultiSelectChangeListener(mOnMultiSelectChangeListener);
        }
    }

    public void setScheduleSelectedState(ScheduleModel scheduleModel, boolean selected) {
        mAdapter.setSelectedState(scheduleModel, selected);
        if (mOnMultiSelectChangeListener != null) {
            mOnMultiSelectChangeListener.onChange(mAdapter.getSelectedList());
        }
    }

    public List<ScheduleModel> getSelectedList() {
        return mAdapter.getSelectedList();
    }

    public void setScrollTarget(ScheduleModel scrollTarget) {
        mScrollTarget = scrollTarget;
    }

    public interface OnSingleSelectChangeListener {
        void onItemClick(ScheduleModel scheduleModel);
    }

    public interface OnMultiSelectChangeListener {
        void onChange(List<ScheduleModel> list);
    }
}
