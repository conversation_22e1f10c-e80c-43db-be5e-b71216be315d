package com.jd.oa.unifiedsearch.org;

import android.content.Context;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.utils.HighlightText;

import java.util.List;

public class OrgSearchAdapter extends BaseRecyclerAdapter<OrgRecord, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;
    private int mHighlightColor;
    private final String mHighlightTag = "em";
    private final String mStartTag = "<" + mHighlightTag + ">";
    private final String mEndTag = "</" + mHighlightTag + ">";

    private boolean showFeedback = false;

    private int VIEW_TYPE_ITEM = 0;
    private int VIEW_TYPE_FEEDBACK = 1;

    public OrgSearchAdapter(Context context) {
        super(context);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_color_1869F5);
    }

    public OrgSearchAdapter(Context context, List<OrgRecord> data) {
        super(context, data);
        mHighlightColor = ContextCompat.getColor(context, R.color.unifiedsearch_color_1869F5);
    }

    @Override
    public int getItemCount() {
        if (super.getItemCount() > 0 && showFeedback) {
            return super.getItemCount() + 1;
        }
        return super.getItemCount();
    }

    @Override
    public int getItemViewType(int index) {
        if (showFeedback) {
            if (index < getItemCount() - 1) {
                return VIEW_TYPE_ITEM;
            } else {
                return VIEW_TYPE_FEEDBACK;
            }
        } else {
            return VIEW_TYPE_ITEM;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (VIEW_TYPE_ITEM == viewType) {
            return new ViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_recycler_item_org, viewGroup, false));
        } else {
            return new FeedbackViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.unifiedsearch_pub_feedback_content, viewGroup, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {
        if (viewHolder instanceof ViewHolder) {
            ViewHolder holder = (ViewHolder) viewHolder;
            OrgRecord orgRecord = getItem(i);
            if(orgRecord.refPro.orgType == 1){
                holder.orgImage.setImageResource(R.drawable.unifiedsearch_organization_structure);
                holder.orgLabel.setVisibility(View.GONE);
            }else if(orgRecord.refPro.orgType == 2){
                holder.orgImage.setImageResource(R.drawable.unifiedsearch_relative_team);
                holder.orgLabel.setVisibility(View.VISIBLE);
            }
            if(orgRecord.highlightJoin != null && orgRecord.highlightJoin.size() > 0
                    && orgRecord.highlightJoin.get(0).highlightSort != null
                    && orgRecord.highlightJoin.get(0).highlightSort.size() > 0
                    && orgRecord.highlightJoin.get(0).highlightSort.get(0) != null
                    && "organizationName".equals(orgRecord.highlightJoin.get(0).highlightSort.get(0).key)){
                HighlightText.with(orgRecord.highlightJoin.get(0).highlightSort.get(0).value).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.orgName);
                holder.orgMember.setText(orgRecord.refPro.organizationFullname);
            }else {
                HighlightText.with(orgRecord.refPro.organizationName).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.orgName);
                StringBuilder member = new StringBuilder();
                if(orgRecord.highlightJoin != null && orgRecord.highlightJoin.size() > 0){
                    for (int j = 0;j < orgRecord.highlightJoin.size();j++){
                        List<OrgRecord.HighlightSort> highlightSorts = orgRecord.highlightJoin.get(j).highlightSort;
                        if(highlightSorts != null && highlightSorts.size() > 0){
                            if(j == 0){
                                member.append(getContext().getString(R.string.unifiedsearch_org_includes));
                            }
                            member.append(highlightSorts.get(0).value);
                        }
                        if(j < orgRecord.highlightJoin.size()-1){
                            member.append(getContext().getString(R.string.unifiedsearch_org_separator));
                        }
                    }
                }
                if(TextUtils.isEmpty(member)){
                    holder.orgMember.setText(orgRecord.refPro.organizationFullname);
                }else {
                    HighlightText.with(member.toString()).startTag(mStartTag).endTag(mEndTag).color(mHighlightColor).into(holder.orgMember);
                }
            }
        } else if (viewHolder instanceof FeedbackViewHolder) {
            FeedbackViewHolder holder = (FeedbackViewHolder) viewHolder;
            holder.tvContent.setVisibility(View.VISIBLE);
            holder.tvContent.setText(SearchUtil.getFeedbackContent());
        }
    }

    public void showFeedback() {
        showFeedback = true;
    }


    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        ImageView orgImage;
        TextView orgName;
        TextView orgLabel;
        TextView orgMember;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            orgImage = itemView.findViewById(R.id.iv_org_image);
            orgName = itemView.findViewById(R.id.tv_org_name);
            orgLabel = itemView.findViewById(R.id.tv_org_label);
            orgMember = itemView.findViewById(R.id.tv_org_member);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        int position = getBindingAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) return;
                        mOnItemClickListener.onItemClick(OrgSearchAdapter.this, v, position);
                    }
                }
            });
        }
    }

    class FeedbackViewHolder extends RecyclerView.ViewHolder {

        public TextView tvContent;

        public FeedbackViewHolder(@NonNull View itemView) {
            super(itemView);
            tvContent = itemView.findViewById(R.id.tv_conent);
            tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }
}
