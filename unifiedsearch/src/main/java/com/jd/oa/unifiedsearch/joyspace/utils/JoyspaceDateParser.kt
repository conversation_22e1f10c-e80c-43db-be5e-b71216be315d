package com.jd.oa.unifiedsearch.joyspace.utils

import android.content.Context
import android.text.TextUtils
import com.jd.oa.unifiedsearch.R
import com.jd.oa.utils.DateUtils
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class JoyspaceDateParser {

    companion object {
        private val PARSE_DATE_FORMAT =
            SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSz", Locale.getDefault())

        /**
         * 分享的文档时间格式和其他的不一样 f**k
         */
        private val SHARED_PARSE_DATE_FORMAT =
            SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssz", Locale.getDefault())

        private val NEW_PARSE_DATE_FORMAT =
            SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())

        private val SIMPLE_TIME_FORMAT = SimpleDateFormat("HH:mm", Locale.getDefault())
    }

    /**
     * 兼容不同格式的日期😐
     */
    fun parseDateTime(context: Context, dateTime: String?): String? {
        try {
            return parseDateTime(context, NEW_PARSE_DATE_FORMAT, dateTime)
        } catch (e: ParseException) {
            try {
                return parseDateTime(context, PARSE_DATE_FORMAT, dateTime)
            } catch (parseException: ParseException) {
                try {
                    return parseDateTime(
                        context,
                        SHARED_PARSE_DATE_FORMAT,
                        dateTime
                    )
                } catch (exception: ParseException) {
                    exception.printStackTrace()
                }
            }
        }
        return ""
    }

    @Throws(ParseException::class)
    fun parseDateTime(context: Context, dateFormat: SimpleDateFormat, dateTime: String?): String? {
        if (TextUtils.isEmpty(dateTime)) return ""
        var date: Date? = null
        date = dateFormat.parse(dateTime)
        return if (date == null) dateTime else parseDateTime(context, date.time)
    }

    fun parseDateTime(context: Context, dateTime: Long): String? {
        return parseDateTime(context, SIMPLE_TIME_FORMAT, dateTime)
    }

    fun parseDateTime(context: Context, dateFormat: SimpleDateFormat, dateTime: Long): String? {
        val date = Date(dateTime)
        val isToday = DateUtils.isToday(date.time)
        val isYesterday = DateUtils.isYesterday(date.time)
        val time = dateFormat.format(date)
        return if (isToday) {
            context.getString(R.string.unifiedsearch_joyspace_date_today) + " " + time
        } else if (isYesterday) {
            context.getString(R.string.unifiedsearch_joyspace_date_yesterday) + " " + time
        } else if (DateUtils.isSameYear(date.time, Date().time)) {
            val format = SimpleDateFormat(
                context.getString(R.string.unifiedsearch_joyspace_date_format),
                Locale.getDefault()
            )
            val dateString = format.format(date)
            "$dateString $time"
        } else {
            val format = SimpleDateFormat(
                context.getString(R.string.unifiedsearch_joyspace_date_format_full),
                Locale.getDefault()
            )
            val dateString = format.format(date)
            "$dateString $time"
        }
    }
}