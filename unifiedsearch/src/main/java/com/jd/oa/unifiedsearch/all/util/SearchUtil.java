package com.jd.oa.unifiedsearch.all.util;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchPreference;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;

import java.util.HashMap;
import java.util.Map;

/*
 * Time: 2023/10/24
 * Author: qudongshi
 * Description:
 */
public class SearchUtil {

    public static SpannableString getFeedbackContent() {
        if (AppBase.getAppContext() == null) {
            return null;
        }
        Context mContext = ContextUtil.applyLanguage(AppBase.getAppContext());
        SpannableString spannableString = new SpannableString(mContext.getString(R.string.me_all_search_section_no_result));
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                SearchLogUitl.LogD("SearchUtil", "SpannableString onClick");
                String searchConfig = SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_SEARCH_FILTER_CONFIG);
                Map<String,Object> searchConfigMap = JSON.parseObject(searchConfig,new TypeReference<Map<String,Object>>(){});
                String deepLink = null;
                if(searchConfigMap != null){
                    deepLink = (String) searchConfigMap.get("mobileFeedbackUrl");
                }
                if (TextUtils.isEmpty(deepLink)) {
                    deepLink = LocalConfigHelper.getInstance(mContext).getUrlConstantsModel().unifiedSearchFeedbackUrl;
                }
                Router.build(deepLink).go(mContext);
            }

            public void updateDrawState(TextPaint textPaint) {
                textPaint.setColor(mContext.getResources().getColor(R.color.unifiedsearch_color_1869F5));
                textPaint.setUnderlineText(false);
                textPaint.clearShadowLayer();
            }
        };
        if (LocaleUtils.getUserSetLocaleStr(mContext).toLowerCase().startsWith("zh")) {
            if (spannableString.length() > 9) {
                spannableString.setSpan(clickableSpan, 9, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        } else {
            if (spannableString.length() > 29) {
                spannableString.setSpan(clickableSpan, 29, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }
        return spannableString;
    }

    public static void showMore(Context context, SearchFloorType floorType) {
        Intent i = new Intent();
        i.setAction("search.action.floor.showmore");
        i.putExtra("floorType", SearchConfigHelper.getInstance().getRealTabType(floorType.toString()));
        LocalBroadcastManager.getInstance(context).sendBroadcast(i);
    }

    public static String EVENT_ID_SEARCH_OPEN = "Mobile_Im_Search_Open";
    public static String EVENT_ID_SEARCH_CLK_CANCEL = "Mobile_Im_Search_Clk_Cancel";
    public static String EVENT_ID_SEARCH_CLK_DELETE = "Mobile_Event_Im_Search_Clk_Delete";
    public static String EVENT_ID_SEARCH_CLK_RESULT = "Mobile_Im_Search_Clk_Result";
    public static String EVENT_ID_SEARCH_SEARCH_ONCE = "Mobile_Event_UnifiedSearch_TypeSearch_Once";
    public static String PAGE_ID = "Mobile_Page_Jdme_Im_Search";

    public static void sendEvent(String sessionId, String searchId, String eventId) {
        Map<String, String> eventParam = new HashMap<>();
        eventParam.put("jdme_searchId", searchId);
        eventParam.put("jdme_sessionId", sessionId);
        eventParam.put("timestamp", System.currentTimeMillis() + "");
        eventParam.put("erp", PreferenceManager.UserInfo.getUserName());
        JDMAUtils.clickEvent(PAGE_ID, eventId, eventParam);
    }

    public static void sendClickEvent(String sessionId, String searchId, String clickUrl) {
        Map<String, String> eventParam = new HashMap<>();
        eventParam.put("jdme_searchId", searchId);
        eventParam.put("jdme_sessionId", sessionId);
        eventParam.put("jdme_clickUrl", clickUrl);
        eventParam.put("timestamp", System.currentTimeMillis() + "");
        eventParam.put("erp", PreferenceManager.UserInfo.getUserName());
        JDMAUtils.clickEvent(PAGE_ID, EVENT_ID_SEARCH_CLK_RESULT, eventParam);
    }

    public static boolean enablePendingTransition() {
        String brand = Build.BRAND;
        if (TextUtils.isEmpty(brand)) {
            return true;
        }
        if (brand.toLowerCase().equals("samsung") || brand.toLowerCase().equals("asus")) {
            return false;
        }
        return true;
    }
}
