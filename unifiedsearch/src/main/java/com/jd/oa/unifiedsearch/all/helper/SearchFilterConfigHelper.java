package com.jd.oa.unifiedsearch.all.helper;

import android.content.Context;
import android.util.Log;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.local.Json5Parser;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfigWrapper;
import com.jd.oa.unifiedsearch.all.data.SearchPreference;
import com.jd.oa.unifiedsearch.all.filter.FilterNode;
import com.jd.oa.unifiedsearch.approval.filter.ApprovalFilterFactory;
import com.jd.oa.unifiedsearch.joyspace.filter.FilterContext;
import com.jd.oa.unifiedsearch.joyspace.filter.UnifiedSearchFilter;
import com.jd.oa.utils.LocaleUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import XcoreXipworkssmimeX200X7350.P;

/**
 * 审批tab已使用
 * 技术文档：https://joyspace.jd.com/h/personal/pages/79VYDiGdx617I2nupZ9E
 */

public class SearchFilterConfigHelper {
    public static final String TYPE_SINGLE_SELECT = "single_select";
    public static final String TYPE_APPROVAL_CONTACT = "approval_contact";
    public static final String TYPE_APPROVAL_APPLICATION_TIME = "approval_application_time";
    public static final String TYPE_APPROVAL_TASK_ARRIVAL_TIME = "approval_task_arrival_time";
    public static final String TYPE_APPROVAL_COMPLETION_TIME = "approval_completion_time";
    public static final String TYPE_APPROVAL_NOTIFY_TIME = "approval_notify_time";
    private static final String TAG = "Search_Filter_Configuration";
    private static SearchFilterConfigHelper helper = null;
    private SearchFilterConfig mSearchFilterConfig;

    private SearchFilterConfigHelper() {}

    public static synchronized SearchFilterConfigHelper getInstance() {
        if (helper == null) {
            helper = new SearchFilterConfigHelper();
        }
        return helper;
    }

    public void initSearchConfig(Context context) {
        InputStream is;
        try {
            //获取接口配置
            String configJsonDynamic = SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_SEARCH_FILTER_CONFIG);
            //接口配置解析结果
            mSearchFilterConfig = getConfigFromJson(configJsonDynamic);
            if (mSearchFilterConfig == null) {
                //获取本地兜底配置
                String configJsonLocal = "";
                is = context.getAssets().open("config/filterConfig.json5");
                BufferedReader streamReader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                StringBuilder stringBuilder = new StringBuilder();
                String inputStr;
                while ((inputStr = streamReader.readLine()) != null) {
                    stringBuilder.append(inputStr);
                    stringBuilder.append(" \n");
                }
                configJsonLocal = Json5Parser.formatJson(stringBuilder.toString());
                is.close();
                streamReader.close();
                //接口解析结果为空则使用本地配置
                mSearchFilterConfig =  getConfigFromJson(configJsonLocal);
            }
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(TAG, e.toString());
        }
    }

    private SearchFilterConfig getConfigFromJson(String source) {
        if (source == null || source.isEmpty()) {
            return null;
        }
        SearchFilterConfigWrapper searchFilterConfigWrapper = new Gson().fromJson(source, new TypeToken<SearchFilterConfigWrapper>() {}.getType());
        if (searchFilterConfigWrapper != null) {
            SearchFilterConfig result = searchFilterConfigWrapper.searchFilterConfig;
            if (result != null && result.approval != null && result.approval.filterItems != null) {
                //将filterKey放入filterItem
                for (String filterKey : result.approval.filterItems.keySet()) {
                    SearchFilterConfig.FilterItem item = result.approval.filterItems.get(filterKey);
                    if (item != null) {
                        item.filterItemKey = filterKey;
                    }
                }
                return result;
            }
        }
        return null;
    }

    public List<UnifiedSearchFilter<?>> getFilters(
            @NonNull FilterContext filterContext,
            @NonNull Context context,
            @NonNull ApprovalFilterFactory filterFactory) {
        List<UnifiedSearchFilter<?>> result = new ArrayList<>();
        if (mSearchFilterConfig == null || mSearchFilterConfig.approval == null || mSearchFilterConfig.approval.filter == null) {
            MELogUtil.localE(TAG, "重试初始化SearchConfig");
            initSearchConfig(context);
            if (mSearchFilterConfig == null || mSearchFilterConfig.approval == null || mSearchFilterConfig.approval.filter == null) {
                MELogUtil.localE(TAG, "搜索配置获取失败，本地兜底配置获取失败，检查接口返回值与本地兜底配置");
                return result;
            }
        }
        for (String filterName: mSearchFilterConfig.approval.filter) {
            Pair<SearchFilterConfig.FilterItem, SearchFilterConfig.Option> pair = getFilterItemFromCacheOrConfig(filterName, filterFactory);
            if (pair == null || pair.first == null) {
                continue;
            }
            List<SearchFilterConfig.FilterItem> subResults = getSelectedFilterItems(pair.first, pair.second, filterFactory);
            if (subResults != null && !subResults.isEmpty()) {
                for (SearchFilterConfig.FilterItem filters : subResults) {
                    result.add(filterFactory.getSubType(filterContext, context, filters));
                }
            }
        }
        return result;
    }

    private List<SearchFilterConfig.FilterItem> getSelectedFilterItems(
            @NonNull SearchFilterConfig.FilterItem parentItem,
            @Nullable SearchFilterConfig.Option parentOption,
            @NonNull ApprovalFilterFactory filterFactory) {
        // 返回结果
        List<SearchFilterConfig.FilterItem> stepResult = new ArrayList<>();
        // 当前过滤器项
        stepResult.add(parentItem);

        // 如果当前 Option 有子过滤器项
        if (parentOption != null && parentOption.subFilterItems!= null && !parentOption.subFilterItems.isEmpty()) {
            // 遍历每个子过滤器项
            for (String subFilterItemKey : parentOption.subFilterItems) {
                Pair<SearchFilterConfig.FilterItem, SearchFilterConfig.Option> pair = getFilterItemFromCacheOrConfig(subFilterItemKey, filterFactory);
                if (pair == null || pair.first == null) {
                    continue;
                }
                List<SearchFilterConfig.FilterItem> filters = getSelectedFilterItems(pair.first, pair.second, filterFactory); //递归
                if (filters != null) {
                    stepResult.addAll(filters);
                }
            }
        }
        return stepResult;
    }

    private Pair<SearchFilterConfig.FilterItem, SearchFilterConfig.Option> getFilterItemFromCacheOrConfig(String filterName, ApprovalFilterFactory filterFactory) {
        //尝试从缓存中获取
        if (filterFactory != null) {
            UnifiedSearchFilter<?> filter = filterFactory.getFilter(filterName);
            if (filter != null && filter.getValue() instanceof FilterNode) {
                FilterNode node = (FilterNode) filter.getValue();
                return new Pair<>(node.getFilterItem(), node.getOption());
            }
        }
        //从配置中获取
        if (mSearchFilterConfig != null && mSearchFilterConfig.approval != null && mSearchFilterConfig.approval.filterItems != null) {
            SearchFilterConfig.FilterItem filterItem = mSearchFilterConfig.approval.filterItems.get(filterName);
            if (filterItem != null) {
                if (filterItem.options == null) {
                    return new Pair<>(filterItem, null);
                } else {
                    for (SearchFilterConfig.Option option : filterItem.options) {
                        if (option.selected) {
                            return new Pair<>(filterItem, option);
                        }
                    }
                }
            }
        }
        return null;
    }
}
