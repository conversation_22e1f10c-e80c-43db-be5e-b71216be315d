package com.jd.oa.unifiedsearch.all.section;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.text.Html;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.basic.ImBasic;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchAIModel;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.all.helper.SearchHelper;
import com.jd.oa.unifiedsearch.all.util.TextViewLinesUtil;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.DisplayUtils;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/30
 * Author: qudongshi
 * Description:
 */
public class AISection extends BaseSection {

    public final String TAG = "AISection";

    private List<?> data;
    private Context context;
    private String keyword;
    private SearchFloorHelper helper;
    private int contentWidth = 0;
    private String sessionId;
    private String requestId;


    public AISection(Context context, List<?> data, String keyword, SearchFloorHelper helper, String sessionId, String requestId) {
        super(SectionParameters.builder().itemResourceId(R.layout.unifiedsearch_section_ai_content).build(), keyword);
        this.data = data;
        this.context = context;
        this.keyword = keyword;
        this.floorType = SearchFloorType.AI;
        this.helper = helper;
        this.sessionId = sessionId;
        this.requestId = requestId;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        if (loadingViewHolder != null) {
            return loadingViewHolder;
        }
        AIContentViewHolder viewHolder = new AIContentViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        if (data.get(0) instanceof SearchAIModel) {
            SearchAIModel aiModel = (SearchAIModel) data.get(0);
            AIContentViewHolder aiContentViewHolder = (AIContentViewHolder) viewHolder;
            aiContentViewHolder.tvContentTitle.setText(aiModel.keyword);
            contentWidth = DisplayUtil.getScreenWidth(context) - (DisplayUtils.dip2px(20) * 2);
            if (aiContentViewHolder.tvContentVal.getText().length() == 0) {
                aiContentViewHolder.showLoading();
            }
            //loading
            this.loadingViewHolder = aiContentViewHolder;
            loadingData(aiModel);
        }

    }

    private AIContentViewHolder loadingViewHolder;

    public void loadingData(SearchAIModel mode) {
        if (mode.finished && TextUtils.isEmpty(mode.responseAll)) {
            mode.responseAll = context.getString(R.string.me_search_section_ai_havent_result);
            loadingViewHolder.tvContentVal.setText(Html.fromHtml(mode.responseAll));
            SearchLogUitl.LogD(TAG, "content 1 = " + mode.responseAll);
        }

        if (mode.finished && loadingViewHolder.rlOp.getVisibility() == View.GONE) {
            loadingViewHolder.rlOp.setVisibility(View.VISIBLE);
        }

        if (!TextUtils.isEmpty(mode.responseAll)) {
            initView(mode);
        }
        if (context != null && context instanceof Activity) {
            if (((Activity) context).isFinishing()) {
                return;
            }
        }
        if (!TextUtils.isEmpty(mode.responseAll)) {
            loadingViewHolder.tvContentVal.setText(Html.fromHtml(mode.responseAll));
            SearchLogUitl.LogD(TAG, "content 2= " + mode.responseAll);
        }

        if (mode.isRunning()) {
            helper.searchMEAIFloor(keyword, sessionId, requestId, mode.traceId);
        }

        int lines = TextViewLinesUtil.getTextViewLines(loadingViewHolder.tvContentVal, contentWidth);
        SearchLogUitl.LogD(TAG, "loadingData content lines = " + lines);
        if (lines > 5 && loadingViewHolder.llOp.getVisibility() == View.GONE) {
            if (loadingViewHolder.tvContentVal.getMaxLines() > 5) {
                loadingViewHolder.tvContentVal.setMaxLines(5);
                loadingViewHolder.rlOp.setVisibility(View.VISIBLE);
                loadingViewHolder.llOp.setVisibility(View.VISIBLE);
            }
        }
    }

    public void initView(SearchAIModel aiModel) {
//        if (content.toString().length() > 0) {
        if (!TextUtils.isEmpty(aiModel.responseAll)) {
            loadingViewHolder.hideLoading();
            loadingViewHolder.llSubTitle.setVisibility(View.VISIBLE);
//            loadingViewHolder.rlOp.setVisibility(View.VISIBLE);
            loadingViewHolder.tvContentVal.setVisibility(View.VISIBLE);
        }
        loadingViewHolder.llOp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (loadingViewHolder.tvContentVal.getMaxLines() == 5) {
                    loadingViewHolder.tvContentVal.setMaxLines(20);
                    loadingViewHolder.iftOpFlag.setText(R.string.icon_direction_up);
                    loadingViewHolder.tvOptTitle.setText(R.string.me_search_section_ai_op_retract);
                } else {
                    loadingViewHolder.tvContentVal.setMaxLines(5);
                    loadingViewHolder.iftOpFlag.setText(R.string.icon_direction_down);
                    loadingViewHolder.tvOptTitle.setText(R.string.me_search_section_ai_op_expand);
                }
            }
        });
        // 赞 选中 #FF431A  非#000000 res icon_general_like   icon_padding_like
        loadingViewHolder.iftOpUp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (loadingViewHolder.iftOpUp.getText().toString().equals(context.getString(R.string.icon_general_like))) {
                    loadingViewHolder.iftOpUp.setText(R.string.icon_padding_like);
                    loadingViewHolder.iftOpUp.setTextColor(Color.parseColor("#FF431A"));
                    // 点赞
                    SearchHelper.searchAIFeedback(1, aiModel.traceId);
                    if (loadingViewHolder.iftOpDown.getText().toString().equals(context.getString(R.string.icon_padding_like2))) {
                        loadingViewHolder.iftOpDown.callOnClick();
                    }
                } else {
                    loadingViewHolder.iftOpUp.setText(R.string.icon_general_like);
                    loadingViewHolder.iftOpUp.setTextColor(Color.parseColor("#000000"));
                    // 取消赞
                    SearchHelper.searchAIFeedback(2, aiModel.traceId);
                }
            }
        });
        // 踩 选中 #0066FF  非#000000 res icon_general_like1  icon_padding_like2
        loadingViewHolder.iftOpDown.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (loadingViewHolder.iftOpDown.getText().toString().equals(context.getString(R.string.icon_general_like1))) {
                    loadingViewHolder.iftOpDown.setText(R.string.icon_padding_like2);
                    loadingViewHolder.iftOpDown.setTextColor(Color.parseColor("#0066FF"));
                    // 点踩
                    SearchHelper.searchAIFeedback(3, aiModel.traceId);
                    if (loadingViewHolder.iftOpUp.getText().toString().equals(context.getString(R.string.icon_padding_like))) {
                        loadingViewHolder.iftOpUp.callOnClick();
                    }
                } else {
                    loadingViewHolder.iftOpDown.setText(R.string.icon_general_like1);
                    loadingViewHolder.iftOpDown.setTextColor(Color.parseColor("#000000"));
                    // 取消踩
                    SearchHelper.searchAIFeedback(4, aiModel.traceId);
                }
            }
        });
        //  相关资料
        if (aiModel.hasInfo()) {
            loadingViewHolder.llContentInfo.setVisibility(View.VISIBLE);
            if (loadingViewHolder.llContentInfoContainer.getChildCount() > 0) {
                loadingViewHolder.llContentInfoContainer.removeAllViews();
            }
            for (SearchAIModel.RelDoc doc : aiModel.relDoc) {
                if (TextUtils.isEmpty(doc.deeplink)) {
                    continue;
                }
                TextView tvDoc = (TextView) LayoutInflater.from(context).inflate(R.layout.unifiedsearch_section_ai_content_info_item, loadingViewHolder.llContentInfoContainer, false);
                tvDoc.setText(doc.pageName);
                tvDoc.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        SearchConfigHelper.getInstance().getMainHandler().post(new Runnable() {
                            @Override
                            public void run() {
                                Router.build(doc.deeplink).go(context);
                            }
                        });
                    }
                });
                loadingViewHolder.llContentInfoContainer.addView(tvDoc);
            }
        } else {
            loadingViewHolder.llContentInfo.setVisibility(View.GONE);
        }
        //used
        if (aiModel.relApp == null || aiModel.relApp.size() == 0) {
            loadingViewHolder.mTvUsed.setVisibility(View.GONE);
            loadingViewHolder.mIvUsedIcon.setVisibility(View.GONE);
            loadingViewHolder.mTvUsedName.setVisibility(View.GONE);
        } else {
            loadingViewHolder.mTvUsed.setVisibility(View.VISIBLE);
//            loadingViewHolder.mIvUsedIcon.setVisibility(View.VISIBLE);
            loadingViewHolder.mTvUsedName.setVisibility(View.VISIBLE);
//            ImageLoader.load(context, loadingViewHolder.mIvUsedIcon, aiModel.relApp.get(0).appAvatar, R.drawable.jdme_ic_app_default);
            loadingViewHolder.mTvUsedName.setText(aiModel.relApp.get(0).appName);
            loadingViewHolder.mTvUsedName.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ImBasic.openSingleChat(context, aiModel.relApp.get(0).appTenant, aiModel.relApp.get(0).appPin, null);
                }
            });
        }
    }

    @Override
    public void refreshData(SectionedRecyclerViewAdapter adapter, List<?> data, String keyword) {
        if (loadingViewHolder != null && data != null) {
            if (data.get(0) instanceof SearchAIModel) {
                loadingData((SearchAIModel) data.get(0));
            }
        }
    }

    private class AIContentViewHolder extends RecyclerView.ViewHolder {
        public LinearLayout llBottom;
        public LinearLayout llContent;
        public TextView tvContentTitle;
        public TextView tvContentSubTitle;
        public TextView tvContentVal;
        public View vSplit;
        public TextView tvMore;

        public LinearLayout llOp;
        public IconFontView iftOpFlag;
        public TextView tvOptTitle;
        public IconFontView iftOpUp;
        public IconFontView iftOpDown;
        public LinearLayout llContentInfo;
        public LinearLayout llContentInfoContainer;
        public LinearLayout llSubTitle;
        public RelativeLayout rlOp;
        public LinearLayout llLoading;

        public TextView mTvUsed;
        public ImageView mIvUsedIcon;
        public TextView mTvUsedName;

        private Animation mLoadingAnimation;
        private View mLoadingIndicator;

        public AIContentViewHolder(@NonNull View itemView) {
            super(itemView);
            llBottom = itemView.findViewById(R.id.section_bottom);
            llContent = itemView.findViewById(R.id.section_content);
            vSplit = itemView.findViewById(R.id.section_bottom_split);
            tvMore = itemView.findViewById(R.id.tv_more);
            tvContentSubTitle = itemView.findViewById(R.id.section_content_sub_title);
            tvContentTitle = itemView.findViewById(R.id.section_content_title);
            tvContentVal = itemView.findViewById(R.id.section_content_val);
            //opt 展开/折叠 踩/赞
            llOp = itemView.findViewById(R.id.section_content_op);
            iftOpFlag = itemView.findViewById(R.id.ift_op_flag);
            tvOptTitle = itemView.findViewById(R.id.tv_op_title);
            iftOpUp = itemView.findViewById(R.id.tv_op_up);
            iftOpDown = itemView.findViewById(R.id.tv_op_down);
            //相关信息
            llContentInfo = itemView.findViewById(R.id.section_content_info);
            llContentInfoContainer = itemView.findViewById(R.id.section_content_info_container);
            //副标题容器
            llSubTitle = itemView.findViewById(R.id.section_subtitle);
            //操作容器
            rlOp = itemView.findViewById(R.id.section_op);
            //加载
            llLoading = itemView.findViewById(R.id.ll_loading);
            //used
            mTvUsed = itemView.findViewById(R.id.section_content_sub_title_use);
            mIvUsedIcon = itemView.findViewById(R.id.section_content_sub_title_use_icon);
            mTvUsedName = itemView.findViewById(R.id.section_content_sub_title_use_name);

            mLoadingIndicator = itemView.findViewById(R.id.iv_loading_indicator);
            mLoadingAnimation = AnimationUtils.loadAnimation(itemView.getContext(), R.anim.unifiedsearch_loading_indicator);
        }

        public void showBottom() {
            vSplit.setVisibility(View.VISIBLE);
            llBottom.setVisibility(View.VISIBLE);
        }

        public void hideBottom() {
            vSplit.setVisibility(View.GONE);
            llBottom.setVisibility(View.GONE);
        }

        private void showLoading() {
            mLoadingAnimation.cancel();
            mLoadingAnimation.reset();

            mLoadingIndicator.startAnimation(mLoadingAnimation);
            llLoading.setVisibility(View.VISIBLE);
        }


        public void hideLoading() {
            llLoading.setVisibility(View.GONE);
        }
    }
}
