package com.jd.oa.unifiedsearch.all.data;

import android.content.Context;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

/*
 * Time: 2023/10/18
 * Author: qudongshi
 * Description:
 */
public class SearchPreference extends AbsKvEntities {

    public static KvEntity<Boolean> KV_ENTITY_IMPORTED = new KvEntity("imported", false);

    public static KvEntity<Boolean> KV_ENTITY_TAB_IMPORTED = new KvEntity("tab_imported", false);
    public static KvEntity<String> KV_ENTITY_TAB_CUSTOM = new KvEntity("tab_sel_item", "[]");
    public static KvEntity<String> KV_ENTITY_TAB_UNSEL = new KvEntity("tab_unsel_item", "[]");

    public static KvEntity<String> KV_ENTITY_SEARCH_HISORY = new KvEntity("search_history", "[]");
    public static KvEntity<String> KV_ENTITY_SEARCH_FILTER_CONFIG = new KvEntity("search_filter_config", "");

    private static SearchPreference preference;

    private SearchPreference() {
    }

    public static synchronized SearchPreference getInstance() {
        if (preference == null) {
            preference = new SearchPreference();
        }
        return preference;
    }


    private final UseType useType = UseType.TENANT;

    @NonNull
    @Override
    public String getPrefrenceName() {
        return "SearchHistory";
    }

    @Override
    public UseType getDefaultUseType() {
        return useType;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }
}
