package com.jd.oa.unifiedsearch.all.util;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Vibrator;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.unifiedsearch.R;

/*
 * Time: 2024/1/9
 * Author: qudongshi
 * Description:
 */
public class LongClickMenuListener {

    TextView mTextView;
    Activity mActivity;

    PopupWindow mPopupWindow;

    private Long timeDown;
    private Long timeMove;
    private boolean isLongClick = false;

    public LongClickMenuListener(TextView textView, Activity activity) {
        this.mTextView = textView;
        this.mActivity = activity;
        initListener();
    }

    private void initListener() {
        if (mPopupWindow == null) {
            View contentView = LayoutInflater.from(mActivity).inflate(R.layout.unifiedsearch_popup_menu, null, false);
            View view = contentView.findViewById(R.id.menu_copy);
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    copy();
                }
            });
            mPopupWindow = new PopupWindow(contentView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            mPopupWindow.setFocusable(true);
            mPopupWindow.setOutsideTouchable(true);
        }

        mTextView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        isLongClick = false;
                        timeDown = System.currentTimeMillis();
                        break;
                    case MotionEvent.ACTION_MOVE:
                        timeMove = System.currentTimeMillis();
                        long durationMs = timeMove - timeDown;
                        //Log.d(TAG, "onTouch: durationMs="+durationMs);
                        if (durationMs > 500) {
                            vibrate();
                            isLongClick = true;
                            //重置
                            showMenu(v, event.getRawX(), event.getRawY());
                        }
                        break;
                    case MotionEvent.ACTION_UP:
                        break;
                }
                return true;
            }
        });
    }

    private void showMenu(View v, float downX, float downY) {
        if (mPopupWindow != null && !mPopupWindow.isShowing()) {
            mPopupWindow.showAtLocation(v, Gravity.NO_GRAVITY, (int) downX, (int) downY);
        }
    }


    private void vibrate() {
        if (!isLongClick) {
            Vibrator vibrator = (Vibrator) mActivity.getSystemService(Context.VIBRATOR_SERVICE);
            vibrator.vibrate(100);
        }
    }

    private void copy() {
        ClipboardManager cm = (ClipboardManager) mActivity.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData mClipData = ClipData.newPlainText("me_search", mTextView.getText());
        cm.setPrimaryClip(mClipData);
        Toast.makeText(mActivity, R.string.me_search_copied, Toast.LENGTH_LONG).show();
        if (mPopupWindow != null) {
            mPopupWindow.dismiss();
        }
    }
}
