package com.jd.oa.unifiedsearch.joyspace.filter;

import android.content.Intent;

import androidx.annotation.Nullable;

public interface FilterContext {

    //@JoySpaceFilter.FilterType
    @Nullable
    String checkedFilterType();

    void startActivity(UnifiedSearchFilter<?> searchFilter, Intent intent);

    void startActivityForResult(UnifiedSearchFilter<?> searchFilter, Intent intent, int requestCode);
}
