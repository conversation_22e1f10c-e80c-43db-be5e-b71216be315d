package com.jd.oa.unifiedsearch.joyspace.filter;

import static com.jd.oa.JDMAConstants.Mobile_Event_MEAI_docSearch_docFilterType_ck;
import static com.jd.oa.JDMAConstants.Mobile_Page_MEAI_Main_Home;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.jd.oa.ui.popupwindow.MultiSelectedPopupWindow;
import com.jd.oa.ui.popupwindow.PopupItem;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.joyspace.FilterOption;
import com.jd.oa.unifiedsearch.joyspace.view.FilterArrow;
import com.jd.oa.unifiedsearch.joyspace.view.FilterLayout;
import com.jd.oa.unifiedsearch.util.SelectPopupWindowKt;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function2;

/**
 * 文档类型
 */
public class DocumentTypeFilter extends JoySpaceFilter<FilterOption>{

    public static final String DOCUMENT_TYPE_ALL = "all";
    public static final String DOCUMENT_TYPE_DOC = "doc";
    public static final String DOCUMENT_TYPE_TABLE = "table";
    public static final String DOCUMENT_TYPE_MD_TABLE = "md_table";
    public static final String DOCUMENT_TYPE_FILE = "file";
    public static final String DOCUMENT_TYPE_FORM = "form";
    public static final String DOCUMENT_TYPE_FOLDER = "folder";
    public static final String DOCUMENT_TYPE_PPT = "ppt";
    public static final String DOCUMENT_TYPE_TRADITIONAL_DOC = "traditional_doc";

    public static final String DOCUMENT_MEETING_DOC = "meeting_doc";

    public static final String DOCUMENT_MIND_MAP = "mind_map";

    private FilterOption mOption;

    private FilterLayout mFilterLayout;

    private TextView mTextView;

    public DocumentTypeFilter(FilterContext filterContext) {
        super(filterContext);
    }

    public static List<FilterOption> documentTypes(Context context) {
        return new ArrayList<FilterOption>(){{
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_all), DOCUMENT_TYPE_ALL));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_document), DOCUMENT_TYPE_DOC));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_table), DOCUMENT_TYPE_TABLE));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_mind_map), DOCUMENT_MIND_MAP));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_meeting_doc), DOCUMENT_MEETING_DOC));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_ppt), DOCUMENT_TYPE_PPT));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_traditional_doc), DOCUMENT_TYPE_TRADITIONAL_DOC));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_form), DOCUMENT_TYPE_FORM));
            add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_file), DOCUMENT_TYPE_FILE));
            //add(new FilterOption(context.getString(R.string.unifiedsearch_joyspace_type_option_folder), DOCUMENT_TYPE_FOLDER));
        }};
    }

    @Override
    protected View onCreateView(Context context, ViewGroup container) {
        mFilterLayout = (FilterLayout) LayoutInflater.from(context).inflate(R.layout.unifiedsearch_joyspace_filter_item, container, false);
        mTextView = mFilterLayout.findViewById(R.id.tv_text);
        mTextView.setText(R.string.unifiedsearch_joyspace_filter_document_type);

        FilterArrow arrow = mFilterLayout.findViewById(R.id.arrow);
        arrow.observeTo(mFilterLayout);

        return mFilterLayout;
    }

    private MultiSelectedPopupWindow popupWindow;

    @Override
    public void onFilterClick(AppCompatActivity activity, View view) {

        InputMethodManager im = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (im.isActive()) {
            im.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }

        view.postDelayed(new Runnable() {
            @Override
            public void run() {
                List<FilterOption> options = new ArrayList<>(documentTypes(activity));
                if (!noFolderOption(mFilterContext.checkedFilterType())) {
                    options.add(new FilterOption(activity.getString(R.string.unifiedsearch_joyspace_type_option_folder), DOCUMENT_TYPE_FOLDER));
                }
                //最后添加多维表格类型
                options.add(new FilterOption(activity.getString(R.string.unifiedsearch_joyspace_type_multi_dimensional_table), DOCUMENT_TYPE_MD_TABLE));
                FilterOption selected = mOption;
                if (selected == null) {
                    selected = getOption(options, DOCUMENT_TYPE_ALL);
                }

                popupWindow = new MultiSelectedPopupWindow(
                        activity,
                        SelectPopupWindowKt.filterOptionsToPopupItems(options, selected),
                        false,
                        Collections.singletonList(SelectPopupWindowKt.filterOptionToPopupItem(getOption(options, DOCUMENT_TYPE_ALL), false)),
                        new Function2<List<PopupItem>, Boolean, Unit>() {
                            @Override
                            public Unit invoke(List<PopupItem> popupItems, Boolean aBoolean) {
                                if (popupItems.isEmpty()) {
                                    mOption = getOption(options, DOCUMENT_TYPE_ALL);
                                } else {
                                    mOption = new FilterOption(popupItems.get(0).getText(), popupItems.get(0).getValue());
                                }

                                //选择过滤条件埋点
                                if (mOption != null) {
                                    HashMap<String, String> params = new HashMap<>();
                                    switch (mOption.getValue()) {
                                        case DocumentTypeFilter.DOCUMENT_TYPE_ALL:
                                            params.put("tyid", "");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_DOC:
                                            params.put("tyid", "1");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_FORM:
                                            params.put("tyid", "2");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_TABLE:
                                            params.put("tyid", "3");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_FILE:
                                            params.put("tyid", "4");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_FOLDER:
                                            params.put("tyid", "0");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_PPT:
                                            params.put("tyid", "5");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_TRADITIONAL_DOC:
                                            params.put("tyid", "6");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_MEETING_DOC:
                                            params.put("tyid", "7");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_MIND_MAP:
                                            params.put("tyid", "8");
                                            break;
                                        case DocumentTypeFilter.DOCUMENT_TYPE_MD_TABLE:
                                            params.put("tyid", "10");
                                            break;
                                    }
                                    JDMAUtils.clickEvent(Mobile_Page_MEAI_Main_Home, Mobile_Event_MEAI_docSearch_docFilterType_ck, params);
                                }

                                if (DOCUMENT_TYPE_ALL.equals(mOption.getValue())) {
                                    reset();
                                } else {
                                    setSelected(true);
                                    mTextView.setText(mOption.getText());
                                    mFilterLayout.select();
                                }

                                if (mOnChangedListener != null) {
                                    mOnChangedListener.onChanged(mOption);
                                }
                                return null;
                            }
                        },
                        new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                mFilterLayout.close();
                                popupWindow = null;
                                return null;
                            }
                        }
                );
                popupWindow.show(view);
                mFilterLayout.open();
            }
        }, 100);     //等待键盘隐藏
    }

    @Override
    public void reset() {
        mTextView.setText(R.string.unifiedsearch_joyspace_filter_document_type);
        mFilterLayout.reset();
        mOption = null;
        super.reset();
    }

    @Override
    public FilterOption getValue() {
        return mOption;
    }


    @Override
    public void onSaveState(String type, Bundle outState) {
        super.onSaveState(type, outState);
        if (mOption != null) {
            outState.putParcelable(type, mOption);
        }

    }

    @Override
    public void onRestoreState(String type, Bundle savedState) {
        super.onRestoreState(type, savedState);
        mOption = savedState.getParcelable(type);
        if (mOption != null) {
            setSelected(true);
            mTextView.setText(mOption.getText());
        }
    }

    public static boolean noFolderOption(String type) {
        return JoySpaceFilter.TYPE_RECENT.equals(type) ||
                JoySpaceFilter.TYPE_CREATED.equals(type) ||
                JoySpaceFilter.TYPE_PUBLIC.equals(type);
    }

    private FilterOption getOption(List<FilterOption> list, String type) {
        for (int i = 0; i < list.size(); i++) {
            FilterOption option = list.get(i);
            if (option.getValue().equals(type)) {
                return option;
            }
        }
        return null;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.onConfigurationChanged(newConfig);
        }
    }
}
