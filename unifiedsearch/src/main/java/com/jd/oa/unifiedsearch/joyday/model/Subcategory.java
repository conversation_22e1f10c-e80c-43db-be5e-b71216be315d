package com.jd.oa.unifiedsearch.joyday.model;

import android.text.TextUtils;

import androidx.annotation.Keep;

import com.jd.oa.AppBase;
import com.jd.oa.utils.LocaleUtils;

@Keep
public class Subcategory {

    String subcategory;
    TextConfig title;

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public TextConfig getTitle() {
        return title;
    }

    public void setTitle(TextConfig title) {
        this.title = title;
    }

    public String getText() {
        if (title == null) return null;
        boolean isCN = TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN");
        return isCN ? title.getZhCN() : title.getEnUS();
    }
}