package com.jd.oa.unifiedsearch.joyspace.view

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.jd.oa.unifiedsearch.R

class FilterLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : LinearLayout(context, attrs, defStyleAttr, defStyleRes) {

    private var mState: FilterState = FilterState.None

    var stateChangeListener: ((state: FilterState) -> Unit)? = null

    private var mContentLayout: View? = null
    private var mBottomPadding: View? = null

    fun open() {
        this.mState = FilterState.Opened
        this.mContentLayout?.background = ContextCompat.getDrawable(context, R.drawable.unifiedsearch_filter_layout_opened_bg)
        mBottomPadding?.background = ContextCompat.getDrawable(context, R.drawable.unifiedsearch_filter_color)

        stateChangeListener?.invoke(mState)
    }

    override fun onViewAdded(child: View?) {
        super.onViewAdded(child)
        if (child?.id == R.id.view_bottom_padding) {
            mBottomPadding = child
        } else if (child?.id == R.id.layout_content) {
            mContentLayout = child
        }
    }

    fun close() {
        this.mState = FilterState.Closed
        this.mContentLayout?.background = ContextCompat.getDrawable(context, R.drawable.unifiedsearch_filter_layout_bg)
        mBottomPadding?.setBackgroundColor(Color.TRANSPARENT)

        stateChangeListener?.invoke(mState)
    }

    fun reset() {
        this.mState = FilterState.None
        this.mContentLayout?.background = ContextCompat.getDrawable(context, R.drawable.unifiedsearch_filter_layout_bg)
        mBottomPadding?.setBackgroundColor(Color.TRANSPARENT)

        stateChangeListener?.invoke(mState)
    }

    fun select() {
        this.mState = FilterState.Selected
        this.mContentLayout?.background = ContextCompat.getDrawable(context, R.drawable.unifiedsearch_filter_layout_bg)
        mBottomPadding?.background = ContextCompat.getDrawable(context, R.drawable.unifiedsearch_filter_color)

        this.isSelected = true

        stateChangeListener?.invoke(mState)
    }
}

enum class FilterState {
    None, Opened, Closed, Selected
}