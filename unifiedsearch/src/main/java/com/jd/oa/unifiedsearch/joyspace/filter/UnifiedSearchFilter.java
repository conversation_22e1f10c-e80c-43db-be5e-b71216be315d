package com.jd.oa.unifiedsearch.joyspace.filter;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

public abstract class UnifiedSearchFilter<T> {

    protected View mView;
    protected OnChangedListener<T> mOnChangedListener;
    private boolean mSelected;
    private boolean mRested;

    protected FilterContext mFilterContext;

    public UnifiedSearchFilter(FilterContext filterContext) {
        mFilterContext = filterContext;
    }

    public View getView(Context context, ViewGroup container) {
        if (mView == null) {
            mView = onCreateView(context, container);
        }
        return mView;
    }

    protected abstract View onCreateView(Context context, ViewGroup container);

    public abstract void onFilterClick(AppCompatActivity activity, View view);

    //只修改状态，不更新筛选器ui
    public void setSelectedWithoutUIChange(boolean selected) {
        mSelected = selected;
    }

    public void setSelected(boolean selected) {
        mSelected = selected;
        mView.setSelected(selected);
    }

    public boolean isSelected() {
        return mSelected;
    }

    public boolean isRested() {
        return mRested;
    }

    @CallSuper
    public void reset() {
        setSelected(false);
    }

    public void setOnChangedListener(OnChangedListener<T> onChangedListener) {
        mOnChangedListener = onChangedListener;
    }

    public abstract T getValue();

    public void onSaveState(@JoySpaceFilter.FilterSubType String type, Bundle outState) {}

    public void onRestoreState(@JoySpaceFilter.FilterSubType String type, Bundle savedState) {}

    public void onConfigurationChanged(@NonNull Configuration newConfig) {}

    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        return false;
    }

    public Context getContext() {
        return mView.getContext();
    }

    void startActivity(Intent intent) {
        mFilterContext.startActivity(this, intent);
    }

    protected void startActivityForResult(Intent intent, int requestCode) {
        mFilterContext.startActivityForResult(this, intent, requestCode);
    }

    public interface OnChangedListener<T> {
        void onChanged(T data);
    }
}