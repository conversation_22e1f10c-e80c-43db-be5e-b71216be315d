package com.jd.oa.unifiedsearch.approval.filter;

import android.content.Context;
import android.util.ArrayMap;

import com.jd.oa.AppBase;
import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.data.SearchFilterConfig;
import com.jd.oa.unifiedsearch.all.filter.ContactFilter;
import com.jd.oa.unifiedsearch.all.filter.DropDownFilter;
import com.jd.oa.unifiedsearch.all.filter.TimeFilter;
import com.jd.oa.unifiedsearch.all.helper.SearchFilterConfigHelper;
import com.jd.oa.unifiedsearch.joyspace.filter.FilterContext;
import com.jd.oa.unifiedsearch.joyspace.filter.UnifiedSearchFilter;
import com.jd.oa.utils.LocaleUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class ApprovalFilterFactory {

    private final ArrayMap<String, UnifiedSearchFilter<?>> sCache = new ArrayMap<>();

    public UnifiedSearchFilter<?> getSubType(FilterContext filterContext, Context context, SearchFilterConfig.FilterItem filterItem) {
        UnifiedSearchFilter<?> filter = sCache.get(filterItem.filterItemKey);
        if (filter != null) {
            return filter;
        }
        switch (filterItem.labelType) {
            case SearchFilterConfigHelper.TYPE_SINGLE_SELECT:
                filter = new DropDownFilter(filterContext, filterItem);
                break;
            case SearchFilterConfigHelper.TYPE_APPROVAL_CONTACT:
                filter = new ContactFilter(filterContext, filterItem, context.getString(R.string.unifiedsearch_approval_applicant));
                break;
            case SearchFilterConfigHelper.TYPE_APPROVAL_APPLICATION_TIME:
            case SearchFilterConfigHelper.TYPE_APPROVAL_NOTIFY_TIME:
            case SearchFilterConfigHelper.TYPE_APPROVAL_COMPLETION_TIME:
            case SearchFilterConfigHelper.TYPE_APPROVAL_TASK_ARRIVAL_TIME:
                filter = new TimeFilter(filterContext, filterItem, filterItem.labelType);
                break;
        }
        sCache.put(filterItem.filterItemKey, filter);
        return filter;
    }

    public UnifiedSearchFilter<?> getFilter(String filterName) {
        return sCache.get(filterName);
    }

    public void resetAll() {
        if (!sCache.isEmpty()) {
            for (UnifiedSearchFilter<?> filter : sCache.values()) {
                filter.reset();
            }
        }
    }
}
