package com.jd.oa.unifiedsearch.joyday.model;

import java.util.HashMap;

public class JoySpaceInfo {
    public String id;
    public String deepLink;
    public String title;
    public String pageType;
    public String type;
    public String permissionType;

    public HashMap<String, Object> toJson() {
        HashMap<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("deepLink", deepLink);
        data.put("title", title);
        data.put("pageType", pageType);
        data.put("type", type);
        data.put("permissionType", permissionType);
        return data;
    }


/*    JoySpaceInfo({
        this.id,
                this.deepLink,
                this.title,
                this.pageType,
                this.type,
                this.permissionType});

  JoySpaceInfo.fromJson(dynamic json) {
        id = json["id"];
        deepLink = json["deepLink"] ?? json["url"];
        title = json["title"];
        pageType = json["pageType"]?.toString();
        type = json["type"]?.toString();
        permissionType = json["permissionType"]?.toString();
    }

    Map<String, dynamic> toJson() {
        var map = <String, dynamic>{};
        map["id"] = id;
        map["deepLink"] = deepLink;
        map["title"] = title;
        map["pageType"] = pageType;
        map["type"] = type;
        map["permissionType"] = permissionType;
        return map;
    }*/

}
