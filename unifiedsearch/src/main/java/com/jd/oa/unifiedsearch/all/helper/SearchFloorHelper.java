package com.jd.oa.unifiedsearch.all.helper;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.model.SearchFloorConfig;
import com.jd.oa.im.listener.Callback4;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.unifiedsearch.all.callback.IDataLoadedCallback;
import com.jd.oa.unifiedsearch.all.data.SearchPreference;
import com.jd.oa.unifiedsearch.all.section.BaseSection;
import com.jd.oa.unifiedsearch.util.SearchLogUitl;
import com.jd.oa.utils.JsonUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/*
 * Time: 2023/10/20
 * Author: qudongshi
 * Description:
 */
public class SearchFloorHelper {

    private final String TAG = "SearchFloorHelper";

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    private IDataLoadedCallback dataLoadedCallback;

    private List<SearchFloorConfig> searchFloorConfigs;

    public HashMap<String, Boolean> imIsLoadOverMap = new HashMap<>();

    public List<SearchFloorType> needWaitTypes = new ArrayList<>();

    public SearchFloorHelper(IDataLoadedCallback callback) {
        dataLoadedCallback = callback;
        searchFloorConfigs = SearchConfigHelper.getInstance().getSearchFloorConfigs();
        needWaitTypes.add(SearchFloorType.CONTACT);
        needWaitTypes.add(SearchFloorType.GROUP);
        getSectionsOrder();
    }

    public void searchIMFloor(List<SearchFloorType> types, String keyword, String sessionId, String requestId) {
        if (types == null || types.size() == 0 || TextUtils.isEmpty(keyword)) {
            return;
        }
        List<String> strTypes = new ArrayList<>();
        for (SearchFloorType sType : types) {
            if (sType != SearchFloorType.POWERED && sType != SearchFloorType.FEED_BACK && sType != SearchFloorType.EMPTY && sType != SearchFloorType.APP && sType != SearchFloorType.TASK && sType != SearchFloorType.JOY_SPACE && sType != SearchFloorType.ENCYCLO) {
                strTypes.add(sType.toString());
            }
        }
        long startTime = System.currentTimeMillis();
        HashMap<String, List<?>> map = new HashMap<>();
        long waitTime = 1000;
        try {
            waitTime = Long.parseLong(ConfigurationManager.get().getEntry("android.search.wait.im.timeout.time", "1000"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        long finalWaitTime = waitTime;
        setImIsLoadOver(keyword, false);
        imDdService.getSearchData(strTypes, keyword, sessionId, requestId, new Callback4<String, List<?>>() {
            @Override
            public void onResult(String keyword, String strType, List<?> data) {
                if (dataLoadedCallback == null) {
                    return;
                }
                if (isImLoaded(keyword)) {
                    dataLoadedCallback.onResult(convertFloorType(strType), keyword, data);
                    return;
                }
                map.put(strType, data);
                boolean needWaitIsLoaded = true;
                for (SearchFloorType type : needWaitTypes) {
                    if (!map.containsKey(type.toString())) {
                        needWaitIsLoaded = false;
                        break;
                    }
                }
                if (needWaitIsLoaded || (System.currentTimeMillis() - startTime) >= finalWaitTime) {
                    setImIsLoadOver(keyword, true);
                    for (String s : map.keySet()) {
                        dataLoadedCallback.onResult(convertFloorType(s), keyword, map.get(s));
                    }
                    map.clear();
                }
            }
        });
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!map.isEmpty()) {
                for (String s : map.keySet()) {
                    dataLoadedCallback.onResult(convertFloorType(s), keyword, map.get(s));
                }
                map.clear();
                setImIsLoadOver(keyword, true);
            }
        }, (waitTime + 100));

    }

    public void searchMEFloor(String keyword, SearchFloorType floorType, String sessionId, String requestId) {
        if (SearchFloorType.APP == floorType) {
            SearchHelper.searchAppInfo(keyword, dataLoadedCallback, sessionId, requestId);
        } else {
            SearchHelper.searchByFloorType(keyword, dataLoadedCallback, floorType, sessionId, requestId);
        }
    }

    //旧AI搜索楼层
    public void searchMEAIFloor(String keyword, String sessionId, String requestId, String traceId) {
        SearchHelper.searchAI(keyword, traceId, dataLoadedCallback, sessionId, requestId);
    }

    List<BaseSection> sectionList = new ArrayList<>();
    List<SearchFloorType> floorOrder;
    List<SearchFloorType> imFloors;

    public List<SearchFloorType> getSectionsOrder() {
        if (floorOrder == null && searchFloorConfigs != null) {
            floorOrder = new ArrayList<>();
            for (SearchFloorConfig config : searchFloorConfigs) {
                SearchFloorType tmp = convertFloorType(config.groupName);
                if (tmp != null) {
                    floorOrder.add(tmp);
                }
            }
            floorOrder.add(SearchFloorType.HISTORY);
            floorOrder.add(SearchFloorType.AI_RECOMMEND);
            floorOrder.add(SearchFloorType.RECOMMEND);
            floorOrder.add(SearchFloorType.DISCOVERY);
            floorOrder.add(SearchFloorType.PROCESS);
            floorOrder.add(SearchFloorType.APPROVAL);
            floorOrder.add(SearchFloorType.EMPTY);
            floorOrder.add(SearchFloorType.FEED_BACK);
            floorOrder.add(SearchFloorType.POWERED);
        }
        return floorOrder;
    }

    public List<SearchFloorType> getIMFloorType() {
        if (imFloors == null) {
            imFloors = new ArrayList<>();
            imFloors.add(SearchFloorType.CONTACT);
            imFloors.add(SearchFloorType.ROBOT);
            imFloors.add(SearchFloorType.GROUP);
            imFloors.add(SearchFloorType.MESSAGE);
            if (SearchConfigHelper.getInstance().searchNoticeEnable()){
                imFloors.add(SearchFloorType.NOTICE);
            }
            if (imDdService.hasWaiterPermission()) {
                imFloors.add(SearchFloorType.WAITER);
            }
        }
        return imFloors;
    }

    public synchronized void addSection(SectionedRecyclerViewAdapter adapter, SearchFloorType floorType, BaseSection section) {
        if (currentKey != null && !currentKey.equals(section.keyword)) {
            return;
        }
        section.floorType = floorType;
        sectionList.add(section);
        if (sectionList.size() == 1) {
            adapter.addSection(floorType.toString(), section);
            adapter.notifyDataSetChanged();
        } else {
            Collections.sort(sectionList, new Comparator<BaseSection>() {
                @Override
                public int compare(BaseSection o1, BaseSection o2) {
                    try {
                        if (o1 == null || o2 == null) {
                            return 0;
                        }
                        SearchLogUitl.LogD(TAG, o1.floorType.toString() + " ---- " + o2.floorType);
                        if (floorOrder.indexOf(o2.floorType) < 0) {
                            return -1;
                        }
                        if (floorOrder.indexOf(o1.floorType) < 0) {
                            return 1;
                        }
                        if (floorOrder.indexOf(o1.floorType) < floorOrder.indexOf(o2.floorType)) {
                            return -1;
                        }
                        if (floorOrder.indexOf(o1.floorType) > floorOrder.indexOf(o2.floorType)) {
                            return 1;
                        }
                    } catch (Exception e) {
                        SearchLogUitl.LogE(TAG, "Collections.sort exception", e);
                    }
                    return 0;
                }
            });
            adapter.removeAllSections();
            for (BaseSection tmp : sectionList) {
                if (SearchFloorType.EMPTY == tmp.floorType && sectionList.size() > 3) {
                    continue;
                }
                adapter.addSection(tmp.floorType.toString(), tmp);
            }
            adapter.notifyDataSetChanged();
        }
    }

    public void removeAllSection(SectionedRecyclerViewAdapter adapter) {
        sectionList.clear();
        adapter.removeAllSections();
        adapter.notifyDataSetChanged();
    }

    public void removeSection(SectionedRecyclerViewAdapter adapter, SearchFloorType searchFloorType) {
        for (int i = 0; i < sectionList.size(); i++) {
            if (sectionList.get(i).floorType == searchFloorType) {
                if (adapter.getSection(searchFloorType.toString()) != null) {
                    adapter.removeSection(searchFloorType.toString());
                }
                sectionList.remove(i);
                adapter.notifyDataSetChanged();
                break;
            }
        }
    }

    public List<String> getHistory() {
        String jsonData = "[]";
        List<String> data;
        if (!SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_IMPORTED)) {
            data = imDdService.getSearchHistory();
            if (data != null) {
                jsonData = JsonUtils.getGson().toJson(data);
                SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_SEARCH_HISORY, jsonData);
            }
            SearchPreference.getInstance().put(SearchPreference.KV_ENTITY_IMPORTED, true);
        } else {
            jsonData = SearchPreference.getInstance().get(SearchPreference.KV_ENTITY_SEARCH_HISORY);
            data = JsonUtils.getGson().fromJson(jsonData, List.class);
        }
        return data;
    }

    public SearchFloorType convertFloorType(String type) {
        switch (type) {
            case "CONTACT":
                return SearchFloorType.CONTACT;
            case "GROUP":
                return SearchFloorType.GROUP;
            case "MESSAGE":
                return SearchFloorType.MESSAGE;
            case "NOTICE":
                return SearchFloorType.NOTICE;
            case "ORG":
                return SearchFloorType.ORG;
            case "ROBOT":
                return SearchFloorType.ROBOT;
            case "WAITER":
                return SearchFloorType.WAITER;
            case "JOY_SPACE":
                return SearchFloorType.JOY_SPACE;
            case "ENCYCLO":
                return SearchFloorType.ENCYCLO;
            case "APP":
                return SearchFloorType.APP;
            case "TASK":
                return SearchFloorType.TASK;
            case "AI":
                return SearchFloorType.AI;
            default:
                return null;
        }
    }

    String currentKey;

    public void setCurrentKey(String key) {
        currentKey = key;
    }

    public boolean searchAIEnable() {
        String flag = ABTestManager.getInstance().getConfigByKey("me.search.ai.android.enable", "0");
        if (flag.equals("0")) {
            return false;
        }
        return true;
    }

    public Boolean isImLoaded(String keyword) {
        if (imIsLoadOverMap.containsKey(keyword)) {
            return imIsLoadOverMap.get(keyword);
        } else
            return true;
    }

    public void setImIsLoadOver(String keyword, boolean isOver) {
        imIsLoadOverMap.put(keyword, isOver);
    }
}
