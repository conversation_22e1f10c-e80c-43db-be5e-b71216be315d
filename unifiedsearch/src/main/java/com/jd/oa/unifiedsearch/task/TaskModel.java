package com.jd.oa.unifiedsearch.task;

import android.content.Context;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.utils.DateShowUtils;

import java.io.Serializable;
import java.util.List;

/*
 * Time: 2023/11/23
 * Author: qudongshi
 * Description:
 */
public class TaskModel implements Serializable {

    public User creator;
    public String taskIcon;
    public List<User> owners;
    public long endTime;
    public String title;
    public String taskId;
    public int taskStatus;
    public int status;
    public String remark;
    public String seqUrl;


    public class User {
        public String app;
        public String ddAppId;
        public int readStatus;
        public String realName;
        public String emplAccount;
        public int chief;
    }

    public String buildDec(Context context) {
        StringBuilder stringBuilder = new StringBuilder();
        // 创建人
        if (creator != null) {
            stringBuilder.append(context.getResources().getString(R.string.me_joywork_creator, creator.realName));
        }
        // 执行人
        if (owners != null && owners.size() > 0) {
            for (int i = 0; i < owners.size(); i++) {
                if (i == 0) {
                    stringBuilder.append(" ");
                    stringBuilder.append(context.getResources().getString(R.string.me_joywork_owner, owners.get(i).realName));
                } else if (i > 2) {
                    stringBuilder.append("...");
                    break;
                } else {
                    stringBuilder.append(",");
                    stringBuilder.append(owners.get(i).realName);
                }
            }
        }
        //截止日期
        if (endTime != 0) {
            stringBuilder.append(" ");
            stringBuilder.append(context.getResources().getString(R.string.me_search_deadline_normal, DateShowUtils.getTimeShowTextWithHourMinute(endTime)));
        }
        return stringBuilder.toString();
    }
}

