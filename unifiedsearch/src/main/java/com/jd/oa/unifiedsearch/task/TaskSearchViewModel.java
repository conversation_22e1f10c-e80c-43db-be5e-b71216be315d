package com.jd.oa.unifiedsearch.task;

import android.text.TextUtils;
import android.util.Pair;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.alibaba.fastjson.JSON;
import com.jd.oa.network.httpmanager.CancelTag;
import com.jd.oa.unifiedsearch.all.callback.IDataLoadedCallback;
import com.jd.oa.unifiedsearch.all.data.SearchModel;
import com.jd.oa.unifiedsearch.all.helper.SearchAnalyzeHelper;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.all.helper.SearchHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class TaskSearchViewModel extends ViewModel {

    private MutableLiveData<Boolean> mLoading = new MutableLiveData<>(false);
    private MutableLiveData<Pair<String, List<TaskModel>>> mTaskInfoList = new MutableLiveData<>();
    private MutableLiveData<String> mError = new MutableLiveData<>();

    private String mKeyword;

    private CancelTag mCancelTag;

    public void refresh(String keyword, String sessionId) {
        mLoading.setValue(true);
        mKeyword = keyword;

        if (TextUtils.isEmpty(keyword)) {
            mTaskInfoList.setValue(Pair.create(keyword, Collections.emptyList()));
            mLoading.setValue(false);
            return;
        }

        if (mCancelTag != null) {
            //HttpManager.cancel(mCancelTag);
        }

        mCancelTag = new CancelTag(keyword);
        String requestId = System.currentTimeMillis() + "";
        SearchAnalyzeHelper.getInstance().searchStart(keyword, sessionId, requestId, SearchFloorType.TASK.toString(), false);
        SearchHelper.searchByFloorType(keyword, new IDataLoadedCallback() {
            @Override
            public void onResult(SearchFloorType floorType, String keyword, List<?> data) {
                mLoading.setValue(false);
                if (data != null && data.size() > 0) {
                    if (Objects.equals(mKeyword, keyword)) {
                        List<TaskModel> listData = new ArrayList<>();
                        for (int i = 0; i < data.size(); i++) {
                            if (data.get(i) instanceof SearchModel.Record) {
                                SearchModel.Record record = (SearchModel.Record) data.get(i);
                                String strData = JSON.toJSONString(record.refPro);
                                TaskModel item = JSON.parseObject(strData, TaskModel.class);
                                listData.add(item);
                            }
                        }
                        mTaskInfoList.setValue(Pair.create(keyword, listData));
                    }
                } else {
                    mTaskInfoList.setValue(Pair.create(keyword, new ArrayList<>()));
                }
            }
        }, SearchFloorType.TASK, 0, 100);
    }

    public LiveData<Boolean> getLoading() {
        return mLoading;
    }

    public LiveData<Pair<String, List<TaskModel>>> getTaskInfoList() {
        return mTaskInfoList;
    }

    public LiveData<String> getError() {
        return mError;
    }

    public void clear() {
        mTaskInfoList.setValue(Pair.create(null, Collections.emptyList()));
    }
}
