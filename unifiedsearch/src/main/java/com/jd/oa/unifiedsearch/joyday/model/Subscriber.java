package com.jd.oa.unifiedsearch.joyday.model;

import java.util.HashMap;

public class Subscriber implements Comparable<Subscriber> {
    public static int PUBLIC = 1;
    public static int DETAIL = 2;
    public static int BUSY = 3;

    public int access;
    /// 1. 创建者 2. 个人日历订阅者 3.个人日历被动订阅者 4.团队日历管理员 5.团队日历编辑者 6.团队日历订阅者
    public int userType;
    public User user;
    ///是否订阅 1:订阅，0:未订阅
    public int unsubscribe;

    @Override
    public String toString() {
        final HashMap<String, Object> data = new HashMap<>();
        data.put("access", this.access);
        if (this.user != null) {
            data.put("user", this.user.toJson());
        }
        data.put("userType", userType);
        data.put("unsubscribe", unsubscribe);
        return data.toString();
    }

    @Override
    public int compareTo(Subscriber other) {
        if (this.userType == other.userType) {
            return this.user.compareTo(other.user);
        } else {
            return Integer.compare(this.userType, other.userType);
        }
    }
}
