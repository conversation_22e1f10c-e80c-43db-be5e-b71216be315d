package com.jd.oa.unifiedsearch.joyspace;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Nullable;

import java.util.Objects;

public class FilterOption implements Parcelable {

    private String text;
    private String value;

    public FilterOption(String text, String value) {
        this.text = text;
        this.value = value;
    }


    protected FilterOption(Parcel in) {
        text = in.readString();
        value = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(text);
        dest.writeString(value);
    }

    @Override
    public String toString() {
        return "FilterOption{" +
                "text='" + text + '\'' +
                ", value='" + value + '\'' +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<FilterOption> CREATOR = new Creator<FilterOption>() {
        @Override
        public FilterOption createFromParcel(Parcel in) {
            return new FilterOption(in);
        }

        @Override
        public FilterOption[] newArray(int size) {
            return new FilterOption[size];
        }
    };

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (!(obj instanceof FilterOption)) return false;
        FilterOption other = (FilterOption) obj;
        return Objects.equals(this.text, other.text) && Objects.equals(this.value, other.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.text, this.value);
    }
}
