package com.jd.oa.unifiedsearch.all.viewholder;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.R;
import com.jd.oa.unifiedsearch.all.helper.SearchFloorType;
import com.jd.oa.unifiedsearch.util.ResourceUtil;

/*
 * Time: 2023/10/26
 * Author: qudongshi
 * Description:
 */
public class ContentViewHolder extends RecyclerView.ViewHolder {
    public LinearLayout llBottom;
    public LinearLayout llContent;

    public RecyclerView recyclerView;
    public View vSplit;
    public TextView tvMore;

    public ContentViewHolder(@NonNull View itemView) {
        super(itemView);
        llBottom = itemView.findViewById(R.id.section_bottom);
        llContent = itemView.findViewById(R.id.section_content);
        vSplit = itemView.findViewById(R.id.section_bottom_split);
        tvMore = itemView.findViewById(R.id.tv_more);
        recyclerView = itemView.findViewById(R.id.recycle_view);
    }

    public void showBottom(Context context, SearchFloorType type) {
        vSplit.setVisibility(View.VISIBLE);
        llBottom.setVisibility(View.VISIBLE);
        String str = context.getString(R.string.me_all_search_section_bottom, ResourceUtil.getSectionHeaderRes(context, type));
        tvMore.setText(str);

    }

    public void hideBottom() {
        vSplit.setVisibility(View.GONE);
        llBottom.setVisibility(View.GONE);
    }

}