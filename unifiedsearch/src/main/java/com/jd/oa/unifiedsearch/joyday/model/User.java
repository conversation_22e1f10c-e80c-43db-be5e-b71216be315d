package com.jd.oa.unifiedsearch.joyday.model;

import android.text.TextUtils;

import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class User implements Comparable<User> {

    public static final String TYPE_FORMAL = "FORMAL"; // 正式在职账号
    public static final String TYPE_VIRTUAL = "VIRTUAL"; // 虚拟账号
    public static final String TYPE_PUBLIC = "PUBLIC"; // 公有账号
    public static final String TYPE_QUIT = "QUIT"; // 离职账号
    public static final String TYPE_UNKNOWN = "UN_KNOWN"; // 未知账号类型

    public String account;
    public String imageUrl;
    public String realName;
    public String teamId;
    public String userId;
    public String ddAppId;
    public String userType;
    public String tenantCode;
    //2022.04新接口数据格式
    public String email;
    public int external;

    public static User fromParams(String account, String userId, String realName, String userType, String appId) {
        User user = new User();
        user.account = account;
        user.userId = userId;
        user.realName = realName;
        user.userType = userType;
        user.ddAppId = appId;
        return user;
    }

    @Override
    public int compareTo(User other) {
        if (TextUtils.equals(this.account, other.account)) {
            return this.ddAppId.compareTo(other.ddAppId);
        } else {
            return this.account.compareTo(other.account);
        }
    }

    public boolean isMySelf() {
        return TextUtils.equals(this.ddAppId, PreferenceManager.UserInfo.getTimlineAppID()) &&
                TextUtils.equals(this.account, PreferenceManager.UserInfo.getUserName());
    }

    public HashMap<String, Object> toJson() {
        HashMap<String, Object> data = new HashMap<>();
        data.put("account", this.account);
        data.put("imageUrl", this.imageUrl);
        data.put("realName", this.realName);
        data.put("teamId", this.teamId);
        data.put("userId", this.userId);
        data.put("ddAppId", this.ddAppId);
        data.put("userType", this.userType);
        data.put("tenantCode", this.tenantCode);
        data.put("email", this.email);
        data.put("external", this.external);
        return data;
    }
}
