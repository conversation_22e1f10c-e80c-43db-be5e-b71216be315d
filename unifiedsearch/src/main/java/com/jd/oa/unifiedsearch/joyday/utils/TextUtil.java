package com.jd.oa.unifiedsearch.joyday.utils;

import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;

import java.util.HashSet;
import java.util.Set;

public class TextUtil {

    //<em>产品</em>运营月度例会
    //产品运营月度<em>例会</em>
    //产品<em>运营月度</em>例会
    //<em>产品运营月度例会</em>
    //产品运营月度例会
    //<em></em>产品运营<em></em>月度例会<em></em>
    //<em>产品</em>运<em>营月</em>度<em>例会</em>
    //<em>产品</em><em>运营</em>月度<em>例会</em>
    public static SpannableStringBuilder getSpanText(String text, int highlightColor) {
        if (TextUtils.isEmpty(text)) {
            return new SpannableStringBuilder("");
        }

        String[] list = text.split("<em>");
        if (list.length <= 0) {
            return new SpannableStringBuilder(text);
        }

        Set<String> highlights = new HashSet<>();//全部高亮词，去重
        for (String s : list) {
            if (TextUtils.isEmpty(s)) {
                continue;
            }
            int end = s.indexOf("</em>");
            if (end < 0) {
                continue;
            }
            String sub = s.substring(0, end);
            if (TextUtils.isEmpty(sub)) {
                continue;
            }
            highlights.add(sub);
        }

        text = text.replaceAll("<em>", "");
        text = text.replaceAll("</em>", "");
        SpannableStringBuilder style = new SpannableStringBuilder(text);
        for (String highlight : highlights) {
            int start = text.indexOf(highlight, 0);
            while (start >= 0) {
                style.setSpan(new ForegroundColorSpan(highlightColor), start, start + highlight.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                start = text.indexOf(highlight, start + highlight.length());
            }
        }
        return style;
    }

}
