package com.jd.oa.unifiedsearch.joyspace.data

import android.content.Context
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.jd.oa.unifiedsearch.joyspace.sortcondition.SelectedSortCondition
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import java.util.*
import java.util.concurrent.TimeUnit

class JoySpaceMockDataSource(private val context: Context) : JoySpaceDataSource {

    private var mRecentCache: List<JoySpaceDocument>? = null
    private var mReceivedCache: List<JoySpaceDocument>? = null
    private var mSentCache: List<JoySpaceDocument>? = null

    override fun searchJoySpaceDocuments(
        keyword: String,
        params: SearchFilterQuery,
        start: Int,
        length: Int
    ): Single<List<JoySpaceDocument>> {
        TODO("Not yet implemented")
    }

    override fun getRecentOpenedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        val result = if (mRecentCache != null) {
            Single.just(mRecentCache)
        } else {
            Single.create<List<JoySpaceDocument>> {
                val inputStream = context.assets.open("recent.json")
                it.onSuccess(JSON.parseObject(inputStream, object : TypeReference<List<JoySpaceDocument>>() {}.type))
            }.subscribeOn(Schedulers.io())
        }
        return result.delay(200, TimeUnit.MILLISECONDS)
    }

    override fun getReceivedDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        val result = if (mReceivedCache != null) {
            Single.just(mReceivedCache)
        } else {
            Single.create<List<JoySpaceDocument>> {
                val inputStream = context.assets.open("received.json")
                it.onSuccess(JSON.parseObject(inputStream, object : TypeReference<List<JoySpaceDocument>>() {}.type))
            }.subscribeOn(Schedulers.io())
        }
        return result.delay(200, TimeUnit.MILLISECONDS)
    }

    override fun getSentDocuments(start: Int, length: Int, sortCondition: SelectedSortCondition?): Single<List<JoySpaceDocument>> {
        val result = if (mSentCache != null) {
            Single.just(mSentCache)
        } else {
            Single.create<List<JoySpaceDocument>> {
                val inputStream = context.assets.open("sent.json")
                it.onSuccess(JSON.parseObject(inputStream, object : TypeReference<List<JoySpaceDocument>>() {}.type))
            }.subscribeOn(Schedulers.io())
        }
        return result.delay(200, TimeUnit.MILLISECONDS)
    }

    override fun getCreatedByMySelfDocuments(
        start: Int,
        length: Int,
        sortCondition: SelectedSortCondition?
    ): Single<List<JoySpaceDocument>> {
        return Single.just(Collections.emptyList())
    }

    override fun getRelatedDocuments(
        start: Int,
        length: Int,
        query: SearchFilterQuery
    ): Single<List<JoySpaceDocument>> {
        return Single.just(Collections.emptyList())
    }
}