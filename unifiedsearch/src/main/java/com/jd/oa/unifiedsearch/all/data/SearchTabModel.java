package com.jd.oa.unifiedsearch.all.data;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.List;

/*
 * Time: 2023/10/26
 * Author: qudongshi
 * Description:
 */
public class SearchTabModel implements Serializable {

    public List<String> defaultOrder;
    public List<String> customOrder;
    public List<String> unSelectedTab;

    public static class SearchTab {
        public String type;
        public int seq;

        public boolean canDelete() {
            if (TextUtils.isEmpty(type)) {
                return false;
            }
            return type.equals("all");
        }
    }
}
