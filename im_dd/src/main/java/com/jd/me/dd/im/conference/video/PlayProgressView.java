package com.jd.me.dd.im.conference.video;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.jd.me.dd.im.R;


/**
 * Created by c<PERSON><PERSON><PERSON><PERSON> on 2018/8/15.
 */

public class PlayProgressView extends View {

    private Paint mBgPaint;
    private Paint mPaint;

    private int mWidth;
    private int mHight;

    private int mRadius;

    //进度线条长，宽
    private int mRealWidth;
    private int mRealHeight;

    private int mMax; //最大进度
    private int mCurrent; //当前进度

    public PlayProgressView(Context context) {
        this(context, null);
    }

    public PlayProgressView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, -1);
    }

    public PlayProgressView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initPaint();
    }

    public void setMax(int max) {
        mMax = max;
    }

    public void setCurrentProgress(int current) {
        //解决拖拽进度抬手后，进度条跳跃问题
        if (current > mLastUpProgress || current == 0) {
            mCurrent = current;
            invalidate();
        }
    }

    private void initPaint() {
        //背景
        mBgPaint = new Paint();
        mBgPaint.setAntiAlias(true);
        mBgPaint.setColor(Color.parseColor("#888888"));

        //前景
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(getResources().getColor(R.color.white));
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWidth = w;
        mHight = h;

        mRadius = mHight / 2;
        mRealWidth = mWidth - 2 * mRadius;
        mRealHeight = 5;
    }


    private float mDownX;

    private boolean mTouchBall = false;  //是否点中了球

    private int mDownBallCenter;

    private int mLastUpProgress;

    @Override
    public boolean onTouchEvent(MotionEvent e) {
        switch (e.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = e.getX();
                if (Math.abs(mDownX - getCurrentBallCenter()) < 40) {
                    mTouchBall = true;
                    mDownBallCenter = getCurrentBallCenter();
                    if (mListener != null) {
                        mListener.start();
                    }
                }

                break;
            case MotionEvent.ACTION_MOVE:
                if (mTouchBall) {
                    float moveX = mDownX - e.getX();
                    //公式
                    //mDownBallCenter - moveX = mRadius + new_width
                    // --> new_witdh = (mRealWidth * ((float) new_mCurrent / mMax)) = mDownBallCenter - moveX - mRaduis
                    mCurrent = (int) ((mDownBallCenter - moveX - mRadius) / mRealWidth * mMax);
                    if (mCurrent > mMax) {
                        mCurrent = mMax;
                    } else if (mCurrent < 0) {
                        mCurrent = 0;
                    }

                    if (mListener != null) {
                        mListener.drag(mCurrent);
                    }

                    invalidate();
                }

                break;
            case MotionEvent.ACTION_UP:
                if (mTouchBall) {
                    mTouchBall = false;
                    if (mListener != null) {
                        mListener.play(mCurrent);
                    }
                    mLastUpProgress = mCurrent;
                }
                break;
        }

        return true;
    }

    public void reset() {
        mLastUpProgress = 0;
        mCurrent = 0;
        invalidate();
    }


    private int getCurrentBallCenter() {
        int width = (int) (mRealWidth * ((float) mCurrent / mMax));
        return mRadius + width;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        //通知父View不拦截事件，自己处理
        getParent().requestDisallowInterceptTouchEvent(true);
        return super.dispatchTouchEvent(event);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //背景
        RectF rectFBg = new RectF(mRadius, mRadius - mRealHeight / 2, mRealWidth, mRadius + mRealHeight / 2);
        canvas.drawRoundRect(rectFBg, 10, 10, mBgPaint);

        //前景
        int width = (int) (mRealWidth * ((float) mCurrent / mMax));
        RectF rectFFg = new RectF(mRadius, mRadius - mRealHeight / 2, width, mRadius + mRealHeight / 2);
        canvas.drawRoundRect(rectFFg, 10, 10, mPaint);

        //球
        canvas.drawCircle(mRadius + width, mRadius, mRadius, mPaint);
    }


    public interface onPlayListener {
        void start();

        void drag(int progress);

        void play(int progress);
    }

    private onPlayListener mListener;

    public void setOnPlayListener(onPlayListener listener) {
        mListener = listener;
    }
}
