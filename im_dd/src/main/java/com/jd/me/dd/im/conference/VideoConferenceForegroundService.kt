package com.jd.me.dd.im.conference

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.media.AudioAttributes
import android.net.Uri
import android.os.*
import android.text.TextUtils
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import cdyjy.com.jd.downloaduploadlibrary.download.BaseDownloadProgressListener
import cdyjy.com.jd.downloaduploadlibrary.download.DownloadManager
import com.google.gson.Gson
import com.jd.cdyjy.common.base.ui.custom.global_config.GlobalConfigServiceManager
import com.jd.cdyjy.icsp.cache.AppCache
import com.jd.cdyjy.opim.util.android.UiThreadUtils
import com.jd.me.dd.im.R
import com.jd.me.dd.im.conference.model.ConferenceUser
import jd.cdyjy.jimcore.application.BaseCoreApplication
import jd.cdyjy.jimcore.broadcast.NotificationBroadcastReceiver.VIDEO_CONFERENCE_CALL
import jd.cdyjy.jimcore.commoninterface.utils.BitmapUtils
import jd.cdyjy.jimcore.core.ipc_global.MyInfo
import jd.cdyjy.jimcore.core.ipc_global.ServerTime
import jd.cdyjy.jimcore.core.utils.ImageUtils
import jd.cdyjy.jimcore.tcp.protocol.entity.NotificationVideoConferenceEntity
import jd.cdyjy.jimcore.tools.CoreCommonUtils
import jd.cdyjy.jimcore.tools.CoreThreadManager

class VideoConferenceForegroundService : Service() {

    private val TAG = "VideoConferenceForegroundService"
    private val CHANNEL_ID = "JDME_CONFERENCE_NEW" //"ConferenceCall_jdme_voip";

    private val CHANNEL_NAME: CharSequence = "视频通话通知" //"ConferenceCall";

    private val CHANNEL_DESCRIPTION = "视频通话时使用的通知类别"

    companion object {

        val NOTIFICATION_CONFERENCE_NAME = "notificationConferenceName"
        val NOTIFICATION_CONFERENCE_ID = "notificationConferenceID"
        val NOTIFICATION_CONFERENCE = "notificationConference"
        val NOTIFICATION_INVITER = "notificationInviter"
        val NOTIFICATION_CONFERENCE_TYPE = "notificationConferenceType"
        val NOTIFICATION_GROUPID = "notificationGroupId"

        fun startService(
                context: Context,
                conferenceId: String,
                conferenceName: String?,
                conference: String,
                inviter: String,
                conferenceType: Int,
                groupId: String?
        ) {
            val intent = Intent(context, VideoConferenceForegroundService::class.java)
            intent.putExtra(NOTIFICATION_CONFERENCE, conference)
            intent.putExtra(NOTIFICATION_INVITER, inviter)
            intent.putExtra(NOTIFICATION_CONFERENCE_TYPE, conferenceType)
            intent.putExtra(NOTIFICATION_CONFERENCE_ID, conferenceId)
            intent.putExtra(NOTIFICATION_CONFERENCE_NAME, conferenceName)
//        intent.putExtra(NOTIFICATION_GROUPID, groupId)
            context.startService(intent)
        }

        fun stopService(context: Context) {
            val intent = Intent(context, VideoConferenceForegroundService::class.java)
            context.stopService(intent)
        }

        fun isRunning(context: Context): Boolean {
            val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val serviceList = manager.getRunningServices(30)
            if (serviceList.isNullOrEmpty()) {
                return false
            }

            var isRunning = false
            serviceList.forEach {
                if (it.service.className == VideoConferenceForegroundService::class.java.name) {
                    isRunning = true
                    return@forEach
                }
            }

            return isRunning
        }
    }


    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        stopForeground(true)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let {
            val conference = intent.getStringExtra(NOTIFICATION_CONFERENCE)
            val inviter = intent.getStringExtra(NOTIFICATION_INVITER)
            val conferenceType = intent.getIntExtra(NOTIFICATION_CONFERENCE_TYPE, 0)
            val groupId = intent.getStringExtra(NOTIFICATION_GROUPID)
            val conferenceId = intent.getStringExtra(NOTIFICATION_CONFERENCE_ID)
            val conferenceName = intent.getStringExtra(NOTIFICATION_CONFERENCE_NAME)
            startConferenceNotify(conferenceId, conferenceName, conference, inviter, conferenceType, groupId)
        }
        return START_STICKY
    }

    private fun startConferenceNotify(
            conferenceId: String?,
            conferenceName: String?,
            conference: String?,
            inviter: String?,
            conferenceType: Int,
            groupId: String?
    ) {
        val inviterInfo = Gson().fromJson(inviter, ConferenceUser::class.java)
        inviterInfo?.let {
            if (conferenceId != null && conference != null) {
                createConferenceNotify(conferenceId, conferenceName, conference, inviterInfo, conferenceType, groupId)
            }
        }
    }

    private fun createConferenceNotify(
            conferenceId: String,
            conferenceName: String?,
            conference: String,
            inviter: ConferenceUser,
            conferenceType: Int,
            groupId: String?
    ) {
        var inviterAvatar: String? = null
        var inviterName = inviter.pin
        var fileName = inviter.pin + inviter.appId
        val contactInfo = AppCache.getInstance().getContactInfo(inviter.pin, inviter.appId, false)
        contactInfo?.let {
            if (!TextUtils.isEmpty(it.avatar)) {
                inviterAvatar = it.avatar
            }
            if (!TextUtils.isEmpty(it.name)) {
                inviterName = it.name
            }
        }

        newConferenceNotify(
                conferenceId,
                conferenceName,
                conference,
                Gson().toJson(inviter),
                conferenceType,
                groupId,
                inviterName,
                null,
                false
        )

        if (!TextUtils.isEmpty(inviterAvatar) && !CoreCommonUtils.isGifImg(inviterAvatar)) {
            val url = ImageUtils.splitUrl(
                    inviterAvatar,
                    BitmapUtils.sScreenWidth,
                    BitmapUtils.sScreenHeight,
                    70
            )
            CoreThreadManager.excuteOnCachedThread {
                DownloadManager.getInstance().addTask(DownloadManager.TYPE_AVATAR,
                        conferenceId,
                        url,
                        fileName,
                        ServerTime.getServerTimestamp(),
                        object : BaseDownloadProgressListener() {
                            override fun onCancel(tag: Any?, attachmentBundle: Bundle?) {
                                //do nothing
                            }

                            override fun onComplete(
                                    tag: Any?,
                                    file: String?,
                                    attachmentBundle: Bundle?
                            ) {
                                newConferenceNotify(
                                        conferenceId,
                                        conferenceName,
                                        conference,
                                        Gson().toJson(inviter),
                                        conferenceType,
                                        groupId,
                                        inviterName,
                                        ImageUtils.loadBitmapFromPath(
                                                file,
                                                BitmapUtils.sScreenWidth,
                                                BitmapUtils.sScreenHeight
                                        ),
                                        true
                                )
                            }

                            override fun onFailure(
                                    tag: Any?,
                                    code: Int,
                                    err: String?,
                                    attachmentBundle: Bundle?
                            ) {
                                //do nothing
                            }

                            override fun onException(
                                    tag: Any?,
                                    e: Exception?,
                                    attachmentBundle: Bundle?
                            ) {
                                //do nothing
                            }
                        },
                        null
                )
            }
        }
    }

    private fun newConferenceNotify(
            conferenceId: String,
            conferenceName: String?,
            conference: String,
            inviter: String,
            conferenceType: Int,
            groupId: String?,
            name: String?,
            avatar: Bitmap?,
            updateNotify: Boolean
    ) {
        if (Thread.currentThread() == Looper.getMainLooper().thread) {
            newConferenceNotifyReal(
                    conferenceId,
                    conferenceName,
                    conference,
                    Gson().toJson(inviter),
                    conferenceType,
                    groupId,
                    name,
                    avatar,
                    updateNotify
            )
        } else {
            UiThreadUtils.runOnUiThread {
                newConferenceNotifyReal(
                        conferenceId,
                        conferenceName,
                        conference,
                        Gson().toJson(inviter),
                        conferenceType,
                        groupId,
                        name,
                        avatar,
                        updateNotify
                )
            }
        }
    }

    private fun newConferenceNotifyReal(
            conferenceId: String,
            conferenceName: String?,
            conference: String,
            inviter: String,
            conferenceType: Int,
            groupId: String?,
            name: String?,
            avatar: Bitmap?,
            updateNotify: Boolean
    ) {
        val entity = NotificationVideoConferenceEntity()
        entity.conference = conference
        entity.inviter = inviter
        entity.conferenceType = conferenceType
        entity.groupId = groupId
        val requestId = conferenceId.hashCode()
        val pendingIntent = sendNotificationActivity(this, VIDEO_CONFERENCE_CALL)
        val notification = createNotification(this, pendingIntent, conferenceName, conferenceType, name, avatar)
        notification?.let {
            notification.defaults = Notification.DEFAULT_LIGHTS
            notification.flags = Notification.FLAG_AUTO_CANCEL
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                notification.visibility = Notification.VISIBILITY_PUBLIC
            }
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN_MR2) {
                startForeground(requestId, Notification())
            } else {
                if (updateNotify) {
                    val notificationManager = this.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
                    notificationManager.notify(requestId, notification)
                } else {
                    startForeground(requestId, notification)
                }
            }
        }
    }

    private fun sendNotificationActivity(context: Context?, requestCode: Int): PendingIntent? {
        val intent = Intent()
        val appInfo = BaseCoreApplication.getAppInfo(context)
        val mainActivityActionName = appInfo.metaData.getString("mainActivityActionName")
        intent.action = mainActivityActionName
        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK) //进程不在top时
        return PendingIntent.getActivity(context, requestCode, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
    }

    private fun createNotification(context: Context, pendingIntent: PendingIntent?, conferenceName: String?,
                                   conferenceType: Int, name: String?, avatar: Bitmap?): Notification? {
        var builder: NotificationCompat.Builder? = null
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            var channel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_DEFAULT)
            channel.description = CHANNEL_DESCRIPTION
            val audioAttributes = AudioAttributes.Builder().setUsage(AudioAttributes.USAGE_NOTIFICATION).build()
            channel.setSound(Uri.parse("android.resource://" + context.getPackageName() + "/"
                    + R.raw.opim_timline_voip_ring), audioAttributes)
            val notificationManager = context.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            builder = NotificationCompat.Builder(context, CHANNEL_ID)
            builder.setChannelId(CHANNEL_ID)
        } else {
            builder = NotificationCompat.Builder(context, CHANNEL_ID)
        }

        builder.setSmallIcon(getNotifySmallIcon()).setAutoCancel(true).setWhen(ServerTime.getServerTimestamp())
                .setFullScreenIntent(pendingIntent, true)
                .setContentIntent(pendingIntent).setSound(Uri.parse("android.resource://" + context.packageName + "/" + R.raw.opim_timline_voip_ring)).priority =
                NotificationCompat.PRIORITY_MAX
        if (MyInfo.mConfig.msgNotifyVibrate) {
            builder.setVibrate(longArrayOf(200, 300))
        } else {
            builder.setVibrate(null)
            builder.setVibrate(longArrayOf(0))
        }

        val view = RemoteViews(context.packageName, R.layout.opim_notify_voip)
        var resId = GlobalConfigServiceManager.defaultPersonIcon()
        if (resId == -1) {
            resId = R.drawable.opim_default_person_icon
        }

        var res = R.drawable.jdme_app_launcher
        if (res <= 0) {
            res = R.mipmap.opim_notify_icon_trans
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                res = R.mipmap.opim_notify_icon
            }
        }
        view.setImageViewResource(R.id.opim_voip_icon, res)

        if (null == avatar) {
            view.setImageViewResource(R.id.opim_inviter_avatar, resId)
        } else {
            view.setImageViewBitmap(R.id.opim_inviter_avatar, avatar)
        }
        view.setTextViewText(R.id.opim_inviter_name, name)

        if (TextUtils.isEmpty(conferenceName)) {
            if (0 == conferenceType) {
                view.setTextViewText(
                        R.id.opim_voip_tip,
                        context.getString(R.string.meeting_notify_videoconference_single)
                )
            } else {
                view.setTextViewText(
                        R.id.opim_voip_tip,
                        context.getString(R.string.meeting_notify_videoconference_group)
                )
            }

        } else {
            view.setTextViewText(
                    R.id.opim_voip_tip,
                    conferenceName
            )
        }
        builder.setContent(view).setCustomContentView(view).setCustomContentView(view)
        return builder.build()
    }

    private fun getNotifySmallIcon(): Int {
        var res = R.drawable.jdme_app_launcher
        if (res <= 0) {
            res = R.mipmap.opim_notify_icon_trans
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                res = R.mipmap.opim_notify_icon
            }
        }
        return res
    }
}