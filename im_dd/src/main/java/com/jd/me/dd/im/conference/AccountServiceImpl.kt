package com.jd.me.dd.im.conference

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.alibaba.android.arouter.facade.annotation.Route
import com.jd.me.dd.im.conference.model.JMConferenceUser
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.jdmeeting.JDMeetingManager
import com.jd.oa.preference.PreferenceManager
import com.jingdong.conference.account.AccountService
import com.jingdong.conference.account.model.LocalUser
import com.jingdong.conference.core.extension.updateValue
import com.jingdong.conference.integrate.ServiceHub
import com.jingdong.conference.integrate.ServiceHubLazy

@Route(path = "/im_dd/account")
class AccountServiceImpl : AccountService, ServiceHub by ServiceHubLazy {
    private val _user = MutableLiveData<LocalUser?>(JMConferenceUser)
    override val user: LiveData<LocalUser?>
        get() = _user

    override fun init(context: Context?) {
    }

    override fun signIn() {
        JMConferenceUser.avatar = PreferenceManager.UserInfo.getUserCover()
        JMConferenceUser.pin = JMConferenceUser.getUserPin()
        JMConferenceUser.teamId = JMConferenceUser.getUserTeamId()
        JMConferenceUser.appId = PreferenceManager.UserInfo.getTimlineAppID()
        JMConferenceUser.nickname = PreferenceManager.UserInfo.getUserRealName()
        MELogUtil.localI("AccountServiceImpl", "JMConferenceUser.pin = ${JMConferenceUser.pin}, nickName = ${PreferenceManager.UserInfo.getUserRealName()}")

//        AppCache.getInstance().getContactInfo(PreferenceManager.UserInfo.getUserName(), PreferenceManager.UserInfo.getTimlineAppID(), false).also {
//            JMConferenceUser.avatar = it?.avatar
//            JMConferenceUser.nickname = it?.mShowName
//            JMConferenceUser.pin = PreferenceManager.UserInfo.getUserName().toLowerCase(Locale.getDefault())
//            JMConferenceUser.appId = PreferenceManager.UserInfo.getTimlineAppID()
//            JMConferenceUser.teamId = "0"
//        }
        _user.updateValue()
    }

    override fun signOut() {
        JMConferenceUser.clear()
        jdmtmeService?.signOut()
        JDMeetingManager.instance.releaseInit()
    }
}