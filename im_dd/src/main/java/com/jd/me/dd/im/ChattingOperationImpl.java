package com.jd.me.dd.im;

import static com.jd.me.dd.im.ImDdServiceImpl.checkFileExpiredInternal;
import static com.jd.oa.BaseActivity.REQUEST_NET_DISK;
import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;
import static com.jd.oa.router.DeepLink.DYNAMIC_CONTAINER_FRG;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatEntity;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatLongClickEntity;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatMsgLongClickBackData;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatMsgLongClickData;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatMsgLongClickEntry;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatPlusBackData;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatPlusEntity;
import com.jd.cdyjy.common.base.ui.custom.chat.ChatPlusItemRequest;
import com.jd.cdyjy.common.base.ui.custom.chat.ConversationAttributes;
import com.jd.cdyjy.common.base.ui.custom.chat.IMChatOperation;
import com.jd.cdyjy.common.base.ui.custom.chat.ToolEntry;
import com.jd.cdyjy.jimui.UiCommonInterface;
import com.jd.me.activitystarter.ActivityStarter;
import com.jd.me.activitystarter.ResultCallback;
import com.jd.me.activitystarter.ResultParser;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.cloudprint.PrintableFilesKt;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.MessageRecord;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.JoyWorkService;
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.dialog.BottomSheetActionDialog;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.URLUtils;
import com.jd.oa.utils.Utils2App;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import jd.cdyjy.jimcore.business.chat.MessageTypes;
import jd.cdyjy.jimcore.tcp.protocol.common.chatMessage.TcpChatMessageBase;

public class ChattingOperationImpl extends IMChatOperation {
    private static final String TAG = "ChattingOperationImpl";

    public static final int ID_VOTE = 1000;
    public static final int ID_TASK = 1001;
    public static final int ID_SCHEDULE = 1002;
    public static final int ID_JD_NETDISK = 1003;
    public static final int ID_PRINT = 1004;
    public static final int ID_JOYSPACE = 1005;
    public static final int ID_MANUAL = 1006;
    public static final int ID_CHECK_IN = 1007;

    private static final String NO_SHARE_PERMISSION = "1101704";

    private static final String LIANG_YAN_APP_ID = "202204221251"; // 202204221251 是良研的应用id

    @Override
    public HashMap<ToolEntry, ChatPlusEntity> addPlusItem(ChatPlusItemRequest request) {
        Activity activity = AppBase.getTopActivity();
        HashMap<ToolEntry, ChatPlusEntity> map = new HashMap<>();
        if (activity == null) {
            return map;
        }

        //添加云文档
        if (TenantConfigBiz.INSTANCE.isJoySpaceEnable()) {
            ChatPlusEntity joyspaceEntity = new ChatPlusEntity();
            joyspaceEntity.setTitle(activity.getString(R.string.me_im_dd_chat_bottom_joyspace));
            joyspaceEntity.setDefaultIcon(R.drawable.jdme_im_chatting_bottom_joyspace_selector);
            joyspaceEntity.setId(ID_JOYSPACE);
            map.put(ToolEntry.JOYSPACE, joyspaceEntity);
        }

        if (request.getConversationAttributes().isGroup() && Objects.equals(ABTestManager.getInstance().getConfigByKey("android.info.collect.poll.enable", "0"), "1")) {
            ChatPlusEntity voteEntity = new ChatPlusEntity();
            voteEntity.setId(ID_VOTE);
            voteEntity.setTitle(activity.getString(R.string.me_im_dd_chat_bottom_vote));
            voteEntity.setDefaultIcon(R.drawable.jdme_im_chatting_bottom_vote_selector);
            map.put(ToolEntry.VOTE, voteEntity);
        }

        if (request.getConversationAttributes().isGroup() && Objects.equals(ABTestManager.getInstance().getConfigByKey("android.info.collect.signin.enable", "0"), "1")) {
            ChatPlusEntity voteEntity = new ChatPlusEntity();
            voteEntity.setId(ID_CHECK_IN);
            voteEntity.setTitle(activity.getString(R.string.me_im_dd_chat_bottom_sign_in));
            voteEntity.setDefaultIcon(R.drawable.jdme_im_chatting_bottom_sign_in_selector);
            map.put(ToolEntry.CHECK_IN, voteEntity);
        }

        if (TenantConfigBiz.INSTANCE.isJoyWorkEnable()) {
            ChatPlusEntity taskEntity = new ChatPlusEntity();
            taskEntity.setTitle(activity.getString(R.string.me_im_dd_chat_bottom_task));
            taskEntity.setDefaultIcon(R.drawable.jdme_im_chat_plus_task_selector);
            taskEntity.setId(ID_TASK);
            map.put(ToolEntry.TASK, taskEntity);
        }

        if (TenantConfigBiz.INSTANCE.isJoyDayEnable()) {
            ChatPlusEntity scheduleEntity = new ChatPlusEntity();
            scheduleEntity.setTitle(activity.getString(R.string.me_im_dd_chat_bottom_schedule));
            scheduleEntity.setDefaultIcon(R.drawable.jdme_im_chatting_bottom_schedule_selector);
            scheduleEntity.setId(ID_SCHEDULE);
            map.put(ToolEntry.SCHEDULE, scheduleEntity);
        }

        if (TenantConfigBiz.INSTANCE.isManualShareEnable()) {
            boolean hasManual;
            ConversationAttributes ca = request.getConversationAttributes();
            if (!ca.isGroup()) {//单聊
                String appId = ca.getApp();//对方appId，去掉印尼泰国，文件助手不会返回appId
                hasManual = TextUtils.equals(appId, PreferenceManager.UserInfo.getTimlineAppID());
            } else {
                hasManual = true;
            }
            if (hasManual) {
                ChatPlusEntity expEntity = new ChatPlusEntity();
                expEntity.setTitle(activity.getString(R.string.me_im_dd_chat_bottom_manual));
                expEntity.setDefaultIcon(R.drawable.jdme_im_chat_plus_manual_selector);
                expEntity.setId(ID_MANUAL);
                map.put(ToolEntry.MANUAL, expEntity);
            }
        }

        return map;
    }

    public boolean enableLiangYanH5() {
        // 默认是1=enable, mobile.im.liangyan.h5.enable因各种原因失效后，默认是H5良研
        String enable = ABTestManager.getInstance().getConfigByKey("mobile.im.liangyan.h5.enable", "1");
        return "1".equals(enable);
    }

    @Override
    public void onClick(ChatPlusBackData data) {
        super.onClick(data);
        Activity activity = AppBase.getTopActivity();
        if (data == null || activity == null) {
            return;
        }
        // 模型化
        MessageRecord messageRecord = null;
        try {
            messageRecord = new Gson().fromJson(data.shareJson, MessageRecord.class);
        } catch (JsonSyntaxException e) {
            MELogUtil.localE(TAG, e.getMessage());
        }

        switch (data.id) {
            case ID_CHECK_IN: {
                try {
                    // 数据保护
                    if (messageRecord == null) {
                        MELogUtil.localE(TAG, "messageRecord data is null");
                        return;
                    }
                    // 签到
                    if (enableLiangYanH5()) {
                        String checkInUrl = LocalConfigHelper.getInstance(activity).getUrlConstantsModel().getCheckInUrl();

                        JSONObject param = new JSONObject();
                        String language = LocaleUtils.getUserSetLocaleStr();
                        param.put("lang", language);

                        JSONObject bizParam = new JSONObject();
                        bizParam.put("sessionType", messageRecord.getSessionType());
                        bizParam.put("gid", messageRecord.getSessionId());
                        param.put("bizparam", bizParam.toString());
                        String finalUrl = URLUtils.appendParametersToURL(checkInUrl, param, false);

                        Router.build(DeepLink.webApp(LIANG_YAN_APP_ID, finalUrl, 1, 0)).go(activity);
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("halfScreenDisplay", "1");
                        jsonObject.put("pageName", "templateInfoCollectInsert");
                        jsonObject.put("title", activity.getString(R.string.me_im_dd_chat_bottom_sign_in));

                        JSONObject optionsObj = new JSONObject();
                        optionsObj.put("bizType", "IM");
                        optionsObj.put("bizId", messageRecord.getSessionId());
                        jsonObject.put("options", optionsObj);
                        Uri.Builder builder = Uri.parse(DYNAMIC_CONTAINER_FRG).buildUpon();
                        builder.appendQueryParameter("mparam", jsonObject.toString());
                        Router.build(builder.build()).go(activity);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            }
            case ID_VOTE: {
                JDMAUtils.clickEvent(JDMAConstants.mobile_timline_add_vote_click, JDMAConstants.mobile_timline_add_vote_click, null);
                try {
                    // 数据保护
                    if (messageRecord == null) {
                        MELogUtil.localE(TAG, "messageRecord data is null");
                        return;
                    }
                    // 投票
                    if (enableLiangYanH5()) {
                        String voteUrl = LocalConfigHelper.getInstance(activity).getUrlConstantsModel().getVoteUrl();

                        JSONObject param = new JSONObject();
                        String language = LocaleUtils.getUserSetLocaleStr();
                        param.put("lang", language);

                        JSONObject bizParam = new JSONObject();
                        bizParam.put("sessionType", messageRecord.getSessionType());
                        bizParam.put("gid", messageRecord.getSessionId());
                        param.put("bizparam", bizParam.toString());
                        String finalUrl = URLUtils.appendParametersToURL(voteUrl, param, false);

                        Router.build(DeepLink.webApp(LIANG_YAN_APP_ID, finalUrl, 1, 0)).go(activity);
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("pageName", GatewayNetEnvironment.getCurrentEnv().isPre() ? "template47nTDT7P" : "templateInfoCollectFeedback");
                        jsonObject.put("title", activity.getString(R.string.me_im_dd_chat_bottom_vote));

                        JSONObject optionsObj = new JSONObject();
                        optionsObj.put("bizType", "IM");
                        optionsObj.put("bizId", messageRecord.getSessionId());
                        jsonObject.put("options", optionsObj);
                        Uri.Builder builder = Uri.parse(DYNAMIC_CONTAINER_FRG).buildUpon();
                        builder.appendQueryParameter("mparam", jsonObject.toString());
                        Router.build(builder.build()).go(activity);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            }
            case ID_TASK: { // 聊天界面加号
//                PageEventUtil.onEvent(activity, PageEventUtil.EVENT_TASK_ADD_MSG_CREATE);
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_add_task_click, JDMAConstants.mobile_timline_add_task_click);
                MEChattingLabel labels = MEChattingLabel.getFromIM();
                if (labels != null && labels.hasJoyWorkLabel()) {
                    AppJoint.service(JoyWorkService.class).goCompleteCreate(activity, null, null, null, true, labels);
                } else {
                    AppJoint.service(JoyWorkService.class).goShortcutCreate(activity, null, data.shareJson, null, true);
                }
                AppJoint.service(JoyWorkService.class).jdmaClick(JoyWorkService.TYPE_EX);
//                JoyWorkMediator.Companion.goCreate(activity, null, data.shareJson);
//                Intent intent = Router.build(RouterConstant.TASK_DETAIL).getIntent(activity);
//                intent.putExtra("extra_message_record", messageRecord);
//                intent.putExtra("extra_from_im_plus", true);
//                intent.putExtra("extra_session_id", messageRecord.getSessionId());
//                if (messageRecord.getContent() != null) {
//                    intent.putExtra("extra_content", messageRecord.getContent().getContent());
//                }
//                activity.startActivity(intent);
            }
            break;
            case ID_SCHEDULE: {
                openCalendarDialog(activity, data, messageRecord);
            }
            break;
            case ID_JOYSPACE:
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_add_joyspace_click, JDMAConstants.mobile_timline_add_joyspace_click);
                onJoySpace(messageRecord.getSessionId(), messageRecord.getTo(), messageRecord.getToApp(), messageRecord.getSessionType());
                break;
            case ID_MANUAL:
                Map<String, String> param = new HashMap<>();
                param.put("origin", "chat");
                param.put("info", data.shareJson);
                Router.build(DeepLink.EXP_MANUAL + "?mparam=" + Uri.encode(new Gson().toJson(param))).go(activity, new RouteNotFoundCallback(activity));
            default:
                Log.e("666666", data.id + "");
                break;
        }
    }

    private void openCalendarDialog(final Activity context, final ChatPlusBackData data, final MessageRecord messageRecord) {
        List<String> list = Arrays.asList(
                context.getString(R.string.me_im_share_schedule),
                context.getString(R.string.me_im_create_schedule),
                context.getString(R.string.me_im_view_calendar)
        );
        final BottomSheetActionDialog dialog = new BottomSheetActionDialog(context, list);
        dialog.setOnActionClickListener(new BottomSheetActionDialog.OnActionClickListener() {
            @Override
            public void onActionClick(int position) {
                if (position == 0) {
                    selectAppointment(context, messageRecord);
                }
                if (position == 1) {
                    // 点击加号创建日程
                    JDMAUtils.onEventClick(JDMAConstants.mobile_timline_add_schedule_click, JDMAConstants.mobile_timline_add_schedule_click);
                    Bundle bundle = new Bundle();
                    bundle.putString("routeTag", "create");
                    bundle.putString("msg", data.shareJson);
                    bundle.putString("from", "timlineChat");
                    Router.build(CALENDER_SCHEDULE).with(bundle).go(context);
                } else if (position == 2) {
                    try {
                        JSONObject object = new JSONObject(data.shareJson);
                        int sessionType = object.getInt("sessionType");
                        if (sessionType == 0) {
                            //单聊
                            JSONObject sender = object.getJSONObject("sender");
                            String app = sender.getString("app");
                            String pin = sender.getString("uid");
                            String name = sender.getString("nickName");
                            JDMEImpl.openUserCalendar(app, pin, name);
                        } else if (sessionType == 1) {
                            //群聊
                            String sessionId = object.getString("sessionId");
                            JDMEImpl.openGroupCalendar(sessionId);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                dialog.cancel();
            }
        });
        dialog.setOnCancelClickListener(new BottomSheetActionDialog.OnCancelClickListener() {
            @Override
            public void onCancelClick() {
                dialog.cancel();
            }
        });
        dialog.show();
    }

    private void selectAppointment(Activity activity, final MessageRecord messageRecord) {
        Uri.Builder builder = Uri.parse(DeepLink.CALENDAR_SCHEDULE_SELECT)
                .buildUpon();
        //builder.appendQueryParameter("mparam", params);
        Intent intent = Router.build(builder.build()).getIntent(AppBase.getAppContext());
        ActivityStarter.<ScheduleModel>from((FragmentActivity) activity)
                .setIntent(intent)
                .setResultParser(new ResultParser<ScheduleModel>() {
                    @Override
                    public ScheduleModel parseResult(int resultCode, @Nullable Intent data) {
                        if (resultCode == Activity.RESULT_OK && data != null) {
                            Bundle bundle = data.getExtras();
                            if (bundle.containsKey("schedule")) {
                                String schedule = data.getExtras().getString("schedule");
                                ScheduleModel scheduleModel = new Gson().fromJson(schedule, ScheduleModel.class);
                                return scheduleModel;
                            }
                        }
                        return null;
                    }
                })
                .start(new ResultCallback<ScheduleModel>() {
                    @Override
                    public void onResult(ScheduleModel result) {
                        Log.d(TAG, "onResult: " + result);
                        if (result == null) return;
                        String gid = null;
                        String ddAppId = null;
                        String account = null;
                        if (messageRecord.getSessionType() == 0) {
                            ddAppId = messageRecord.getToApp();
                            account = messageRecord.getTo();
                        } else {
                            gid = messageRecord.getSessionId();
                        }
                        sendAppointmentDynamicCard(activity, result, gid, ddAppId, account);
                    }
                });
    }

    private void sendAppointmentDynamicCard(Activity activity, ScheduleModel scheduleModel, String gid, String ddAppId, String account) {
        Map<String, Object> params = new HashMap<>(8);
        params.put("scheduleId", scheduleModel.getScheduleId());
        params.put("originalStart", scheduleModel.getOriginalStart());
        params.put("relatedDate", scheduleModel.getRelatedDate());
        if (gid != null) {
            params.put("groupIds", Collections.singletonList(gid));
        }
        if (!TextUtils.isEmpty(ddAppId) && !TextUtils.isEmpty(account)) {
            Map<String, String> user = new HashMap<>(4);
            user.put("ddAppId", ddAppId);
            user.put("account", account);

            params.put("ddUsers", Collections.singletonList(user));
        }
        HttpManager.color().post(params,null, NetworkConstant.API_SEND_DYNAMIC_CARD, new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (!response.isSuccessful() && NO_SHARE_PERMISSION.equals(response.getErrorCode())) {
                    Toast.makeText(activity, R.string.me_im_no_share_permission, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    @Override
    public boolean handleMsg(RecyclerView.ViewHolder holder, ChatEntity object, int postion, int totalCount) {
        return super.handleMsg(holder, object, postion, totalCount);
    }

    @Override
    public HashMap<ChatMsgLongClickEntry, ChatLongClickEntity> addLongClickItem(ChatMsgLongClickData data) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return null;
        }

        HashMap<ChatMsgLongClickEntry, ChatLongClickEntity> hashMap = new HashMap<>();
        switch (TcpChatMessageBase.getType(data.type)) {
            case MessageTypes.TEXT:
                joyWorkConfig(hashMap, activity);
                if (TenantConfigBiz.INSTANCE.isJoyDayEnable()) {
                    ChatMsgLongClickEntry remind = ChatMsgLongClickEntry.REMIND;
                    ChatLongClickEntity remindEntity = new ChatLongClickEntity();
                    remindEntity.setId(ID_SCHEDULE);
                    remindEntity.setIcon(R.drawable.jdme_im_chatmsg_op_meeting);
                    remindEntity.setTitle(activity.getString(R.string.me_im_dd_chat_long_click_calendar));
                    hashMap.put(remind, remindEntity);
                }
                break;
            case MessageTypes.FILE:
                if (!TextUtils.isEmpty(data.appendixData)) {
                    UiCommonInterface.AppendixData appendixData = new Gson().fromJson(data.appendixData, UiCommonInterface.AppendixData.class);
                    if (appendixData == null) {
                        return hashMap;
                    }
                    boolean canProcess = !TextUtils.isEmpty(appendixData.url);
                    boolean msgSent = data.isMsgSent;
                    if (canProcess && TenantConfigBiz.INSTANCE.isNetDiskEnable()) {
                        ChatLongClickEntity netdiskEntity = new ChatLongClickEntity();
                        netdiskEntity.setId(ID_JD_NETDISK);
                        netdiskEntity.setIcon(R.drawable.jdme_im_chatmsg_op_medrive);
                        netdiskEntity.setTitle(activity.getString(R.string.me_im_dd_chat_op_save_netdisk));
                        hashMap.put(ChatMsgLongClickEntry.SAVE_TO_JD_CLOUD, netdiskEntity);
                    }
                    if (canProcess && msgSent && !TextUtils.isEmpty(appendixData.url) && jdCloudControl(appendixData.ext) && TenantConfigBiz.INSTANCE.isCloudPrintEnable()) {
                        ChatLongClickEntity printEntity = new ChatLongClickEntity();
                        printEntity.setId(ID_PRINT);
                        printEntity.setIcon(R.drawable.jdme_im_chatmsg_op_print);
                        printEntity.setTitle(activity.getString(R.string.me_im_dd_chat_op_print));
                        hashMap.put(ChatMsgLongClickEntry.JD_CLOUD_PRINT, printEntity);
                    }
                }
                break;
            case MessageTypes.IMAGE:
                if (!TextUtils.isEmpty(data.appendixData)) {
                    UiCommonInterface.AppendixData appendixData = new Gson().fromJson(data.appendixData, UiCommonInterface.AppendixData.class);
                    if (appendixData == null) {
                        return hashMap;
                    }
                    boolean canProcess = !TextUtils.isEmpty(appendixData.url);
                    boolean msgSent = data.isMsgSent;
                    if (canProcess && msgSent && !TextUtils.isEmpty(appendixData.url) && jdCloudControl(appendixData.ext) && TenantConfigBiz.INSTANCE.isCloudPrintEnable()) {
                        ChatLongClickEntity printEntity = new ChatLongClickEntity();
                        printEntity.setId(ID_PRINT);
                        printEntity.setIcon(R.drawable.jdme_im_chatmsg_op_print);
                        printEntity.setTitle(activity.getString(R.string.me_im_dd_chat_op_print));
                        hashMap.put(ChatMsgLongClickEntry.JD_CLOUD_PRINT, printEntity);
                    }
                }
                break;
            case MessageTypes.TEMPLATE_CARD_FORWARD:
            case MessageTypes.TEMPLATE_DYNAMIC:
                joyWorkConfig(hashMap, activity);
        }
        return hashMap;
    }

    private void joyWorkConfig(
            HashMap<ChatMsgLongClickEntry, ChatLongClickEntity> hashMap, Activity activity) {
        if (TenantConfigBiz.INSTANCE.isJoyWorkEnable()) {
            ChatMsgLongClickEntry task = ChatMsgLongClickEntry.TASK;
            ChatLongClickEntity taskEntity = new ChatLongClickEntity();
            taskEntity.setId(ID_TASK);
            taskEntity.setIcon(R.drawable.jdme_im_chatmsg_op_task);
            taskEntity.setTitle(activity.getString(R.string.me_im_dd_chat_bottom_task));
            hashMap.put(task, taskEntity);
        }
    }

    @Override
    public void onItemClick(ChatMsgLongClickBackData data) {
        super.onItemClick(data);
        switch (data.id) {
            case ID_SCHEDULE:
                String from = data.fromSmart ? "timlineChatSmartMessage" : "timlineChatMessage";
                AppJoint.service(AppService.class).onOpenNewSchedule(data.shareString, from);
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_longClick_schedule_click, JDMAConstants.mobile_timline_longClick_schedule_click);
                break;
            case ID_TASK:
                AppJoint.service(AppService.class).onOpenNewTask(data.shareString);
                break;
            case ID_PRINT:
                if (!TextUtils.isEmpty(data.appendixString)) {
                    UiCommonInterface.AppendixData appendixData = new Gson().fromJson(data.appendixString, UiCommonInterface.AppendixData.class);
                    if (appendixData != null) {
                        onJDCloudPrint(appendixData.url, appendixData.name, appendixData.size, appendixData.ext);
                    }
                }
                break;
            case ID_JD_NETDISK:
                if (!TextUtils.isEmpty(data.appendixString)) {
                    UiCommonInterface.AppendixData appendixData = new Gson().fromJson(data.appendixString, UiCommonInterface.AppendixData.class);
                    if (appendixData != null) {
                        onFileSave2JDBox(appendixData.url, appendixData.name, appendixData.size, appendixData.ext);
                    }
                }
                break;
        }
    }

    private void onJDCloudPrint(final String url, final String fileName, final long size, String ext) {
        if (!TextUtils.isEmpty(ext) && !ext.startsWith(".")) {
            ext = "." + ext;
        }
        //检查文件是否过期
        final String finalExt = ext;
        checkFileExpiredInternal(url, new LoadDataCallback<Boolean>() {
            @Override
            public void onDataLoaded(Boolean expired) {
                if (AppBase.getTopActivity() == null) {
                    return;
                }
                if (!expired) {
                    AppJoint.service(AppService.class).onJDCloudPrint(url, fileName, size, finalExt);
                } else {
                    ToastUtils.showToast(R.string.me_print_file_expired);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                ToastUtils.showToast(R.string.me_print_access_file_failed);
            }
        });
    }

    private boolean jdCloudControl(String type) {
        if (TextUtils.isEmpty(type)) {
            return false;
        }
        if (!type.startsWith(".")) {
            type = "." + type;
        }
        // 大小写脱敏文件类型
        type = type.toLowerCase();
        return PrintableFilesKt.getPrintableFiles().contains(type);
    }

    private void onFileSave2JDBox(final String filePath, final String fileName, final long size, final String ext) {
        NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(Utils2App.getApp()) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                Map<String, String> map = response.getData();
                ToNetDiskBean bean = new ToNetDiskBean();
                bean.setFileName(fileName);
                bean.setFileUrl(filePath);
                bean.setFileSize(String.valueOf(size));
                bean.setFileSuffix(ext);
                bean.setToken(map.get("third_token"));
                bean.setUserCode(map.get("third_name"));
                bean.setThirdTimestamp(map.get("third_timestamp"));
                bean.setUserName(MyPlatform.getCurrentUser().getUserName());
                AppJoint.service(AppService.class).saveFileToNetDisk(bean, REQUEST_NET_DISK);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                Toast.makeText(Utils2App.getApp(), R.string.me_save_to_netdisk_fail, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void onJoySpace(String sessionKey, String pin, String app, int sessionType) {
//        PageEventUtil.onEvent(AppBase.getTopActivity(), PageEventUtil.EVENT_JOYSPACE_ADD_MSG_CREATE);
        JSONObject param = new JSONObject();
        try {
            param.put("sessionKey", sessionKey);
            param.put("pin", pin);
            param.put("app", app);
            param.put("sessionType", sessionType);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        //预发
//        Router.build("jdme://web/201906270537?browser=1&url="
//                + Uri.encode("https://joyspace-pre.jd.com/jdme/share?session=" + param.toString())).go(AppBase.getTopActivity());

        if (NetEnvironmentConfigModel.PREV.equalsIgnoreCase(PreferenceManager.UserInfo.getNetEnvironment())) {
            //预发
            Router.build(DeepLink.webApp("201906270537", "https://joyspace-pre2.jd.com/jdme/share?session=" + param.toString(), 0, 1)
            ).go(AppBase.getTopActivity());
        } else {
            //正式
            Router.build(DeepLink.webApp("201905050466", "https://joyspace.jd.com/jdme/share?session=" + param.toString(), 0, 1)
            ).go(AppBase.getTopActivity());
        }
    }
}
