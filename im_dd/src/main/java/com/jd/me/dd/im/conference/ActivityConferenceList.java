package com.jd.me.dd.im.conference;

import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.jd.me.dd.im.R;
import com.jd.oa.BaseActivity;
import com.jd.oa.utils.ActionBarHelper;

import voip.ui.FragmentConference;

/**
 * Created by cdtangkan on 2017/10/16.
 */
public class ActivityConferenceList extends BaseActivity implements View.OnClickListener {

    // TODO: 2023/8/23 FragmentConference 通过咚咚接口获取
    FragmentConference mFragmentConferenceList;

    private View mVideoConferenceFL;
    private TextView mVideoConferenceTitle;
    private View mVideoLine;

    private View mVoiceConferenceFL;
    private TextView mVoiceConferenceTitle;
    private View mVoiceLine;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setHideNetWorkByIn(true);
        setContentView(R.layout.meeting_activity_voip_conference_list);
        initToolBar();
        switchFragment(FragmentVideoConference.class.getName());
    }

    private void initToolBar() {
        ActionBarHelper.initActionBar(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
//        actionBar.setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);
        actionBar.setDisplayShowCustomEnabled(true);
        actionBar.setCustomView(R.layout.meeting_activity_conference_list_title);
        View mToolbar = actionBar.getCustomView();


//        mToolbar.setNavigationIcon(R.drawable.jdme_icon_back_black);
//        Drawable backDrawable = mToolbar.getNavigationIcon();
//        mToolbar.addFullLayout(R.layout.meeting_activity_conference_list_title);
        ImageView back = mToolbar.findViewById(R.id.back_icon);
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        mVideoConferenceFL = mToolbar.findViewById(R.id.video_conference_fl);
        mVideoConferenceTitle = mToolbar.findViewById(R.id.video_conference);
        mVideoConferenceTitle.setText(R.string.meeting_video_conference_title);
        mVideoLine = mToolbar.findViewById(R.id.video_line);
        mVideoConferenceFL.setOnClickListener(this);

        mVoiceConferenceFL = mToolbar.findViewById(R.id.voice_conference_fl);
        mVoiceConferenceTitle = mToolbar.findViewById(R.id.voice_conference);
        mVoiceConferenceTitle.setText(R.string.meeting_voice_conference_title);
        mVoiceLine = mToolbar.findViewById(R.id.voice_line);
        mVoiceConferenceFL.setOnClickListener(this);

    }


    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            onBackPressed();
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (null != mFragmentConferenceList) {
            mFragmentConferenceList.destroy();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public Resources getResources() {
        Resources res = super.getResources();
        Configuration conf = res.getConfiguration();
        conf.fontScale = 1;
        res.updateConfiguration(conf, res.getDisplayMetrics());
        return res;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.voice_conference_fl) {
            //语音会议
            switchFragment(FragmentConference.class.getName());
        } else if (id == R.id.video_conference_fl) {
            //视频会议
            switchFragment(FragmentVideoConference.class.getName());
        }
    }


    private Fragment mCurrentFragment;

    private void switchFragment(String fragmentName) {
        if (TextUtils.equals(fragmentName, FragmentVideoConference.class.getName())) {
            mVideoConferenceTitle.setTextColor(Color.parseColor("#262626"));
            mVideoConferenceTitle.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            mVoiceConferenceTitle.setTextColor(Color.parseColor("#666666"));
            mVoiceConferenceTitle.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            mVoiceLine.setVisibility(View.GONE);
        } else {
            mVideoLine.setVisibility(View.GONE);
            mVoiceLine.setVisibility(View.VISIBLE);
            mVoiceConferenceTitle.setTextColor(Color.parseColor("#262626"));
            mVoiceConferenceTitle.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            mVideoConferenceTitle.setTextColor(Color.parseColor("#666666"));
            mVideoConferenceTitle.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }

        FragmentManager manager = getSupportFragmentManager();
        FragmentTransaction transaction = manager.beginTransaction();
        Fragment fragment = manager.findFragmentByTag(fragmentName);

        if (mCurrentFragment == null) {
            if (fragment == null) {
                fragment = Fragment.instantiate(this, fragmentName);
                transaction.add(R.id.conference_list_frame, fragment, fragmentName);
            } else {
                transaction.show(fragment);
            }

            transaction.commitNowAllowingStateLoss();
            mCurrentFragment = fragment;
        } else {
            if (fragment == null) {
                //隐藏当前Fragment
                if (!mCurrentFragment.isHidden()) {
                    transaction.hide(mCurrentFragment);
                }

                fragment = Fragment.instantiate(this, fragmentName);
                transaction.add(R.id.conference_list_frame, fragment, fragmentName);
                transaction.commitNowAllowingStateLoss();
                mCurrentFragment = fragment;
            } else if (mCurrentFragment != fragment) {
                //隐藏当前Fragment
                if (!mCurrentFragment.isHidden()) {
                    transaction.hide(mCurrentFragment);
                }
                transaction.show(fragment);
                transaction.commitNowAllowingStateLoss();
                mCurrentFragment = fragment;
            }
        }
    }
}
