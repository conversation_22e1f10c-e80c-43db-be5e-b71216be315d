package com.jd.me.dd.im;

import androidx.annotation.Keep;

@SuppressWarnings({"unused", "WeakerAccess"})
@Keep
public class TimlineToken {
    private String loginToken;
    private String nonce;

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getLoginToken() {
        return loginToken;
    }

    public void setLoginToken(String loginToken) {
        this.loginToken = loginToken;
    }


}
