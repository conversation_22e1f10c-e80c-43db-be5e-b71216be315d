package com.jd.me.dd.im.noticeviewholder;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.cdyjy.common.base.ui.custom.notice.NoticeEntity;
import com.jd.cdyjy.common.base.ui.custom.notice.OnNoticeItemListener;
import com.jd.cdyjy.jimui.ui.util.DisplayUtils;
import com.jd.cdyjy.jimui.ui.widget.ExpandableTextView;
import com.jd.cdyjy.jimui.ui.widget.NoticeImageView;
import com.jd.me.dd.im.R;
import com.jd.oa.joymeeting.JoyMeetingUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.JPushService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONException;
import org.json.JSONObject;

import jd.cdyjy.jimcore.commoninterface.utils.UnifiedNoticeUtils;
import jd.cdyjy.jimcore.core.utils.DateTimeUtils;

import static com.jd.oa.utils.Utils.compatibleDeepLink;

/**
 * JoyMeeting会议
 * Created by peidongbiao on 2019/4/16
 */
public class JoyMeetingViewHolder extends TimeLineNoticeViewHolder {
    //TODO appid硬编码
    public static final String JOY_MEETING_APP_ID = "201803290218";
    //    private static final int STATUS_OK = 200;
//    private static final int STATUS_OVER = 504;
    private Context context;
    private TextView time;
    private TextView title;
    private NoticeImageView noticePic;
    private ExpandableTextView content;
    private View action;
    private TextView reject;
    private TextView accept;
    private TextView join;
    private LinearLayout extend;
    private View lastGap;

    private NoticeEntity mNoticeEntity;
    private OnNoticeItemListener mItemListener;

    public static JoyMeetingViewHolder create(Context context, ViewGroup parent) {
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_item_joy_meeting_notice, parent, false);
        return new JoyMeetingViewHolder(view);
    }

    private JoyMeetingViewHolder(View itemView) {
        super(itemView);
        context = itemView.getContext();
        time = itemView.findViewById(R.id.notice_time);
        title = itemView.findViewById(R.id.notice_title);
        noticePic = itemView.findViewById(R.id.notice_pic);
        content = itemView.findViewById(R.id.notice_content);
        reject = itemView.findViewById(R.id.tv_reject);
        accept = itemView.findViewById(R.id.tv_accept);
        extend = itemView.findViewById(R.id.notice_extend);
        lastGap = itemView.findViewById(R.id.notice_last_gap);
        action = itemView.findViewById(R.id.layout_action);
        join = itemView.findViewById(R.id.tv_join);
        final JPushService jPushService = AppJoint.service(JPushService.class);

        reject.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNoticeEntity == null || getAdapterPosition() == RecyclerView.NO_POSITION)
                    return;
                try {
                    //拒绝会议
                    MeetingNotify notify = getMeetingNotify(mNoticeEntity);
                    String prompt;
                    if (JoyMeetingUtils.TYPE_IMMEDIATE.equals(notify.type) || JoyMeetingUtils.TYPE_SCHEDULED_REMAIN.equals(notify.type)) {
                        //即时会议、预约会议
                        prompt = context.getString(R.string.me_joy_meeting_reject_prompt);
                    } else {
                        //预约会议
                        prompt = context.getString(R.string.me_joy_meeting_reject_schedule_prompt);
                    }
                    updateMeetingStatus(notify.meetingId, notify.type, JoyMeetingUtils.STATUS_REJECT, prompt, null);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        accept.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNoticeEntity == null || getAdapterPosition() == RecyclerView.NO_POSITION)
                    return;
                try {
                    final MeetingNotify notify = getMeetingNotify(mNoticeEntity);
                    if (JoyMeetingUtils.TYPE_IMMEDIATE.equals(notify.type) || JoyMeetingUtils.TYPE_SCHEDULED_REMAIN.equals(notify.type)) {
                        //即时会议、预约会议。立即加入会议
                        updateMeetingStatus(notify.meetingId, notify.type, JoyMeetingUtils.STATUS_ACCEPT, null, new OnSuccessListener() {
                            @Override
                            public void onSuccess() {
                                jPushService.gainTokenAndGoPlugin(notify.deepLink, "" + notify.appId);
                            }
                        });
                    } else {
                        //预约会议。接受会议
                        updateMeetingStatus(notify.meetingId, notify.type, JoyMeetingUtils.STATUS_ACCEPT, context.getString(R.string.me_joy_meeting_accept_schedule_prompt), null);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        join.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MeetingNotify notify;
                try {
                    notify = getMeetingNotify(mNoticeEntity);
                    jPushService.gainTokenAndGoPlugin(notify.deepLink, "" + notify.appId);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        final GestureDetector gestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public void onLongPress(MotionEvent e) {
                if (mNoticeEntity == null || mItemListener == null) return;
                mItemListener.onItemLongClicked(e, mNoticeEntity);
            }
        });
        itemView.setOnTouchListener(new View.OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                gestureDetector.onTouchEvent(event);
                return true;
            }
        });
    }

    @Override
    public void bindItemView(RecyclerView.ViewHolder holder, NoticeEntity noticeEntity, int position, int totalCount, OnNoticeItemListener itemListener) {
        mNoticeEntity = noticeEntity;
        mItemListener = itemListener;
        time.setText(DateTimeUtils.formatChatMsgTimeShow(context, noticeEntity.time));
        title.setText(noticeEntity.title);
        // copy from timline
        // 内容区域的宽度=屏幕宽度-外层各个view的padding及margin。,不直接取view的width的
        // 原因是因为外层recyclerView在scrollToPosition的时候，需要知道item的高，
        // 而我们要取view的with却需要延迟才能取到，有冲突，所以需要手动设置这个宽度
        int contentWidth = DisplayUtils.getScreenWidth() - DisplayUtils.dip2px(12) - DisplayUtils.dip2px(12)
                - DisplayUtils.dip2px(24) - DisplayUtils.dip2px(10) - DisplayUtils.dip2px(10);
        content.setWidthForStaticLayout(contentWidth);
        content.setCloseText(noticeEntity.content);
        if (!TextUtils.isEmpty(noticeEntity.extend)) {
            String picUrl = UnifiedNoticeUtils.parsePic(noticeEntity.extend);
            if (!TextUtils.isEmpty(picUrl)) {
                noticePic.setVisibility(View.VISIBLE);
                noticePic.loadImage(picUrl);
            } else {
                noticePic.setVisibility(View.GONE);
            }
        } else {
            noticePic.setVisibility(View.GONE);
        }
        lastGap.setVisibility(position == totalCount - 1 ? View.VISIBLE : View.GONE);

        try {
            MeetingNotify notify = getMeetingNotify(mNoticeEntity);
            if (JoyMeetingUtils.TYPE_IMMEDIATE.equals(notify.type) || JoyMeetingUtils.TYPE_SCHEDULED_REMAIN.equals(notify.type)) {
                //即时会议；预约会议前一分钟
                extend.setVisibility(View.VISIBLE);
                action.setVisibility(View.VISIBLE);
                join.setVisibility(View.GONE);
                reject.setText(R.string.me_joy_meeting_decline);
                accept.setText(R.string.me_joy_meeting_join_meeting);
            } else if (JoyMeetingUtils.TYPE_SCHEDULED.equals(notify.type)) {
                //预约会议
                extend.setVisibility(View.VISIBLE);
                action.setVisibility(View.VISIBLE);
                join.setVisibility(View.GONE);
                reject.setText(R.string.me_joy_meeting_reject);
                accept.setText(R.string.me_joy_meeting_accept);
            } else if (JoyMeetingUtils.TYPE_PERIODIC.equals(notify.type)) {
                //周期会议
                extend.setVisibility(View.VISIBLE);
                action.setVisibility(View.GONE);
                join.setVisibility(View.VISIBLE);
            } else if (JoyMeetingUtils.TYPE_CANCEL.equals(notify.type)) {
                //取消会议通知
                extend.setVisibility(View.GONE);
            } else {
                extend.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新会议状态
     */
    private void updateMeetingStatus(String meetingId, String type, String status, final String successPrompt, final OnSuccessListener listener) {
        JoyMeetingUtils.updateMeetingStatus(context, JoyMeetingViewHolder.JOY_MEETING_APP_ID, meetingId, type, status, new LoadDataCallback<Object>() {
            @Override
            public void onDataLoaded(Object data) {
                try {
                    JSONObject object = new JSONObject(data.toString());
                    int code = object.optInt("code");
                    if (code == 200) {
                        if (listener != null) {
                            listener.onSuccess();
                        } else {
                            ToastUtils.showToast(successPrompt);
                        }
                    } else if (code == 504) {
                        //会议结束或不存在
                        ToastUtils.showToast(R.string.me_joy_meeting_meeting_finished);
                    } else {
                        String msg = object.optString("msg");
                        ToastUtils.showToast(msg);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                ToastUtils.showToast(context.getString(R.string.me_access_server_failed));
            }
        });
    }

    private MeetingNotify getMeetingNotify(NoticeEntity entity) throws Exception {
        JSONObject jsonObject = new JSONObject(entity.infox);
        String deepLink = compatibleDeepLink(jsonObject);
        if (TextUtils.isEmpty(deepLink)) throw new IllegalArgumentException("deepLink is null");
        Uri uri = Uri.parse(deepLink);
        String id = uri.getQueryParameter("id");
        String type = uri.getQueryParameter("meetingType");
        String appId = jsonObject.optString("appId");
        return new MeetingNotify(deepLink, id, type, appId);
    }

    @SuppressWarnings("unused")
    private static class MeetingNotify {
        private String deepLink;
        private String meetingId;
        private String type;
        private String appId;

        MeetingNotify(String deepLink, String meetingId, String type, String appId) {
            this.deepLink = deepLink;
            this.meetingId = meetingId;
            this.type = type;
            this.appId = appId;
        }

        public String getDeepLink() {
            return deepLink;
        }

        public void setDeepLink(String deepLink) {
            this.deepLink = deepLink;
        }

        public String getMeetingId() {
            return meetingId;
        }

        public void setMeetingId(String meetingId) {
            this.meetingId = meetingId;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }
    }

    private interface OnSuccessListener {
        void onSuccess();
    }
}