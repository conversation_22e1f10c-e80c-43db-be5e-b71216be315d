package com.jd.me.dd.im.view;

import androidx.fragment.app.FragmentActivity;

import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.Keep;

import com.jd.oa.mask.MaskDecoration;

/**
 * create by huf<PERSON> on 2020-02-17
 */
@Keep
public class CalendarMaskHelper implements MaskDecoration {

    @Override
    public void decoration(float left, float top, float right, float bottom, FragmentActivity activity, FrameLayout parent) {
//        View view = LayoutInflater.from(activity).inflate(R.layout.jdme_im_calendar_mask, parent);
//        DottedLine topLine = view.findViewById(R.id.top_line);
//        if (topLine != null) {
//            handleDefault(left, top, right, bottom, activity, view);
//        } else {
//            handleEN(left, top, right, bottom, activity, view);
//        }
//        view.findViewById(R.id.mask_know).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                PreferenceManager.UserInfo.setCalendarMaskHadShow(true);
//                if (sOnClickListener != null) {
//                    sOnClickListener.onClick(v);
//                }
//            }
//        });
    }

    private void handleEN(float left, final float top, final float right, final float bottom, final FragmentActivity activity, View view) {
//        final ArcDottedLine line = view.findViewById(R.id.arc_line);
//        final ImageView info = view.findViewById(R.id.info);
//        info.post(new Runnable() {
//            @Override
//            public void run() {
//                int w = info.getDrawable().getIntrinsicWidth();
//                PointF start = new PointF(info.getRight() - w / 8, info.getBottom() - CommonUtils.dp2px(16));
//                PointF end = new PointF(right, top - CommonUtils.dp2px(5));
//                PointF c = new PointF(info.getRight(), (end.y + start.y) / 2);
//                line.setPoints(start, end, c, (float) Math.toDegrees(Math.atan((c.y - end.y) / (c.x - end.x))), 30);
//            }
//        });
    }

    private void handleDefault(float left, float top, float right, float bottom, FragmentActivity activity, View view) {
//        ImageView title = view.findViewById(R.id.title);
//        int drawableWidth = title.getDrawable().getIntrinsicWidth();
//        int titleLeft = ScreenUtil.getScreenWidth(activity) / 2 + drawableWidth / 2;
//        // 两条水平虚线的最左边
//        int leftPosition = (int) Math.max(titleLeft + CommonUtils.dp2px(40), right + CommonUtils.dp2px(50));
//
//        DottedLine topLine = view.findViewById(R.id.top_line);
//        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) topLine.getLayoutParams();
//        lp.width = leftPosition - CommonUtils.dp2px(10) - titleLeft;
//        topLine.setLayoutParams(lp);
//
//        DottedLine bottomLine = view.findViewById(R.id.bottom_line);
//        bottomLine.setDirAndStart(DottedLine.DottedLineDir.HOR, DottedLine.StartShape.TRI);
//        lp = (ConstraintLayout.LayoutParams) bottomLine.getLayoutParams();
//        lp.leftMargin = (int) (right + CommonUtils.dp2px(activity, 10));
//        lp.bottomMargin = (int) ((bottom - top) / 2);
//        lp.width = (int) (leftPosition - right - CommonUtils.dp2px(10));
//        bottomLine.setLayoutParams(lp);
//
//        DottedLine vLine = view.findViewById(R.id.v_line);
//        vLine.setDirAndStart(DottedLine.DottedLineDir.VER, DottedLine.StartShape.TRI);
    }

    @Override
    public void clickMask() {

    }

    @Override
    public void onBackPressed() {

    }

    private static View.OnClickListener sOnClickListener;

    public static void setKnowListener(View.OnClickListener listener) {
        sOnClickListener = listener;
    }
}
