package com.jd.me.dd.im.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.util.AttributeSet;
import android.view.View;

import com.jd.oa.utils.CommonUtils;

/**
 * create by huf<PERSON> on 2019-09-17
 */
public class DottedLine extends View {
    enum DottedLineDir {
        HOR, VER
    }

    enum StartShape {
        CIRCLE, TRI
    }

    private Paint mPaint;
    private Paint mFillPaint;
    private static final int GAP = 3;
    private static final int LENGTH = 5;

    private DottedLineDir mDir;
    private StartShape mStartShape;
    private Path mTriPath, mLinePath;

    public DottedLine(Context context) {
        super(context);
        init();
    }

    public DottedLine(Context context, @androidx.annotation.Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public DottedLine(Context context, @androidx.annotation.Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        mPaint = new Paint();
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);//画线条，线条有宽度
        mPaint.setAntiAlias(true);
        mPaint.setStrokeWidth(2);
        // 设置虚线
        PathEffect effects = new DashPathEffect(new float[]{CommonUtils.dp2px(getContext(), LENGTH), CommonUtils.dp2px(getContext(), GAP)}, 1);
        mPaint.setPathEffect(effects);

        mFillPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mFillPaint.setColor(Color.WHITE);
        mFillPaint.setStyle(Paint.Style.FILL);

        mTriPath = new Path();
        mLinePath = new Path();

        mStartShape = StartShape.CIRCLE;
        mDir = DottedLineDir.HOR;
    }

    public DottedLineDir getDir() {
        return mDir;
    }

    public void setDirAndStart(DottedLineDir dir, StartShape startShape) {
        mDir = dir;
        mStartShape = startShape;
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mDir == DottedLineDir.HOR) {
            mLinePath.reset();
            mLinePath.moveTo(0, getHeight() / 2);
            mLinePath.lineTo(getWidth(), getHeight() / 2);
            canvas.drawPath(mLinePath, mPaint);
            if (mStartShape == StartShape.CIRCLE) {
                canvas.drawCircle(getHeight() / 2, getHeight() / 2, getHeight() / 2, mFillPaint);
            } else {
                mTriPath.reset();
                mTriPath.moveTo(0, getHeight() / 2);
                mTriPath.lineTo(getHeight(), 0);
                mTriPath.lineTo(getHeight(), getHeight());
                mTriPath.close();
                canvas.drawPath(mTriPath, mFillPaint);
            }
        } else {
            mLinePath.reset();
            mLinePath.moveTo(getWidth() / 2, 0);
            mLinePath.lineTo(getWidth() / 2, getHeight());
            canvas.drawPath(mLinePath, mPaint);
        }
    }
}
