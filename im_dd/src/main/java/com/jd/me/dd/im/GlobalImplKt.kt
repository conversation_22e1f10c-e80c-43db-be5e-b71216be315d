package com.jd.me.dd.im

import com.google.gson.reflect.TypeToken
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.encrypt.DesUtil

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/17 15:52
 */
class GlobalImplKt {

    fun isSourceValid(source: String?, secret: String?): Boolean {
        if (source.isNullOrEmpty() || secret.isNullOrEmpty()) return false
        return runCatching {
            val config = ConfigurationManager.get().getEntry("restricted.api.white.list", "[]")
            val sources: List<String?> =
                JsonUtils.getGson().fromJson(config, object : TypeToken<List<String?>?>() {}.type)
            val contains = sources.any {
                source.equals(it, true)
            }
            if (!contains) {
                false
            } else {
                val encrypt = DesUtil.ecbEncryptToHexString("com.jdme.im.sendmsgcard", source)
                secret.equals(encrypt, true)
            }
        }.getOrNull() ?: false
    }
}