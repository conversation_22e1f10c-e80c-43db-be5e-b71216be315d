package com.jd.me.dd.im.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;


/**
 * 咚咚的打开和关闭都会发送广播
 * 但是咚咚内部的activity跳转也发送广播，这个需要后期优化
 *
 * <AUTHOR>
 */
public class TimlineOnOffReceiver extends BroadcastReceiver {
    public static final String TIMLINE_ON = "com.jd.oa.timLine.RESUME";
    public static final String TIMLINE_OFF = "com.jd.oa.timLine.PAUSE";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (action.equals(TIMLINE_ON)) {
            //MyPlatform.checkRunBackgroundTime();
//            PageEventUtil.onResume(context);
//            PageEventUtil.onEventPageBegin(context, PageEventUtil.PAGE_TIMLINE);
        } else if (intent.getAction().equals(TIMLINE_OFF)) {
            //MyPlatform.startVerify();
//            PageEventUtil.onPause(context);
//            PageEventUtil.onEventPageEnd(context, PageEventUtil.PAGE_TIMLINE);
        }
    }
}