package com.jd.me.dd.im;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.cdyjy.common.base.ui.custom.conversation_list.ConversationEntity;
import com.jd.cdyjy.common.base.ui.custom.conversation_list.IMConversationUi;

import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.Utils2App;

/**
 * 接入方实现
 */
public class ConversationUiImpl extends IMConversationUi {

    private AppService appService = AppJoint.service(AppService.class);

    /**
     * 设置会话列表title
     */
    @Override
    public String setTitle() {
        return Utils2App.getApp().getString(R.string.me_message);
    }

    @Override
    public RecyclerView.ViewHolder getBannerView(Context context, View convertView, ViewGroup parent) {
        return appService.getBannerView(context, convertView, parent);
    }

    @Override
    public boolean handleMsg(RecyclerView.ViewHolder holder, ConversationEntity object, int postion, int totalCount) {
        return appService.handleMsg(holder, object, postion, totalCount);
    }
}
