package com.jd.me.dd.im.view

import android.app.Dialog
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import android.widget.FrameLayout
import androidx.lifecycle.lifecycleScope
import com.chenenyu.router.Router
import com.jd.cdyjy.common.base.util.SharedPreferencesUtil
import com.jd.me.dd.im.R
import com.jd.me.dd.im.conference.ActivityConferenceList
import com.jd.me.dd.im.conference.ExternalServiceImpl
import com.jd.oa.AppBase
import com.jd.oa.JDMAConstants
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.TabletUtil
import com.jingdong.conference.conference.model.StartConferenceType
import com.jingdong.conference.core.extension.showToast
import com.jingdong.conference.core.widget.ConferenceBottomSheetDialogFragment
import com.jingdong.conference.integrate.ServiceHub
import com.jingdong.conference.integrate.ServiceHubLazy
import jd.cdyjy.jimcore.core.ipc_global.MyInfo
import jd.cdyjy.jimcore.http.reportLog.ReportLogUtil
import kotlinx.coroutines.launch


class StartConferenceGlobalFragment : ConferenceBottomSheetDialogFragment(), View.OnClickListener,
        ServiceHub by ServiceHubLazy {
    override fun onCreateContentView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.jdme_fragment_start_conference_global, container, false)
    }

    override fun getTheme(): Int = R.style.Theme_Conference_BottomSheetDialog

    private var mDialog: Dialog? = null

    private var mRootContent: ViewGroup? = null

    private var joinVideoConference: FrameLayout? = null
    private var createVideoConference: FrameLayout? = null
    private var scheduleConference: FrameLayout? = null
    private var conferenceList: Button? = null


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        mDialog = super.onCreateDialog(savedInstanceState)
        return mDialog!!
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        joinVideoConference = view.findViewById(R.id.joinVideoConference)
        createVideoConference = view.findViewById(R.id.createVideoConference)
        scheduleConference = view.findViewById(R.id.scheduleConference)
        conferenceList = view.findViewById(R.id.conferenceList)
        joinVideoConference?.setOnClickListener(this)
        createVideoConference?.setOnClickListener(this)
        scheduleConference?.setOnClickListener(this)
        conferenceList?.setOnClickListener(this)

        //一期待删
//        voiceConference.setOnClickListener(this)

        scheduleConference?.visibility = if (MyInfo.hasVideoConferenceReversePermission()) View.VISIBLE else View.GONE

        val decorView = mDialog!!.window!!.decorView
        mRootContent = decorView.findViewById(Window.ID_ANDROID_CONTENT)

        val key = StringBuilder(SharedPreferencesUtil.Key.VIDEO_CONFERENCE_ENTRY).append("_").append(MyInfo.myPin()).toString()
        if (!SharedPreferencesUtil.getBoolean(context, key, false)) {
//            gotoGuide()
            SharedPreferencesUtil.putBoolean(context, key, true)
        }
    }

    private fun gotoGuide() {
        val view = LayoutInflater.from(context).inflate(R.layout.opim_video_conferene_guide, mRootContent, false)
        view.setOnClickListener {
            mRootContent?.removeView(it)
        }
        mRootContent?.addView(view)
    }

    private fun gotoVideoConference(v: View?) {
        lifecycleScope.launchWhenResumed {
            //判断Voip
            when {
                !JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING) -> {
                    showToast(R.string.meeting_start_conference_conference_busy)
                }

                jdmtmeService?.busy == true -> {
                    showToast(R.string.meeting_start_conference_conference_busy)
                }

                else -> {
                    if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
                        return@launchWhenResumed
                    }
                    if (TabletUtil.isEasyGoAndFoldOrTablet()) {
                        val joinVideoConferenceId = joinVideoConference?.id
                        TabletUtil.startFullScreenActivity {
                            jdmtmeService?.startConference(
                                    activity,
                                    if (v?.id == joinVideoConferenceId) StartConferenceType.JOIN else StartConferenceType.CREATE,
                                    false,
                                    false,
                                    true,
                                    null,
                                    Bundle().apply {
                                        putSerializable(
                                                ExternalServiceImpl.EXTRA_SOURCE,
                                                ReportLogUtil.CreateConferenceType.GLOBAL
                                        )
                                    })
                        }
                    } else {
                        jdmtmeService?.startConference(
                                activity, if (v?.id == joinVideoConference?.id) StartConferenceType.JOIN else StartConferenceType.CREATE,
                                false,
                                false,
                                true,
                                null,
                                Bundle().apply {
                                    putSerializable(
                                            ExternalServiceImpl.EXTRA_SOURCE,
                                            ReportLogUtil.CreateConferenceType.GLOBAL
                                    )
                                })
                    }
                    dismiss()
                }
            }
        }
    }

    private fun createVideoConference() {
        lifecycleScope.launch {
            ServiceHubLazy.accountService?.signIn()
            //判断Voip
            when {
                !JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING) -> {
                    showToast(R.string.meeting_start_conference_conference_busy)
                }

                ServiceHubLazy.jdmtmeService?.busy == true -> {
                    showToast(R.string.meeting_start_conference_conference_busy)
                }

                else -> {
                    if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
                        return@launch
                    }
                    if (TabletUtil.isEasyGoAndFoldOrTablet()) {
                        TabletUtil.startFullScreenActivity {
                            ServiceHubLazy.jdmtmeService?.startConference(
                                    activity,
                                    StartConferenceType.CREATE,
                                    false,
                                    false,
                                    true,
                                    null,
                                    Bundle().apply {
                                        putSerializable(
                                                ExternalServiceImpl.EXTRA_SOURCE,
                                                ReportLogUtil.CreateConferenceType.LIST
                                        )
                                    })
                        }
                    } else {
                        ServiceHubLazy.jdmtmeService?.startConference(
                                activity,
                                StartConferenceType.CREATE,
                                false,
                                false,
                                true,
                                null,
                                Bundle().apply {
                                    putSerializable(
                                            ExternalServiceImpl.EXTRA_SOURCE,
                                            ReportLogUtil.CreateConferenceType.LIST
                                    )
                                })
                    }
                }
            }
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            joinVideoConference -> {
                if (Build.VERSION.SDK_INT >= 31) {
                    PermissionHelper.requestPermission(requireActivity(),
                            getString(R.string.me_request_permission_audio_normal),
                            object : RequestPermissionCallback {
                                override fun allGranted() {
                                    gotoVideoConference(v)
                                }

                                override fun denied(deniedList: MutableList<String>?) {
                                }
                            }, "android.permission.BLUETOOTH_CONNECT")
//                    PermissionUtil.requestPermission(mutableListOf<String>().apply {
//                        add("android.permission.BLUETOOTH_CONNECT")
//                    }, {
//                        if (it) {
//
//                        }
//                    }, getString(R.string.opim_func_video_meeting), null, false)
                } else {
                    gotoVideoConference(v)
                }
            }

            createVideoConference -> {
//                internalService?.initJRTC(AppBase.getAppContext())
                createVideoConference()
//                if (MyInfo.hideVoip()) {
//                } else {
//                    val fragment = StartConferenceGlobalSecondFragment()
//                    fragment.show(requireFragmentManager(), "startList")
//                }
                dismiss()
            }

            conferenceList -> {
                try {
                    val intent = Intent()
                    intent.setClass(requireContext(), ActivityConferenceList::class.java)
                    requireContext().startActivity(intent)
                    dismiss()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }


            scheduleConference -> {
                val activity = AppBase.getTopActivity() ?: return
                val bundle = Bundle()
                bundle.putString("routeTag", "create")
                bundle.putBoolean("timlineMeeting", true)
                bundle.putString("from", "timlineMeeting")
                Router.build(DeepLink.CALENDER_SCHEDULE).with(bundle).go(activity)
                JDMAUtils.onEventClick(JDMAConstants.timline_video_create_schedule, JDMAConstants.timline_video_create_schedule)
                dismiss()
            }

            //一期待删
//            voiceConference -> {
//                TimlineUIHelper.showConferenceStart(context)
//                dismiss()
//            }
        }
    }
}