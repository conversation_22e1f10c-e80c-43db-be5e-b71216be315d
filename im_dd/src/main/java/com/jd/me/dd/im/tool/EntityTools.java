package com.jd.me.dd.im.tool;

import android.text.TextUtils;

import com.jd.cdyjy.icsp.entity.MemberEntity;
import com.jd.cdyjy.icsp.entity.MemberListEntity;
import com.jd.cdyjy.icsp.entity.SelectorConfig;
import com.jd.cdyjy.icsp.selector.SelectorParam;
import com.jd.cdyjy.icsp.selector.data.DataConfig;
import com.jd.cdyjy.icsp.selector.ui.UIConfig;
import com.jd.cdyjy.icsp.selector.whiteList.WhiteListConfig;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;

import java.util.ArrayList;

import jd.cdyjy.jimcore.commoninterface.tab.TabEntity;

@SuppressWarnings({"unused", "WeakerAccess"})
public class EntityTools {
    public static TabEntityJd getTabEntityJd(TabEntity tabEntity) {
        if (tabEntity == null) {
            return null;
        }
        TabEntityJd tabEntityJd = new TabEntityJd();
        tabEntityJd.button = tabEntity.button;
        tabEntityJd.data = tabEntity.data;
        tabEntityJd.tips = tabEntity.tips;
        tabEntityJd.type = tabEntity.type;
        tabEntityJd.url = tabEntity.url;
        return tabEntityJd;
    }

    public static TabEntity getTabEntity(TabEntityJd tabEntityJd) {
        if (tabEntityJd == null) {
            return null;
        }
        TabEntity tabEntity = new TabEntity();
        tabEntity.button = tabEntityJd.button;
        tabEntity.data = tabEntityJd.data;
        tabEntity.tips = tabEntityJd.tips;
        tabEntity.type = tabEntityJd.type;
        tabEntity.url = tabEntityJd.url;
        return tabEntity;
    }

    public static MemberEntityJd getMemberEntityJd(MemberEntity memberEntity) {
        if (memberEntity == null) {
            return null;
        }
        MemberEntityJd memberEntityJd = new MemberEntityJd();
        memberEntityJd.mApp = memberEntity.mApp;
        memberEntityJd.mAvatar = memberEntity.mAvatar;
        memberEntityJd.mId = memberEntity.mId;
        memberEntityJd.mIdentity = memberEntity.mIdentity;
        memberEntityJd.mIsFriend = memberEntity.mIsFriend;
        memberEntityJd.mIsGroup = memberEntity.mIsGroup;
        memberEntityJd.mName = memberEntity.mName;
        memberEntityJd.mType = memberEntity.mType;
        if (MemberListEntityJd.EXTERNAL_CONTACT_EMAIL.equals(memberEntity.mApp)) {
            memberEntityJd.setExternal(true);
            memberEntityJd.setExternalType(MemberEntityJd.EXTERNAL_TYPE_EMAIL);
            memberEntityJd.setEmail(memberEntity.mId);
            memberEntityJd.setName(memberEntity.mId);
        } else if (MemberListEntityJd.EXTERNAL_CONTACT_PHONE.equals(memberEntity.mApp)) {
            memberEntityJd.setExternal(true);
            memberEntityJd.setExternalType(MemberEntityJd.EXTERNAL_TYPE_PHONE);
            memberEntityJd.setPhone(memberEntity.mId);
            memberEntityJd.setName(memberEntity.mId);
        } else {
            memberEntityJd.setExternal(false);
            memberEntityJd.setExternalType(MemberEntityJd.EXTERNAL_TYPE_NONE);
        }
        return memberEntityJd;
    }

    public static MemberEntity getMemberEntity(MemberEntityJd memberEntityJd) {
        if (memberEntityJd == null) {
            return null;
        }
        MemberEntity memberEntity = new MemberEntity();
        memberEntity.mApp = memberEntityJd.mApp;
        memberEntity.mAvatar = memberEntityJd.mAvatar;
        memberEntity.mId = memberEntityJd.mId;
        memberEntity.mIdentity = memberEntityJd.mIdentity;
        memberEntity.mIsFriend = memberEntityJd.mIsFriend;
        memberEntity.mIsGroup = memberEntityJd.mIsGroup;
        memberEntity.mName = memberEntityJd.mName;
        memberEntity.mType = memberEntityJd.mType;
        return memberEntity;
    }

    public static MemberListEntityJd getMemberListEntityJd(MemberListEntity memberListEntity) {
        if (memberListEntity == null) {
            return null;
        }
        MemberListEntityJd memberListEntityJd = new MemberListEntityJd();
        memberListEntityJd.mFrom = memberListEntity.from();
        memberListEntityJd.mShowSelf = memberListEntity.showSelf();
        memberListEntityJd.mShowConstantFilter = memberListEntity.showConstantFilter();
        memberListEntityJd.mShowOptionalFilter = memberListEntity.showOptionalFilter();
        memberListEntityJd.mGidFilter = memberListEntity.groupFilter();
        ArrayList<MemberEntityJd> mConstantFilter = new ArrayList<>();
        if (memberListEntity.constantFilter() != null) {
            for (MemberEntity memberEntity : memberListEntity.constantFilter()) {
                mConstantFilter.add(getMemberEntityJd(memberEntity));
            }
        }
        memberListEntityJd.mConstantFilter = mConstantFilter;
        ArrayList<MemberEntityJd> mOptionalFilter = new ArrayList<>();
        if (memberListEntity.optionalFilter() != null) {
            for (MemberEntity memberEntity : memberListEntity.optionalFilter()) {
                mOptionalFilter.add(getMemberEntityJd(memberEntity));
            }
        }
        memberListEntityJd.mOptionalFilter = mOptionalFilter;
        return memberListEntityJd;
    }

    public static MemberListEntity getMemberListEntity(MemberListEntityJd memberListEntityJd) {
        if (memberListEntityJd == null) {
            return null;
        }
        MemberListEntity memberListEntity = new MemberListEntity();
        memberListEntity.setFrom(memberListEntityJd.mFrom);
        memberListEntity.setShowSelf(memberListEntityJd.mShowSelf);
        memberListEntity.setShowConstantFilter(memberListEntityJd.mShowConstantFilter);
        memberListEntity.setShowOptionalFilter(memberListEntityJd.mShowOptionalFilter);
        memberListEntity.setGroupFilter(memberListEntityJd.mGidFilter);
        for (String excludeAppId : memberListEntityJd.getExcludeAppIds()) {
            memberListEntity.addExcludeAppId(excludeAppId);
        }
        ArrayList<MemberEntity> mConstantFilter = new ArrayList<>();
        if (memberListEntityJd.mConstantFilter != null) {
            for (MemberEntityJd memberEntity : memberListEntityJd.mConstantFilter) {
                mConstantFilter.add(getMemberEntity(memberEntity));
            }
        }
        memberListEntity.setConstantFilter(mConstantFilter);
        ArrayList<MemberEntity> mOptionalFilter = new ArrayList<>();
        if (memberListEntityJd.mOptionalFilter != null) {
            for (MemberEntityJd memberEntity : memberListEntityJd.mOptionalFilter) {
                mOptionalFilter.add(getMemberEntity(memberEntity));
            }
        }
        memberListEntity.setOptionalFilter(mOptionalFilter);
        memberListEntity.setExternalDataEnable(memberListEntityJd.isExternalDataEnable());
        return memberListEntity;
    }

    public static SelectorParam getSelectParam(MemberListEntityJd memberListEntityJd) {
        WhiteListConfig whiteListConfig = new WhiteListConfig.Builder()
                .setAppIdWhiteList(memberListEntityJd.mSpecifyAppId)
                .build();
        SelectorParam.Builder builder = new SelectorParam.Builder()
                .setWhiteListConfig(whiteListConfig)
                .setSelectMode(memberListEntityJd.selectMode == MemberListEntityJd.SELECT_MODE_MULTI ?
                        SelectorConfig.SelectMode.SELECT_MODE_MULTI : SelectorConfig.SelectMode.SELECT_MODE_SINGLE)
                 // 处理留言、preview逻辑
                .setDataConfig(new DataConfig.Builder().setDialogDefaultMessage(memberListEntityJd.getLeaveMessage()).setDialogMessagePreview(memberListEntityJd.getPreviewContent()).setShowConfirmDialog(memberListEntityJd.sendDirectly).build());

        if (!TextUtils.isEmpty(memberListEntityJd.getTitle())) {
            builder.setUIConfig(new UIConfig.Builder()
                    .setSelectorTitle(memberListEntityJd.getTitle())
                    .build()
            );
        }

        return builder.build();
    }
}
