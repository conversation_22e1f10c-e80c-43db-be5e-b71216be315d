package com.jd.me.dd.im.conference.model

import android.os.Parcel
import android.os.Parcelable
import com.chibatching.kotpref.KotprefModel
import com.jd.oa.preference.PreferenceManager
import com.jingdong.conference.account.model.LocalUser
import com.jingdong.conference.account.model.User
import java.util.Locale

object JMConferenceUser : KotprefModel(), User, LocalUser {
    override var pin by nullableStringPref()
    override var appId by nullableStringPref()
    override var teamId by nullableStringPref()
    override var aid by nullableStringPref()


    override var nickname: String?
        get() = _nickname
        set(value) {
            _nickname = value
        }

    override var avatar: String?
        get() = _avatar
        set(value) {
            _avatar = value
        }

    internal var _nickname by nullableStringPref()
    internal var _avatar by nullableStringPref()

    override fun describeContents(): Int {
        return 0
    }

    override fun isSignedIn(): Boolean = !pin.isNullOrEmpty() && !appId.isNullOrEmpty() && !teamId.isNullOrEmpty() && PreferenceManager.UserInfo.getUserName().toLowerCase(Locale.getDefault()) == pin

    override fun writeToParcel(parcel: Parcel, flags: Int) {
    }

    @JvmField
    val CREATOR = object : Parcelable.Creator<JMConferenceUser> {
        override fun createFromParcel(parcel: Parcel): JMConferenceUser {
            return JMConferenceUser
        }

        override fun newArray(size: Int): Array<JMConferenceUser?> {
            return arrayOfNulls(size)
        }
    }
}