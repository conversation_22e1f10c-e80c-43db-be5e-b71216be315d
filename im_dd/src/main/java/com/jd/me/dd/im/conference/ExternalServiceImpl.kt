package com.jd.me.dd.im.conference

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.MutableLiveData
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.chenenyu.router.Router
import com.jd.cdyjy.common.base.util.SharedPreferencesUtil
import com.jd.cdyjy.icsp.cache.AppCache
import com.jd.cdyjy.icsp.entity.GroupMemberListEntity
import com.jd.cdyjy.icsp.entity.MemberEntity
import com.jd.cdyjy.icsp.entity.MemberListEntity
import com.jd.cdyjy.icsp.entity.SelectorConfig
import com.jd.cdyjy.icsp.permission.PermissionUtil
import com.jd.cdyjy.icsp.selector.SelectorParam
import com.jd.cdyjy.icsp.selector.SelectorParamHolder
import com.jd.cdyjy.icsp.selector.ui.UIConfig
import com.jd.cdyjy.icsp.selector.whiteList.WhiteListConfig
import com.jd.cdyjy.jimui.ui.OpimUiWrapper
import com.jd.cdyjy.jimui.ui.UIHelper
import com.jd.cdyjy.jimui.ui.activity.ActivityVideoPlayer
import com.jd.cdyjy.jimui.ui.selector.SelectorBottomUI.externalInterface.business.SelectorBusinessConferenceUI
import com.jd.me.dd.im.ImMeetingImpl
import com.jd.me.dd.im.R
import com.jd.oa.AppBase
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel
import com.jd.oa.jdmeeting.JDMeetingManager
import com.jd.oa.model.ShareMeetingBean
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink.CALENDER_SCHEDULE_V2_DETAIL
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.TabletUtil
import com.jdcloud.mt.me.modle.MeetingConfig
import com.jdcloud.mt.me.view.activity.MeetingSettingActivity
import com.jingdong.conference.account.model.User
import com.jingdong.conference.conference.model.*
import com.jingdong.conference.core.content.ConfigurationProvider
import com.jingdong.conference.core.content.PermissionProvider
import com.jingdong.conference.core.extension.hasSystemWindowPermission
import com.jingdong.conference.integrate.*
import jd.cdyjy.jimcore.appid.AppIdManager
import jd.cdyjy.jimcore.business.contact.ContactInfoUtils
import jd.cdyjy.jimcore.core.ipc_global.MyInfo
import jd.cdyjy.jimcore.core.tcp.TcpConstant
import jd.cdyjy.jimcore.core.utils.NetworkConstantEvn
import jd.cdyjy.jimcore.reportevent.DDReportorUsecase
import jd.cdyjy.jimcore.share.Protocols
import jd.cdyjy.jimcore.tcp.protocol.common.BaseMessage
import jd.cdyjy.jimcore.tcp.protocol.common.rosterMessage.down.TcpDownUserInfo
import jd.cdyjy.jimcore.tcp.protocol.common.rosterMessage.up.TcpUpGetUsersInfo
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.resume

@Route(path = "/im_dd/external")
class ExternalServiceImpl : ExternalService, ConfigurationProvider, PermissionProvider, ServiceHub by ServiceHubLazy {
    private lateinit var _context: Context
    var selectStart: Boolean = false
    val TAG: String = "ExternalServiceImpl"

    override var permissionRequest: Boolean = false

    override fun needMinimize(): Boolean {
        val need = !selectStart && _context.hasSystemWindowPermission() && !permissionRequest
        MELogUtil.localI(ImMeetingImpl.TAG, "call attendMeeting return:$need")
        return need
    }

    override fun needShowSettingTip(): Boolean {
        val show = SharedPreferencesUtil.getBoolean(_context, "${MyInfo.myPin()}#${SharedPreferencesUtil.Key.MEETING_SETTING_SHOW}", false)
        return !show
    }

    override suspend fun needUpdateUser(user: User): Boolean {
        var userInfo = AppCache.getInstance().getContactInfo(user.pin, user.appId, false)
        return userInfo == null || !TextUtils.equals(user.avatar, userInfo.avatar)
                || !TextUtils.equals(user.nickname, userInfo.name ?: userInfo.uid)
    }

    override fun isPad(): Boolean {
        return TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())
    }

    private val _analysis = object : Callback {
        override fun hideNoVideoClick(hide: Boolean) {
            kotlin.runCatching {
                DDReportorUsecase.reportClickEvent(
                        "ddmeeting",
                        if (hide) "bottomvideo-dehostquit" else "bottomvideo-unhidenonvideo",
                        null,
                        null,
                        null
                )
            }
        }

        override fun lockConferenceClick(lock: Boolean) {
            kotlin.runCatching {
                DDReportorUsecase.reportClickEvent(
                        "ddmeeting",
                        if (lock) "bottomvideo-lockmeeting" else "bottomvideo-unlockmeeting",
                        null,
                        null,
                        null
                )
            }
        }

        override fun onAddParticipantClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_addmember", conference, null)
//            }
        }

        override fun onAnswer(
                startTime: Long,
                duration: Long,
                conference: Conference,
                reason: Reason?,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportAnswerConferenceCall(startTime, duration, conference, reason)
//            }
        }

        override fun onAudioControlClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_hostmute", conference, null)
//            }
        }

        override fun onAudioOutputClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick(
//                        "click_video_speaker", conference, when (page) {
//                    Page.CONFERENCE -> "1"
//                    Page.CALL, Page.CALLED, Page.CREATE, Page.JOIN -> "2"
//                    else -> null
//                }
//                )
//            }
        }

        override fun onAudioToggleClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick(
//                        "click_video_mute", conference, when (page) {
//                    Page.CONFERENCE, Page.CONFERENCE_WINDOW -> {
//                        when (subPage) {
//                            SubPage.PARTICIPANTS -> "2"
//                            else -> "1"
//                        }
//                    }
//
//                    Page.CALL, Page.CALLED, Page.CREATE, Page.JOIN -> "3"
//                    else -> null
//                }
//                )
//            }
        }

        override fun onCallAllClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_callmember", conference, null)
//            }
        }

        override fun onCopyInfoClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_detail", conference, null)
//            }
        }

        override fun onCreateConference(
                startTime: Long,
                duration: Long,
                conference: Conference?,
                groupId: String?,
                extra: Bundle?,
                reason: Reason?,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportCreateConference(
//                        startTime,
//                        duration,
//                        ReportLogUtil.ReportConferenceconference,
//                        (extra?.getSerializable(EXTRA_SOURCE) as? ReportLogUtil.CreateConferenceType)
//                                ?: ReportLogUtil.CreateConferenceType.GLOBAL,
//                        groupId,
//                        reason
//                )
//            }
        }

        override fun onExitClick(conference: Conference?, page: Page, subPage: SubPage?, sn: Int) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_hostleave", conference, null)
//            }
        }

        override fun onMuteAllClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_fullmute", conference, null)
//            }
        }

        override fun onParticipantsClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_member", conference, null)
//            }
        }

        override fun onReject(
                startTime: Long,
                duration: Long,
                conference: Conference,
                reason: Reason?,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportRejectConferenceCall(startTime, duration, conference, reason)
//            }
        }

        override fun onShare(
                startTime: Long,
                duration: Long,
                conference: Conference,
                page: Page,
                targets: List<ShareTarget>?,
                reason: Reason?,
        ) {
//            kotlin.runCatching {
//                targets?.forEach {
//                    ReportLogUtil.reportShareConference(
//                            startTime,
//                            duration,
//                            conference,
//                            when (it) {
//                                is ReportLogUtil.MemberShareTarget -> it.pin
//                                is ReportLogUtil.GroupShareTarget -> it.groupId
//                                else -> null
//                            },
//                            when (it) {
//                                is ReportLogUtil.MemberShareTarget -> it.app
//                                else -> null
//                            },
//                            if (page == Page.CONFERENCE) ReportLogUtil.ShareConferenceType.CONFERENCE else ReportLogUtil.ShareConferenceType.DETAIL,
//                            reason
//                    )
//                }
//            }
        }

        override fun onVideoControlClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick("click_video_hostcamera", conference, null)
//            }
        }

        override fun onVideoToggleClick(
                conference: Conference?,
                page: Page,
                subPage: SubPage?,
                sn: Int,
        ) {
//            kotlin.runCatching {
//                ReportLogUtil.reportConferenceClick(
//                        "click_video_camera", conference, when (page) {
//                    Page.CONFERENCE -> {
//                        when (subPage) {
//                            SubPage.PARTICIPANTS -> "2"
//                            else -> "1"
//                        }
//                    }
//
//                    Page.CALL, Page.CALLED, Page.CREATE, Page.JOIN -> "3"
//                    else -> null
//                }
//                )
//            }
        }

        override fun setHostClick() {
            kotlin.runCatching {
                DDReportorUsecase.reportClickEvent(
                        "ddmeeting",
                        "participants-sethost",
                        null,
                        null,
                        null
                )
            }
        }

        override fun transferAndExistClick() {
            kotlin.runCatching {
                DDReportorUsecase.reportClickEvent(
                        "ddmeeting",
                        "bottomvideo-dehostquit",
                        null,
                        null,
                        null
                )
            }
        }
    }
    override val analysis: Callback?
        get() = _analysis

    /**
     * 添加参会人
     */
    override suspend fun chooseParticipants(
            groupId: String?,
            existUser: List<User>,
            page: Page,
            removable: Boolean,
    ): Pair<List<User>, Any?> = suspendCancellableCoroutine { cont ->
        var filter = ArrayList<MemberEntity>()
        existUser.forEach {
            filter.add(MemberEntity().apply {
                mId = it.pin
                mApp = it.appId
                mAvatar = it.avatar
                mName = it.nickname
            })
        }

        if (groupId.isNullOrEmpty() || page != Page.CREATE) {
            val entity = MemberListEntity().apply {
                setFrom(MemberListEntity.TYPE_ADD_MEMBER)
                if (!removable) {
                    setConstantFilter(filter)
                } else {
                    setOptionalFilter(filter)
                }
            }
            var conference = SelectorCallbackConference(cont)
            selectStart = true

            if(MultiAppConstant.isSaasFlavor()) {
                UIHelper.showMemberList(
                    _context, entity, SelectorParam.Builder()
                        .build(), conference
                )
            } else {
                UIHelper.showMemberList(
                    _context, entity, SelectorParam.Builder()
                        .setWhiteListConfig(
                            WhiteListConfig.Builder()
                                .setAppIdWhiteList(AppIdManager.APP_ID_FOR_CONFERENCE.toList()).build()
                        )
                        .build(), conference
                )
            }

        } else {
            val entity = GroupMemberListEntity().apply {
                if (!removable) {
                    setConstantFilter(filter)
                } else {
                    setOptionalFilter(filter)
                }

                setGid(groupId)
                setSelectAll(true)
                setFrom(GroupMemberListEntity.TYPE_GROUP_MEMBER_SELECT)
            }
            if(MultiAppConstant.isSaasFlavor()) {
                SelectorParamHolder.INSTANCE.putParam(this@ExternalServiceImpl::class.qualifiedName, SelectorParam.Builder()
                    .setUIConfig(
                        UIConfig.Builder().setContainerBusinessUI(SelectorBusinessConferenceUI(_context))
                            .build()
                    ).build())
            } else {
                SelectorParamHolder.INSTANCE.putParam(this@ExternalServiceImpl::class.qualifiedName, SelectorParam.Builder()
                    .setWhiteListConfig(
                        WhiteListConfig.Builder()
                            .setAppIdWhiteList(AppIdManager.APP_ID_FOR_CONFERENCE.toList()).build()
                    ).setUIConfig(
                        UIConfig.Builder().setContainerBusinessUI(SelectorBusinessConferenceUI(_context))
                            .build()
                    ).build())
            }

            var conference = SelectorCallbackConference(cont)
            selectStart = true
            UIHelper.showGroupMemberList(_context, entity, conference)
        }
    }

    override fun getCameraOpen() = MeetingConfig.cameraSetting

    override fun getJAIAppId() = "1"

    override fun getMicrophoneOpen() = MeetingConfig.microphoneSetting

    override fun getNotificationTitle(single: Boolean): String = if (single) _context.getString(R.string.meeting_chat_bottom_voip_single)
    else _context.getString(R.string.meeting_video_conference_title)

    private val _configuration = MutableLiveData<Configuration?>()
    override val configuration: MutableLiveData<Configuration?>
        get() = _configuration

    override fun init(context: Context) {
        _context = context
    }

    override fun initJDMeMeeting(sdkConfig:String?) {
        JDMeetingManager.instance.initJBMeetingInNeed(sdkConfig)
        MELogUtil.localI("JMExternalServiceImpl", "JMExternalServiceImpl: initJDMeMeeting")
    }

    override fun requestPermission(manager: FragmentManager, permissionList: List<String>, onGranted: () -> Unit, onDenied: () -> Unit) {
        MELogUtil.localI(TAG, "call requestPermission permissionList:".plus(JsonUtils.getJsonString(permissionList)).plus(" joinToString:${permissionList.joinToString(",")}"))
        val topActivity = AppBase.getTopActivity()
        if (topActivity != null) {
//            PermissionHelper.requestPermissions(topActivity as FragmentActivity,
//                    topActivity.getString(R.string.permission_title_default),
//                    topActivity.getString(R.string.me_request_permission_camera_video),
//                    object : RequestPermissionCallback {
//                        override fun allGranted() {
//                            onGranted.invoke()
//                        }
//
//                        override fun denied(deniedList: MutableList<String>?) {
//                            onDenied.invoke()
//                        }
//                    }, permissionList.joinToString(","))
            PermissionUtil.requestPermission(manager, permissionList, {
                if (it) onGranted.invoke() else onDenied.invoke()
            }, _context.getString(R.string.meeting_permission_conference_name), false)
        }
    }

    override fun isFirstCameraSetting() = !SharedPreferencesUtil.getBoolean(_context, "${MyInfo.myPin()}#${SharedPreferencesUtil.Key.MEETING_CAMERA_SET}", false)

    override fun isFirstMicrophoneSetting() = !SharedPreferencesUtil.getBoolean(_context, "${MyInfo.myPin()}#${SharedPreferencesUtil.Key.MEETING_MICRO_PHONE_SET}", false)
    override fun isPreEnv(): Boolean {
        val isPre = NetEnvironmentConfigModel.PREV == PreferenceManager.UserInfo.getNetEnvironment()
        MELogUtil.localI(TAG, "isPreEnv $isPre")
        return isPre
    }

    override suspend fun onCalledStop(
            conference: Conference,
            inviter: User,
            stopReason: StopReason,
    ) {
    }

    override fun onCloseTip() {
        val show = SharedPreferencesUtil.getBoolean(_context, "${MyInfo.myPin()}#${SharedPreferencesUtil.Key.MEETING_SETTING_SHOW}", false)
        if (!show) {
            SharedPreferencesUtil.putBoolean(_context, "${MyInfo.myPin()}#${SharedPreferencesUtil.Key.MEETING_SETTING_SHOW}", true)
        }
    }

    override suspend fun onCallStop(
            conference: Conference,
            waitUsers: List<User>,
            stopReason: StopReason,
    ) {
//        waitUsers.forEach {
//            var pin = String(it.identity.split(" @ ")[0].base64Decode())
//            var app = String(it.identity.split(" @ ")[2].base64Decode())
//
//            var parameter = MsgParameter.createVideoConferenceOpParameter(pin, app, 53, conference.id,
//                    conference.code, 0)
//            var factory = VideoConferenceOpMsgFactory()
//            var msg = factory.sendMsg(parameter) { msgId, mid, state ->
//                MsgEvent.eventUpdateMsgState(msgId, mid, state)
//            }
//
//            if (msg != null) {
//                MsgEvent.eventAddMsgToBottom(msg)
//            }
//        }
    }

    override suspend fun onShare(meeting: Conference, page: Page): List<ShareTarget> =
            suspendCancellableCoroutine {
                //向users中每个用户发送template消息
                val shareMeetingBean = ShareMeetingBean(
                        meeting.id,
                        meeting.code,
                        meeting.subject,
                        meeting.participantCount,
                        meeting.status,
                        meeting.password ?: "",
                        meeting.startTime?:"",
                        meeting.host?.pin,
                        meeting.host?.appId,
                        meeting.host?.teamId,
                        meeting.host?.displayName ?: meeting.remindTime//这里从会议详情页分享时用remindTime传递的主持人displayName
                )
                var shareConference = SelectorCallbackShareVideoConference(_context, shareMeetingBean, it)
                val entity = MemberListEntity()
                entity.setFrom(MemberListEntity.TYPE_SHARE)
                selectStart = true

                val param = SelectorParam.Builder()
                        .setSelectMode(SelectorConfig.SelectMode.SELECT_MODE_SINGLE)
                        .build()
                OpimUiWrapper.getInstance().gotoMemberList(AppBase.getTopActivity(), entity, param, shareConference)
            }

    @SuppressLint("SimpleDateFormat")
    override suspend fun onShowSchedule(conference: Conference) {
        conference.setting?.let {
//            JdmeServiceManager.onScheduleMeetingDetail(
//                    it.scheduleId,
//                    it.calendarId,
//                    conference.scheduleStartTime,
//                    conference.endTime
//
//            )

            val `object` = JSONObject()
            try {
                val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
                format.timeZone = TimeZone.getTimeZone("UTC")
                val start = format.parse(conference.scheduleStartTime.toString())
                val end = format.parse(conference.endTime.toString())
                `object`.put("calendarId", it.calendarId)
                        .put("scheduleId", it.scheduleId)
                        .put("beginTime", start?.time ?: "")
                        .put("endTime", end?.time ?: "")
                        .put("isNeedCheck", true)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            val deeplink = Uri.parse(CALENDER_SCHEDULE_V2_DETAIL).buildUpon()
                    .appendQueryParameter("mparam", `object`.toString())
                    .build()
            Router.build(deeplink).go(_context)
        }
    }

    /**
     * 当需要查看用户资料时回调
     */
    override suspend fun onShowUser(user: User, callback: (() -> Unit)?) {
        selectStart = true
        AppJoint.service(ImDdService::class.java)?.showContactDetailInfo(_context, user.appId, user.pin)
    }

    override fun playScreenRecord(url: String, duration: Long) {
        ActivityVideoPlayer.start(_context, url, duration)
    }

    override fun releaseDDMeetingAudioPermission(secret: String) {
        MELogUtil.localI(TAG, "releaseDDMeetingAudioPermission $secret")
        JMAudioCategoryManager.getInstance().releaseAudio(secret)
    }

    override suspend fun setAudioDefaultOpen(){
       MeetingConfig.microphoneSetting = true
    }

    override fun setDDMeetingAudioPermission(): String? {
        return JMAudioCategoryManager.getInstance().setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_MEETING).secret
    }

    override suspend fun setVideoDefaultOpen(){
       MeetingConfig.cameraSetting = true
    }

     override suspend fun showMeetingSetting(): MeetingSetting? = suspendCancellableCoroutine {
        val intent = Intent(_context, MeetingSettingActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        MeetingSettingActivity.cont = it
        _context.startActivity(intent)
        onCloseTip()
    }

    override suspend fun updateUserInfo(user: User) {
        withTimeoutOrNull(NetworkConstantEvn.TCP_PROTOCOL_REQUEST_TIMEOUT.toLong()) {
            suspendCancellableCoroutine<Unit> { cont ->
                val erp = user.pin
                val appId = user.appId
                if (erp.isNullOrBlank() || appId.isNullOrBlank()) {
                    cont.cancel()
                    return@suspendCancellableCoroutine
                }
                var userInfo = AppCache.getInstance().getContactInfo(erp, appId, false)
                if (userInfo != null) {
                    user.nickname = userInfo.name ?: userInfo.uid
                    user.avatar = userInfo.avatar
                    cont.resume(Unit)
                } else {
                    //注册接收广播
                    val receiver = registerReceiver(erp, appId, user, cont)
                    cont.invokeOnCancellation {
                        unregisterReceiver(receiver)
                    }
                    //发送请求get_user_info
                    getUserInfo(erp, appId)
                }
            }
        }
    }


    private fun getUserInfo(pin: String, app: String) {
        var users = mutableListOf<TcpUpGetUsersInfo.Body.User>().also {
            val user = TcpUpGetUsersInfo.Body.User().apply {
                this.uid = pin
                this.app = app
                this.ver = 0
            }
            it.add(user)
        }

        ContactInfoUtils.getInstance().getContactInfoTimely(users, 1)
    }

    private fun registerReceiver(
            pin: String,
            app: String,
            user: User,
            con: CancellableContinuation<Unit>,
    ): UserInfoBroadcast {
        val receiver = UserInfoBroadcast(pin, app, user, con)
        val filter = IntentFilter()
        filter.addAction(TcpConstant.BROADCAST_PACKET_RECEIVED)
        val lbm = LocalBroadcastManager.getInstance(_context)
        lbm.registerReceiver(receiver, filter)
        return receiver
    }

    private fun unregisterReceiver(receiver: BroadcastReceiver) {
        val lbm = LocalBroadcastManager.getInstance(_context)
        lbm.unregisterReceiver(receiver)
    }

//    fun convert2ReportConference(conference: Conference) :ReportConference{
//        return ReportConference().apply{
//            code = conference.code
//            subject = conference.subject
//        }
//    }
//
//    fun convert2ReportConferenceReason(reason: Reason) :ReportConferenceReason{
//        return ReportConferenceReason().apply{
//            message = reason
//        }
//    }

    inner class UserInfoBroadcast(
            val pin: String,
            val app: String,
            val user: User,
            val con: CancellableContinuation<Unit>,
    ) : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == TcpConstant.BROADCAST_PACKET_RECEIVED) {
                val baseMessage =
                        intent?.getSerializableExtra(TcpConstant.SERVICE_BROADCAST_OBJECT1) as? BaseMessage
                if (TextUtils.equals(baseMessage?.type, Protocols.MESSAGE_USER_INFO)) {
                    val packet = baseMessage as? TcpDownUserInfo
                    var body = packet?.body as? TcpDownUserInfo.Body

                    if (TextUtils.equals(body?.pin, pin) && TextUtils.equals(body?.app, app)) {
                        user.nickname = body?.name ?: body?.pin
                        user.avatar = body?.avatar
                        unregisterReceiver(this)
                        con.resume(Unit)
                    }
                }
            }

        }
    }

    companion object {
        val EXTRA_SOURCE = "source"
    }
}