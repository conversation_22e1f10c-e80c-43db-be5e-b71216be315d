package com.jd.me.dd.im;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.cdyjy.common.base.ui.custom.ServiceBind;
import com.jd.cdyjy.common.base.ui.custom.chat.SearchTabType;
import com.jd.cdyjy.common.base.ui.custom.chat.SearchType;
import com.jd.cdyjy.common.base.util.ThemeUtil;
import com.jd.cdyjy.common.gallery.gallery_interface.GalleryConfigServiceManager;
import com.jd.cdyjy.common.smiley.smiley_interface.SmileyConfigServiceManager;
import com.jd.cdyjy.icsp.broadcast.BroadcastCenter;
import com.jd.cdyjy.icsp.cache.AppCache;
import com.jd.cdyjy.icsp.entity.GroupCreateCallback;
import com.jd.cdyjy.icsp.entity.GroupCreateFailResult;
import com.jd.cdyjy.icsp.entity.GroupCreateSuccessResult;
import com.jd.cdyjy.icsp.entity.MemberEntity;
import com.jd.cdyjy.icsp.entity.MemberListEntity;
import com.jd.cdyjy.icsp.entity.SelectorConfig;
import com.jd.cdyjy.icsp.selector.SelectorParam;
import com.jd.cdyjy.icsp.selector.ui.UIConfig;
import com.jd.cdyjy.icsp.utils.EventBusUtils;
import com.jd.cdyjy.jimui.UiCommonInterface;
import com.jd.cdyjy.jimui.UiCommonInterface.UnReadCallBack;
import com.jd.cdyjy.jimui.ui.OpimUiWrapper;
import com.jd.cdyjy.jimui.ui.external.GroupExternal;
import com.jd.cdyjy.jimui.ui.externalBean.ConferenceBean;
import com.jd.cdyjy.jimui.ui.externalBean.ProcessCenterBean;
import com.jd.cdyjy.jimui.ui.fragment.FragmentCollect;
import com.jd.cdyjy.jimui.ui.search.external.SearchResultCallback;
import com.jd.cdyjy.jimui.ui.selector.SelectorCallback.SelectorCallback;
import com.jd.cdyjy.jimui.ui.selector.SelectorCallback.SelectorCallbackController;
import com.jd.cdyjy.jimui.ui.selector.SelectorCallback.SelectorUICallback;
import com.jd.cdyjy.jimui.ui.sendmsgapi.BaseSession;
import com.jd.cdyjy.jimui.ui.sendmsgapi.GroupSession;
import com.jd.cdyjy.jimui.ui.sendmsgapi.HookProcess;
import com.jd.cdyjy.jimui.ui.sendmsgapi.SendResultCallback;
import com.jd.cdyjy.jimui.ui.sendmsgapi.SingleSession;
import com.jd.cdyjy.jimui.ui.sendmsgapi.dynamic.SendMsgInfoDynamic;
import com.jd.cdyjy.jimui.ui.sendmsgapi.sharelink.SendMsgInfoShareLink;
import com.jd.cdyjy.jimui.ui.sendmsgapi.text.SendMsgInfoText;
import com.jd.cdyjy.jimui.ui.util.ActivityResultListener;
import com.jd.cdyjy.jimui.ui.util.share.ShareResult;
import com.jd.cdyjy.jimui.ui.util.share.ShareResultInterface;
import com.jd.cdyjy.jimui.ui.util.share.ShareResultManager;
import com.jd.cdyjy.jimui.ui.util.share.ShareUtil;
import com.jd.cdyjy.jimui.ui.widget.BadgeView;
import com.jd.me.dd.im.biz.quickapp.QuickAppHelper;
import com.jd.me.dd.im.conference.video.VideoPlayerActivity;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.OnConfigUpdatedListener;
import com.jd.oa.joymeeting.JoyMeetingHelper;
import com.jd.me.dd.im.tool.EntityTools;
import com.jd.me.dd.im.tool.ErrorCode;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.configuration.local.model.UserCenterModel;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.badge.RedDotView;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.MeAiConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.configuration.local.model.PlusMenuModel;
import com.jd.oa.elliptical.SuperEllipticalImageView;
import com.jd.oa.eventbus.DeeplinkCallbackProcessor;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.eventbus.JmEventProcessor;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.ShareCardBean;
import com.jd.oa.guide.GuidePreference;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback3;
import com.jd.oa.im.listener.Callback4;
import com.jd.oa.im.listener.Callback5;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.FileInfo;
import com.jd.oa.model.ShareMeetingBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.im.dd.IMUnReadCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.JoyWorkShareBean;
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity;
import com.jd.oa.model.service.im.dd.entity.IFileListResult;
import com.jd.oa.model.service.im.dd.entity.ImDownloadResult;
import com.jd.oa.model.service.im.dd.entity.ImDownloadState;
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;
import com.jd.oa.model.service.im.dd.entity.UploadEntry;
import com.jd.oa.model.service.im.dd.listener.UserServiceListener;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.InitListener;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.multitask.SmallTvWindowManager;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.CrossPlatformPreference;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UnitUtils;
import com.jd.oa.utils.Utils2App;
import com.jd.oa.utils.VerifyUtils;
import com.jd.redpackets.manager.RedPacketsManager;
import com.jingdong.conference.conference.view.activity.CallActivity;
import com.jingdong.conference.conference.view.activity.CalledActivity;
import com.jingdong.conference.conference.view.activity.ConferenceActivity;
import com.jingdong.conference.conference.view.activity.ConferenceDetailActivity;
import com.jingdong.conference.conference.view.activity.ConferenceErrorActivity;
import com.jingdong.conference.conference.view.activity.ConferenceListActivity;
import com.jingdong.conference.conference.view.activity.ConferenceUsersActivity;

import org.jetbrains.annotations.Nullable;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import application.PUiApplication;
import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;
import jd.cdyjy.jimcore.OpimCoreWrapper;
import jd.cdyjy.jimcore.business.QuickOperateListener;
import jd.cdyjy.jimcore.business.SmartChatListener;
import jd.cdyjy.jimcore.business.chat.AtUser;
import jd.cdyjy.jimcore.business.chat.CardForwardZipMsg;
import jd.cdyjy.jimcore.business.chat.ChatQuickOperationUtils;
import jd.cdyjy.jimcore.business.chat.ChatSmartSummaryUtils;
import jd.cdyjy.jimcore.business.windowlabel.ChattingLabelInfo;
import jd.cdyjy.jimcore.business.windowlabel.ChattingLabelList;
import jd.cdyjy.jimcore.commoninterface.PointServiceEnum;
import jd.cdyjy.jimcore.commoninterface.user.ImUserInfoManager;
import jd.cdyjy.jimcore.commoninterface.user.service.IUserInfoModifyListener;
import jd.cdyjy.jimcore.gateway.api.windowlabel.WindowLabelLinkUrl;
import jd.cdyjy.jimcore.tcp.protocol.common.chatMessage.TcpChatMessageTemplate2DynamicBase;
import jd.cdyjy.jimcore.tools.CoreThreadManager;
import jd.cdyjy.jimcore.tools.jdme.FileListData;
import jd.cdyjy.jimcore.tools.jdme.IFileInvalidCallback;
import jd.cdyjy.jimcore.tools.jdme.IJDMEDownLoadCallBack;
import jd.cdyjy.jimcore.tools.jdme.JDMEMainDownloadUtils;
import ui.activity.ActivityPictureShare;
import ui.activity.TimlineActivitySetUser;
import voip.util.CallingVoipUtils;
import wrapper.TimlineOpimUiWrapper;

//import com.jd.oa.dd.redpacket.RedpacketUtil;

public class ImDdServiceImpl implements ImDdService {
    public static final String EVENT_OPEN_DRAWER_OF_PERSONAL_CENTER = "openDrawerOfPersonalCenterEvent";

    //    private static final String TAG = "TimlineUtils";
    private static final int TIME_RETRY = 5;

    private final AppService appService = AppJoint.service(AppService.class);

//    private ViewGroup mTopRightView;
    private WeakReference<ViewGroup> mTopLeftViewRef;

    public ImDdServiceImpl(){
        ConfigurationManager.get().addOnConfigUpdatedListener(new OnConfigUpdatedListener() {
            @Override
            public void onConfigUpdated(@NonNull String config) {
                OpimUiWrapper.getInstance().onGetConfig(config);
            }
        });
    }

    private void bindService() {
        LogRecorder.getDefault().record(MELogUtil.TAG_IM, "ImDdServiceImpl bindService", null);
        // bind service at here
        jd.cdyjy.jimcore.commoninterface.ServiceBind.bindService(PointServiceEnum.GLOBAL_POINT, GlobalImpl.class);
        jd.cdyjy.jimcore.commoninterface.ServiceBind.bindService(PointServiceEnum.JDME_POINT, JDMEImpl.class);
        jd.cdyjy.jimcore.commoninterface.ServiceBind.bindService(PointServiceEnum.USER_POINT, ImUserServiceImpl.class);
        jd.cdyjy.jimcore.commoninterface.ServiceBind.bindService(PointServiceEnum.TAB_POINT, TabImpl.class);
        jd.cdyjy.jimcore.commoninterface.ServiceBind.bindService(PointServiceEnum.SESSION_POINT, SessionImpl.class);
        jd.cdyjy.jimcore.commoninterface.ServiceBind.bindService(PointServiceEnum.THEME_POINT, ChangThemeImpl.class);
        jd.cdyjy.jimcore.commoninterface.ServiceBind.bindService(PointServiceEnum.JDME_MEETING, ImMeetingImpl.class);
        //UI定制化
        ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.CONVERSATION_UI_POINT, ConversationUiImpl.class);
        ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.CHATTING_UI_POINT, ChatUiImpl.class);
        ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.GLOBAL_UI_POINT, GlobalConfigImpl.class);
        ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.USER_INFO_POINT, UserInfoUIImpl.class);
        ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.NOTICE_OPERATION_POINT, CustomNoticeItemImpl.class);
        ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.CHATTING_OPERATION_POINT, ChattingOperationImpl.class);

        com.jd.cdyjy.common.base.ui.custom.ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.SHARE_POINT, ShareConfigImpl.class);//聊天UI定制化
        com.jd.cdyjy.common.base.ui.custom.ServiceBind.bindService(com.jd.cdyjy.common.base.ui.custom.PointServiceEnum.SEARCH_POINT, ImSearchServiceImpl.class);
    }

    @Override
    public void loginTimline() {
        loginIM(false);
    }

    @Override
    public void loginIM(boolean fromUserUi) {
        String env = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_NET_ENVIRONMENT);
        if (!(env.equals(NetEnvironmentConfigModel.PREV) || env.equals(NetEnvironmentConfigModel.PROD))) {
            return;
        }
        boolean isLogout = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT);
        if (isLogout) {
            return;
        }
        if(MultiAppConstant.isSaasFlavor()){
            String userId = PreferenceManager.UserInfo.getUserId();
            String appId =  PreferenceManager.UserInfo.getTeamId();
            String token = TokenManager.getInstance().getAccessToken();
            loginIM(userId,appId,token,null,fromUserUi);
            return;
        }
        String erp = PreferenceManager.UserInfo.getUserName();
        String token = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_TOKEN);
        String nonce = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_NONCE);
        LogRecorder.getDefault().record(MELogUtil.TAG_IM, "loginTimline ", "loginIM loginIMSDK erp: " + erp + ", token: " + token + ",nonce: " + nonce);
        if (!TextUtils.isEmpty(token)) {
            try {
                String appId = getAppID();
//                initJIMDb(erp);
//                OpimUiWrapper.loginAar(erp, appId, token, nonce);
                loginIM(erp, appId, token, nonce, fromUserUi);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            getTimlineTokenAndLogin(fromUserUi);
        }
    }

    @Override
    public void onNoticePushClick(String json) {
        OpimUiWrapper.getInstance().onNoticePushClick(json);
    }

    // 此处 appId 需要找咚咚要
    public MEChattingLabel getChattingLabels(String appId) {
        ChattingLabelList chattingLabels = OpimUiWrapper.getInstance().getChattingLabels(appId);
        MEChattingLabel label = new MEChattingLabel();
        fillMEChattingLabel(label, chattingLabels);
        return label;
    }

    private void fillMEChattingLabel(MEChattingLabel label, ChattingLabelList im) {
        label.labels = new ArrayList<>();
        if (im != null && im.getLabels() != null && !im.getLabels().isEmpty()) {
            for (ChattingLabelInfo imLabel : im.getLabels()) {
                WindowLabelLinkUrl imLabelLinkUrl = imLabel.getLinkUrl();
                if (imLabelLinkUrl != null) {
                    MEChattingLabel.ChattingLabelInfo meLabelInfo = new MEChattingLabel.ChattingLabelInfo();
                    label.labels.add(meLabelInfo);
                    meLabelInfo.linkUrl = new MEChattingLabel.WindowLabelLinkUrl();
                    meLabelInfo.linkUrl.defaultUrl = imLabelLinkUrl.getDefault();
                    meLabelInfo.linkUrl.desktop = imLabelLinkUrl.getDesktop();
                    meLabelInfo.linkUrl.mobile = imLabelLinkUrl.getMobile();
                }
            }
        }
    }

    @Override
    public void onNotifyIMInstallApk() {
        OpimUiWrapper.getInstance().onNotifyIMInstallApk();
    }

    @Override
    public void loginIM(String erp, String appId, String token, String nonce, boolean fromLogin) {
        OpimUiWrapper.loginIMSDK(erp, appId, token, nonce, fromLogin);
    }

    @Override
    public void showChattingActivity(Context context, String erp) {
        TimlineOpimUiWrapper.getInstance().showChattingActivity(context, erp, getAppID(), false);
    }

    @Override
    public void showChattingActivity(Context context, String erp, String appId) {
        TimlineOpimUiWrapper.getInstance().showChattingActivity(context, erp, appId, false);
    }

    @Override
    public void showGroupChattingActivity(Context context, String groupId) {
        TimlineOpimUiWrapper.getInstance().showChattingActivity(context, groupId, getAppID(), true);
    }

    @Override
    public boolean sendQrCodeResult(Context context, String json) {
        return OpimUiWrapper.getInstance().QRCodeResult(json);
    }

    @Override
    public void showContactDetailInfo(Context context, String erp) {
        OpimUiWrapper.getInstance().showUserInfo(context, erp, getAppID(), true);
    }

    @Override
    public void showContactDetailInfo(Context context, String appId, String erp) {
        OpimUiWrapper.getInstance().showUserInfo(context, erp, appId, true);
    }

    @Override
    public void getTimlineTokenAndLogin(final boolean fromUserUi) {
        //老数据迁移登录在MainActivity
        if (isOldMsgUpdate(Utils2App.getApp()) && !PreferenceManager.UserInfo.hasTimlineUpgradeTip()) {
            return;
        }
        boolean isLogout = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT);
        if (isLogout) {
            return;
        }
        LogRecorder.getDefault().record(MELogUtil.TAG_IM, "getTimlineTokenAndLogin start", null);

        NetWorkManagerLogin.getTimlineToken(new SimpleReqCallbackAdapter<>(new AbsReqCallback<TimlineToken>(TimlineToken.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                OpimCoreWrapper.getTokenFailed(PreferenceManager.UserInfo.getUserName(), errorMsg);
                getTimlineTokenAndLoginDelay(fromUserUi);

                LogRecorder.getDefault().record(MELogUtil.TAG_IM, "getTimlineToken failure ", errorMsg);
            }

            @Override
            protected void onSuccess(TimlineToken map, List<TimlineToken> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);

                LogRecorder.getDefault().record(MELogUtil.TAG_IM, "getTimlineToken success ", null);

                if (map != null) {
//                    PreferenceManager.setString(PreferenceManager.UserInfo.TIMLINE_TOKEN, map.getLoginToken());
//                    PreferenceManager.setString(PreferenceManager.UserInfo.TIMLINE_NONCE, map.getNonce());
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_TOKEN, map.getLoginToken());
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_NONCE, map.getNonce());
                    loginIM(fromUserUi);
                }
            }
        }));
    }

    @Override
    public void getTimlineTokenAndLoginDelay(final boolean fromUserUi) {
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                getTimlineTokenAndLogin(fromUserUi);
            }
        }, TIME_RETRY, TimeUnit.SECONDS);
    }

    @Override
    public void init() {
        BroadcastCenter.init();
        PUiApplication.onCreateInit();
//        bindService();
        GalleryConfigServiceManager.intance().init(GalleryUIConfigImpl.class.getName());
        SmileyConfigServiceManager.instance().init(SmileyUIConfigImpl.class.getName());
        OpimUiWrapper.getInstance().setLaunchChat(new UiCommonInterface.LaunchChat() {
            @Override
            public void goChat() {
                Activity activity = AppBase.getTopActivity();
                if (activity != null) {
                    Intent intent = Router.build(DeepLink.MESSAGE_OLD).getIntent(activity);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    activity.startActivity(intent);
                }
            }
        });
        TimlineMessageDispatcher.getInstance().init();
        CoreThreadManager.excuteOnCachedThread(new Runnable() {
            @Override
            public void run() {
                if (PreferenceManager.UserInfo.getLogin()) {
                    loginTimline();
                }
            }
        });

        JoyMeetingHelper.INSTANCE.setOpenContactsPageListener();
        List<Class<? extends Activity>> activities = new ArrayList<>();
        activities.add(ConferenceActivity.class);
        activities.add(ConferenceDetailActivity.class);
        activities.add(ConferenceErrorActivity.class);
        activities.add(ConferenceListActivity.class);
        activities.add(ConferenceUsersActivity.class);
//        activities.add(StartConferenceActivity.class);
        MultiTaskManager.getInstance().addWhiteList(activities, null);
        if (AppBase.getAppContext() != null) {
            activities.add(CalledActivity.class);
            activities.add(CallActivity.class);
            SmallTvWindowManager.getInstance(AppBase.getAppContext()).addWhiteList(activities, null);
        }
    }

    @Override
    public void beforeMainOnCreate(Activity activity) {
        @SuppressWarnings("deprecation") int themeId = ThemeUtil.getInstance().getThemeId(activity);
        if (-1 != themeId) {
            activity.setTheme(themeId);
        } else {
            activity.setTheme(R.style.AppTheme);
        }
//        EventBusUtils.register(activity);
    }

    @Override
    public void initMainLayout(Activity activity, int id) {// R.id.me_root_container
        try {
            TimlineOpimUiWrapper.getInstance().init(activity, id);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Fragment getChatListFragment() {
        return TimlineOpimUiWrapper.getInstance().getTimlineFragmentChatList();//获取消息列表的页面
    }

    @Override
    public Fragment getContactFragment() {
        return TimlineOpimUiWrapper.getInstance().getTimlineFragmentContact();
    }

    @Override
    public Fragment getCollectFragment() {
        return TimlineOpimUiWrapper.getInstance().getFragmentCollect();
    }

    @Override
    public String getContactSecondaryFragment() {
        return TimlineOpimUiWrapper.getInstance().getContactSecondaryFragment();
    }

    @Override
    public void logout() {
        String pin = PreferenceManager.UserInfo.getUserName();
        String appId = getAppID();
        if(MultiAppConstant.isSaasFlavor()){
            pin = PreferenceManager.UserInfo.getUserId();
            appId =  PreferenceManager.UserInfo.getTeamId();
        }
        OpimUiWrapper.logout(pin,appId);
        LogRecorder.getDefault().record(MELogUtil.TAG_IM, "ImDdServiceImpl logout ", Arrays.toString(Thread.currentThread().getStackTrace()));
    }

    @Override
    public void onMainDestroy(Activity activity) {
        EventBusUtils.unRegister(activity);
        TimlineOpimUiWrapper.getInstance().destroy();
        OpimUiWrapper.getInstance().destroy();
        AppCache.getInstance().clearCache();
        AppJoint.service(ImDdService.class).unregisterUserStatusChangeListener("initTopLeftView");
    }

    @Override
    public void handleOnActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        TimlineOpimUiWrapper.getInstance().onActivityResult(activity, requestCode, resultCode, data);
    }

    @Override
    public void handleOnRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        TimlineOpimUiWrapper.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public void setTipsView(final Activity context, Bundle savedInstanceState, int tipId, int rootId, int mainBottom, int tab1, int tab2) {
        final FrameLayout tipLayout = context.findViewById(tipId);
        OpimUiWrapper.getInstance().initTipsView(context, savedInstanceState, tipLayout);
        final View rootView = context.findViewById(rootId);
        final int[] msgLocation = new int[2];
        final View msgIcon = rootView.findViewWithTag(context.getString(tab1));
        final View bottomView = rootView.findViewById(mainBottom);
        if (msgIcon == null) {
            return;
        }
        msgIcon.postDelayed(new Runnable() {
            @Override
            public void run() {
                msgIcon.getLocationOnScreen(msgLocation);

                OpimUiWrapper.getInstance().tipsViewattach(context, tipLayout, BadgeView.POSITION_BOTTOM_LEFT, msgLocation[0] + msgIcon.getWidth() * 3 / 4 - CommonUtils.dp2px(3), (bottomView.getBottom() - bottomView.getTop() - CommonUtils.dp2px(16) - CommonUtils.dp2px(3)));
            }
        }, 200);

//        final int[] contactLocation = new int[2];
//        final View contactIcon = rootView.findViewWithTag(context.getString(tab2));
//        if (contactIcon == null) {
//            return;
//        }
//        contactIcon.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                contactIcon.getLocationOnScreen(contactLocation);
//                OpimUiWrapper.getInstance().tipsUnReadAapplyAttach(context, tipLayout, BadgeView.POSITION_BOTTOM_LEFT, contactLocation[0] + contactIcon.getWidth() * 3 / 4,
//                        (bottomView.getBottom() - bottomView.getTop() - CommonUtils.dp2px(16)));
//            }
//        }, 200);
    }

    @Override
    public void gotoMemberList(final Activity activity, int requestCode, MemberListEntityJd entity, final Callback<ArrayList<MemberEntityJd>> cb) {
        if (entity.isExternalDataEnable() && !CollectionUtil.isEmptyOrNull(entity.mSpecifyAppId)) {
            entity.mSpecifyAppId.add(MemberListEntityJd.EXTERNAL_CONTACT_EMAIL);
        }

        MemberListEntity memberListEntity = EntityTools.getMemberListEntity(entity);

        SelectorParam param = EntityTools.getSelectParam(entity);

        if (entity.maxNum != Integer.MAX_VALUE) {
            memberListEntity.setMaxNum(entity.maxNum);
        }

        OpimUiWrapper.getInstance().gotoMemberList(activity, memberListEntity, param, new SelectorCallback() {
            @Override
            public void commit(ArrayList<MemberEntity> items, SelectorUICallback callback) {
                SelectorCallbackController.instance().unregisterBusiness(getClass().getName().hashCode());
                callback.closeSelector();
                ArrayList<MemberEntity> memberEntityArrayList = items;
                if (cb != null) {
                    if (items == null) {
                        cb.onFail();
                    } else {
                        ArrayList<MemberEntityJd> memberEntityJds = new ArrayList<>();
                        for (MemberEntity memberEntity : memberEntityArrayList) {
                            memberEntityJds.add(EntityTools.getMemberEntityJd(memberEntity));
                        }
                        cb.onSuccess(memberEntityJds);
                    }
                }
            }

            @Override
            public void back() {
                SelectorCallbackController.instance().unregisterBusiness(getClass().getName().hashCode());
                if (cb != null) {
                    cb.onFail();
                }
            }
        });
    }

    public void closeSelector() {
        OpimUiWrapper.getInstance().closeSelector();
    }

    public void addMemberToSelector(MemberEntityJd entity) {
        MemberEntity memberEntity = EntityTools.getMemberEntity(entity);
        OpimUiWrapper.getInstance().addMemberToSelector(memberEntity);
    }

    public void deleteMemberToSelector(MemberEntityJd entity) {
        MemberEntity memberEntity = EntityTools.getMemberEntity(entity);
        OpimUiWrapper.getInstance().deleteMemberFromSelector(memberEntity);
    }

    @Override
    public void unregisterCallback(Integer hashCode) {
        if (hashCode != null) {
            SelectorCallbackController.instance().unregisterBusiness(hashCode);
        }
    }

    @Override
    public void joyworkSelectExecutor(Activity activity, String title, String next, MemberListEntityJd entity, final Callback<ArrayList<MemberEntityJd>> cb, final Callback<Integer> hashCodeCallback, final Callback<Context> contextCallback) {
        if (entity.isExternalDataEnable() && !CollectionUtil.isEmptyOrNull(entity.mSpecifyAppId)) {
            entity.mSpecifyAppId.add(MemberListEntityJd.EXTERNAL_CONTACT_EMAIL);
        }
        MemberListEntity memberListEntity = EntityTools.getMemberListEntity(entity);
        SelectorParam param = EntityTools.getSelectParam(entity);
        UIConfig config = new UIConfig.Builder().setConfirmText(next).setSelectorTitle(title).build();
        param.setUiConfig(config);
        if (entity.maxNum != Integer.MAX_VALUE) {
            memberListEntity.setMaxNum(entity.maxNum);
        }
        OpimUiWrapper.getInstance().gotoMemberList(activity, memberListEntity, param, new SelectorCallback() {
            @Override
            public void commit(ArrayList<MemberEntity> items, SelectorUICallback callback) {
                if (hashCodeCallback != null) {
                    hashCodeCallback.onSuccess(getClass().getName().hashCode());
                }
                if (contextCallback != null) {
                    contextCallback.onSuccess(callback.getContext());
                }
                ArrayList<MemberEntity> memberEntityArrayList = items;
                if (cb != null) {
                    if (items != null) {
                        ArrayList<MemberEntityJd> memberEntityJds = new ArrayList<>();
                        for (MemberEntity memberEntity : memberEntityArrayList) {
                            memberEntityJds.add(EntityTools.getMemberEntityJd(memberEntity));
                        }
                        cb.onSuccess(memberEntityJds);
                    }
                }
            }

            @Override
            public void back() {
                SelectorCallbackController.instance().unregisterBusiness(getClass().getName().hashCode());
                if (cb != null) {
                    cb.onFail();
                }
            }
        });
    }

    @Override
    public void searchOnLine(String info, Object o) {

    }

    @Override
    public String getMySignature() {
        return ImUserInfoManager.getMySignature();
    }

    @Override
    public void modifySignature(final String signature, final LoadDataCallback<String> callback) {
        ImUserInfoManager.modifySignature(signature, new IUserInfoModifyListener() {
            @Override
            public void onModifySucceed() {
                callback.onDataLoaded(signature);
            }

            @Override
            public void onModifyFailed(String s) {
                callback.onDataNotAvailable(s, -1);
            }

            @Override
            public void onModifyTimeout() {
                callback.onDataNotAvailable("", -1);
            }
        });
    }

    @Override
    public String getMyAvatar() {
        return ImUserInfoManager.getMyAvatar();
    }

    @Override
    public void modifyAvatar(String newAvatar) {
        ImUserInfoManager.modifyAvatar(newAvatar, new IUserInfoModifyListener() {
            @Override
            public void onModifySucceed() {
                ToastUtils.showToast(R.string.me_way_tips_setting_success);
            }

            @Override
            public void onModifyFailed(String s) {
                ToastUtils.showToast(R.string.me_way_tips_setting_failure);
            }

            @Override
            public void onModifyTimeout() {
                ToastUtils.showToast(R.string.me_way_tips_setting_failure);
            }
        });
    }

    @Override
    public void syncAvatar() {
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                String avatar = getMyAvatar();
                PreferenceManager.UserInfo.setUserCover(avatar);
                appService.updateIconSuccess(avatar);
                ImUserServiceImpl.notifyAvatarChanged(avatar);
            }
        }, TIME_RETRY, TimeUnit.SECONDS);
    }

    @Override
    public boolean isOldMsgUpdate(Context context) {
        try {
            return OpimUiWrapper.getInstance().isOldMsgUpdate(context, PreferenceManager.UserInfo.getUserName(), getAppID());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public void goOldMsgUpdateActivity(Context context, int requestCode, boolean fromSetting) {
        OpimUiWrapper.getInstance().goOldMsgUpdateActivity(context, PreferenceManager.UserInfo.getUserName(), getAppID(), requestCode, fromSetting);
    }

    @Override
    public void goUnReadMsgLine() {
        TimlineOpimUiWrapper.getInstance().goUnReadMsgLine();
    }

//    @Override
//    public void initJIMDb(String erp) {
//        OpimUiWrapper.initJIMDb(erp, getAppID());
//    }

    @Override
    public void clearCache() {
        OpimCoreWrapper.clearCache();
    }

    @Override
    public void clearChatHistory() {
        OpimUiWrapper.getInstance().clearHistory();
    }

    @Override
    public void clearChatMsg() {
        OpimUiWrapper.clearChatMsgs(PreferenceManager.UserInfo.getUserName());
    }

    @Override
    public void goChatMigrateActivity(Context context) {
        OpimUiWrapper.getInstance().goChatMigrateActivity(context);
    }

    @Override
    public void clearNoticeUnReadCount(final String noticeId) {
        clearNoticeUnReadCountInternal(noticeId);
    }

    public static void syncNoticeUnread(String mid, String pin, String toApp, String toPin) {
        if (mid != null) {
            OpimUiWrapper.getInstance().notifyRead(pin, Long.parseLong(mid));
        }
        NetWorkManager.syncMessageServiceUnread(null, true, mid, toApp, toPin, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                MELogUtil.localI("MessageServiceUnread", "success: " + info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MELogUtil.localI("MessageServiceUnread", "fail: " + info);
            }
        });
    }

    static void clearNoticeUnReadCountInternal(final String noticeId) {
        clearNoticeUnReadCountInternal(noticeId, System.currentTimeMillis());
        NetWorkManager.request(null, NetworkConstant.API_GET_SERVER_TIMESTAMP, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            protected void onSuccess(JSONObject s, List<JSONObject> tArray, String rawData) {
                super.onSuccess(s, tArray, rawData);
                try {
                    JSONObject jsonObject = new JSONObject(rawData);
                    String timeStampStr = jsonObject.optString("content");
                    long timeStamp = Long.parseLong(timeStampStr);
                    clearNoticeUnReadCountInternal(noticeId, timeStamp);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(String errorMsg) {
                super.onFailure(errorMsg);
            }
        }), null);
    }

    @Override
    public void clearNoticeUnReadCount(String noticeId, long time) {
        clearNoticeUnReadCountInternal(noticeId, time);
    }

    private static void clearNoticeUnReadCountInternal(String noticeId, long time) {
        if (TextUtils.isEmpty(noticeId)) {
            return;
        }
        OpimUiWrapper.getInstance().clearNoticeUnReadCount(noticeId, time);
    }

    @Override
    public void showTab(TabEntityJd tab) {
        if (AppBase.isMultiTask()) {
            boolean hasJdHt = MultiTaskManager.getInstance().hasItem(MultiTaskManager.JD_HU_TONG_ID);
            if (hasJdHt) {
                return;
            }
        }
        OpimCoreWrapper.showTab(EntityTools.getTabEntity(tab));
    }

    @Override
    public void hideTab() {
        OpimCoreWrapper.hideTab();
    }

    //    /**
//     * 检查咚咚文件是否过期
//     */
//    @SuppressLint("CheckResult")
//    @Override
//    public void checkFileExpired(final String fileUrl, final LoadDataCallback<Boolean> callback) {
//        checkFileExpiredInternal(fileUrl, callback);
//    }
//
//
    @SuppressWarnings("ResultOfMethodCallIgnored")
    @SuppressLint("CheckResult")
    static void checkFileExpiredInternal(final String fileUrl, final LoadDataCallback<Boolean> callback) {
        Single.create(new SingleOnSubscribe<Boolean>() {
            @Override
            public void subscribe(SingleEmitter<Boolean> e) throws Exception {
                HttpURLConnection httpUrlConn = (HttpURLConnection) new URL(fileUrl).openConnection();
                int statusCode = httpUrlConn.getResponseCode();
                if (HttpURLConnection.HTTP_OK == statusCode) {
                    e.onSuccess(false);
                } else if (HttpURLConnection.HTTP_NOT_FOUND == statusCode || HttpURLConnection.HTTP_BAD_REQUEST == statusCode || HttpURLConnection.HTTP_FORBIDDEN == statusCode) {
                    //已过期
                    e.onSuccess(true);
                } else {
                    e.onError(new Exception("error"));
                }
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Boolean>() {
            @SuppressWarnings("RedundantThrows")
            @Override
            public void accept(Boolean aBoolean) throws Exception {
                callback.onDataLoaded(aBoolean);
            }
        }, new Consumer<Throwable>() {
            @SuppressWarnings("RedundantThrows")
            @Override
            public void accept(Throwable throwable) throws Exception {
                callback.onDataNotAvailable(throwable.getMessage(), 0);
            }
        });
    }


    @Override
    public void setLocale(Locale locale) {
        OpimCoreWrapper.setLang(locale);
        OpimUiWrapper.notifyLangChange();
    }

    @Override
    public String getAppID() {
        String appId = PreferenceManager.UserInfo.getTimlineAppID();
        if (TextUtils.isEmpty(appId)) {
            appId = APP_ID_JDME_CHINA;
        }
        return appId;
    }

    @Override
    public void share(Context context, String title, String content, String url, String icon, String type) {
        Context from = AppBase.getTopActivity();
        if (from == null) {
            if (context == null) {
                from = Utils2App.getApp();
            } else {
                from = context;
            }
        }
        Intent sendIntent = new Intent(from, ActivityPictureShare.class);
        sendIntent.setAction(Intent.ACTION_SEND);
        sendIntent.setType(type);
        CardForwardZipMsg.ShareLink link = new CardForwardZipMsg.ShareLink();
        link.title = title;
        link.content = content;
        link.url = url;
        link.icon = icon;
        sendIntent.putExtra("jdim_ShareLink", new Gson().toJson(link));
        if (!(from instanceof Activity)) {
            sendIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        from.startActivity(sendIntent);
        ShareResultManager.instance().registJustOnce(new ShareResultInterface() {
            @Override
            public void onResult(ShareResult shareResult) {
                Log.d("share", "onResult: " + shareResult);
            }
        });
    }

    @Override
    public void shareJoyWork(Context context, JoyWorkShareBean bean) {
        Context from = context;
        if (from == null) {
            from = Utils2App.getApp();
        }
        if (from == null) {
            return;
        }

        Intent sendIntent = new Intent(context, ActivityPictureShare.class);
        sendIntent.setAction(Intent.ACTION_SEND);
        sendIntent.setType(ShareUtil.ShareType.TYPE_JD_TASK_CARD);
        sendIntent.putExtra("jdim_taskCard", new Gson().toJson(bean));
        sendIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        from.startActivity(sendIntent);

//        Intent sendIntent = new Intent(from, ActivityPictureShare.class);
//        sendIntent.setAction(Intent.ACTION_SEND);
//        sendIntent.setType(type);
//        CardForwardZipMsg.ShareLink link = new CardForwardZipMsg.ShareLink();
//        link.title = title;
//        link.content = content;
//        link.url = url;
//        link.icon = icon;
//        sendIntent.putExtra("jdim_ShareLink", new Gson().toJson(link));
//        if (!(from instanceof Activity)) {
//            sendIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        }
//        from.startActivity(sendIntent);
    }

    @Override
    public void sendJoyWorkCard(JoyWorkShareBean bean, String to, String toApp, String gid) {
        OpimUiWrapper instance = OpimUiWrapper.getInstance();
        instance.sendTaskCard(to, toApp, gid, false, new Gson().toJson(bean));
    }

    @Override
    public void formatIMSessionId(String from, String fromApp, String to, String toApp, String gid, boolean secret) {
        OpimUiWrapper.getInstance().formatSessionId(from, fromApp, to, toApp, gid, secret);
    }

    @Override
    public void cancelAllNotify() {
        OpimUiWrapper.getInstance().cancelAllNotify();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (OpimUiWrapper.getInstance().dispatchKeyEventPreIme(event)) {
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_BACK) {
            /*if (TimlineOpimUiWrapper.getInstance().isFlowShow()) {
                TimlineOpimUiWrapper.getInstance().dismissFlowMenu();
                return true;
            } else */
            if (!OpimUiWrapper.getInstance().isChattingViewShow()) {
                return false;
            } else {
                if (null != OpimUiWrapper.getInstance().getFragmentChatting() && OpimUiWrapper.getInstance().getFragmentChatting().mIsEditMode) {
                    OpimUiWrapper.getInstance().getFragmentChatting().switchNormal();
                } else if (null != OpimUiWrapper.getInstance().getFragmentChatting() && OpimUiWrapper.getInstance().getFragmentChatting().isRecordShow()) {
                    return true;
                } else {
                    OpimUiWrapper.getInstance().closeChatting();
                }


                return true;
            }
        } else {
            return false;
        }
    }

    @Override
    public boolean onBackPressed() {
        if (OpimUiWrapper.getInstance().isChattingViewShow()) {
            OpimUiWrapper.getInstance().closeChatting();
            return true;
        } else {
            if (OpimUiWrapper.getInstance().isLeftMenuShow()) {
                OpimUiWrapper.getInstance().closeLeftMenu();
                return true;
            } else {
                return false;
            }
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        //noinspection deprecation
        TimlineOpimUiWrapper.getInstance().dispatchTouchEvent(event);
        return false;
    }

    @Override
    public void sendMultiFile(Activity activity, String jsonShare) {
        Intent intent = new Intent(activity, ActivityPictureShare.class);
        intent.setAction(Intent.ACTION_SEND);
        intent.setType("jd_storage_share");
        intent.putExtra("jdFileShare_Array", jsonShare);
        activity.startActivity(intent);
    }

    @Override
    public void sendSingleFile(Activity activity, String jsonShare) {
        Intent intent = new Intent(activity, ActivityPictureShare.class);
        intent.setAction(Intent.ACTION_SEND);
        intent.setType("jd_storage_share");
        intent.putExtra("jdFileShare", jsonShare);
        activity.startActivity(intent);
        //似乎整个软件中都没有使用这个
        ShareResultManager.instance().registJustOnce(new ShareResultInterface() {
            @Override
            public void onResult(ShareResult shareResult) {
//                Log.d(TAG, "onResult: " + shareResult);
            }
        });
    }

    @Override
    public void appInit(Application application, final InitListener initListener) {
        bindService();
        PUiApplication.init(application, new OpimCoreWrapper.OpimCoreListener() {
            @Override
            public String getParentUid() {
                return initListener.getParentUid();
            }

            @Override
            public String getParentApp() {
                return initListener.getParentApp();
            }

            @Override
            public float getScaledDensity() {
                return initListener.getScaledDensity();
            }
        });
    }

    @Override
    public void onQrResultForMigrate(String data) {
        OpimUiWrapper.getInstance().onQrResultForMigrate(data);
    }

    @Override
    public void registerTimlineMessage(String flag, TimlineMessageListener listener) {
        TimlineMessageDispatcher.getInstance().registerListener(flag, listener);
    }

    @Override
    public void unregisterListener(String flag, TimlineMessageListener listener) {
        TimlineMessageDispatcher.getInstance().unregisterListener(flag, listener);
    }

    @Override
    public void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl) {
        OpimUiWrapper.getInstance().sendVoteMsg(gId, url, title, content, iconUrl, source, sourceIconUrl);
    }

    @Override
    public void updateUserAvatar(String userIcon, String userName, String appID) {
        OpimCoreWrapper.updateUserAvatar(userIcon, PreferenceManager.UserInfo.getUserName(), getAppID());
    }

    @Override
    public void sharePic(Context context, Uri uri) {
        Intent sendIntent = new Intent(Utils2App.getApp(), ActivityPictureShare.class);
        sendIntent.setAction(Intent.ACTION_SEND);
        sendIntent.setType("image/*");
        sendIntent.putExtra(Intent.EXTRA_STREAM, uri);
        sendIntent.putExtra("external_share", false);
        sendIntent.setAction(Intent.ACTION_SEND);
        if (!(context instanceof Activity)) {
            sendIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(sendIntent);
    }

    @Override
    public void shareFile(Context context, Uri uri) {
        Intent sendIntent = new Intent(Utils2App.getApp(), ActivityPictureShare.class);
        sendIntent.setAction(Intent.ACTION_SEND);
        sendIntent.putExtra(Intent.EXTRA_STREAM, uri);
        sendIntent.putExtra("external_share", false);
        if (!(context instanceof Activity)) {
            sendIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(sendIntent);
    }

    @Override
    public void putDeepLink(Map<String, Class<?>> map) {
        map.put(DeepLink.ACTIVITY_URI_PictureShare, ActivityPictureShare.class);
    }

    @Override
    public int getChatListLayout() {
        return R.layout.opim_layout_main_title;
    }

    @Override
    public int getContactLayout() {
        return R.layout.opim_layout_contact_title;
    }

    @Override
    public void showPersonalRedPackets(Activity activity) {
        RedPacketsManager.showPersonalRedPackets(activity);
    }

    @Override
    public void addUserService(UserServiceListener userServiceListener) {
        ImUserServiceImpl.add(userServiceListener);
    }

    @Override
    public void removeUserService(UserServiceListener userServiceListener) {
        ImUserServiceImpl.remove(userServiceListener);
    }

    @Override
    public void showSettingMessage(Context context) {
        OpimUiWrapper.showSettingMessage(context);
    }


    @Override
    public boolean goChatActivity(Context context, String sessionKey, String to, String toApp, String msgId, long mid, String content, long timestamp, int sessionType, boolean checkExist) {
        return OpimUiWrapper.getInstance().goChatActivity(context, sessionKey, to, toApp, msgId, mid, content, timestamp, sessionType, checkExist);
    }

    @Override
    public void getGroupRoster(String gid, boolean net, final LoadDataCallback<ArrayList<MemberEntityJd>> callback) {
        OpimUiWrapper.getInstance().getGroupRoster(gid, net, new UiCommonInterface.GroupRosterGetListener() {
            @Override
            public void onRosterGetResult(ArrayList<UiCommonInterface.ContactInfo> arrayList, int code) {
                if (arrayList != null) {
                    ArrayList<MemberEntityJd> list = new ArrayList<>();
                    for (UiCommonInterface.ContactInfo contactInfo : arrayList) {
                        MemberEntityJd memberEntityJd = new MemberEntityJd();
                        memberEntityJd.mName = contactInfo.nickName; // 姓名
                        memberEntityJd.mAvatar = contactInfo.avatar;
                        memberEntityJd.mApp = contactInfo.app; // app
                        memberEntityJd.mId = contactInfo.uid; // erp
                        memberEntityJd.titleName = contactInfo.position;  // 职位
                        memberEntityJd.department = validateDepartment(contactInfo.department); // 部门，全乎的
                        list.add(memberEntityJd);
                    }
                    callback.onDataLoaded(list);
                } else {
                    callback.onDataNotAvailable("", code);
                }
            }
        });
    }

    /**
     * 咚咚返回的department可能是json
     * {"name":"xxx", "clickable":1 }
     *
     * @param value
     * @return
     */
    private String validateDepartment(String value) {
        String result = value;
        try {
            result = JsonUtils.getKeyValue(value, "name");
            if (result == null) {
                result = value;
            }
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return result;
    }

    public GroupInfoEntity getGroupInfoLocal(String gid) {
        UiCommonInterface.GroupInfo info = OpimUiWrapper.getInstance().getGroupInfoFromLocal(gid);
        GroupInfoEntity groupInfoEntity = new GroupInfoEntity();
        imGroupInfo2MeGroupInfo(info, groupInfoEntity);
        return groupInfoEntity;
    }

    private void imGroupInfo2MeGroupInfo(UiCommonInterface.GroupInfo imInfo, GroupInfoEntity meInfo) {
        meInfo.setName(imInfo.name);
        meInfo.setAvatar(imInfo.avatar);
        meInfo.setGid(imInfo.gid);
        meInfo.setFlag(imInfo.flag);
    }

    @Override
    public void getGroupInfo(String gid, final LoadDataCallback<GroupInfoEntity> callback) {
        OpimUiWrapper.getInstance().getGroupInfoFromNet(gid, new UiCommonInterface.GroupInfoListener() {
            @Override
            public void onInfoResult(UiCommonInterface.GroupInfo groupInfo, int code) {
                if (groupInfo != null) {
                    GroupInfoEntity groupInfoEntity = new GroupInfoEntity();
                    groupInfoEntity.setName(groupInfo.name);
                    groupInfoEntity.setAvatar(groupInfo.avatar);
                    groupInfoEntity.setGid(groupInfo.gid);
                    groupInfoEntity.setFlag(groupInfo.flag);
                    callback.onDataLoaded(groupInfoEntity);
                } else {
                    callback.onDataNotAvailable("", code);
                }
            }
        });
    }

    @Override
    public void sendShareLinkMsg(String sessionKey, String to, String appId, int sessionType, String url, String title, String content, String icon, String source, String sourceIcon, String category) {
        OpimUiWrapper.getInstance().sendShareLinkMsg(sessionKey, to, appId, sessionType, url, title, content, icon, source, sourceIcon, category);
    }

    @Override
    public void checkChatList() {
//        int lastCount = PreferenceManager.getInt(PreferenceManager.Other.USER_CHAT_COUNT_HISTORY);
//        int count = OpimUiWrapper.getInstance().getChatListCacheCount();
//        PreferenceManager.setInt(PreferenceManager.Other.USER_CHAT_COUNT_HISTORY, count);
//        if (lastCount < 0) {
//            return;
//        }
//        if (lastCount - count > 15) {
//            OpimUiWrapper.getInstance().reLoadRecentToView();
//            CrashReport.postCatchedException(new Exception("lastCount=" + lastCount + "  count=" + count));
//        }
    }

    @Override
    public String getContactPosition(String uId) {
        UiCommonInterface.ContactInfo contactInfo = OpimUiWrapper.getInstance().getContactInfoFromLocal(uId, getAppID());
        return contactInfo.position;
    }

    @Override
    public boolean isChattingClose() {
        return OpimUiWrapper.getInstance().isChattingClose();
    }

    @Override
    public void openRedpacketSetting(Activity activity, String pin, String cookie) {
//        RedpacketUtil.openSetting(activity, pin, cookie);
    }

    private static Activity getActivityFromView(View view) {
        Context context = view.getContext();
        while (context instanceof ContextWrapper) {
            if (context instanceof Activity) {
                return (Activity) context;
            }
            context = ((ContextWrapper) context).getBaseContext();
        }
        return null;
    }

    /*顶部左侧头像区域*/
    @Override
    public void initTopLeftView(ViewGroup parent) {
        if(parent == null) {
            return;
        }
        final Activity activity = getActivityFromView(parent);
        if (activity == null) {
            return;
        }
        mTopLeftViewRef = new WeakReference<>(parent);
        LayoutInflater.from(activity).inflate(R.layout.jdme_im_top_left, parent);
        ImageView iv_top_left_feeling = parent.findViewById(R.id.iv_top_left_feeling);
        updateCurrentUserInfo();

        //设置状态
        UserCenterModel userCenterModel = LocalConfigHelper.getInstance(activity).getUserCenterConfig();
        if(userCenterModel != null && userCenterModel.sign){
            iv_top_left_feeling.setVisibility(View.VISIBLE);
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            imDdService.registerUserStatusChangeListener(PreferenceManager.UserInfo.getUserName(), "initTopLeftView", new Callback3<String>() {
                @Override
                public void onSuccess(String icon, String title, String titleEn) {
                    if(!TextUtils.isEmpty(icon)){
                        iv_top_left_feeling.setVisibility(View.VISIBLE);
                        ImageLoader.load(activity,iv_top_left_feeling,icon,null);
                    }else{
                        iv_top_left_feeling.setVisibility(View.GONE);
                    }
                }
                @Override
                public void onFail(String msg) {

                }
            });
        }else{
            iv_top_left_feeling.setVisibility(View.GONE);
        }

        parent.setOnClickListener(v -> {
            if (NClick.isFastDoubleClick()) {
                return;
            }
            if(MultiAppConstant.isMeFlavor() && TabletUtil.isEasyGoEnable() && TabletUtil.isTablet()){
                appService.showPersonalCenterPopupWindow(activity,iv_top_left_feeling);
            }else{
                EventBusMgr.getInstance().post(EVENT_OPEN_DRAWER_OF_PERSONAL_CENTER);
            }
        });
    }

    @Override
    public void updateCurrentUserInfo(){
        if (mTopLeftViewRef == null) return;
        ViewGroup topLeftView = mTopLeftViewRef.get();
        if (topLeftView == null) return;
        final Activity activity = getActivityFromView(topLeftView);
        if (activity == null || activity.isDestroyed()) return;
        SuperEllipticalImageView iv_top_left_avatar = topLeftView.findViewById(R.id.iv_top_left_avatar);
        RedDotView rdv_top_left_badge = topLeftView.findViewById(R.id.rdv_top_left_badge);

        //更新提示角标
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        boolean mDarkMode = false;
        String imageType = "01";
        if(themeData != null){
            imageType = themeData.imageType;
            mDarkMode = "02".equals(imageType);
        }
        rdv_top_left_badge.setAppLinks(BadgeManager.BADGE_APP_UPDATE);
        rdv_top_left_badge.setImageDrawable(ContextCompat.getDrawable(activity,
                mDarkMode ? R.drawable.red_dot_color_white : R.drawable.red_dot_color_red));

        //更新头像
        ImageLoader.load(activity, iv_top_left_avatar,PreferenceManager.UserInfo.getUserCover(),R.drawable.jdme_profile_avatar_default);
    }

    /*
     * 初始化顶部自定义区域
     * */
    @Override
    public void initTopRightView(ViewGroup parent) {
        if (parent == null) {
            return;
        }
        if (VerifyUtils.isVerifyUser()) {
            return;
        }
//        mTopRightView = parent;
        final Activity activity = getActivityFromView(parent);
        if (activity == null) {
            return;
        }
        LayoutInflater.from(activity).inflate(R.layout.jdme_cmn_top_right, parent);
        // 通讯录跳转
        parent.findViewById(R.id.right_img_0).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isQuickClick()) {
                    return;
                }
                if (activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                Intent intent = new Intent(activity, FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, getContactSecondaryFragment());
                intent.putExtra(FunctionActivity.SHOW_ACTION, false);
                activity.startActivity(intent);
                clickTime = System.currentTimeMillis();
            }
        });

        if (QuickAppHelper.getInstance().disable()) {
            // 邮箱跳转
            if (TextUtils.isEmpty(PreferenceManager.UserInfo.getEmailAddress()) || !TenantConfigBiz.INSTANCE.isJoyMailEnable()) {
                parent.findViewById(R.id.right_img_1).setVisibility(View.GONE);
            }
//        boolean assistantEnable = ABTestManager.getInstance().getConfigByKey("mobile.ai.chat.enable", "0").equals("1");
//        parent.findViewById(R.id.right_img_2).setVisibility(assistantEnable ? View.VISIBLE : View.GONE);
            parent.findViewById(R.id.right_img_2).setVisibility(View.GONE);
            OpimUiWrapper.getInstance().setConferenceEntryVisible(true);
        } else {
            parent.findViewById(R.id.right_img_1).setVisibility(View.GONE);
        }
        boolean assistantEnable = ABTestManager.getInstance().getConfigByKey("mobile.ai.chat.enable", "0").equals("1");
        if (assistantEnable) {
            parent.findViewById(R.id.right_img_2).setVisibility(View.VISIBLE);
            parent.findViewById(R.id.right_img_2).getLayoutParams().width = UnitUtils.dip2px(activity, 22);
            parent.findViewById(R.id.right_img_2).getLayoutParams().height = UnitUtils.dip2px(activity, 22);
        }
        //根据配置判断Max、邮箱的显隐
        PlusMenuModel plusMenuModel = LocalConfigHelper.getInstance(activity).getPlusMenuConfig();
        if(plusMenuModel != null){
            boolean showMax = plusMenuModel.max > 0 && plusMenuModel.max < 900;
            boolean showEmail = plusMenuModel.email > 0 && plusMenuModel.email < 900;
            parent.findViewById(R.id.right_img_2).setVisibility(
                    (parent.findViewById(R.id.right_img_2).getVisibility() == View.VISIBLE
                            && showMax) ? View.VISIBLE : View.GONE);
            parent.findViewById(R.id.right_img_1).setVisibility(
                    (parent.findViewById(R.id.right_img_1).getVisibility() == View.VISIBLE
                            && showEmail) ? View.VISIBLE : View.GONE);
        }

        parent.findViewById(R.id.right_img_1).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isQuickClick()) {
                    return;
                }
                if (activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }

                if (!PreferenceManager.UserInfo.hasBindEmailAccount()) {
                    Router.build(DeepLink.EMAIL_BIND).go(activity);
                    return;
                }

//                Router.build(DeepLink.ROUTER_SCHEME_JDME + "web/************").go(AppBase.getTopActivity());
//                String mailUrl = "https://em.jd.com/mobileweb/webmail/index.html";
                String mailUrl = LocalConfigHelper.getInstance(activity).getUrlConstantsModel().getJoyMailUrl();
                Router.build(DeepLink.webUrl(mailUrl, 0)).go(activity);
//                Log.i("mailUrl====", "" + Uri.encode(mailUrl));
                clickTime = System.currentTimeMillis();
//                PageEventUtil.onEvent(activity, PageEventUtil.EVENT_MAIN_MAIL_CARD);
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_mail_icon_click, JDMAConstants.mobile_timline_mail_icon_click);
            }
        });

        parent.findViewById(R.id.right_img_2).setOnClickListener(v -> {
            if (isQuickClick()) {
                return;
            }
            if (activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            String deepLink = MeAiConfigHelper.getAppCenterUrl(activity, "Tool_Enter");
            if (!TextUtils.isEmpty(deepLink)) {
                Router.build(deepLink).go(activity);
            }
            clickTime = System.currentTimeMillis();
//                PageEventUtil.onEvent(activity, PageEventUtil.EVENT_MAIN_MAIL_CARD);
            JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_MEAI_IM_Main_ck, JDMAConstants.Mobile_Event_MEAI_IM_Main_ck);
        });

        if (QuickAppHelper.getInstance().disable()) {
            QuickAppHelper.getInstance().initSubContainer(parent.findViewById(R.id.right_img_1));
        }

        // 头部是否有MEAI
        if (parent.findViewById(R.id.right_img_2).getVisibility() == View.VISIBLE) {
            GuidePreference.getInstance().put(GuidePreference.KV_ENTITY_HOME_HAS_MEAI, true);
        } else {
            GuidePreference.getInstance().put(GuidePreference.KV_ENTITY_HOME_HAS_MEAI, false);
        }

        // 头部是否有Mail
        if (parent.findViewById(R.id.right_img_1).getVisibility() == View.VISIBLE) {
            GuidePreference.getInstance().put(GuidePreference.KV_ENTITY_HOME_HAS_MAIL, true);
        } else {
            GuidePreference.getInstance().put(GuidePreference.KV_ENTITY_HOME_HAS_MAIL, false);
        }
    }

    @Override
    public void refreshRightView(ViewGroup parent) {
        if (VerifyUtils.isVerifyUser()) {
            return;
        }
        if (QuickAppHelper.getInstance().disable()) {
            if (ThemeApi.isGlobal()) {
                if (ThemeApi.isDarkTheme()) {
                    ((ImageView) parent.findViewById(R.id.right_img_1)).setImageResource(R.drawable.jdme_sel_cmn_dd_mail_dark);
                    ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title_dark);
                } else {
                    ((ImageView) parent.findViewById(R.id.right_img_1)).setImageResource(R.drawable.jdme_sel_cmn_dd_mail);
                    ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title);
                }
            } else {
                ((ImageView) parent.findViewById(R.id.right_img_1)).setImageResource(R.drawable.jdme_sel_cmn_dd_mail);
                ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title);
            }
        } else {
            if (ThemeApi.isGlobal()) {
                if (ThemeApi.isDarkTheme()) {
                    ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title_dark);
                } else {
                    ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title);
                }
            } else {
                ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title);
            }
            QuickAppHelper.getInstance().refreshSubContainer(parent.findViewById(R.id.right_img_1));
        }

    }

    @Override
    public void setDefaultRight(ViewGroup parent) {
        if (VerifyUtils.isVerifyUser()) {
            return;
        }
        if (QuickAppHelper.getInstance().disable()) {
            ((ImageView) parent.findViewById(R.id.right_img_1)).setImageResource(R.drawable.jdme_sel_cmn_dd_mail);
            ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title);
        } else {
            ((ImageView) parent.findViewById(R.id.right_img_2)).setImageResource(R.drawable.jdme_icon_ai_im_title);
            QuickAppHelper.getInstance().setDefaultSubContainer(parent.findViewById(R.id.right_img_1));
        }
    }

    /*
     * 设置加好友消息未读数
     * */
    @Override
    public void setRosterUnread(int unread) {
        if (rosterUnReadLisener != null) {
            rosterUnReadLisener.refresh(unread);
        }
//        TextView tvUnread = mTopRightView.findViewById(R.id.right_tip_0);
//        if (unread <= 0) {
//            tvUnread.setVisibility(View.GONE);
//        } else {
//            tvUnread.setVisibility(View.VISIBLE);
//            tvUnread.setText(unread + "");
//        }
    }

    AppService.UnReadLisener rosterUnReadLisener = null;

    @Override
    public void registerRosterUnReadLisener(AppService.UnReadLisener lisener) {
        if (lisener != null) {
            rosterUnReadLisener = lisener;
        }
    }

    @Override
    public String getPendan() {
        String uId = PreferenceManager.UserInfo.getUserName();
        UiCommonInterface.ContactInfo contactInfo = OpimUiWrapper.getInstance().getContactInfoFromLocal(uId, getAppID());
        if (null == contactInfo) {
            return "";
        }
        return contactInfo.pendant;
    }

    @Override
    public void getPendanByNet(final Callback<String> callback) {
        String uId = PreferenceManager.UserInfo.getUserName();
        // 只读取  pendant 传 0。详情见群 10203146221
        OpimUiWrapper.getInstance().getContactInfoFromNet(uId, getAppID(), 0, new UiCommonInterface.ContactInfoGetListener() {
            @Override
            public void onInfoResult(UiCommonInterface.ContactInfo contactInfo) {
                if (null != contactInfo) {
                    callback.onSuccess(contactInfo.pendant);
                }
            }
        });
    }


    @Override
    public void pushJump(Context context, String msgType, int sessionType, String gid, String senderPin, String senderApp, String toPin, String toApp, String packetId, String mid, String subType, String noticeInfo) {
        OpimUiWrapper.getInstance().pushJump(context, msgType, sessionType, gid, senderPin, senderApp, toPin, toApp, packetId, mid, subType, noticeInfo);
    }

    @Override
    public Fragment showBottomChat(String to, String toApp, boolean isGroup, int chatType) {
        return OpimUiWrapper.getInstance().showBottomChat(to, toApp, isGroup, chatType);
    }

    @Override
    public void onFullScreenEnd(Activity activity) {
        OpimUiWrapper.getInstance().onFullScreenEnd(activity);
    }

    @Override
    public void wardRedPacket(Activity activity, String to, String toApp, String actId, String singleAmountStr, String remark) {
        OpimUiWrapper.getInstance().rewardRedPacket(activity, to, toApp, actId, singleAmountStr, remark);
    }

    @Override
    public void showSessionTagSettingActivity(Context context) {
        OpimUiWrapper.getInstance().showSessionTagSettingActivity(context);
    }

    @Override
    public void getUnReadCount(final IMUnReadCallback callback) {
        OpimUiWrapper.getInstance().getUnReadCount(new UnReadCallBack() {
            @Override
            public void unReadCount(int i) {
                callback.unReadCount(i);
            }
        });
    }

    @Override
    public void getUnReadApplyRoster(final IMUnReadCallback callback) {
        OpimUiWrapper.getInstance().getUnReadApplyRoster(new UiCommonInterface.ApplyRosterCount() {
            @Override
            public void unReadApplyRosterCount(int i) {
                callback.unReadCount(i);
            }
        });
    }

    @Override
    public void sendTaskCardMsg(Context context, String to, String toApp, String gid, String jsonBean) {
        Context from = AppBase.getTopActivity();
        if (from == null) {
            if (context == null) {
                from = Utils2App.getApp();
            } else {
                from = context;
            }
        }
        Intent sendIntent = new Intent(from, ActivityPictureShare.class);
        sendIntent.setAction(Intent.ACTION_SEND);
        sendIntent.setType(ShareUtil.ShareType.TYPE_JD_TASK_CARD);
        sendIntent.putExtra("jdim_taskCard", jsonBean);
        if (!(from instanceof Activity)) {
            sendIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        from.startActivity(sendIntent);
    }

    @Override
    public void createGroup(Context context, String sourceId, String rKey, ArrayList<MemberEntityJd> users, @DDGroupMode int mode, String groupName, boolean canSearch, boolean gotoEdit, final LoadDataCallback<String> callback) {
        GroupExternal.GroupMode groupMode;
        if (ImDdService.GROUP_MODE_JD_INTERNAL == mode) {
            groupMode = GroupExternal.GroupMode.JD_INTERNAL;
        } else if (ImDdService.GROUP_MODE_JD_EXTERNAL == mode) {
            groupMode = GroupExternal.GroupMode.JD_EXTERNAL;
        } else if (ImDdService.GROUP_MODE_TRADE_EXTERNAL == mode) {
            groupMode = GroupExternal.GroupMode.TRADE_EXTERNAL;
        } else {
            callback.onDataNotAvailable("illegal group mode: " + mode, 1);
            return;
        }

        if (users == null) {
            callback.onDataNotAvailable("users cant be null", 1);
            return;
        }

        ArrayList<MemberEntity> list = new ArrayList<>();
        for (int i = 0; i < users.size(); i++) {
            list.add(EntityTools.getMemberEntity(users.get(i)));
        }

        OpimUiWrapper.getInstance().createGroupForExternal(context, sourceId, rKey, list, groupMode, groupName, canSearch, gotoEdit, new GroupCreateCallback() {
            @Override
            public void onSuccess(@NonNull GroupCreateSuccessResult groupCreateSuccessResult) {
                callback.onDataLoaded(groupCreateSuccessResult.gid);
            }

            @Override
            public void onFail(@NonNull GroupCreateFailResult groupCreateFailResult) {
                callback.onDataNotAvailable(groupCreateFailResult.msg, groupCreateFailResult.type);
            }

            @Override
            public void onTimeOut() {
                callback.onDataNotAvailable("timeout", 3);
            }
        });
    }


    @Override
    public void openChat(Context context, String appId, String pin, String groupId, final LoadDataCallback<Void> callback) {
        openChat(context, appId, pin, groupId, 1, callback);
    }

    @Override
    public void openChat(Context context, String appId, String pin, String groupId, int chatType, final LoadDataCallback<Void> callback) {
        OpimUiWrapper.getInstance().openChatForExternal(context, pin, appId, groupId, chatType, new GroupExternal.GroupChatCallback() {
            @Override
            public void onSuccess() {
                callback.onDataLoaded(null);
            }

            @Override
            public void onFail(int i) {
                //1是群不存在，，2是你不在群里
                String message;
                if (i == 1) {
                    message = "group not exist";
                } else if (i == 2) {
                    message = "you are not in this group";
                } else {
                    message = "unknown";
                }
                callback.onDataNotAvailable(message, i);
            }

            @Override
            public void onTimeOut() {
                callback.onDataNotAvailable("timeout", 3);
            }
        });
    }

    /**
     * 1
     * 加群成功
     * <p>
     * 236001
     * 你输入的验证码不正确
     * <p>
     * 236023
     * 发送请求次数过多，请24小时之后再试
     * <p>
     * 236035
     * 对不起，您在1小时之内超过3次输入错误验证码，请1小时后再尝试
     * <p>
     * 236024
     * 入群申请已成功发送
     * <p>
     * 其他
     * 其他错误导致失败
     *
     * @param gid
     * @param sCode
     * @param callback
     */
    @Override
    public void joinGroup(String gid, String sCode, final LoadDataCallback<Void> callback) {
        OpimUiWrapper.getInstance().joinGroupForExternal(gid, sCode, new GroupExternal.GroupInCallback() {
            @Override
            public void onResult(int code) {
                if (code == 1 || code == 236024) {
                    callback.onDataLoaded(null);
                } else {
                    String message;
                    if (code == 236001) {
                        message = "verify code not correct";
                    } else if (code == 236023) {
                        message = "Too many requests have been sent, please try again in 24 hours";
                    } else if (code == 236035) {
                        message = "Sorry, you have entered the wrong verification code more than 3 times within 1 hour, please try again in 1 hour";
                    } else {
                        message = "unknown error";
                    }
                    callback.onDataNotAvailable(message, code);
                }
            }

            @Override
            public void onTimeOut() {
                callback.onDataNotAvailable("timeout", 3);
            }
        });
    }

    @Override
    public void sendTextCard(String jsonData, LoadDataCallback<Void> callback) {
        try {
            MELogUtil.localD(MELogUtil.TAG_IM, "sendTextCard data = " + jsonData);
            MemberListEntity entity = new MemberListEntity();
            entity.setFrom(MemberListEntity.TYPE_SEND_TO_OTHER);

            SelectorParam selectorParam = new SelectorParam.Builder().build();
            selectorParam.setSelectMode(SelectorConfig.SelectMode.SELECT_MODE_SINGLE);

            SendMsgInfoText msgInfoText = new SendMsgInfoText();
            SendMsgInfoText.MsgData msgData = new SendMsgInfoText.MsgData();
            ArrayList atUsers = new ArrayList<SendMsgInfoText.User>();

            JSONObject jsonObject = new JSONObject(jsonData);
            if (jsonObject.has("atUsers")) {
                JSONArray jsonUsers = jsonObject.optJSONArray("atUsers");
                for (int i = 0; i < jsonUsers.length(); i++) {
                    SendMsgInfoText.User user = new SendMsgInfoText.User();
                    JSONObject obj = jsonUsers.optJSONObject(i);
                    user.setPin(obj.optString("pin"));
                    user.setNickname(obj.optString("nickname"));
                    user.setApp(obj.optString("app"));
                    atUsers.add(user);
                }
                msgData.setAtUsers(atUsers);
            }
            boolean multiple = jsonObject.optBoolean("multiple");
            if (multiple) {
                selectorParam.setSelectMode(SelectorConfig.SelectMode.SELECT_MODE_MULTI);
            } else {
                selectorParam.setSelectMode(SelectorConfig.SelectMode.SELECT_MODE_SINGLE);
            }

            String content = jsonObject.optString("content");
            msgData.setContent(content);
            msgInfoText.setData(msgData);

            //构造HookProcess,如果没有customPage参数则返回null值
            final HookProcess hookProcess = getHookProcess(callback, jsonObject);
            new Handler(Looper.getMainLooper()).post(() -> OpimUiWrapper.getInstance().openSelectorAndSendMsg(entity, selectorParam, msgInfoText, new SendResultCallback() {
                @Override
                public void onSuccess(@androidx.annotation.Nullable List<? extends MemberEntity> list, String businessKey) {
                    if (!TextUtils.isEmpty(businessKey)) { //流程结束后关闭页面
                        finishHookProcess(hookProcess, businessKey);
                    }
                }

                @Override
                public void onCancel() {
                    if (callback != null) {
                        callback.onDataNotAvailable("sendTextCard canceled", ErrorCode.CANCEL);
                    }
                }
            }, hookProcess));
            callback.onDataLoaded(null);
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_IM, "sendTextCard exception data = " + jsonData, e);
            if (callback != null) {
                callback.onDataNotAvailable("send card failed", -1);
            }
        }
    }

    @Override
    public void sendShareLink(String jsonData, LoadDataCallback<Void> callback) {
        try {
            MELogUtil.localD(MELogUtil.TAG_IM, "sendShareLink data = " + jsonData);
            MemberListEntity entity = new MemberListEntity();
            entity.setFrom(MemberListEntity.TYPE_SEND_TO_OTHER);

            SelectorParam selectorParam = new SelectorParam.Builder().build();
            selectorParam.setSelectMode(SelectorConfig.SelectMode.SELECT_MODE_SINGLE);

            SendMsgInfoShareLink msgInfo = new SendMsgInfoShareLink();
            SendMsgInfoShareLink.MsgData msgData = new SendMsgInfoShareLink.MsgData();
            JSONObject jsonObject = new JSONObject(jsonData);
            msgData.setTitle(jsonObject.optString("title"));
            msgData.setContent(jsonObject.optString("content"));
            msgData.setUrl(jsonObject.optString("url"));
            msgData.setIcon(jsonObject.optString("icon"));
            msgData.setSource(jsonObject.optString("source"));
            msgData.setSourceicon(jsonObject.optString("sourceicon"));
            msgData.setCategory(jsonObject.optString("category"));
            msgInfo.setData(msgData);

            //构造HookProcess,如果没有customPage参数则返回null值
            final HookProcess hookProcess = getHookProcess(callback, jsonObject);
            new Handler(Looper.getMainLooper()).post(() -> OpimUiWrapper.getInstance().openSelectorAndSendMsg(entity, selectorParam, msgInfo, new SendResultCallback() {
                @Override
                public void onSuccess(@androidx.annotation.Nullable List<? extends MemberEntity> list, String businessKey) {
                    if (!TextUtils.isEmpty(businessKey)) { //流程结束后关闭页面
                        finishHookProcess(hookProcess, businessKey);
                    }
                }

                @Override
                public void onCancel() {
                    if (callback != null) {
                        callback.onDataNotAvailable("sendShareLink canceled", ErrorCode.CANCEL);
                    }
                }
            }, hookProcess));
            callback.onDataLoaded(null);
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_IM, "sendShareLink exception data = " + jsonData, e);
            if (callback != null) {
                callback.onDataNotAvailable("sendShareLink failed", -1);
            }
        }
    }


    @Override
    public void sendJueCard(String jsonData, LoadDataCallback<Void> callback) {
        MELogUtil.localD(MELogUtil.TAG_IM, "sendJueCard data = " + jsonData);
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setFrom(MemberListEntity.TYPE_SEND_TO_OTHER);
        entity.setSelectMode(MemberListEntityJd.SELECT_MODE_SINGLE);

        openSelectorAndSendJueCard(jsonData, entity, new LoadDataCallback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onDataLoaded(ArrayList<MemberEntityJd> memberEntityJds) {
                callback.onDataLoaded(null);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (callback != null) {
                    callback.onDataNotAvailable(s, i);
                }
            }
        });
    }

    @Override
    public void sendJueCardToChat(String pin, String app, boolean isGroup, String source, String jsonData, LoadDataCallback<Void> callback) {
        try {
            SendMsgInfoDynamic.MsgData data = JsonUtils.getGson().fromJson(jsonData, SendMsgInfoDynamic.MsgData.class);
            new Handler(Looper.getMainLooper()).post(() -> OpimUiWrapper.getInstance().sendJueCard(pin, app, isGroup, source, data));
            callback.onDataLoaded(null);
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_IM, "sendJueCardToChat exception data = " + jsonData, e);
            if (callback != null) {
                callback.onDataNotAvailable("sendShareLink failed", -1);
            }
        }
    }

    @Override
    public void openSelectorAndSendJueCard(String jsonData, MemberListEntityJd entityJd, LoadDataCallback<ArrayList<MemberEntityJd>> callback) {
        try {
            MELogUtil.localD(MELogUtil.TAG_IM, "openSelectorAndSendJueCard = " + jsonData);
            MemberListEntity entity = EntityTools.getMemberListEntity(entityJd);

            SendMsgInfoDynamic msgInfo = new SendMsgInfoDynamic();
            SendMsgInfoDynamic.MsgData msgData = new SendMsgInfoDynamic.MsgData();
            JSONObject jsonObject = new JSONObject(jsonData);
            msgData.setTemplateId(jsonObject.optString("templateId"));
            msgData.setAppId(jsonObject.optString("appId"));
            msgData.setReload(jsonObject.optBoolean("reload"));
            msgData.setSummary(jsonObject.optString("summary"));
            if (jsonObject.has("cardData")) {
                Map<Object, Object> mCardData = JsonUtils.getGson().fromJson(jsonObject.optJSONObject("cardData").toString(), new TypeToken<Map<Object, Object>>() {
                }.getType());
                msgData.setCardData(mCardData);
            }
            if (jsonObject.has("callbackData")) {
                Map<Object, Object> mCallbackData = JsonUtils.getGson().fromJson(jsonObject.optJSONObject("callbackData").toString(), new TypeToken<Map<Object, Object>>() {
                }.getType());
                msgData.setCallbackData(mCallbackData);
            }
            if (jsonObject.has("forward")) {
                JSONObject jsonForword = jsonObject.optJSONObject("forward");
                TcpChatMessageTemplate2DynamicBase.Forward forward = new TcpChatMessageTemplate2DynamicBase.Forward();
                forward.reload = jsonForword.optBoolean("reload");
                forward.flowCard = jsonForword.optBoolean("flowCard");
                forward.completed = jsonForword.optBoolean("completed");
                // forward
                if (jsonForword.has("cardData")) {
                    Map<Object, Object> mForwardData = JsonUtils.getGson().fromJson(jsonForword.getJSONObject("cardData").toString(), new TypeToken<Map<Object, Object>>() {
                    }.getType());
                    forward.cardData = mForwardData;
                }
                msgData.setForward(forward);
            }

            // At
            if (jsonObject.has("at")) {
                JSONObject jsonAt = jsonObject.optJSONObject("at");
                TcpChatMessageTemplate2DynamicBase.At at = new TcpChatMessageTemplate2DynamicBase.At();
                at.atAll = jsonAt.optBoolean("atAll");
                JSONArray jsonUsers = jsonAt.optJSONArray("users");
                ArrayList atUsers = new ArrayList<TcpChatMessageTemplate2DynamicBase.User>();
                for (int i = 0; i < jsonUsers.length(); i++) {
                    TcpChatMessageTemplate2DynamicBase.User user = new TcpChatMessageTemplate2DynamicBase.User();
                    JSONObject obj = jsonUsers.getJSONObject(i);
                    user.pin = obj.optString("pin");
                    user.app = obj.optString("app");
                    atUsers.add(user);
                }
                at.users = atUsers;
                msgData.setAt(at);
            }
            msgData.setFlowCard(jsonObject.optBoolean("flowCard"));
            msgData.setCompleted(jsonObject.optBoolean("completed"));
            msgInfo.setData(msgData);

            //创建final变量，供匿名函数使用
            final HookProcess hookProcess = getHookProcess(callback, jsonObject);
            if (hookProcess != null) {
                //设置留言框
                entityJd.sendDirectly = true;
            }
            SelectorParam selectorParam = EntityTools.getSelectParam(entityJd);
            new Handler(Looper.getMainLooper()).post(() -> OpimUiWrapper.getInstance().openSelectorAndSendMsg(entity, selectorParam, msgInfo, new SendResultCallback() {
                @Override
                public void onSuccess(@Nullable List<? extends MemberEntity> list, String businessKey) {
                    if (callback != null) {
                        ArrayList<MemberEntityJd> members = new ArrayList<>();
                        for (int i = 0; i < list.size(); i++) {
                            members.add(EntityTools.getMemberEntityJd(list.get(i)));
                        }
                        callback.onDataLoaded(members);
                    }
                    if (!TextUtils.isEmpty(businessKey)) { //流程结束后关闭页面
                        finishHookProcess(hookProcess, businessKey);
                    }
                }

                @Override
                public void onCancel() {
                    if (callback != null) {
                        callback.onDataNotAvailable("sendJueCard canceled", ErrorCode.CANCEL);
                    }
                }
            }, hookProcess));
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_IM, "sendJueCard exception data = " + jsonData, e);
            if (callback != null) {
                callback.onDataNotAvailable("sendJueCard failed", ErrorCode.EXCEPTION);
            }
        }
    }

    @androidx.annotation.Nullable
    private HookProcess getHookProcess(LoadDataCallback<?> callback, JSONObject jsonObject) {
        final String SCENE_CHAT_SELECTOR = "chatSelector";
        // Hook Process
        HookProcess tempHookProcess = null;
        if (jsonObject.has("customPageAppId")) {
            final String customPageUrl = jsonObject.optString("customPageUrl", "");
            final String customPageAppId = jsonObject.optString("customPageAppId", "");
            if (!TextUtils.isEmpty(customPageAppId)) { //appId必填
                //交给IM打开自定义页面
                tempHookProcess = (list, hookResult) -> {
                    /* 通过deeplink打开自定义页面 like
                    jdme://jm/sys/browser?mparam={"url":"https://www.xxxxxxx","appId":"xxxxxx"}&bizparam={"sceneId":"chatSelector","type":2}
                     */
                    try {
                        //处理已选联系人数据
                        JSONObject data = memberListToContextOptions(list);
                        //暂存数据
                        CrossPlatformPreference.getDefault().setContextOptions(SCENE_CHAT_SELECTOR, 2, data.toString());

                        //拼接deeplink
                        JSONObject mparam = new JSONObject();
                        mparam.put("url", customPageUrl);
                        mparam.put("appId", customPageAppId);
                        JSONObject bizParam = new JSONObject();
                        bizParam.put("sceneId", SCENE_CHAT_SELECTOR);
                        bizParam.put("type", 2);
                        Uri.Builder uri = Uri.parse(DeepLink.BROWSER).buildUpon();
                        String deeplink = uri.appendQueryParameter(DeepLink.DEEPLINK_PARAM, mparam.toString())
                                .appendQueryParameter(DeepLink.DEEPLINK_BIZPARAM, bizParam.toString()).toString();

                        //注册Deeplink回调
                        final String callbackId = String.valueOf(hookResult.hashCode());
                        //检查deeplink回调是否已经被注册
                        JmEventProcessor processor = JmEventDispatcher.firstOf(DeeplinkCallbackProcessor.callbackPredicate(callbackId));
                        if (processor == null) {
                            IServiceCallback<String> wrapper = (success, s, error) -> { //sendOperationResult回调部分，业务端已处理好联系人选择
                                if (TextUtils.isEmpty(s)) { //sendOperationResult返回空数据
                                    callback.onDataNotAvailable("sendOperationResult returned empty data", ErrorCode.EXCEPTION);
                                    return;
                                }
                                //解析sendOperationResult返回的数据，必须是MemberEntityResponse的格式
                                MemberEntityResponse memberEntityResponse = JsonUtils.getGson().fromJson(s, MemberEntityResponse.class);
                                if (memberEntityResponse == null || memberEntityResponse.data == null || memberEntityResponse.data.isEmpty()) {
                                    //sendOperationResult返回数据无法被正确解析
                                    callback.onDataNotAvailable("sendOperationResult returned empty or wrong data", ErrorCode.EXCEPTION);
                                    return;
                                }
                                if (!validateMemberEntity(memberEntityResponse.data, list)) { //sendOperationResult返回数据存在不匹配的情况
                                    callback.onDataNotAvailable("sendOperationResult returned wrong data", ErrorCode.EXCEPTION);
                                    return;
                                }
                                //通知IM筛选后的结果
                                Context context = AppBase.getTopActivity();
                                if (context != null) {
                                    hookResult.onHookResult(context, getBaseSessionList(memberEntityResponse.data), callbackId);
                                } else {
                                    callback.onDataNotAvailable("can't retrieve topActivity after success", ErrorCode.EXCEPTION);
                                }
                            };
                            //注册deeplink回调
                            JmEventDispatcher.registerProcessor(new DeeplinkCallbackProcessor(callbackId, wrapper));
                        }
                        deeplink = DeeplinkCallbackProcessor.appendCallbackId(deeplink, callbackId);

                        //deeplink跳转
                        Router.build(deeplink).go(AppBase.getTopActivity());
                    } catch (Exception e) {
                        MELogUtil.localE(MELogUtil.TAG_IM, "openSelectorAndSendJueCard exception", e);
                        callback.onDataNotAvailable("openSelectorAndSendJueCard failed", ErrorCode.EXCEPTION);
                    }
                };
            }
        }
        return tempHookProcess;
    }

    @NonNull
    private static JSONObject memberListToContextOptions(List<? extends MemberEntity> list) throws JSONException {
        JSONObject options = new JSONObject();
        if (list == null) {
            return options;
        }
        JSONArray dataArray = new JSONArray();
        for (MemberEntity memberEntity : list) {
            JSONObject memberData = new JSONObject();
            if (!memberEntity.isGroup()) {
                memberData.put("userId", memberEntity.mId);
                memberData.put("appId", memberEntity.mApp);
                memberData.put("sessionType", 0);
            } else {
                memberData.put("groupId", memberEntity.mId);
                memberData.put("sessionType", 1);
            }
            memberData.put("name", memberEntity.mName);
            memberData.put("avatar", memberEntity.mAvatar);
            dataArray.put(memberData);
        }
        JSONObject data = new JSONObject();
        data.put("data", dataArray);
        options.put("options", data);
        return options;
    }

    /**
     * 校验联系人数据，业务端返回的数据里是否有未选中的联系人
     */
    private boolean validateMemberEntity(List<MemberEntityShort> shortList, List<? extends MemberEntity> entityList) {
        if (shortList == null || entityList == null) {
            return false;
        }
        if (shortList.size() > entityList.size()) {
            return false;
        }

        Map<String, MemberEntity> entityMap = new HashMap<>();

        for (MemberEntity entity : entityList) {
            String key = entity.isGroup() ? entity.mId : entity.mId + "_" + entity.mApp;
            entityMap.put(key, entity);
        }

        for (MemberEntityShort shortEntity : shortList) {
            String key = shortEntity.isGroup() ? shortEntity.groupId : shortEntity.userId + "_" + shortEntity.appId;
            if (!entityMap.containsKey(key)) {
                return false;
            }
            entityMap.remove(key);
        }

        return true;
    }

    /**
     * HookProcess结束后关闭页面
     */
    private void finishHookProcess(HookProcess hookProcess, String callbackId) {
        if (hookProcess == null || TextUtils.isEmpty(callbackId)) {
            return;
        }

        Activity topActivity = AppBase.getTopActivity();
        if (!(topActivity instanceof FunctionActivity)) {
            return;
        }

        Fragment topFragment = ((FunctionActivity) topActivity).getFragment();
        if (!(topFragment instanceof WebFragment2)) {
            return;
        }
        //获取fragmentDeepLinkCallbackId，如果为空说明当前fragment没有deeplink回调
        String fragmentCallBackId = ((WebFragment2) topFragment).getDeepLinkCallbackId();
        if (TextUtils.isEmpty(fragmentCallBackId)) {
            return;
        }
        //DeepLinkCallbackId不为空，且和当前HookProcess的callbackId一致，安全关闭页面
        if (fragmentCallBackId.equals(callbackId) && !topActivity.isFinishing() && !topActivity.isDestroyed()) {
            topActivity.finish();
        }
    }

    private List<? extends BaseSession> getBaseSessionList(List<MemberEntityShort> data) {
        if (data == null || data.isEmpty()) return null;
        List<BaseSession> result = new ArrayList<>();
        for (MemberEntityShort entity : data) {
            if (!TextUtils.isEmpty(entity.groupId)) {
                result.add(new GroupSession(entity.groupId));
            } else {
                result.add(new SingleSession(entity.userId, entity.appId));
            }
        }
        return result;
    }

    private long clickTime = 0;
    private final long MIN_DELAY_TIME = 1000;

    private boolean isQuickClick() {
        return System.currentTimeMillis() - clickTime < MIN_DELAY_TIME;
    }


    @Override
    public Fragment getUnifiedSearchFragment(String type) {
        return UnifiedSearchFragment.newInstance(type);
    }

    @Override
    public void appOnCreate(Application application) {
        String pn = application.getPackageName();
        Log.d("fengyiyi", pn);
        PUiApplication.initContextInject(application);
    }

    @Override
    public void getContactInfo(String app, String erp, final Callback<MemberEntityJd> callback) {
        UiCommonInterface.ContactInfo info = OpimUiWrapper.getInstance().getContactInfoFromLocal(erp, app);
        if (info != null) {
            callback.onSuccess(contactInfoToEntity(info));
            return;
        }
        OpimUiWrapper.getInstance().getContactInfoFromNet(erp, app, 0,
                contactInfo -> callback.onSuccess(contactInfoToEntity(contactInfo)));
    }

    @Override
    public void getContactInfoFromNet(String app, String erp, Callback<MemberEntityJd> callback) {
        OpimUiWrapper.getInstance().getContactInfoFromNet(erp, app, 0,
                contactInfo -> callback.onSuccess(contactInfoToEntity(contactInfo)));
    }

    @Override
    public void joinTimlineMeeting(Activity activity, String meetingId, Long meetingCode) {
        OpimUiWrapper.getInstance().joinConference(activity, meetingId, meetingCode);
    }

    private MemberEntityJd contactInfoToEntity(UiCommonInterface.ContactInfo info) {
        MemberEntityJd entity = new MemberEntityJd();
        entity.setApp(info.app);
        entity.setId(info.uid);
        entity.setAvatar(info.avatar);
        entity.setDepartment(info.department);
        entity.setPosition(info.position);
        entity.setName(info.nickName);
        entity.setEmail(info.email);
        return entity;
    }

    /******************************  心情状态  ******************************/

    private final Map<String, UiCommonInterface.UserStatusGetListener> mListenerCache = new HashMap<>();

    @Override
    public void registerUserStatusChangeListener(String erp, String listenerId, final Callback3<String> callback) {
        unregisterUserStatusChangeListener(listenerId);//删除上一个listener，否则出现多次回调
        UiCommonInterface.UserStatusGetListener newListener = new UiCommonInterface.UserStatusGetListener() {
            @Override
            public void onGetUserStatus(UiCommonInterface.Status status) {
                if (callback != null) {
                    if (status != null) {
                        callback.onSuccess(status.icon, status.title, status.titleEn);
                    } else {
                        callback.onSuccess(null, null, null);//选择了无状态，没选择不会回调
                    }
                }
            }
        };
        mListenerCache.put(listenerId, newListener);
        String appId = getAppID();
        if(!TextUtils.isEmpty(appId)){
            appId = appId.toLowerCase();
        }
        if(!TextUtils.isEmpty(erp)){
            erp = erp.toLowerCase();
        }
        OpimUiWrapper.getInstance().registeUserStatus(erp, appId, newListener);
    }

    @Override
    public void unregisterUserStatusChangeListener(String listenerId) {
        UiCommonInterface.UserStatusGetListener listener = mListenerCache.get(listenerId);
        if (listener != null) {
            OpimUiWrapper.getInstance().unregisteUserStatus(listener);
            mListenerCache.remove(listenerId);
        }
    }

    @Override
    public void openSetUserStatus(Context context) {
        OpimUiWrapper.getInstance().openSetUserStatus(context);
    }

    /**
     * 跳转聊天管理页
     *
     * @param context
     */
    @Override
    public void showMessageMgr(Context context) {
        OpimUiWrapper.showMessageMgr(context);
    }


    @Override
    public boolean isChattingFragmentShow() {
        return OpimUiWrapper.getInstance().isChattingViewShow();
    }


    public void sendProcessCenterCard(String to, String toApp, String gid, boolean secret, ShareCardBean bean) {
        ProcessCenterBean processCenterBean = new ProcessCenterBean();
        processCenterBean.summary = bean.summary;
        processCenterBean.contentImage = bean.contentImage;
        processCenterBean.contentText = bean.contentText;
        processCenterBean.titleIcon = bean.titleIcon;
        processCenterBean.titleText = bean.titleText;

        processCenterBean.linkInfo = new ProcessCenterBean.LinkInfo();
        processCenterBean.linkInfo.defaultUrl = bean.linkInfo.defaultUrl;
        processCenterBean.linkInfo.mac = bean.linkInfo.mac;
        processCenterBean.linkInfo.ipad = bean.linkInfo.ipad;
        processCenterBean.linkInfo.pc = bean.linkInfo.pc;
        processCenterBean.linkInfo.mobile = bean.linkInfo.mobile;
        OpimUiWrapper.getInstance().sendProcessCenterCard(to, toApp, gid, secret, processCenterBean);
    }

    @Override
    public void sendVideoConferenceMsg(String gid, String to, String toApp, ShareMeetingBean shareBean) {
        ConferenceBean bean = new ConferenceBean();
        bean.code = shareBean.code;
        bean.id = shareBean.id;
        bean.hostApp = shareBean.hostApp;
        bean.hostPin = shareBean.hostPin;
        bean.status = shareBean.status;
        bean.hostDisplayName = shareBean.hostDisplayName;
        bean.hostTeamId = shareBean.hostTeamId;
        bean.participantCount = shareBean.participantCount;
        bean.password = shareBean.password;
        bean.startTime = shareBean.startTime;
        bean.subject = shareBean.subject;
        OpimUiWrapper.getInstance().sendVideoConferenceMsg(gid, to, toApp, bean);
    }

    @Override
    public void openSignatureEdit(AppCompatActivity context, String title, String hint, final Callback<CharSequence> callback) {
        ActivityResultListener listener = new ActivityResultListener() {
            @Override
            public void onResult(int resultCode, @Nullable Intent data) {
                if (resultCode == TimlineActivitySetUser.RESULT_CODE_CHANGED && data != null) {
                    String value = data.getStringExtra(TimlineActivitySetUser.EXTRA_FIELD_VALUE);
                    ArrayList<AtUser> atUsers = (ArrayList<AtUser>) data.getSerializableExtra(TimlineActivitySetUser.EXTRA_FIELD_ATUSERS);
                    if (callback != null) {
                        callback.onSuccess(OpimUiWrapper.getInstance().formatSignature(value, atUsers));
                    }
                }
            }
        };
        String sign = "";
        ArrayList<AtUser> atUsers = new ArrayList<>();
        Pair<String, ArrayList<AtUser>> result = ImUserInfoManager.getMySignatureNew();
        if (result != null && result.first != null) {
            sign = result.first;
        }
        if (result != null && result.second != null) {
            atUsers = result.second;
        }
        OpimUiWrapper.getInstance().openSignatureEdit(context, title, sign, atUsers, hint, listener);
    }

    @Override
    public CharSequence getMyFormatSignature() {
        String sign = "";
        ArrayList<AtUser> atUsers = new ArrayList<>();
        Pair<String, ArrayList<AtUser>> result = ImUserInfoManager.getMySignatureNew();
        if (result != null && result.first != null) {
            sign = result.first;
        }
        if (result != null && result.second != null) {
            atUsers = result.second;
        }
        return OpimUiWrapper.getInstance().formatSignature(sign, atUsers);
    }

    @Override
    public void showNoticeSetGuide(AppCompatActivity activity) {
        OpimUiWrapper.getInstance().showNoticeSetGuide(activity);
    }

    @Override
    public void addBannerData(boolean request) {
        OpimUiWrapper.getInstance().addBannerData();
    }

    @Override
    public View getMainTabView() {
        return OpimUiWrapper.getInstance().getMainTabView();
    }

    @Override
    public void hideBanner() {
        OpimUiWrapper.getInstance().removeBanner();
    }

    @Override
    public void registerModule() {
        OpimUiWrapper.getInstance().registerModule();
    }

    @Override
    public void initIMFile() {
        OpimUiWrapper.getInstance().initFilePath();
    }

    @Override
    public Fragment getSearchFragment(String searchTabType, String bizParam) {
        if (UnifiedSearchTabDelegate.TYPE_APP.equals(searchTabType)) {
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_APP);
        } else if (UnifiedSearchTabDelegate.TYPE_ALL.equals(searchTabType)) {
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_ALL);
        } else if (UnifiedSearchTabDelegate.TYPE_JOYSPACE.equals(searchTabType)) {
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_JOYSPACE);
        } else if (UnifiedSearchTabDelegate.TYPE_JOYDAY.equals(searchTabType)) {
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_JOYDAY);
        } else if (UnifiedSearchTabDelegate.TYPE_MESSAGE.equals(searchTabType)) {
            return OpimUiWrapper.getInstance().getSearchFragment(SearchTabType.MESSAGE, bizParam);
        } else if (UnifiedSearchTabDelegate.TYPE_GROUP.equals(searchTabType)) {
            return OpimUiWrapper.getInstance().getSearchFragment(SearchTabType.GROUP, bizParam);
        } else if (UnifiedSearchTabDelegate.TYPE_CONTACT.equals(searchTabType)) {
            return OpimUiWrapper.getInstance().getSearchFragment(SearchTabType.CONTACT, bizParam);
        } else if (UnifiedSearchTabDelegate.TYPE_NOTICE.equals(searchTabType)) {
            return OpimUiWrapper.getInstance().getSearchFragment(SearchTabType.NOTICE, bizParam);
        } else if (UnifiedSearchTabDelegate.TYPE_ROBOT.equals(searchTabType)) {
            return OpimUiWrapper.getInstance().getSearchFragment(SearchTabType.ROBOT, bizParam);
        } else if (UnifiedSearchTabDelegate.TYPE_WAITER.equals(searchTabType)) {
            return OpimUiWrapper.getInstance().getSearchFragment(SearchTabType.WAITER, bizParam);
        } else if (UnifiedSearchTabDelegate.TYPE_TASK.equals(searchTabType)) {
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_TASK);
        } else if (UnifiedSearchTabDelegate.TYPE_APPROVAL.equals(searchTabType)) {
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_APPROVAL);
        } else if (UnifiedSearchTabDelegate.TYPE_PROCESS.equals(searchTabType)) {
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_PROCESS);
        } else if (UnifiedSearchTabDelegate.TYPE_ORG.equals(searchTabType)){
            return getUnifiedSearchFragment(UnifiedSearchTabDelegate.TYPE_ORG);
        }
        return null;
    }

    public List<String> getSearchHistory() {
        return OpimUiWrapper.getInstance().getSearchHistory();
    }

    @Override
    public boolean hasSecretPermission() {
        return OpimUiWrapper.getInstance().hasSecretPermission();
    }

    @Override
    public void getSearchData(List<String> types, String keyWord, String sessionId, String requestId, final Callback4<String, List<?>> callback) {
        if (types == null || types.size() == 0) {
            return;
        }
        List<SearchType> searchTypes = new ArrayList<>();
        for (String type : types) {
            searchTypes.add(ImSearchServiceImpl.getSearchType(type));
        }
        OpimUiWrapper.getInstance().search(searchTypes, keyWord, sessionId, requestId, new SearchResultCallback() {
            @Override
            public void onResult(@NonNull String s, @NonNull SearchType searchType, @androidx.annotation.Nullable List<?> list) {
                callback.onResult(s, searchType.toString(), list);
            }
        });
    }

    @Override
    public void searchBindUI(ViewGroup group, String searchType, Object data, String keyword, String sessionId, String searchId) {
        SearchType type = ImSearchServiceImpl.getSearchType(searchType);
        OpimUiWrapper.getInstance().bindUI(group, type, data, keyword, searchId, sessionId);
    }

    @Override
    public void getSearchTabConfig(final Callback5<String> callback) {
        OpimUiWrapper.getInstance().getSearchTabInfo(new OpimUiWrapper.LoadTabInfoResult() {
            @Override
            public void onResult(List<OpimUiWrapper.SearchTabInfo> list) {
                if (list != null) {
                    String strData = JsonUtils.getGson().toJson(list);
                    callback.onResult(strData);
                } else {
                    callback.onResult("[]");
                }
            }
        });
    }

    @Override
    public boolean hasWaiterPermission() {
        return OpimUiWrapper.getInstance().hasWaiterPermission();
    }

    @Override
    public void playVideo(Activity activity, String url, long duration) {
        VideoPlayerActivity.Companion.start(activity, url, duration);
    }

    @Override
    public boolean hasVoipCalling() {
        return CallingVoipUtils.Companion.getInstance().hasCalling();
    }

    @Override
    public void tabSelected(Fragment fragment) {
        if (fragment instanceof FragmentCollect) {
            ((FragmentCollect) fragment).onCollectShow(true);
        }
    }

    @Override
    public String getQuickMenuSelectedMessage() {
        return ChatQuickOperationUtils.INSTANCE.getSelectedMessageJson();
    }

    @Override
    public String getQuickMenuSelectedMessage(String actionId) {
        return ChatQuickOperationUtils.INSTANCE.getSelectedMessageJson(actionId);
    }

    @Override
    public void getUploadFilePathOrUrl(String uuid, LoadDataCallback<UploadEntry> callback) {
        ChatQuickOperationUtils.INSTANCE.checkUploadPath(uuid, new QuickOperateListener() {
            @Override
            public void onUploadByUrl(@NonNull String url, @androidx.annotation.Nullable String name, @androidx.annotation.Nullable String type) {
                UploadEntry entry = new UploadEntry();
                entry.setUrl(url);
                entry.setType(1);
                entry.setName(name);
                entry.setFileType(type);
                callback.onDataLoaded(entry);
            }

            @Override
            public void onUploadByPath(@NonNull String path, @androidx.annotation.Nullable String name, @androidx.annotation.Nullable String type) {
                UploadEntry entry = new UploadEntry();
                entry.setPath(path);
                entry.setType(2);
                entry.setName(name);
                entry.setFileType(type);
                callback.onDataLoaded(entry);
            }

            @Override
            public void onUploadPathInvalid() {
                callback.onDataNotAvailable("", -1);
            }
        });
    }

    @Override
    public void getUploadFilePathOrUrl(String actionId, String uuid, LoadDataCallback<UploadEntry> callback) {
        ChatQuickOperationUtils.INSTANCE.checkUploadPath(actionId, uuid, new QuickOperateListener() {
            @Override
            public void onUploadByUrl(@NonNull String url, @androidx.annotation.Nullable String name, @androidx.annotation.Nullable String type) {
                UploadEntry entry = new UploadEntry();
                entry.setUrl(url);
                entry.setType(1);
                entry.setName(name);
                entry.setFileType(type);
                callback.onDataLoaded(entry);
            }

            @Override
            public void onUploadByPath(@NonNull String path, @androidx.annotation.Nullable String name, @androidx.annotation.Nullable String type) {
                UploadEntry entry = new UploadEntry();
                entry.setPath(path);
                entry.setType(2);
                entry.setName(name);
                entry.setFileType(type);
                callback.onDataLoaded(entry);
            }

            @Override
            public void onUploadPathInvalid() {
                callback.onDataNotAvailable("", -1);
            }
        });
    }

    @Override
    public void refreshQuickApp(String data) {
        QuickAppHelper.getInstance().refreshQuickApp(data);
    }

    @Override
    public void clearQuickApp() {
        QuickAppHelper.getInstance().clear();
    }

    @Override
    public void sendAISessionInfo(String sessionId, String reqId, long time, int sessionType, String traceId, Callback<String> callback) {
        ChatSmartSummaryUtils.INSTANCE.uploadUnReadMids(reqId, sessionId, new SmartChatListener() {
            @Override
            public void onUploadMessageSuccess() {
                callback.onSuccess("");
            }

            @Override
            public void onUploadMessageFailure() {
                callback.onFail();
            }
        });
    }

    @Override
    public void downLoadFile(FileInfo fileInfo, String url, IServiceCallback<ImDownloadResult> callBack) {
        JDMEMainDownloadUtils.INSTANCE.downLoadFile(converFileInfo(fileInfo), url, wrapCallback(callBack));
    }

    @Override
    public void closeDocument(FileInfo fileInfo) {
        JDMEMainDownloadUtils.INSTANCE.closeDocument(converFileInfo(fileInfo));
    }

    @Override
    public boolean checkFileIsLoading(FileInfo fileInfo, IServiceCallback<ImDownloadResult> callBack) {
        return JDMEMainDownloadUtils.INSTANCE.checkFileIsLoading(converFileInfo(fileInfo), wrapCallback(callBack));
    }

    private IJDMEDownLoadCallBack wrapCallback(IServiceCallback<ImDownloadResult> callBack) {
        return new IJDMEDownLoadCallBack() {
            @Override
            public void onComplete(@androidx.annotation.Nullable String fileId, @androidx.annotation.Nullable String path) {
                callBack.onResult(true, new ImDownloadResult(ImDownloadState.COMPLETE, fileId, path, 0, 0), null);
            }

            @Override
            public void onProgress(@androidx.annotation.Nullable String fileId, long downByte, long currentLength) {
                callBack.onResult(true, new ImDownloadResult(ImDownloadState.PROGRESS, fileId, null, downByte, currentLength), null);
            }

            @Override
            public void onCancel(@androidx.annotation.Nullable String fileId) {
                callBack.onResult(true, new ImDownloadResult(ImDownloadState.CANCEL, fileId, null, 0, 0), null);
            }

            @Override
            public void onFailure(@androidx.annotation.Nullable String fileId) {
                callBack.onResult(true, new ImDownloadResult(ImDownloadState.FAILURE, fileId, null, 0, 0), null);
            }
        };
    }

    @Override
    public void cancelDownload(FileInfo fileInfo) {
        JDMEMainDownloadUtils.INSTANCE.cancelDownload(converFileInfo(fileInfo));
    }

    @Override
    public void removeDownLoadListener(FileInfo fileInfo) {
        JDMEMainDownloadUtils.INSTANCE.removeDownLoadListener(converFileInfo(fileInfo));
    }

    private final List<IFileInvalidCallback> fileInvalidCallbacks = new ArrayList<>();

    @Override
    public void registerFileInvalid(String fileId, IServiceCallback<FileInfo> callback) {
        JDMEMainDownloadUtils.INSTANCE.registerFileInvalid(new FileInvalidCallback(fileId, callback));
    }

    @Override
    public void unRegisterFileInvalid(String fileId) {
        IFileInvalidCallback toRemove = null;
        for (IFileInvalidCallback invalidCallback : fileInvalidCallbacks) {
            if (invalidCallback instanceof FileInvalidCallback) {
                FileInvalidCallback fileInvalidCallback = (FileInvalidCallback) invalidCallback;
                if (fileInvalidCallback.fileId != null
                        && fileInvalidCallback.fileId.equalsIgnoreCase(fileId)) {
                    toRemove = invalidCallback;
                }
            }
        }
        if (toRemove != null) {
            JDMEMainDownloadUtils.INSTANCE.unRegisterFileInvalid(toRemove);
        }
    }

    private jd.cdyjy.jimcore.tools.jdme.FileInfo converFileInfo(FileInfo fileInfo) {
        jd.cdyjy.jimcore.tools.jdme.FileInfo newInfo = new jd.cdyjy.jimcore.tools.jdme.FileInfo();
        newInfo.setEntry(fileInfo.getEntry());
        newInfo.setFileId(fileInfo.getFileId());
        newInfo.setFileName(fileInfo.getFileName());
        newInfo.setFileSize(fileInfo.getFileSize());
        newInfo.setFileType(fileInfo.getFileType());
        newInfo.setFilePath(fileInfo.getFilePath());
        newInfo.setMsgId(fileInfo.getMsgId());
        return newInfo;
    }

    private static class FileInvalidCallback implements IFileInvalidCallback {
        public final String fileId;
        IServiceCallback<FileInfo> localCallback;

        public FileInvalidCallback(String fileId, IServiceCallback<FileInfo> callback) {
            this.fileId = fileId;
            this.localCallback = callback;
        }

        @Override
        public void onFileRevoke(@NonNull jd.cdyjy.jimcore.tools.jdme.FileInfo fileInfo) {
            if (localCallback != null) {
                localCallback.onResult(false, JDMEImpl.converFileInfo(fileInfo), null);
            }
        }

        @Override
        public void onFileDestroy(@NonNull jd.cdyjy.jimcore.tools.jdme.FileInfo fileInfo) {
            if (localCallback != null) {
                localCallback.onResult(true, JDMEImpl.converFileInfo(fileInfo), null);
            }
        }
    }

    @Override
    public String safeAppId(int sessionType, String srcId) {
        if (UiCommonInterface.WrapperShare.SHARE_SESSIONTYPE_FILEHELPER == sessionType) {
            return "dd.file";
        }
        if (UiCommonInterface.WrapperShare.SHARE_SESSIONTYPE_SINGLE == sessionType) {
            if (TextUtils.isEmpty(srcId)) {
                return getAppID();
            } else {
                return srcId;
            }
        }
        return srcId;
    }

    @Override
    public void getImFileList(IServiceCallback<List<IFileListResult>> callback) {
        if (callback == null) return;
        OpimUiWrapper.getInstance().getIMFileList((result, list) -> {
            if (result) {
                if (list != null) {
                    List<IFileListResult> resultList = new ArrayList<>();
                    for (FileListData data : list) {
                        IFileListResult converted = convertToIFileListResult(data);
                        if (converted != null) {
                            resultList.add(converted);
                        }
                    }
                    callback.onResult(true, resultList, null);
                } else {
                    callback.onResult(true, new ArrayList<>(), null);
                }
            } else {
                callback.onResult(false, null, "get file list failed");
            }
        });
    }

    private IFileListResult convertToIFileListResult(FileListData data) {
        if (data == null) {
            return null;
        }
        IFileListResult result = new IFileListResult();
        result.setName(data.getName());
        result.setSize(data.getSize());
        result.setFileResId(data.getFileResId());
        result.setSizeString(data.getSizeString());
        result.setLastModified(data.getLastModified());
        result.setPath(data.getPath());
        return result;
    }

    @Override
    public void stopSearch() {
        OpimUiWrapper.getInstance().stopSearch();
    }
}