package com.jd.me.dd.im.ui;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.jd.me.dd.im.R;
import com.jd.oa.JDMAConstants;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

public class ContactSelectorAdapter extends RecyclerView.Adapter<ContactSelectorAdapter.VH> {

    private ArrayList<MemberEntityJd> list;
    private ArrayList<MemberEntityJd> selected;

    private Context context;
    private ContactSelectorListener listener;
    private int max;
    Drawable unDeletableCheckDrawable;
    private  boolean mEnableOptional;

    public ContactSelectorAdapter(Context context, ArrayList<MemberEntityJd> list, int max, boolean enableOptional) {
        this.context = context;
        this.list = list;
        selected = new ArrayList<>();
        selected.addAll(list);
        this.max = max;
        this.mEnableOptional = enableOptional;

        this.unDeletableCheckDrawable = ContextCompat.getDrawable(context, R.drawable.jdme_icon_checkbox_checked).mutate();
        this.unDeletableCheckDrawable.setAlpha(255 / 2);
    }

    public ContactSelectorListener getListener() {
        return listener;
    }

    public void setListener(ContactSelectorListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_im_dd_item_contact_selector, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(@NonNull final VH holder, int position) {
        final MemberEntityJd contactInfo = list.get(position);
        if (contactInfo.isExternal()) {
            //外部联系人
            holder.nameTV.setText(contactInfo.getName());
            holder.external.setVisibility(View.GONE);
            holder.subTitleTV.setVisibility(View.GONE);

            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) holder.layoutName.getLayoutParams();
            layoutParams.removeRule(RelativeLayout.ALIGN_TOP);
            layoutParams.addRule(RelativeLayout.CENTER_VERTICAL);
            holder.layoutName.setLayoutParams(layoutParams);
            holder.avatar.setImageResource(R.drawable.jdme_icon_chat_external_avatar);
        } else {
            //非外部联系人
            if (TextUtils.isEmpty(contactInfo.getName())) {
                holder.nameTV.setText(contactInfo.getId());
            } else {
                holder.nameTV.setText(contactInfo.getName());
            }
            holder.external.setVisibility(View.GONE);
            holder.subTitleTV.setVisibility(View.VISIBLE);

            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) holder.layoutName.getLayoutParams();
            layoutParams.addRule(RelativeLayout.ALIGN_TOP, R.id.jdme_contact_avatar);
            layoutParams.removeRule(RelativeLayout.CENTER_VERTICAL);
            holder.layoutName.setLayoutParams(layoutParams);

            holder.subTitleTV.setText(contactInfo.position);
            Glide.with(holder.avatar)
                    .load(contactInfo.getAvatar())
                    .apply(new RequestOptions()
                            .placeholder(R.drawable.opim_default_person_icon))
                    .into(holder.avatar);
        }
        if (contactInfo.isDeletable()) {
            holder.checkBox.setImageResource(contains(selected, contactInfo) ? R.drawable.jdme_icon_checkbox_checked : R.drawable.jdme_icon_checkbox_normal);
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (contains(selected, contactInfo)) {
                        remove(selected, contactInfo);
                    } else {
                        if (selected.size() >= max) {
                            ToastUtils.showToast(v.getContext().getString(R.string.me_im_dd_select_max_tip, String.valueOf(max)));
                            return;
                        }
                        selected.add(contactInfo);
                    }
                    notifyDataSetChanged();
                    if (listener != null) {
                        listener.onSelectorChange();
                    }
                }
            });
        } else {
            holder.checkBox.setImageDrawable(unDeletableCheckDrawable);
        }

        if (mEnableOptional) {
            holder.layoutOptional.setVisibility(View.VISIBLE);
            if (contactInfo.isOptional()) {
                holder.layoutOptional.setSelected(false);
                holder.tvIconOptional.setText(R.string.icon_tabbar_joyme_de);
                holder.tvOptional.setText(R.string.me_im_optional);
            } else {
                holder.layoutOptional.setSelected(true);
                holder.tvIconOptional.setText(R.string.icon_tabbar_joyme_de);
                holder.tvOptional.setText(R.string.me_im_required);
            }

            holder.layoutOptional.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    contactInfo.setOptional(!contactInfo.isOptional());
                    ContactSelectorAdapter.this.notifyItemChanged(holder.getBindingAdapterPosition());
                    JDMAUtils.onEventClick(JDMAConstants.mobile_create_optional_attendees, JDMAConstants.mobile_create_optional_attendees);
                }
            });
        } else {
            holder.layoutOptional.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    public void remove(List<MemberEntityJd> contactInfos, MemberEntityJd contactInfo) {
        if (contactInfos == null || contactInfo == null) {
            return;
        }
        MemberEntityJd removeContactInfo = null;
        for (MemberEntityJd temp : contactInfos) {
            if (Objects.equals(temp, contactInfo)) {
                removeContactInfo = temp;
            }
        }
        if (removeContactInfo != null) {
            contactInfos.remove(removeContactInfo);
        }
        return;
    }

    public boolean contains(List<MemberEntityJd> contactInfos, MemberEntityJd contactInfo) {
        if (contactInfos == null || contactInfo == null) {
            return false;
        }
        for (MemberEntityJd temp : contactInfos) {
            if (Objects.equals(temp, contactInfo)) {
                return true;
            }
        }
        return false;
    }

    public void addContactInfo(MemberEntityJd info, boolean isSelected) {
        if (info != null) {
            if (!contains(list, info)) {
                list.add(info);
                if (isSelected) {
                    selected.add(info);
                }
            } else {
                int index = list.indexOf(info);
                if (index != -1) {
                    selected.add(list.get(index));
                }
            }
        }
        if (listener != null) {
            listener.onSelectorChange();
        }
    }

    public ArrayList<MemberEntityJd> getContactInfos() {
        return list;
    }

    public ArrayList<MemberEntityJd> getSelected() {
        return selected;
    }

    class VH extends RecyclerView.ViewHolder {

        LinearLayout layoutName;
        TextView nameTV;
        TextView subTitleTV;
        ImageView avatar;
        ImageView checkBox;
        TextView external;
        View layoutOptional;
        TextView tvIconOptional;
        TextView tvOptional;

        public VH(View itemView) {
            super(itemView);
            layoutName = itemView.findViewById(R.id.layout_name);
            nameTV = itemView.findViewById(R.id.tv_name);
            subTitleTV = itemView.findViewById(R.id.tv_subtitle);
            avatar = itemView.findViewById(R.id.jdme_contact_avatar);
            checkBox = itemView.findViewById(R.id.checkbox);
            external = itemView.findViewById(R.id.tv_external);
            layoutOptional = itemView.findViewById(R.id.layout_optional);
            tvIconOptional = itemView.findViewById(R.id.tv_icon_optional);
            tvOptional = itemView.findViewById(R.id.tv_optional);
        }
    }

    public interface ContactSelectorListener {
        void onSelectorChange();
    }
}
