package com.jd.me.dd.im;

import static com.jd.me.dd.im.ImDdServiceImpl.checkFileExpiredInternal;
import static com.jd.oa.BaseActivity.REQUEST_QR;
import static com.jd.oa.model.service.im.dd.ImDdService.ACTION_TIMLINE_UPGRADE_INI_FINISH;
import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;
import static com.jd.oa.router.DeepLink.JD_FLUTTER;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.fastjson.JSON;
import com.chenenyu.router.Router;
import com.jd.me.activitystarter.ActivityResult;
import com.jd.me.activitystarter.ActivityResultParser;
import com.jd.me.activitystarter.ActivityStarter;
import com.jd.me.activitystarter.ResultCallback;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.MeAiConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.crossplatform.CrossPlatformPhone;
import com.jd.oa.crossplatform.CrossPlatformResultListener;
import com.jd.oa.eventbus.DeeplinkCallbackProcessor;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.eventbus.JmEventProcessor;
import com.jd.oa.fragment.FileViewer;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.CalendarService;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.ScanService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.qrcode.QRCodeUtil;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.router.DeepLink;
import com.jd.oa.tablet.TabletPlaceHolderActivity;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2App;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;

import jd.cdyjy.jimcore.commoninterface.AppsSearchEntity;
import jd.cdyjy.jimcore.commoninterface.CalendarEntity;
import jd.cdyjy.jimcore.commoninterface.jdme.ILinkJumpCallback;
import jd.cdyjy.jimcore.commoninterface.jdme.IMJdme;
import jd.cdyjy.jimcore.commoninterface.jdme.IQRResult;
import jd.cdyjy.jimcore.commoninterface.jdme.SessionKeyLinkJumpCallback;
import jd.cdyjy.jimcore.tools.jdme.FileInfo;
import kotlin.jvm.functions.Function1;

//import com.jd.oa.business.workbench2.activity.TaskDetailActivity;


/**
 * 接入实现具体业务
 */
public class JDMEImpl extends IMJdme {
    private static final String TAG = "JDMEImpl";

    private AppService appService = AppJoint.service(AppService.class);

    public JDMEImpl() {
        super();
    }

    @Override
    public void onManualClick(String url, boolean isGroup, String gid, String to, String toApp, int sessionType, String appId) {
        super.onManualClick(url, isGroup, gid, to, toApp, sessionType, appId);
        try {
            String deepLink = DeepLink.BROWSER;
            String paramUrl = url;
            if (isGroup) {
                //"https://joyinsight-pre.jd.com/manual-list?sessionType=2&groupId=123456"
                paramUrl += "?sessionType=" + sessionType + "&groupId=" + gid;
            } else {
                paramUrl += "?sessionType=" + sessionType + "&groupId=" + gid + "&toPin=" + to;
                //"https://joyinsight-pre.jd.com/manual-list?sessionType=2&groupId=123456&toPin=123456"
            }
            String param = URLEncoder.encode("{\"appId\":\"" + appId + "\",\"isNativeHead\":\"1\",\"url\":\"" + paramUrl + "\"}", "UTF-8");
            deepLink += "?mparam=" + param;
            Router.build(deepLink).go(AppBase.getAppContext());
        } catch (Exception e) {

        }
    }

    @Override
    public void onOpenNewSchedule(String json) {
        AppJoint.service(AppService.class).onOpenNewSchedule(json, null);
    }

    /**
     * 咚咚消息界面点击打开分享链接的方法
     */
    @Override
    public boolean onOpenWebView(String url) {
        MELogUtil.localI(TAG, "onOpenWebView: " + url);
        //聊天界面打开网页调用的这个方法
        return OpennessApi.openUrlOrDeepLink(url, null, false, true);
    }

    @Override
    public boolean onOpenSmartAiChat(HashMap<String, Object> param) {
        try {
            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject(param);
            Map<String, String> innerParams = new HashMap<>();
            innerParams.put("mode", "messageSummary");
            innerParams.put("session", jsonObject.toString());
            String deepLink = MeAiConfigHelper.getBrowserUrl(AppBase.getAppContext(), innerParams, "Message_Summary");
            if (!TextUtils.isEmpty(deepLink)) {
                Router.build(deepLink).go(AppBase.getAppContext());
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean onLinkJump(String url, HashMap<String, Object> param, ILinkJumpCallback appJumpCallback) {
        MELogUtil.localI(TAG, "onLinkJump: " + url + ", param: " + JSON.toJSON(param) + "callback: " + appJumpCallback);
        OpennessApi.OnGetIntentCallback callback = null;
        if (appJumpCallback != null) {
            callback = new OpennessApi.OnGetIntentCallback() {
                @Override
                public void onIntent(Intent intent) {
                    if (intent == null) return;
                    Activity activity = AppBase.getTopActivity();
                    if (!(activity instanceof FragmentActivity)) return;

                    ActivityStarter.<ActivityResult>from((FragmentActivity) activity)
                            .setIntent(intent)
                            .setResultParser(new ActivityResultParser())
                            .start(new ResultCallback<ActivityResult>() {
                                @Override
                                public void onResult(ActivityResult result) {
                                    Log.d(TAG, "onResult: " + result);
                                    if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                                        Intent data = result.getData();
                                        String stringExtra = data.getStringExtra("result");
                                        appJumpCallback.onLinkJumpResult(stringExtra);
                                    }
//                                    WindowLabelAppEditData appEditData = new WindowLabelAppEditData();
//                                    appEditData.setLabelName("任务123");
//                                    WindowLabelLinkUrl linkUrl = new WindowLabelLinkUrl();
//                                    linkUrl.setDefault("jdme://jm/biz/joywork/goalCreate");
//                                    appEditData.setLinkUrl(linkUrl);
//                                    appJumpCallback.onLinkJumpResult(new Gson().toJson(appEditData));
                                }
                            });
                }
            };
        }
        boolean success = OpennessApi.openUrlOrDeepLink(url, param, false, true, callback);
        if (success) {
            return true;
        } else {
            return super.onLinkJump(url, param, appJumpCallback);
        }
    }

    @Override
    public boolean onLinkJump(Context context, String url, HashMap<String, Object> param, SessionKeyLinkJumpCallback appJumpCallback) {
        MELogUtil.localI(TAG, "onLinkJump: " + url + ", param: " + JSON.toJSON(param) + "callback: " + appJumpCallback);
        String targetUrl = url;
        if (appJumpCallback != null) {
            String callbackId = String.valueOf(appJumpCallback.hashCode());
            targetUrl = DeeplinkCallbackProcessor.appendCallbackId(url, callbackId);
            JmEventProcessor processor = JmEventDispatcher.firstOf(DeeplinkCallbackProcessor.callbackPredicate(callbackId));
            if (processor == null) {
                IServiceCallback<String> wrapper = (success, s, error) -> {
                    if (TextUtils.isEmpty(s)) return;
                    appJumpCallback.onLinkJumpResult(s);
                };
                JmEventDispatcher.registerProcessor(new DeeplinkCallbackProcessor(callbackId, wrapper));
            }
        }
        boolean success = OpennessApi.openUrlOrDeepLink(targetUrl, param, false, true, null);
        if (success) {
            return true;
        } else {
            return super.onLinkJump(url, param, appJumpCallback);
        }
    }

    private Function1<JmEventProcessor, Boolean> imCallbackPredicate(String callbackId) {
        return jmEventProcessor -> {
            if (jmEventProcessor instanceof DeeplinkCallbackProcessor) {
                DeeplinkCallbackProcessor processor = (DeeplinkCallbackProcessor) jmEventProcessor;
                return callbackId.equals(processor.getCallbackId());
            }
            return false;
        };
    }

    @Override
    public void unRegisterLinkJump(ILinkJumpCallback appJumpCallback) {
        if (appJumpCallback == null) return;
        String callbackId = String.valueOf(appJumpCallback.hashCode());
        JmEventDispatcher.unregisterAll(DeeplinkCallbackProcessor.callbackPredicate(callbackId));
    }

    /**
     * 文本消息长按打开任务
     */
    @Override
    public void onOpenNewTask(String content) {
        appService.onOpenNewTask(content);
    }

    /**
     * 图片预览长按识别二维码
     */
    @Override
    public String onQRRecognition(String path, final IQRResult callback) {
        ScanService service = AppJoint.service(ScanService.class);
        if (service != null) {
            service.getStringFromPicFile(path, new ScanService.IScanCallback() {
                @Override
                public void onSuccess(String url) {
                    if (!url.isEmpty()) {
                        callback.onSuccess(url);
                    } else {
                        onFailed();
                    }
                }

                @Override
                public void onFailed() {
                    callback.onFailed();
                }
            });
        }
        return null;
    }

    @Override
    public String onQRJump(final String path) {
        final ScanService service = AppJoint.service(ScanService.class);
        AppBase.getTopActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (service != null) {
                    service.getStringFromPicFile(path, new ScanService.IScanCallback() {
                        @Override
                        public void onSuccess(String url) {
                            if (!TextUtils.isEmpty(url)) {//#1924737
                                ScanResultDispatcher.dispatch(AppBase.getTopActivity(), url);
                            }
                        }

                        @Override
                        public void onFailed() {
                        }
                    });
                }
            }
        });
        return super.onQRJump(path);
    }


    @Override
    public void onQRJumpByUrl(String path, final String url) {
        if (ConfigurationManager.get().getEntry("android.qrcode.screenshot.disable", "0").equals("0")) {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    ScanResultDispatcher.dispatch(AppBase.getTopActivity(), url);
                }
            });
        } else {
            onQRJump(path);
        }
    }


    /**
     * mia点击事件
     */
    @Override
    public void onMIAClick() {
//        Router.build(DeepLink.MIA_OLD).go(Utils2App.getApp());
//        PageEventUtil.onEvent(Utils2App.getApp(), PageEventUtil.EVENT_MIA);
    }

    /**
     * 应用搜索点击事件
     */
    @Override
    public void onAppSearchClick(AppsSearchEntity searchResult) {
        final Activity top = AppBase.getTopActivity();
        if (top == null) {
            return;
        }
        if (searchResult != null && !TextUtils.isEmpty(searchResult.id)) {
//            AppJoint.service(AppCenterService.class).openApp(null, searchResult.id, true, true);
            Router.build(DeepLink.appCenter(searchResult.id, null)).go(top);
        }
    }

    @Override
    public void onIsInMigrating(boolean isMigrating) {
        super.onIsInMigrating(isMigrating);
        appService.setMigrating(isMigrating);
        if (!isMigrating && appService.isForceKickOut()) {
            appService.setForceKickOut(false);
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT, true);
            appService.logout();
        }
    }

    @Override
    public Bitmap onGetQRImage(String content, int width, int height) {
        return QRCodeUtil.createQRCode(content, width, height);
    }

    @Override
    public Intent getQrIntent() {
        return appService.getQrIntent();
    }

    @Override
    public void onFinishOpenOldDbActy() {
        super.onFinishOpenOldDbActy();
        LocalBroadcastManager.getInstance(Utils2App.getApp()).sendBroadcast(new Intent(ACTION_TIMLINE_UPGRADE_INI_FINISH));
    }

    @Override
    public void onScan() {//实现逻辑拷贝到GlobalConfigImpl，这个方法不再调用，不删除以防万一
        Activity activity = AppBase.getTopActivity();
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet()) && AppBase.getMainActivity() != null) {
            activity = AppBase.getMainActivity();
        }
        final Activity avt = activity;
        final String cT = System.currentTimeMillis() + "\n";
        if (avt == null) {
            FileUtils.saveFile("ddscan>>>avt null--" + cT, FileCache.getInstance().getLogFile(), "action", true);
            return;
        }
        PermissionHelper.requestPermission(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_camera_scan),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        FileUtils.saveFile("ddscan>>>onPermissionApplySuccess--" + cT, FileCache.getInstance().getLogFile(), "action", true);
                        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                            TabletPlaceHolderActivity.start(avt, getQrIntent());
                        } else {
                            avt.startActivityForResult(getQrIntent(), REQUEST_QR);
                        }
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        FileUtils.saveFile("ddscan>>>onPermissionApplyFailure--" + cT, FileCache.getInstance().getLogFile(), "action", true);
                        ToastUtils.showToast(R.string.me_permission_denied_camera);
                    }
                }, Manifest.permission.CAMERA);
    }

    @Override
    public void onVoteClick(String gid) {
//        if (AppBase.getTopActivity() != null) {
//            PageEventUtil.onEvent(AppBase.getTopActivity(), PageEventUtil.EVENT_VOTE_ADD_MSG_CREATE);
//        }
        this.onOpenWebView(DeepLink.rnOld("201903120396", "route=voteCreate&source=dialog&groupId=" + gid));
    }

    @Override
    public void onJDCloudPrint(final String url, final String fileName, final long size, String ext) {
        super.onJDCloudPrint(url, fileName, size, ext);
        if (!TextUtils.isEmpty(ext) && !ext.startsWith(".")) {
            ext = "." + ext;
        }
        //检查文件是否过期
        final String finalExt = ext;
        checkFileExpiredInternal(url, new LoadDataCallback<Boolean>() {
            @Override
            public void onDataLoaded(Boolean expired) {
                if (AppBase.getTopActivity() == null) {
                    return;
                }
                if (!expired) {
                    appService.onJDCloudPrint(url, fileName, size, finalExt);
                } else {
                    ToastUtils.showToast(R.string.me_print_file_expired);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                ToastUtils.showToast(R.string.me_print_access_file_failed);
            }
        });
    }

    @Override
    public void onJoySpace(String sessionKey, String pin, String app, int sessionType) {
//        PageEventUtil.onEvent(AppBase.getTopActivity(), PageEventUtil.EVENT_JOYSPACE_ADD_MSG_CREATE);
        JSONObject param = new JSONObject();
        try {
            param.put("sessionKey", sessionKey);
            param.put("pin", pin);
            param.put("app", app);
            param.put("sessionType", sessionType);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        //预发
//        Router.build("jdme://web/201906270537?browser=1&url="
//                + Uri.encode("https://joyspace-pre.jd.com/jdme/share?session=" + param.toString())).go(AppBase.getTopActivity());
        if (NetEnvironmentConfigModel.PREV.equalsIgnoreCase(PreferenceManager.UserInfo.getNetEnvironment())) {
            //预发
            Router.build(DeepLink.webApp("201906270537", "https://joyspace-pre.jd.com/jdme/share?session=" + param.toString(), 0, 1)
            ).go(AppBase.getTopActivity());
        } else {
            //正式
            Router.build(DeepLink.webApp("201905050466", "https://joyspace.jd.com/jdme/share?session=" + param.toString(), 0, 1)
            ).go(AppBase.getTopActivity());
        }
    }

    @Override
    public void onMeetingBooking() {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putString("routeTag", "create");
        bundle.putBoolean("timlineMeeting", true);
        bundle.putString("from", "timlineMeeting");
        Router.build(CALENDER_SCHEDULE).with(bundle).go(activity);
        JDMAUtils.onEventClick(JDMAConstants.timline_video_create_schedule, JDMAConstants.timline_video_create_schedule);
    }

    /**
     * //jdme://jm/biz/appcenter/v2calendar/schedule/detail?mparam={"calendarId":"136796817940938752","endTime":1651825800000,"beginTime":1651824000000,"isNeedCheck":true,"scheduleId":"EXCHANGE_442715949029101568"}
     *
     * @param scheduleId
     * @param calendarId
     * @param startTime
     * @param endTime
     */
    @Override
    public void onScheduleMeetingDetail(String scheduleId, String calendarId, String startTime, String endTime) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }
        try {
            JSONObject object = new JSONObject();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date start = format.parse(startTime);
            Date end = format.parse(endTime);

            CalendarService service = AppJoint.service(CalendarService.class);
            service.openScheduleDetail(scheduleId, calendarId, start.getTime(), 0);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onUserInfoToCalendar(final CalendarEntity entity) {
        super.onUserInfoToCalendar(entity);
        openUserCalendar(entity.app, entity.pin, entity.name);
    }

    public static void openUserCalendar(final String app, final String pin, final String name) {
        final Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }

        ImDdService imDdService = AppJoint.service(ImDdService.class);
        imDdService.getContactInfo(app, pin, new Callback<MemberEntityJd>() {
            @Override
            public void onSuccess(MemberEntityJd bean) {
                JSONObject object = new JSONObject();
                try {
                    JSONObject contact = new JSONObject()
                            .put("app", app)
                            .put("erp", pin)
                            .put("name", name)
                            .put("avatar", bean.getAvatar());

                    object.put("routeName", "schedule_contact_calendar_page")
                            .put("mparam", new JSONObject()
                                    .put("contacts", new JSONArray(Collections.singletonList(contact)))
                            );
                    Uri deeplink = Uri.parse(JD_FLUTTER).buildUpon()
                            .appendQueryParameter("mparam", object.toString())
                            .build();
                    Router.build(deeplink).go(activity);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail() {

            }
        });

        JDMAUtils.onEventClick(JDMAConstants.mobile_joyday_contact_calendar, JDMAConstants.mobile_joyday_contact_calendar);
    }

    @Override
    public void onGroupToCalendar(String gid) {
        super.onGroupToCalendar(gid);
        openGroupCalendar(gid);
    }

    public static void openGroupCalendar(String gid) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }
        JSONObject object = new JSONObject();
        try {
            object.put("routeName", "schedule_group_calendar_page")
                    .put("mparam", new JSONObject()
                            .put("gid", gid)
                            .put("type", "group")
                            .put("contacts", new JSONArray(Collections.emptyList()))
                    );
            Uri deeplink = Uri.parse(JD_FLUTTER).buildUpon()
                    .appendQueryParameter("mparam", object.toString())
                    .build();
            Router.build(deeplink).go(activity);
        } catch (Exception e) {
            e.printStackTrace();
        }

        JDMAUtils.onEventClick(JDMAConstants.mobile_joyday_group_calendar, JDMAConstants.mobile_joyday_group_calendar);
    }

    @Override
    public void onPhoneCall(Context context, String pin, String app) {
        if (!(context instanceof Activity)) {
            context = AppBase.getTopActivity();
        }
        if (context == null) {
            return;
        }
        final Context ctx = context;
        HashMap<String, String> params = new HashMap<>();
        params.put("app", app);
        params.put("pin", pin);
        CrossPlatformPhone.call((Activity) context, params, new CrossPlatformResultListener<Void>() {
            @Override
            public void failure(String code, String message) {
                if (Objects.equals(code, CrossPlatformPhone.BIZ_ERROR)) {
                    String msg = TextUtils.isEmpty(message) ? ctx.getString(R.string.me_virtual_number_error) : message;
                    Toast.makeText(ctx, msg, Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(ctx, R.string.me_virtual_number_error, Toast.LENGTH_SHORT).show();
                }
            }


            @Override
            public void success(Void... t) {

            }
        });
    }

    @Override
    public void onPhoneDial(Context context, String pin, String app) {
        if (!(context instanceof Activity)) {
            context = AppBase.getTopActivity();
        }
        if (context == null) {
            return;
        }
        HashMap<String, String> params = new HashMap<>();
        params.put("app", app);
        params.put("pin", pin);
        final Context ctx = context;
        CrossPlatformPhone.dial((Activity) context, params, new CrossPlatformResultListener<Void>() {
            @Override
            public void failure(String code, String message) {
                if (Objects.equals(code, CrossPlatformPhone.BIZ_ERROR)) {
                    String msg = TextUtils.isEmpty(message) ? ctx.getString(R.string.me_virtual_number_error) : message;
                    Toast.makeText(ctx, msg, Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(ctx, R.string.me_virtual_number_error, Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void success(Void... t) {

            }
        });
    }

    @Override
    public void openDocument(Context context, FileInfo fileInfo, String appId) {
        FileViewer.openDocument(context, converFileInfo(fileInfo), appId);
    }

    @Override
    public boolean isFileSupport(FileInfo fileInfo) {
        return FileViewer.isFileSupport(converFileInfo(fileInfo));
    }

    public static com.jd.oa.model.FileInfo converFileInfo(FileInfo fileInfo) {
        com.jd.oa.model.FileInfo newInfo = new com.jd.oa.model.FileInfo();
        newInfo.setEntry(fileInfo.getEntry());
        newInfo.setFileId(fileInfo.getFileId());
        newInfo.setFileName(fileInfo.getFileName());
        newInfo.setFileSize(fileInfo.getFileSize());
        newInfo.setFileType(fileInfo.getFileType());
        newInfo.setFilePath(fileInfo.getFilePath());
        newInfo.setUrl(fileInfo.getUrl());
        newInfo.setMimeType(fileInfo.getMimeType());
        newInfo.setMsgId(fileInfo.getMsgId());
        return newInfo;
    }
}