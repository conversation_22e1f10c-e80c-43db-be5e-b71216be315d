package com.jd.me.dd.im;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.listener.TimlineMessageListener;

import org.json.JSONObject;

import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import com.jd.oa.utils.Utils2App;
import jd.cdyjy.jimcore.core.tcp.TcpConstant;

public class TimlineMessageDispatcher {

    private static final String TAG = "TimlineMessage";
    private static final String EXTRA_MESSAGE = "meMessage";
    public static final String MESSAGE_TYPE_CAR_POOL = "000000";
    public static final String MESSAGE_TYPE_CAR_POOL_COLLEAGUE = "000010";

    private volatile static TimlineMessageDispatcher sInstance;
    private LocalBroadcastManager localBroadcastManager;
    private ConcurrentHashMap<String, CopyOnWriteArrayList<TimlineMessageListener>> listener;

    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                return;
            }
            String messageBody = intent.getStringExtra(EXTRA_MESSAGE);
            Log.i(TAG, "messageBody = " + messageBody);
            dispatchMessage(messageBody);
        }
    };

    public static TimlineMessageDispatcher getInstance() {
        if (sInstance == null) {
            synchronized (TimlineMessageDispatcher.class) {
                if (sInstance == null) {
                    sInstance = new TimlineMessageDispatcher();
                }
            }
        }
        return sInstance;
    }

    private TimlineMessageDispatcher() {
    }

    public void init() {
        localBroadcastManager = LocalBroadcastManager.getInstance(Utils2App.getApp());
        localBroadcastManager.registerReceiver(receiver, new IntentFilter(TcpConstant.BROADCAST_PACKET_ME));
        listener = new ConcurrentHashMap<>();
    }

    public void release() {
        if (localBroadcastManager != null && receiver != null) {
            localBroadcastManager.unregisterReceiver(receiver);
        }
        if (listener != null) {
            listener.clear();
        }
    }

    public void registerListener(String type, TimlineMessageListener messageReceiver) {
        if (messageReceiver == null || listener == null) {
            return;
        }
        CopyOnWriteArrayList<TimlineMessageListener> list = listener.get(type);
        if (list != null) {
            list.add(messageReceiver);
        } else {
            list = new CopyOnWriteArrayList<>();
            list.add(messageReceiver);
            listener.put(type, list);
        }
    }


    public void unregisterListener(String type, TimlineMessageListener messageReceiver) {
        if (messageReceiver == null || listener == null) {
            return;
        }
        CopyOnWriteArrayList<TimlineMessageListener> list = listener.get(type);
        if (list != null) {
            if (list.contains(messageReceiver)) {
                list.remove(messageReceiver);
            }
        }
    }

    public void unregisterListener(String type, BroadcastReceiver receiver) {
        if (receiver == null || listener == null) {
            return;
        }
        CopyOnWriteArrayList<TimlineMessageListener> list = listener.get(type);
        if (list == null) {
            return;
        }
        Iterator<TimlineMessageListener> it = list.iterator();
        while (it.hasNext()) {
            TimlineMessageListener messageReceiver = it.next();
            if (messageReceiver instanceof ReceiverAdapter
                    && ((ReceiverAdapter) messageReceiver).mBroadcastReceiver == receiver) {
                it.remove();
            }
        }
    }

    private void dispatchMessage(String message) {
        if (TextUtils.isEmpty(message) || listener == null) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(message);
            JSONObject body = jsonObject.optJSONObject("body");
            String type = body.optString("pushType");
            if (TextUtils.isEmpty(type)) {
                return;
            }
            CopyOnWriteArrayList<TimlineMessageListener> list = listener.get(type);
            if (list == null) {
                return;
            }
            for (TimlineMessageListener receiver : list) {
                if (receiver == null) {
                    continue;
                }
                receiver.onMessageReceiver(type, body.toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private class ReceiverAdapter implements TimlineMessageListener {

        private BroadcastReceiver mBroadcastReceiver;

        public ReceiverAdapter(BroadcastReceiver receiver) {
            mBroadcastReceiver = receiver;
        }

        @Override
        public void onMessageReceiver(String type, String message) {
            Intent data = new Intent();
            data.putExtra("type", type);
            data.putExtra("message", message);
            mBroadcastReceiver.onReceive(Utils2App.getApp(), data);
        }
    }

}
