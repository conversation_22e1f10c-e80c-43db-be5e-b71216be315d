package com.jd.me.dd.im;

import android.text.TextUtils;

import java.util.List;

public class MemberEntityResponse {
    List<MemberEntityShort> data;

}

class MemberEntityShort {
    public String userId;
    public String groupId;
    public String appId;
    public int sessionType;
    public String name;
    public String avatar;

    public boolean isGroup() {
        return !TextUtils.isEmpty(groupId);
    }
}