package com.jd.me.dd.im;

import static com.jd.oa.Constant.ANDROID_JOY_MINUTES_REALTIME_TRANSLATE_ENABLE;
import static com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_OFF;
import static com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_ON;
import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;
import static com.jd.oa.router.DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.chenenyu.router.Router;
import com.jd.cdyjy.common.base.ui.custom.global_config.IMGlobalConfig;
import com.jd.cdyjy.common.base.ui.custom.global_config.OverflowEntity;
import com.jd.cdyjy.common.base.ui.custom.global_config.service.ImageEditorResult;
import com.jd.cdyjy.common.base.ui.custom.global_config.service.ImageEditorUICallback;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.MeAiConfigHelper;
import com.jd.oa.configuration.local.model.PlusMenuModel;
import com.jd.oa.lib.editor.IPhotoEditorCallback;
import com.jd.oa.lib.editor.PhotoEditorHelper;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActiveAnalyzeUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StatusBarConfig;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.Utils2App;

import java.util.ArrayList;
import java.util.Objects;
import java.util.List;

import jd.cdyjy.jimcore.commoninterface.jdme.JdmeServiceManager;

public class GlobalConfigImpl extends IMGlobalConfig {

    private AppService appService = AppJoint.service(AppService.class);

    @Override
    public int getGotoIconRes() {
        return R.drawable.jdme_im_map_goto;
    }

    @Override
    public int getBackIconRes() {
        return R.drawable.jdme_im_map_back_location_selector;
    }

    @Override
    public int voipMainColor() {
        return ContextCompat.getColor(Utils2App.getApp(), R.color.dd_colorPrimary);
    }

    @Override
    public int voipRepeatDayBgRes() {
        return R.drawable.jdme_timline_conference_week_selector;
    }

    @Override
    public int singleButtonBackgroundRes() {
        return R.drawable.jdme_bg_btn_red_radius_selector;
    }

    @Override
    public int singleButtonTextColor() {
        return R.color.comm_white;
    }

    @Override
    public int posButtonTextColor() {
        return R.color.red_warn;
    }

    @Override
    public int operableTextColor() {
        return R.color.dd_colorPrimary;
    }


    @Override
    public int buttonDrawableRes() {
        return R.drawable.jdme_selector_checkbox_red;
    }

    @Override
    public void chatListMainTitle(View view) {
        view.setPadding(ImageUtils.dp2px(view.getContext(), 16), 0, 0, 0);
    }

    @Override
    public void contactMainTitle(View view) {
        view.setPadding(ImageUtils.dp2px(view.getContext(), 16), 0, 0, 0);
    }

    @Override
    public void chattingMainBack(View view) {
        view.setPadding(ImageUtils.dp2px(view.getContext(), 10), ImageUtils.dp2px(view.getContext(), 10), ImageUtils.dp2px(view.getContext(), 10), ImageUtils.dp2px(view.getContext(), 10));
    }

    @Override
    public int navBarSelectedBgColorId() {
        return R.color.red_warn;
    }

    @Override
    public int toolBarTextSize() {
        return R.dimen.me_action_bar_title_text_size;
    }

    @Override
    public int toolBarBackSrc() {
        return R.drawable.jdme_icon_back_black;
    }

    @Override
    public int operableTextSize() {
        return R.dimen.me_action_bar_menu_text_size;
    }

    @Override
    public int wiperSwitchIcon() {
        return R.drawable.jdme_switch_on_background;
    }

    @Override
    public ArrayList<OverflowEntity> getOverflows() {
        ArrayList<OverflowEntity> result = new ArrayList<>();
        OverflowEntity entity = new OverflowEntity();
        entity.iconId = R.drawable.jdme_icon_chat_top_add_scan;
        Activity activity = AppBase.getTopActivity();
        List<PlusMenuModel.PlusMenuItem> items = LocalConfigHelper.getInstance().getPlusMenuItem();
        for(PlusMenuModel.PlusMenuItem item : items){
            if("scanQRCode".equals(item.name)){
                if (activity != null) {
                    entity.title = activity.getResources().getString(R.string.me_scan_fun);
                } else {
                    entity.title = "扫一扫";
                }
                entity.id = R.id.dd_overflow_scan_id;
                result.add(entity);
            }else if("newDocument".equals(item.name)){
                // 新建文档
                if (TenantConfigBiz.INSTANCE.isJoySpaceEnable()) {
                    // 添加新建文档
                    OverflowEntity entityJoyspace = new OverflowEntity();
                    entityJoyspace.iconId = R.drawable.jdme_icon_chat_top_add_joyspace;
                    if (activity != null) {
                        entityJoyspace.title = activity.getResources().getString(R.string.me_joyspace_new);
                    } else {
                        entityJoyspace.title = "新建文档";
                    }
                    entityJoyspace.id = R.id.dd_overflow_joyspace_id;
                    result.add(entityJoyspace);
                }
            }else if("newInvite".equals(item.name)){
                if (TenantConfigBiz.INSTANCE.isJoyDayEnable()) {
                    Context context = activity == null ? AppBase.getAppContext() : activity;
                    OverflowEntity schedule = new OverflowEntity();
                    schedule.iconId = R.drawable.jdme_icon_chat_top_add_schedule;
                    schedule.title = context.getString(R.string.me_schedule_new);
                    schedule.id = R.id.dd_overflow_schedule_id;
                    result.add(schedule);
                }
            }else if("newMemo".equals(item.name)){

            }else if ("newTranslate".equals(item.name)){
                if (Objects.equals(ABTestManager.getInstance().getConfigByKey(
                        ANDROID_JOY_MINUTES_REALTIME_TRANSLATE_ENABLE, MULTI_TASK_OFF), MULTI_TASK_ON)) {
                    Context context = activity == null ? AppBase.getAppContext() : activity;
                    OverflowEntity translate = new OverflowEntity();
                    translate.iconId = R.drawable.jdme_icon_chat_top_add_translate;
                    translate.title = context.getString(R.string.me_real_translate);
                    translate.id = R.id.dd_overflow_real_translate;
                    result.add(translate);
                }
            }
        }

//        if (QuickAppHelper.getInstance().disable()) {
//            boolean assistantEnable = ABTestManager.getInstance().getConfigByKey("mobile.ai.chat.enable", "0").equals("1");
//            if (assistantEnable) {
//                Context context = activity == null ? AppBase.getAppContext() : activity;
//                OverflowEntity schedule = new OverflowEntity();
//                schedule.iconId = R.drawable.jdme_icon_ai_im_title;
//                schedule.title = context.getString(R.string.me_im_over_ai);
//                schedule.id = R.id.dd_overflow_ai_id;
//                result.add(schedule);
//            }
//        }
//
//            parent.findViewById(R.id.right_img_2).setVisibility(assistantEnable ? View.VISIBLE : View.GONE);

        return result;
    }

    @Override
    public void overflowClick(int id) {
        Activity activity = AppBase.getTopActivity();
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet()) && AppBase.getMainActivity() != null) {
            activity = AppBase.getMainActivity();
        }
        if (activity == null) {
            return;
        }

        if (id == R.id.dd_overflow_scan_id) {
            JdmeServiceManager.onScan();
            JDMAUtils.onEventClick(JDMAConstants.mobile_timline_head_qrScan_click, JDMAConstants.mobile_timline_head_qrScan_click);
        } else if (id == R.id.dd_overflow_joyspace_id) {
            Router.build(DeepLink.rnOld("201909020601","routeTag=template&rnStandalone=2")).go(activity);
            JDMAUtils.onEventClick(JDMAConstants.mobile_timline_head_create_word_click, JDMAConstants.mobile_timline_head_create_word_click);
        } else if (id == R.id.dd_overflow_schedule_id) {
            Bundle bundle = new Bundle();
            bundle.putString("routeTag", "create");
            Router.build(CALENDER_SCHEDULE).with(bundle).go(activity);
            JDMAUtils.onEventClick(JDMAConstants.mobile_timline_head_new_meeting_invite_click, JDMAConstants.mobile_timline_head_new_meeting_invite_click);
        } else if (id == R.id.dd_overflow_ai_id) {
            if (activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            String deepLink = MeAiConfigHelper.getAppCenterUrl(activity, "");
            if (!TextUtils.isEmpty(deepLink)) {
                Router.build(deepLink).go(activity);
            }
            JDMAUtils.onEventClick(JDMAConstants.Mobile_Event_MEAI_IM_Main_ck, JDMAConstants.Mobile_Event_MEAI_IM_Main_ck);
        } else if (id == R.id.dd_overflow_real_translate) {
            Router.build(JOY_NOTE_REAL_TIME_TRANSLATE).go(activity);
        }
    }

    //    @Override
//    public int topRightView0Icon() {
//        return R.drawable.jdme_sel_cmn_dd_address_book;
//    }
//
//    @Override
//    public boolean topRightView0Click(View view) {
//        Intent intent = new Intent(view.getContext(), FunctionActivity.class);
//        intent.putExtra(FunctionActivity.FLAG_FUNCTION, AppJoint.service(ImDdService.class).getContactSecondaryFragment());
//        intent.putExtra(FunctionActivity.SHOW_ACTION, false);
//        view.getContext().startActivity(intent);
//        return true;
//    }

    @Override
    public void topRightInit(ViewGroup viewGroup) {
        AppJoint.service(ImDdService.class).initTopRightView(viewGroup);
    }

    @Override
    public void topLeftInit(ViewGroup viewGroup) {
        // 设置顶部左边头像
        AppJoint.service(ImDdService.class).initTopLeftView(viewGroup);
    }

    @Override
    public void refreshRight(ViewGroup viewGroup) {
        AppJoint.service(ImDdService.class).refreshRightView(viewGroup);
    }

    @Override
    public void setDefaultRight(ViewGroup viewGroup) {
        AppJoint.service(ImDdService.class).setDefaultRight(viewGroup);
    }

    @Override
    public void refreshRosterUnread(int unread) {
        AppJoint.service(ImDdService.class).setRosterUnread(unread);
    }


    @Override
    public void secondaryContactLeftImage1(ImageView view, final Context context) {
        view.setVisibility(View.VISIBLE);
        view.setImageResource(R.drawable.jdme_ic_arrow_back);
        ViewGroup.LayoutParams params = view.getLayoutParams();
        if (params == null) {
            params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        } else {
            params.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        view.setPadding(CommonUtils.dp2px(10), view.getPaddingTop(), view.getPaddingRight(), view.getPaddingBottom());
        view.setLayoutParams(params);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (context instanceof Activity) {
                    ((Activity) context).finish();
                }
            }
        });
    }

    @Override
    public void secondaryContactTitle(TextView titleView) {
        titleView.setTextSize(TypedValue.COMPLEX_UNIT_PX, titleView.getContext().getResources().getDimensionPixelSize(R.dimen.comm_text_title_large));
    }

    @Override
    public boolean enableImmersive() {
        return StatusBarConfig.enableImmersive();
    }

    @Override
    public void gotoImageEditor(@NonNull Context context, @NonNull String path, @NonNull ImageEditorResult result) {
        MELogUtil.localD(MELogUtil.TAG_IM, "gotoImageEditor path = " + path);
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isDestroyed()) {
            activity = AppBase.getMainActivity();
            if (activity == null || activity.isDestroyed()) {
                MELogUtil.localD(MELogUtil.TAG_IM, "gotoImageEditor activity exception");
                return;
            }
        }

        ImageEditorUICallback uiCallback = new ImageEditorUICallback() {
            @Override
            public void closeEditor() {
                MELogUtil.localD(MELogUtil.TAG_IM, "gotoImageEditor closeEditor");
                PhotoEditorHelper.closePhotoEditor();
            }
        };

        PhotoEditorHelper.openPhotoEditor(path, activity, false, new IPhotoEditorCallback() {
            @Override
            public void done(String filePath) {
                MELogUtil.localD(MELogUtil.TAG_IM, "gotoImageEditor callback done path = " + filePath);
                result.onResult(filePath);
            }

            @Override
            public void ready() {
                MELogUtil.localD(MELogUtil.TAG_IM, "gotoImageEditor callback cancel");
                result.onReady(AppBase.getTopActivity(), uiCallback);
            }

            @Override
            public void cancel() {
                MELogUtil.localD(MELogUtil.TAG_IM, "gotoImageEditor callback cancel");
                result.onClose();
            }
        });
    }

    @Override
    public boolean imageEditorEnable() {
        if ("1".equalsIgnoreCase(ConfigurationManager.get().getEntry(ConfigurationManager.ANDROID_EDITOR_PHOTO_DISABLE, "0"))) {
            return false;
        }
        return true;
    }


    //注意onUserInteraction() 被IM在线程里调用
    @Override
    public void onUserInteraction() {
        super.onUserInteraction();
        ActiveAnalyzeUtil.getInstance().onUserInteraction();
    }
}
