package com.jd.me.dd.im.biz.quickapp;

import static com.jd.me.dd.im.biz.quickapp.Constants.QUICK_APP_CONTAINER_HEIGHT;
import static com.jd.me.dd.im.biz.quickapp.Constants.QUICK_APP_ITEM_MAX;
import static com.jd.oa.JDMAConstants.mobile_event_im_quickapplications;
import static com.jd.oa.router.DeepLink.BROWSER;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.cdyjy.common.base.util.SharedPreferencesUtil;
import com.jd.cdyjy.jimui.ui.OpimUiWrapper;
import com.jd.me.dd.im.R;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.UnitUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jd.cdyjy.jimcore.commoninterface.meeting.MeetingServiceManager;
import jd.cdyjy.jimcore.core.ipc_global.MyInfo;
import jd.cdyjy.jimcore.reportevent.DDReportorUsecase;
import ui.TimlineUIHelper;


/*
 * Time: 2024/7/18
 * Author: qudongshi
 * Description:
 */
public class QuickAppHelper {

    private final String TAG = "QuickAppHelper";

    private static QuickAppHelper helper;

    private static Handler handler;

    private ImDdService imDdService;

    private ImageView subContainer;

    boolean isLoaded = false;

    QuickAppsModel currentData;

    RecyclerView recyclerView;
    QuickAppAdapter appsAdapter;

    private QuickAppHelper() {
        imDdService = AppJoint.service(ImDdService.class);
        handler = new Handler(Looper.getMainLooper());
    }

    public synchronized static QuickAppHelper getInstance() {
        if (helper == null) {
            helper = new QuickAppHelper();
        }
        return helper;
    }

    public void clear() {
        helper = null;
    }


    private View getMainContainer() {
        return imDdService.getMainTabView();
    }

    private ImageView getSubContainer() {
        return subContainer;

    }

    public void setDefaultSubContainer(ImageView imageView) {
        subContainer = imageView;
    }

    public void initSubContainer(ImageView imageView) {
        subContainer = imageView;
    }

    public void refreshSubContainer(ImageView imageView) {
        if (imageView == null) {
            return;
        }
        subContainer = imageView;
        if (subContainer.getVisibility() == View.VISIBLE) {
            if (!validate(currentData)) {
                return;
            }
            QuickAppsModel.QuickAppItem item = currentData.content.quickApp.get(0);
            if (item != null) {
                ImageLoader.load(subContainer.getContext(), subContainer, item.getIcon());
            }
        }
    }

    /**
     * 刷新
     */
    public synchronized void refreshQuickApp(String data) {
        if (QuickAppHelper.helper.disable()) {
            return;
        }
        QuickAppsModel quickAppData = getQuickAppData(data);
        // 数据校验
        if (!validate(quickAppData)) {
            MELogUtil.localW(TAG, "data exception ");
            return;
        }
        // 容器校验
        if (quickAppData.content.quickApp.size() > 1 && getMainContainer() == null) {
            MELogUtil.localW(TAG, "main container exception ");
            return;
        } else if (quickAppData.content.quickApp.size() == 1 && getSubContainer() == null) {
            MELogUtil.localW(TAG, "sub container exception ");
            return;
        }
        if (!isLoaded) {
            // 第一次加载，无数据
            if (quickAppData.content.quickApp.size() == 0) {
                MELogUtil.localD(TAG, "first load data size 0");
                return;
            } else if (quickAppData.content.quickApp.size() > 1) {
                // 加载主容器
                showMainContainer(quickAppData.content.quickApp);
            } else if (quickAppData.content.quickApp.size() == 1) {
                // 加载子容器
                showSubContainer(quickAppData.content.quickApp.get(0));
            }
            // 数据
            saveData(quickAppData);
            isLoaded = true;
        } else {
            // 数据无变化，不刷新
            if (!dataChanged(quickAppData)) {
                return;
            }
            // 无数据
            if (quickAppData.content.quickApp.size() == 0) {
                if (getSubContainer() != null) {
                    getSubContainer().setVisibility(View.GONE);
                    isLoaded = false;
                }
                if (getMainContainer() != null) {
                    hideMainContainer();
                    isLoaded = false;
                }
            } else if (quickAppData.content.quickApp.size() > 1) {
                // 加载主容器
                showMainContainer(quickAppData.content.quickApp);
            } else if (quickAppData.content.quickApp.size() == 1) {
                // 加载子容器
                showSubContainer(quickAppData.content.quickApp.get(0));
            }
            // 数据
            saveData(quickAppData);
        }

    }

    /**
     * 显示主容器内容
     *
     * @param items
     */
    private void showMainContainer(List<QuickAppsModel.QuickAppItem> items) {
        handler.post(new Runnable() {
            @Override
            public void run() {
                if (getMainContainer() instanceof ViewGroup) {
                    ViewGroup viewGroup = (ViewGroup) getMainContainer();
                    showMainContainer();
                    refreshRecyclerView(viewGroup, items);
                    refreshAdapter(getMainContainer().getContext(), items);
                }
            }
        });
    }

    private void refreshRecyclerView(ViewGroup viewGroup, List<QuickAppsModel.QuickAppItem> items) {
        if (recyclerView == null || viewGroup.getChildCount() == 0) {
            LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.jdme_im_quick_app, viewGroup, true);
            recyclerView = viewGroup.findViewById(R.id.recycleView);
            ViewGroup.LayoutParams params = recyclerView.getLayoutParams();
            params.height = UnitUtils.dip2px(getMainContainer().getContext(), QUICK_APP_CONTAINER_HEIGHT);
            recyclerView.setLayoutParams(params);
        }
        GridLayoutManager layoutManager = new GridLayoutManager(viewGroup.getContext(), items.size() > QUICK_APP_ITEM_MAX ? QUICK_APP_ITEM_MAX : items.size());
        layoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                return 1;
            }
        });
        recyclerView.setLayoutManager(layoutManager);
    }

    private void refreshAdapter(Context context, List<QuickAppsModel.QuickAppItem> items) {
        if (appsAdapter == null) {
            appsAdapter = new QuickAppAdapter();
            appsAdapter.setCallback(listener);
        }
        appsAdapter.setData(items);
        if (recyclerView.getAdapter() == null) {
            recyclerView.setAdapter(appsAdapter);
        } else {
            appsAdapter.notifyDataSetChanged();
        }
    }

    /**
     * show子容器
     *
     * @param item
     */
    private void showSubContainer(QuickAppsModel.QuickAppItem item) {
        if (getSubContainer() == null) {
            return;
        }
        hideMainContainer();
        getSubContainer().getLayoutParams().width = UnitUtils.dip2px(getSubContainer().getContext(), 22);
        getSubContainer().getLayoutParams().height = UnitUtils.dip2px(getSubContainer().getContext(), 22);
        ImageLoader.load(getSubContainer().getContext(), getSubContainer(), item.getIcon());
        getSubContainer().setVisibility(View.VISIBLE);
        getSubContainer().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openApp(item, false);
            }
        });
    }


    /**
     * show主容器
     */
    private void showMainContainer() {
        if (getMainContainer() == null) {
            return;
        }
        hideSubContainer();
        getMainContainer().setVisibility(View.VISIBLE);
        ViewGroup.LayoutParams params = getMainContainer().getLayoutParams();
        params.height = UnitUtils.dip2px(getMainContainer().getContext(), QUICK_APP_CONTAINER_HEIGHT);
        getMainContainer().setLayoutParams(params);
    }

    /**
     * 隐藏主容器
     */
    private void hideMainContainer() {
        if (getMainContainer() == null) {
            return;
        }
        if (getMainContainer().getVisibility() == View.GONE) {
            return;
        }
        if (getMainContainer() instanceof ViewGroup) {
            ((ViewGroup) getMainContainer()).removeAllViews();
        }
        getMainContainer().setVisibility(View.GONE);
        recyclerView = null;
        appsAdapter = null;
    }

    /**
     * 隐藏子容器
     */
    private void hideSubContainer() {
        if (getSubContainer() == null) {
            return;
        }
        getSubContainer().setVisibility(View.GONE);
    }

    /**
     * 获取数据
     *
     * @param data
     * @return
     */
    private QuickAppsModel getQuickAppData(String data) {
        try {
            if (!TextUtils.isEmpty(data)) {
                QuickAppsModel remoteData = JsonUtils.getGson().fromJson(data, QuickAppsModel.class);
                if (validate(remoteData)) {
                    return remoteData;
                }
            }
            QuickAppsModel localData = getLocalData();
            if (validate(localData)) {
                return localData;
            }
        } catch (Exception e) {
            QuickAppsModel localData = getLocalData();
            if (validate(localData)) {
                return localData;
            }
            MELogUtil.localE(TAG, "getQuickData exception", e);
        }
        return null;
    }

    private QuickAppsModel getLocalData() {
        try {
            String localDatStr = QuickAppPreference.getInstance().get(QuickAppPreference.KV_ENTITY_DATA);
            QuickAppsModel localData = JsonUtils.getGson().fromJson(localDatStr, QuickAppsModel.class);
            if (validate(localData)) {
                return localData;
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "getLocalData exception", e);
            QuickAppPreference.getInstance().remove(QuickAppPreference.KV_ENTITY_DATA);
        }
        return null;
    }

    /**
     * 数据校验
     *
     * @param model
     * @return
     */
    private boolean validate(QuickAppsModel model) {
        if (model == null || model.content == null) {
            return false;
        }
        return true;
    }

    /**
     * 判断数据是否变化
     *
     * @param data
     * @return
     */
    private boolean dataChanged(QuickAppsModel data) {
        if (validate(currentData)) {
            return true;
        }
        // 数据长度变化
        if (data.content.quickApp.size() != currentData.content.quickApp.size()) {
            return true;
        }
        // 数据内容变化
        for (int i = 0; i < data.content.quickApp.size(); i++) {
            if (!data.content.quickApp.get(i).equals(AppBase.getAppContext(), currentData.content.quickApp.get(i))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 保存数据
     *
     * @param data
     */
    private void saveData(QuickAppsModel data) {
        currentData = data;
        String val = JsonUtils.getGson().toJson(data);
        if (!TextUtils.isEmpty(val)) {
            QuickAppPreference.getInstance().put(QuickAppPreference.KV_ENTITY_DATA, val);
        }
    }

    /**
     * 打开应用
     *
     * @param item
     * @param isMainContainer
     */
    private void openApp(QuickAppsModel.QuickAppItem item, boolean isMainContainer) {
        if (item == null) {
            return;
        }
        if ("201803290218".equals(item.getId())) {
            // 会议点击
            openMeeting();
        } else if ("202105111019".equals(item.getId())) {
            handler.post(() -> {
                String mailLink = BROWSER + "?url=https%3A%2F%2Fem.jd.com%2Fmobileweb%2Fwebmail%2Findex.html&isNativeHead=0&SafeArea=1";
                Router.build(mailLink).go(AppBase.getAppContext());
            });
        } else {
            if (TextUtils.isEmpty(item.deepLink)) {
                return;
            }
            handler.post(new Runnable() {
                @Override
                public void run() {
                    Router.build(item.deepLink).go(AppBase.getAppContext());
                }
            });
        }
        // 埋点
        Map<String, String> params = new HashMap<>();
        if (isMainContainer) {
            params.put("jdme_im_floorname", "quickapplications");
        } else {
            params.put("jdme_im_floorname", "topnavbar");
        }
        params.put("jdme_im_quickapplications", item.getAppNameEn());
        JDMAUtils.clickEvent("Mobile_Page_Message_Home", mobile_event_im_quickapplications, params);
    }

    private IQuickAppClickCallback listener = new IQuickAppClickCallback() {
        @Override
        public void callback(QuickAppsModel.QuickAppItem item) {
            openApp(item, true);
        }
    };

    private void openMeeting() {
        if (MyInfo.hasVideoConferencePermission()) {
            String key = new StringBuilder(SharedPreferencesUtil.Key.VIDEO_CONFERENCE_ENTRY).append("_").append(MyInfo.myPin()).toString();
            SharedPreferencesUtil.putBoolean(AppBase.getAppContext(), key, true);
            MeetingServiceManager.Companion.showMeetingEntrance();
            DDReportorUsecase.INSTANCE.reportClickEvent("ddmeeting", "maintop-ddmeeting",
                    MyInfo.owner(), MyInfo.appId(), "");
        } else {
            if (!OpimUiWrapper.getInstance().hideVoip()) {
                Activity activity = AppBase.getTopActivity();
                if (activity != null) {
                    TimlineUIHelper.showVoipConferenceList(activity);
                }
            }
        }
    }

    /**
     * @return 功能是否关闭
     */
    public boolean disable() {
        return !QuickAppPreference.getInstance().get(QuickAppPreference.KV_ENTITY_ENABLE);
    }

    /**
     * 处理灰度信息
     */
    public static void processGrayInfo() {
        if ("1".equals(ABTestManager.getInstance().getConfigByKey("android.im.quickapp.disable", "0"))) {
            QuickAppPreference.getInstance().put(QuickAppPreference.KV_ENTITY_ENABLE, false);
        } else {
            QuickAppPreference.getInstance().put(QuickAppPreference.KV_ENTITY_ENABLE, true);
        }
    }
}
