package com.jd.me.dd.im;

import com.jd.cdyjy.jimui.ui.OpimUiWrapper;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.listener.UserServiceListener;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import java.util.ArrayList;

import jd.cdyjy.jimcore.business.chat.AtUser;
import jd.cdyjy.jimcore.commoninterface.user.ImUserService;

@SuppressWarnings("unused")
public class ImUserServiceImpl extends ImUserService {

    private AppService appService = AppJoint.service(AppService.class);

    private static ArrayList<UserServiceListener> listeners = new ArrayList<>();

    public static void add(UserServiceListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    public static void remove(UserServiceListener listener) {
        if (listener != null) {
            listeners.remove(listener);
        }
    }

    @Override
    public void onSignatureChanged(String signature) {
        super.onSignatureChanged(signature);
        notifySignatureChanged(signature);
    }

    @Override
    public void onSignatureChangedNew(String signature, ArrayList<AtUser> atUsers) {
        super.onSignatureChangedNew(signature, atUsers);
        CharSequence charSequence = OpimUiWrapper.getInstance().formatSignature(signature, atUsers);
        for (UserServiceListener listener : listeners) {
            if (listener != null) {
                listener.onSignatureChangedNew(charSequence);
            }
        }
    }

    private void notifySignatureChanged(String signature) {
        for (UserServiceListener listener : listeners
        ) {
            if (listener != null) {
                listener.onSignatureChanged(signature);
            }
        }
    }

    @Override
    public void onAvatarChanged(String avatar) {
        super.onAvatarChanged(avatar);
        appService.saveAvatar(avatar);
        notifyAvatarChanged(avatar);
    }

    static void notifyAvatarChanged(String avatar) {
        for (UserServiceListener listener : listeners
        ) {
            if (listener != null) {
                listener.onAvatarChanged(avatar);
            }
        }
    }
}
