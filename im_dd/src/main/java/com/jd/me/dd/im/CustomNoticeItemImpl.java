package com.jd.me.dd.im;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;

import com.jd.cdyjy.common.base.ui.custom.notice.NoticeEntity;
import com.jd.cdyjy.common.base.ui.custom.notice.NoticeOperationImpl;
import com.jd.cdyjy.common.base.ui.custom.notice.OnNoticeItemListener;
import com.jd.me.dd.im.noticeviewholder.EmailViewHolder;
import com.jd.me.dd.im.noticeviewholder.JoyMeetingViewHolder;
import com.jd.me.dd.im.noticeviewholder.TimeLineNoticeViewHolder;

/**
 * 自定义推送消息view
 * Created by peidongbiao on 2019/4/16
 */
public class CustomNoticeItemImpl extends NoticeOperationImpl {
    @SuppressWarnings("unused")
    private static final String TAG = "JoyMeetingNoticeItemImp";
    private static final String PIN_JOY_MEETING = "~me201803290218";

    private static final String PIN_MAIl = "~me201912190669";

    private static final int VIEW_TYPE_JOY_MEETING = 10001;
    private static final int VIEW_TYPE_MAIl = 10002;


    @Override
    public int getViewType(NoticeEntity noticeEntity) {
        if (PIN_JOY_MEETING.equals(noticeEntity.fPin)) {
            return VIEW_TYPE_JOY_MEETING;
        }
        if (PIN_MAIl.equals(noticeEntity.fPin)) {
            return VIEW_TYPE_MAIl;
        }
        return super.getViewType(noticeEntity);
    }

    @Override
    public RecyclerView.ViewHolder getItemView(Context context, View convertView, ViewGroup parent, int viewType) {
        if (VIEW_TYPE_JOY_MEETING == viewType) {
            return JoyMeetingViewHolder.create(context, parent);
        }
        if(VIEW_TYPE_MAIl == viewType) {
            return EmailViewHolder.create(context, parent);
        }
        return null;
    }

    @Override
    public boolean handleMsg(RecyclerView.ViewHolder holder, NoticeEntity noticeEntity, int postion, int totalCount, OnNoticeItemListener listener) {
        if (holder instanceof TimeLineNoticeViewHolder) {
            ((TimeLineNoticeViewHolder) holder).bindItemView(holder, noticeEntity, postion, totalCount, listener);
            return true;
        }
        return false;
    }
}