package com.jd.me.dd.im;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Typeface;

import com.jd.cdyjy.common.base.ui.custom.chat.ChatEntity;
import com.jd.cdyjy.common.base.ui.custom.chat.IMChatUi;
import com.jd.oa.AppBase;
import com.jd.oa.preference.JDMEAppPreference;

import jd.cdyjy.jimcore.business.chat.MessageTypes;
import jd.cdyjy.jimcore.tcp.protocol.common.chatMessage.TcpChatMessageBase;


/**
 * 接入方实现
 */
public class ChatUiImpl extends IMChatUi {
    private final Typeface jd_regular;
    //    private static final String TAG = ChatUiImpl.class.getSimpleName();

    public ChatUiImpl() {
        super();
        AssetManager assets = AppBase.getAppContext().getAssets();
        jd_regular = Typeface.createFromAsset(assets, "fonts/JDLangZhengTi_Regular.TTF");
    }

    /**
     * 定制消息气泡
     */
    @Override
    public int getBubbleResId(Context context, ChatEntity chatEntity) {
        int background = -1;
        if (null == chatEntity) {
            return background;
        }

        int msgType = chatEntity.getMsgType();
        int type = TcpChatMessageBase.getType(msgType);
        if (TcpChatMessageBase.isUp(msgType)) {
            //右边
            switch (type) {
                case MessageTypes.TEXT://文本
                    background = R.drawable.jdme_bg_chatting_right_text;
                    break;
                case MessageTypes.VOICE://语音
                    background = R.drawable.jdme_bg_chatting_right_voice;
                    break;
            }
        }

        return background;
    }

    @Override
    public int getTextLinkColor() {
        return R.color.me_text_link;
    }

    @Override
    public int inputCursorDrawableRes() {
        return R.drawable.jdme_cursor_shap_red;
    }

    @Override
    public int inputBottomLineRes() {
        return R.color.dd_colorPrimary;
    }

    @Override
    public Typeface getChatTextFont() {
        String fontType = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_TYPE);
        switch (fontType) {
            case "font_type_default":
                return Typeface.DEFAULT;
            case "font_type_jd_regular":
                if (null == jd_regular) {
                    return Typeface.DEFAULT;
                } else {
                    return jd_regular;
                }
            default:
                return null;
        }
    }
}
