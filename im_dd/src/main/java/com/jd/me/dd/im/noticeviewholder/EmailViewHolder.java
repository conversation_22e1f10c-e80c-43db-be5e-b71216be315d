package com.jd.me.dd.im.noticeviewholder;

import android.annotation.SuppressLint;
import android.content.Context;

import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.cdyjy.common.base.ui.custom.notice.NoticeEntity;
import com.jd.cdyjy.common.base.ui.custom.notice.OnNoticeItemListener;
import com.jd.me.dd.im.R;
import com.jd.me.dd.im.biz.quickapp.Constants;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import jd.cdyjy.jimcore.core.utils.DateTimeUtils;

import static com.jd.oa.utils.Utils.parseEmailDeepLink;

/**
 * JoyMeeting会议
 * Created by peidongbiao on 2019/4/16
 */
public class EmailViewHolder extends TimeLineNoticeViewHolder {

    private Context context;

    private NoticeEntity mNoticeEntity;
    private OnNoticeItemListener mItemListener;

    private ImageView mIvProfile; //头像
    private TextView mTvName;
    private TextView mTvReceiverTime;
    private TextView mTvSubject;
    private TextView mTvContent;

    private String url = "";

    public static EmailViewHolder create(Context context, ViewGroup parent) {
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_item_email_notice, parent, false);
        return new EmailViewHolder(view);
    }

    private EmailViewHolder(View itemView) {
        super(itemView);
        context = itemView.getContext();
        mIvProfile = itemView.findViewById(R.id.item_mail_addresser_profile);
        mTvName = itemView.findViewById(R.id.item_mail_addresser_name);
        mTvReceiverTime = itemView.findViewById(R.id.item_mail_receiver_time);
        mTvSubject = itemView.findViewById(R.id.item_mail_subject);
        mTvContent = itemView.findViewById(R.id.item_mail_content);

        final GestureDetector gestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public void onLongPress(MotionEvent e) {
                if (mNoticeEntity == null || mItemListener == null) return;
                mItemListener.onItemLongClicked(e, mNoticeEntity);
            }

            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                if (mNoticeEntity == null || mItemListener == null) return false;
//                mItemListener.onItemClicked(mNoticeEntity);
                if (TextUtils.isEmpty(url)) {
                    ToastUtils.showToast("url error!");
                    return true;
                }
                JDMAUtils.clickEvent(
                        Constants.MOBILE_PAGE_MESSAGE_CHAT,
                        Constants.MOBILE_EVENT_ME_NOTICE_MSG_MAIL_VIEW_DETAIL,
                        null);
                Router.build(url).go(context);
                return true;
            }
        });
        itemView.setOnTouchListener(new View.OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                gestureDetector.onTouchEvent(event);
                return true;
            }
        });
    }

    @Override
    public void bindItemView(RecyclerView.ViewHolder holder, NoticeEntity noticeEntity, int position, int totalCount, OnNoticeItemListener itemListener) {
        mNoticeEntity = noticeEntity;
        mItemListener = itemListener;
        try {
            JSONObject jsonObject = new JSONObject(mNoticeEntity.infox);
            mTvName.setText(jsonObject.getString("from"));
            mTvReceiverTime.setText(DateTimeUtils.formatChatMsgTimeShow(context, noticeEntity.time));
            mTvSubject.setText(jsonObject.getString("subject"));
            mTvContent.setText(jsonObject.getString("body"));
            url = parseEmailDeepLink(jsonObject);
//            RequestOptions options = new RequestOptions().placeholder(R.drawable.opim_default_person_icon).error(R.drawable.opim_default_person_icon);
//            Glide.with(context).load(jsonObject.getString("fromAvatar")).apply(options).into(mIvProfile);
        } catch (Exception e) {
            mTvName.setText("");
            mTvReceiverTime.setText(DateTimeUtils.formatChatMsgTimeShow(context, noticeEntity.time));
            mTvSubject.setText("");
            mTvContent.setText("");
            url = "";
        }

    }
}