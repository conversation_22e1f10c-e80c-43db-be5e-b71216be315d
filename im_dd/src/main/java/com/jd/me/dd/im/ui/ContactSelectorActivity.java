package com.jd.me.dd.im.ui;

import android.content.Intent;
import android.os.Bundle;

import androidx.core.content.ContextCompat;
import androidx.appcompat.app.ActionBar;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import com.chenenyu.router.annotation.Route;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.cdyjy.jimui.UiCommonInterface;
import com.jd.cdyjy.jimui.ui.OpimUiWrapper;
import com.jd.me.dd.im.R;
import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;
import static com.jd.oa.router.DeepLink.DEEPLINK_PARAM;
import static com.jd.oa.router.DeepLink.CONTACTS;

@Route(CONTACTS)
@Navigation(hidden = false, displayHome = true)
public class ContactSelectorActivity extends BaseActivity {

    public static final String EXTRA_CONTACT = "extra_contact";
    // 用于 setResult() 时将列表转成 json string 返回
    public static final String EXTRA_CONTACT_STRING = "extra_contact_string";
    public static final String EXTRA_ALTERNATE_CONTACT = "extra_alternate_contact";
    public static final String EXTRA_SPECIFY_APPID = "extra_specify_appId";
    public static final String EXTRA_EXCLUDE_APP_ID = "extra_exclude_appId";

    public static final String EXTRA_TITLE = "title";
    public static final String EXTRA_MAX = "max";
    public static final String EXTRA_EXTERNAL_CONTACT_ENABLE = "extra_external_contact_enable";
    public static final String EXTRA_BROADCAST_RESULT = "extra_broadcast";
    public static final String EXTRA_ENABLE_OPTIONAL = "extra_enable_optional";
    public static final String EXTRA_CLOSE_ON_RESULT = "extra_close_on_result";

    public static final String ACTION_GET_RESULT = "contact_selector_get_result";

    public static final String EXTRA_SELECTOR_TITLE = "selector_title";

    private RecyclerView recyclerView;
    private ContactSelectorAdapter adapter;
    private List<MemberEntityJd> erpList;
    //备选List，只有当erpList为空时才会用到
    private List<MemberEntityJd> altenateErpList;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);
    private CheckBox checkBox;
    private Button button;
    private int max = Integer.MAX_VALUE;
    private ArrayList<String> specifyAppId;
    private ArrayList<String> excludeAppIds;
    public boolean mExternalContactEnable = false;
    public boolean mBroadcastResult = false;
    public boolean mOptionalEnable = false;

    public boolean mCloseOnResult = true;

    private String mSelectorTitle;

    private CompoundButton.OnCheckedChangeListener onCheckedChangeListener = new CompoundButton.OnCheckedChangeListener() {

        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            if (adapter.getContactInfos().size() > max) {
                checkBox.setOnCheckedChangeListener(null);
                ToastUtils.showToast(getString(R.string.me_im_dd_select_max_tip, String.valueOf(max)));
                checkBox.setChecked(false);
                checkBox.setOnCheckedChangeListener(onCheckedChangeListener);
                return;
            }
            if (isChecked) {
                adapter.getSelected().clear();
                adapter.getSelected().addAll(adapter.getContactInfos());
            } else {
                Iterator<MemberEntityJd> iterator = adapter.getSelected().iterator();
                while (iterator.hasNext()) {
                    MemberEntityJd memberEntityJd = iterator.next();
                    if (memberEntityJd.isDeletable()) {
                        iterator.remove();
                    }
                }
            }
            adapter.notifyDataSetChanged();
            setFinishButtonText();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_im_dd_activity_contact_selector);
        ActionBarHelper.init(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        String title = getIntent().getStringExtra(EXTRA_TITLE);
        max = getIntent().getIntExtra(EXTRA_MAX, Integer.MAX_VALUE);
        specifyAppId = getIntent().getStringArrayListExtra(EXTRA_SPECIFY_APPID);
        excludeAppIds = getIntent().getStringArrayListExtra(EXTRA_EXCLUDE_APP_ID);
        mExternalContactEnable = getIntent().getBooleanExtra(EXTRA_EXTERNAL_CONTACT_ENABLE, false);
        mBroadcastResult = getIntent().getBooleanExtra(EXTRA_BROADCAST_RESULT, false);
        mOptionalEnable = getIntent().getBooleanExtra(EXTRA_ENABLE_OPTIONAL, false);
        mCloseOnResult = getIntent().getBooleanExtra(EXTRA_CLOSE_ON_RESULT, true);
        mSelectorTitle = getIntent().getStringExtra(EXTRA_SELECTOR_TITLE);

        if (actionBar != null) {
            if (TextUtils.isEmpty(title)) {
                actionBar.setTitle(R.string.me_contact_selector_title);
            } else {
                actionBar.setTitle(title);
            }
        }
        erpList = (List<MemberEntityJd>) getIntent().getSerializableExtra(EXTRA_CONTACT);
        if (erpList == null) {
            try {
                // 处理外部 sdk 无法直接使用 MemberEntityJd 时
                String mparam = URLDecoder.decode(getIntent().getStringExtra(DEEPLINK_PARAM),"UTF-8");
                Type type = new TypeToken<ArrayList<MemberEntityJd>>() {
                }.getType();
                List<MemberEntityJd> rs = new Gson().fromJson(mparam, type);
                if (rs != null && !rs.isEmpty()) {
                    erpList = rs;
                }
            } catch (Exception e) {

            }
        }
        if (erpList == null) {
            erpList = new ArrayList<>();
        }
        altenateErpList = (List<MemberEntityJd>) getIntent().getSerializableExtra(EXTRA_ALTERNATE_CONTACT);
        if (altenateErpList == null) {
            altenateErpList = new ArrayList<>();
        }
        recyclerView = findViewById(R.id.rv_contact);
        checkBox = findViewById(R.id.cb_all);
        button = findViewById(R.id.bt_finish);
        checkBox.setChecked(true);
        checkBox.setOnCheckedChangeListener(onCheckedChangeListener);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        ArrayList<MemberEntityJd> contactInfos = new ArrayList<>();
        for (MemberEntityJd entityJd : erpList) {
            if (!entityJd.isExternal()) {
                UiCommonInterface.ContactInfo info = OpimUiWrapper.getInstance().getContactInfoFromLocal(entityJd.mId, entityJd.mApp);
                if (info != null) {
                    updateMemberEntity(entityJd, info);
                }
            }
            contactInfos.add(entityJd);
        }

        adapter = new ContactSelectorAdapter(this, contactInfos, max, mOptionalEnable);
        adapter.setListener(new ContactSelectorAdapter.ContactSelectorListener() {
            @Override
            public void onSelectorChange() {
                checkBox.setOnCheckedChangeListener(null);
                if (adapter.getContactInfos().size() == adapter.getSelected().size()) {
                    checkBox.setChecked(true);
                } else {
                    checkBox.setChecked(false);
                }
                checkBox.setOnCheckedChangeListener(onCheckedChangeListener);
                setFinishButtonText();
            }
        });
        recyclerView.setAdapter(adapter);
        recyclerView.setItemAnimator(null);
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ArrayList<MemberEntityJd> list = new ArrayList<>(adapter.getSelected());
                Intent intent = new Intent();
                intent.putExtra(EXTRA_CONTACT, list);
                try{
                    JSONArray array = new JSONArray();
                    for(MemberEntityJd jd : list){
                        JSONObject object = new JSONObject();
                        object.put("mId",jd.mId);
                        object.put("mAvatar",jd.mAvatar);
                        object.put("mName",jd.mName);
                        object.put("mApp",jd.mApp);
                        object.put("mEmail",jd.mEmail);
                        object.put("mPhone",jd.mPhone);
                        object.put("mExternalType",jd.mExternalType);
                        object.put("mExternal", jd.mExternal);
                        object.put("mOptional", jd.isOptional());
                        array.put(object);
                    }
                    intent.putExtra(EXTRA_CONTACT_STRING,array.toString());
                }catch (Exception e) {
                    // empty
                }
                if (mBroadcastResult) {
                    intent.setAction(ACTION_GET_RESULT);
                    LocalBroadcastManager.getInstance(ContactSelectorActivity.this).sendBroadcast(intent);
                    setResult(RESULT_OK);
                } else {
                    setResult(RESULT_OK, intent);
                }
                finish();
            }
        });
        setFinishButtonText();
        if (erpList.size() == 0) {
            if (altenateErpList.size() == 0) {
                openIMContact();
            } else {
                for (MemberEntityJd entityJd : altenateErpList) {
                    UiCommonInterface.ContactInfo info = OpimUiWrapper.getInstance().getContactInfoFromLocal(entityJd.mId, entityJd.mApp);
                    if (info != null) {
                        updateMemberEntity(entityJd, info);
                        adapter.addContactInfo(entityJd, false);
                    }
                }
                adapter.notifyDataSetChanged();
                getContactInfoFromNetIfNeed();
            }
        }
    }

    private void setFinishButtonText() {
        button.setText(getString(R.string.me_im_dd_button_finish, String.valueOf(adapter.getSelected().size())));
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_add, menu);
        MenuItem menuItem = menu.findItem(R.id.action_ok);
        SpannableString s = new SpannableString(getString(R.string.me_im_dd_menu_add));
        s.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.red_warn)), 0, s.length(), 0);
        menuItem.setTitle(s);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_ok) {
            openIMContact();
            return true;
        } else if (item.getItemId() == android.R.id.home) {
            setResult(RESULT_CANCELED);
            finish();
        }
        return super.onOptionsItemSelected(item);
    }

    private void openIMContact() {
        if (adapter.getSelected().size() >= max) {
            ToastUtils.showToast(getString(R.string.me_im_dd_select_max_tip, String.valueOf(max)));
            return;
        }
        ArrayList<MemberEntityJd> erps = new ArrayList(adapter.getSelected());
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setFrom(TYPE_ADD_MEMBER)
                .setShowConstantFilter(true)
                .setConstantFilter(erps)
                .setShowSelf(true)
                .setOptionalFilter(null)
                .setShowOptionalFilter(false)
                .setTitle(mSelectorTitle);

        if (max != Integer.MAX_VALUE) {
            entity.setMaxNum(max - adapter.getSelected().size());
        }

        entity.mSpecifyAppId = specifyAppId;
        entity.setExcludeAppIds(excludeAppIds);
        entity.setExternalDataEnable(mExternalContactEnable);
        imDdService.gotoMemberList(this, 100, entity, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> bean) {
                boolean empty = adapter.getContactInfos().size() == 0;
                if (bean != null) {
                    for (MemberEntityJd memberEntityJd : bean) {
                        if (memberEntityJd != null) {
                            UiCommonInterface.ContactInfo contactInfo = OpimUiWrapper.getInstance().getContactInfoFromLocal(memberEntityJd.mId, memberEntityJd.mApp);
                            updateMemberEntity(memberEntityJd, contactInfo);
                            adapter.addContactInfo(memberEntityJd, true);
                        }
                    }
                    adapter.notifyDataSetChanged();
                    getContactInfoFromNetIfNeed();
                    setFinishButtonText();
                    if (empty && mCloseOnResult) {
                        button.performClick();
                    }
                }
            }

            @Override
            public void onFail() {
                boolean empty = adapter.getContactInfos().size() == 0;
                if (empty) {
                    setResult(RESULT_CANCELED);
                    finish();
                }
            }
        });
    }

    private void getContactInfoFromNetIfNeed() {
        for (MemberEntityJd info : adapter.getContactInfos()) {
            if (!info.isExternal() && TextUtils.isEmpty(info.department)) {
                getContactInfoFromNet(info.getId(), info.getApp());
            }
        }
    }

    private void getContactInfoFromNet(String id, String app) {
        OpimUiWrapper.getInstance().getContactInfoFromNet(id, app, 0, new UiCommonInterface.ContactInfoGetListener() {
            @Override
            public void onInfoResult(UiCommonInterface.ContactInfo contactInfo) {
                if (contactInfo == null) {
                    return;
                }
                for (int i = 0; i < adapter.getContactInfos().size(); i++) {
                    MemberEntityJd info = adapter.getContactInfos().get(i);
                    if (TextUtils.equals(info.getId(), contactInfo.uid) && TextUtils.equals(info.getApp(), contactInfo.app)) {
                        info.setDepartment(contactInfo.department);
                        info.setName(contactInfo.nickName);
                        info.setAvatar(contactInfo.avatar);
                        info.setPosition(contactInfo.position);
                        adapter.notifyItemChanged(i);
                    }
                }
            }
        });
    }

    private void updateMemberEntity(MemberEntityJd entity, UiCommonInterface.ContactInfo info) {
        if(entity == null || info == null) return;
        entity.setAvatar(info.avatar);
        entity.setDepartment(info.department);
        entity.setPosition(info.position);
        entity.setName(info.nickName);
        entity.setEmail(info.email);
    }
}
