package com.jd.me.dd.im;

import android.view.View;

import androidx.annotation.Nullable;

import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import jd.cdyjy.jimcore.commoninterface.conversationlist.IMSession;

public class SessionImpl extends IMSession {

    private AppService appService = AppJoint.service(AppService.class);
    @Override
    public void onSetBannerView(@Nullable View view) {
        appService.setBannerView(view);
    }

    @Override
    public boolean hasBanner() {
        return appService.hasBanner();
    }
}
