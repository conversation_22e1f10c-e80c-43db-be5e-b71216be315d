package com.jd.me.dd.im.biz.quickapp;

import static com.jd.me.dd.im.biz.quickapp.Constants.QUICK_APP_ITEM_MAX;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.me.dd.im.R;

import java.util.List;

/**
 *
 */
/*
 * Time: 2024/7/18
 * Author: qudongshi
 * Description:
 */
public class QuickAppAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private List<QuickAppsModel.QuickAppItem> data;
    private IQuickAppClickCallback listener;

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_im_quick_app_item, parent, false);
        return new ItemViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof ItemViewHolder) {
            ItemViewHolder viewHolder = (ItemViewHolder) holder;
            viewHolder.tvApp.setText(data.get(position).getAppName(viewHolder.itemView.getContext()));
            if (position == getItemCount() - 1) {
                viewHolder.ivSplit.setVisibility(View.GONE);
            } else {
                viewHolder.ivSplit.setVisibility(View.VISIBLE);
            }
            viewHolder.tvApp.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.callback(data.get(position));
                    }
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        if (data == null) {
            return 0;
        }
        if (data.size() > QUICK_APP_ITEM_MAX) {
            return QUICK_APP_ITEM_MAX;
        }
        return data.size();
    }

    public void setData(List<QuickAppsModel.QuickAppItem> items) {
        data = items;
    }

    public void setCallback(IQuickAppClickCallback val) {
        listener = val;
    }

    public class ItemViewHolder extends RecyclerView.ViewHolder {
        public TextView tvApp;
        public ImageView ivSplit;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            tvApp = itemView.findViewById(R.id.app_item);
            ivSplit = itemView.findViewById(R.id.app_split);
        }
    }

}
