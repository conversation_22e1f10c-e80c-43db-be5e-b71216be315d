/*
package com.jd.me.dd.im.ui;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import java.util.ArrayList;
import java.util.List;

import cn.cu.jdmeeting.jme.base.JoyMeetingSDKHelper;
import cn.cu.jdmeeting.jme.handler.OpenContactsPageListener;

import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;

public class JdMeetingContactSelectorImpl implements OpenContactsPageListener {

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Override
    public void openContactsPageCallBack(String json, final int type*/
/* 0 - 预约邀请，1 - 会议中邀请 *//*
) {
        MELogUtil.localI(MELogUtil.TAG_JMET, "openContactsPageCallBack type = " + type);
        int max = 300;
        ArrayList<MemberEntityJd> selected = new Gson().fromJson(json, new TypeToken<List<MemberEntityJd>>() {}.getType());
        if (selected == null) {
            selected = new ArrayList<>();
        }
        MemberListEntityJd entity = new MemberListEntityJd();
        List<String> appIds = TenantConfigBiz.INSTANCE.getCollaborativelyApps();
        entity.setFrom(TYPE_ADD_MEMBER).setShowConstantFilter(false).setConstantFilter(null).setSpecifyAppId(appIds)
                .setShowSelf(true).setOptionalFilter(selected).setShowOptionalFilter(true).setMaxNum(max);
        //setConstantFilter(selected).setOptionalFilter(null).setMaxNum(max - selected.size())，需要减去已选数量
        //setConstantFilter(null).setOptionalFilter(selected).setMaxNum(max)，不减已选数量
        imDdService.gotoMemberList(AppBase.getTopActivity(), 100, entity, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> bean) {
                MELogUtil.localI(MELogUtil.TAG_JMET, "setSelectedContactsJsonStr bean size = " + (bean != null ? bean.size() : 0) + ";type = " + type);
//                JoyMeetingSDKHelper.getInstance().setSelectedContactsJsonStr(new Gson().toJson(bean), type);
            }

            @Override
            public void onFail() {

            }
        });
    }
}
*/
