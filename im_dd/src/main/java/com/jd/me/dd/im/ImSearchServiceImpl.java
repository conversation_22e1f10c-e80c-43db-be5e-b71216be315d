package com.jd.me.dd.im;


import static com.jd.oa.router.DeepLink.UNIFIED_SEARCH;
import static com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate.TYPE_CONTACT;
import static com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate.TYPE_GROUP;
import static com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate.TYPE_MESSAGE;
import static com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate.TYPE_NOTICE;
import static com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate.TYPE_ROBOT;
import static com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate.TYPE_WAITER;

import android.content.Intent;
import android.text.SpannableString;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.fastjson.JSON;
import com.chenenyu.router.Router;
import com.jd.cdyjy.common.base.ui.custom.chat.SearchTabType;
import com.jd.cdyjy.common.base.ui.custom.chat.SearchType;
import com.jd.cdyjy.common.base.ui.custom.search.ExternalSearchEntity;
import com.jd.cdyjy.common.base.ui.custom.search.ExternalSearchMoreEntity;
import com.jd.cdyjy.common.base.ui.custom.search.IMSearchOperation;
import com.jd.cdyjy.common.base.ui.fragment.BaseSearchFragment;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper;
import com.jd.oa.unifiedsearch.all.util.SearchHistoryUtil;
import com.jd.oa.unifiedsearch.all.util.SearchUtil;
import com.jd.oa.unifiedsearch.joyspace.data.JoySpaceDocument;
import com.jd.oa.unifiedsearch.joyspace.utils.JoyspaceDetailDeeplinkGenKt;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Request;
import okhttp3.Response;

public class ImSearchServiceImpl extends IMSearchOperation {
    private static final String TAG = "ImSearchServiceImpl";

    @Override
    public BaseSearchFragment getSearchTabFragment(SearchTabType searchTabType) {
        Log.d(TAG, "getSearchTabFragment: " + searchTabType);
        if (SearchTabType.APP.equals(searchTabType)) {
            return UnifiedSearchFragment.newInstance(UnifiedSearchTabDelegate.TYPE_APP);
        } else if (SearchTabType.JOY_SPACE.equals(searchTabType)) {
            return UnifiedSearchFragment.newInstance(UnifiedSearchTabDelegate.TYPE_JOYSPACE);
        } else if (SearchTabType.SCHEDULE.equals(searchTabType)) {
            return UnifiedSearchFragment.newInstance(UnifiedSearchTabDelegate.TYPE_JOYDAY);
        }
        return null;
    }

    @Override
    public boolean onItemClick(ExternalSearchEntity externalSearchEntity) {
        JSONObject object = null;
        try {
            object = new JSONObject(externalSearchEntity.jsonData);
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }

        if (externalSearchEntity.mSearchType == SearchType.APP) {
            String appId = object.optString("id", null);
            if (!TextUtils.isEmpty(appId)) {
                Router.build(DeepLink.appCenter(appId, null)).go(AppBase.getTopActivity());
                return true;
            }
        } else if (externalSearchEntity.mSearchType == SearchType.JOY_SPACE) {
            JoySpaceDocument document = JSON.parseObject(externalSearchEntity.jsonData, JoySpaceDocument.class);
            String deeplink = JoyspaceDetailDeeplinkGenKt.generateDetailDeeplink(AppBase.getAppContext(), document);
            Router.build(deeplink).go(AppBase.getTopActivity());
            if (!TextUtils.isEmpty(document.getLogUrlMain())) {
                joySpaceLog(document.getLogUrlMain());
            }
            return true;
        }
        return false;
    }

    private void joySpaceLog(String url) {
        Request request = new Request.Builder().url(url).get().build();
        //只支持get请求 没办法
        Call call = HttpManager.getHttpClient().newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
//                Log.e(TAG, "onFailure: "+e.getLocalizedMessage() );
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
//                Log.e(TAG, "onResponse: "+response.body().string() );
            }
        });
    }


    @Override
    public boolean onMoreClick(ExternalSearchMoreEntity externalSearchMoreEntity) {

        return false;
    }

    @Override
    public void finishActivityBySearchBusiness() {
        Intent i = new Intent();
        i.setAction("search.action.close");
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(i);
    }

    @Override
    public void itemClickNotify(SearchType type, String key) {
        SearchHistoryUtil.addSearchHistory(key);
    }

    @Override
    public SpannableString getSearchTips(SearchType searchType) {
        return SearchUtil.getFeedbackContent();
    }

    @Override
    public String getOpenDeeplink() {
        return UNIFIED_SEARCH + "?mparam=%7B%22defaultTab%22%3A%20%220%22%7D";
    }

    @Override
    public boolean newSearchEnable() {
//        String flag = ABTestManager.getInstance().getConfigByKey("android.search.v2.enable", "0");
//        if (flag.equals("0")) {
//            return false;
//        }
        return true;
    }

    @Override
    public void bindEmptyUI(ViewGroup viewGroup) {
        View emptyView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.unifiedsearch_pub_layout_no_result, viewGroup, false);
        emptyView.setVisibility(View.VISIBLE);
        viewGroup.addView(emptyView);
    }

    @Override
    public String getSearchHint() {
        return null;
    }

    /**
    支持咚咚通过deeplink携带参数跳转到主搜
     */
    @Override
    public void gotoSearch(SearchTabType searchTabType, String s) {
        if (searchTabType != null) {
            String deeplink = DeepLink.search(searchTabTypeToId(searchTabType), s);
            if (!TextUtils.isEmpty(deeplink)) {
                Router.build(deeplink).go(AppBase.getTopActivity());
            }
        }
    }


    @Override
    public List<SearchTabType> getDefaultSearchTabs() {

        List<SearchTabType> searchTabTypes = new ArrayList<>();
        searchTabTypes.add(SearchTabType.APP);

        if (TenantConfigBiz.INSTANCE.isJoySpaceEnable()) {
            searchTabTypes.add(SearchTabType.JOY_SPACE);
        }

        if (TenantConfigBiz.INSTANCE.isJoyDayEnable()) {
            searchTabTypes.add(SearchTabType.SCHEDULE);
        }

        return searchTabTypes;
    }

    @Override
    public List<SearchType> getDefaultAllTypes() {
        ArrayList<SearchType> totalSearchType = new ArrayList<>();
        totalSearchType.add(SearchType.APP);

        if (TenantConfigBiz.INSTANCE.isJoySpaceEnable()) {
            totalSearchType.add(SearchType.JOY_SPACE);
        }
        return totalSearchType;
    }

    public static String idToSearchType(int id) {
        switch (id) {
            case 1:
                return SearchConfigHelper.tab_contact;
            case 2:
                return SearchConfigHelper.tab_group;
            case 3:
                return SearchConfigHelper.tab_message;
            case 5:
                return SearchConfigHelper.tab_app;
            case 6:
                return SearchTabType.SECRET_SINGLE.toString().toLowerCase();
            case 7:
                return SearchTabType.SECRET_GROUP.toString().toLowerCase();
            case 8:
                return SearchConfigHelper.tab_notice;
            case 9:
                return SearchConfigHelper.tab_joyspace;
            case 10:
                return SearchConfigHelper.tab_joyday;
            case 11:
                return SearchConfigHelper.tab_waiter;
            case 12:
                return SearchConfigHelper.tab_robot;
            case 13:
                return SearchConfigHelper.tab_task;
            case 14:
                return SearchConfigHelper.tab_process;
            case 15:
                return SearchConfigHelper.tab_approval;
            default:
                return SearchConfigHelper.tab_all;
        }
    }

    /**
     * idToTabType的逆向复原，根据id转换成tabType，方便在deeplink里传递SearchTabType
     */
    public static int searchTabTypeToId(SearchTabType tabType) {
        switch (tabType) {
            case CONTACT:
                return 2;
            case GROUP:
                return 3;
            case MESSAGE:
                return 4;
            case APP:
                return 5;
            case SECRET_SINGLE:
                return 6;
            case SECRET_GROUP:
                return 7;
            case NOTICE:
                return 8;
            case JOY_SPACE:
                return 9;
            case SCHEDULE:
                return 10;
            case ALL:
            default:
                return -1; // 如果没有匹配的类型，返回一个默认值，这里用-1表示
        }
    }

    public static SearchType getSearchType(String type) {
        if (TextUtils.isEmpty(type)) {
            return null;
        }
        switch (type.toLowerCase()) {
            case TYPE_MESSAGE:
                return SearchType.MESSAGE;
            case TYPE_CONTACT:
                return SearchType.CONTACT;
            case TYPE_GROUP:
                return SearchType.GROUP;
            case TYPE_ROBOT:
                return SearchType.ROBOT;
            case TYPE_NOTICE:
                return SearchType.NOTICE;
            case TYPE_WAITER:
                return SearchType.WAITER;
            default:
                return null;
        }
    }
}