package com.jd.me.dd.im

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import androidx.fragment.app.FragmentActivity
import com.chenenyu.router.Router
import com.jd.cdyjy.icsp.cache.AppCache
import com.jd.cdyjy.icsp.utils.PhoneUtil
import com.jd.me.dd.im.conference.VideoConferenceForegroundService
import com.jd.me.dd.im.conference.model.ConferenceUser
import com.jd.me.dd.im.view.StartConferenceGlobalFragment
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.abtest.ABTestManager
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.model.service.AppService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.TabletUtil
import com.jd.oa.utils.ToastUtils
import com.jdcloud.jbmeeting_ui.error.ErrorParse
import com.jdcloud.mt.me.modle.MeetingConfig
import com.jdcloud.mt.me.modle.SourceType
import com.jingdong.conference.conference.model.Conference
import com.jingdong.conference.conference.model.Conference.Companion.STATUS_CREATED
import com.jingdong.conference.conference.model.InviteType
import com.jingdong.conference.conference.model.StartConferenceType
import com.jingdong.conference.conference.model.StopReason
import com.jingdong.conference.integrate.ServiceHubLazy
import com.jingdong.conference.integrate.ServiceHubLazy.conferenceService
import com.jingdong.conference.integrate.ServiceHubLazy.deviceService
import com.jingdong.conference.integrate.ServiceHubLazy.jdmtmeService
import com.jingdong.conference.integrate.ServiceHubLazy.wrapperService
import jd.cdyjy.jimcore.application.BaseCoreApplication
import jd.cdyjy.jimcore.commoninterface.meeting.IMMeeting
import jd.cdyjy.jimcore.commoninterface.meeting.service.MeetingService
import jd.cdyjy.jimcore.conference.ConferenceClickJoinMeeting
import jd.cdyjy.jimcore.conference.ConferenceData
import jd.cdyjy.jimcore.conference.ConferenceInvite
import jd.cdyjy.jimcore.conference.ConferenceReject
import jd.cdyjy.jimcore.conference.User
import jd.cdyjy.jimcore.core.ipc_global.MyInfo
import jd.cdyjy.jimcore.http.reportLog.ReportLogUtil
import jd.cdyjy.jimcore.tools.heatset.HeadsetObserverAndHelper
import org.json.JSONObject
import voip.util.CallingVoipUtils
import java.util.Locale


class ImMeetingImpl : IMMeeting() {

    companion object {
        const val TAG: String = "MEETING"
        const val EVENT_VIDEO_MEETING = "Mobile_event_chat_VedioMeeting"

        fun showNewMeetingEntry() : Boolean{
            return "1" == ABTestManager.getInstance().getConfigByKey("android.meeting.tabbar.enable", "0")
        }
    }

    val appService: AppService = AppJoint.service(AppService::class.java)
    var mCurrentMeetingId: String? = null
    var mConference: Conference? = null
    var mInviter: ConferenceUser? = null
    var mConferenceType: Int? = 0

    private val disable: Boolean by lazy {
        ABTestManager.getInstance().getConfigByKey("monkey.test.disable", "0") != "0"
    }

    /**
     * 启动音视频会议入口dialog
     */
    override fun showMeetingEntrance() {
        if (showNewMeetingEntry()) {
            val activity = AppBase.getTopActivity()
            MELogUtil.localI(TAG, "call showMeetingEntrance New, top: " + activity?.javaClass?.name)
            val deeplink = Uri.parse(DeepLink.MEETING)
                .buildUpon()
                .appendQueryParameter("mparam",
                    JSONObject().apply {
                        put("isTab", "0")
                    }.toString()
                )
                .build()
            val intent = activity?.let { Router.build(deeplink).getIntent(it) }
            intent?.run {
                putExtra(FunctionActivity.SHOW_ACTION, false)
                activity.startActivity(this)
            } ?: MELogUtil.localI(TAG, "call showMeetingEntrance New, intent: $intent")
        } else {
            MELogUtil.localI(TAG, "call showMeetingEntrance Old")
            val conferenceGlobalFragment = StartConferenceGlobalFragment()
            val activity: FragmentActivity = AppBase.getTopActivity() as FragmentActivity
            conferenceGlobalFragment.show(activity.supportFragmentManager, "")
        }
        JDMAUtils.clickEvent("", EVENT_VIDEO_MEETING, null)
    }

    /**
     * 加入会议
     * [meetingId]会议ID
     * [meetingCode]会议Code
     * [passWord]密码
     */
    override fun attendMeeting(meetingId: String?, meetingCode: Long?, passWord: String?, type: MeetingService.AttendMeetingType) {
//        internalService?.initJRTC(AppBase.getAppContext())
        MELogUtil.localI(TAG, "call attendMeeting:{meetingId:$meetingId,meetingCode:$meetingCode}, ${CommonUtils.getCallerStack()}")
        if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
            ToastUtils.showToast(AppBase.getAppContext().getString(R.string.audio_channel_occupied), Gravity.CENTER)
            return
        }
        val sourceType = when (type) {
            MeetingService.AttendMeetingType.MESSAGE_CARD, MeetingService.AttendMeetingType.EVENT -> {
                SourceType.IM
            }
            MeetingService.AttendMeetingType.DEEPLINK -> {
                SourceType.DEEP
            }
            else -> {
                SourceType.UNKNOWN
            }
        }
        if (TabletUtil.isEasyGoAndFoldOrTablet()) {
            TabletUtil.startFullScreenActivity {
                ServiceHubLazy.jdmtmeService?.attend(null, meetingId, meetingCode, passWord, null, sourceType)
            }
        } else {
            ServiceHubLazy.jdmtmeService?.attend(null, meetingId, meetingCode, passWord, null, sourceType)
        }
    }

    /**
     * 会议业务,+号面板 快捷入口会议业务
     */
    override fun funcMeeting(gid: String?, pin: String?, app: String?, groupName: String?) {
        MELogUtil.localI(TAG, "call funcMeeting:{gid:$gid,pin:$pin,app$app}")

        if (disable) return

        if (CallingVoipUtils.instance.hasCalling()) {
            ToastUtils.showToastLong(R.string.meeting_start_conference_conference_busy)
            meetingLog { "CallingVoipUtils.instance.hasCalling(): true, 已经在会议中" }
        } else if (ServiceHubLazy.jdmtmeService?.busy!!) {
            ToastUtils.showToastLong(R.string.meeting_start_conference_conference_busy)
            meetingLog { "ServiceHubLazy.jdmtmeService?.busy!!: true, 已经在会议中" }
        } else {
            if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
                return
            }
            val topActivity = AppBase.getTopActivity()
            if (topActivity == null) {
                return
            }
            val mIsGroup = !gid.isNullOrEmpty()
            if (mIsGroup) {
                if (TabletUtil.isEasyGoAndFoldOrTablet()) {
                    TabletUtil.startFullScreenActivity {
                        val bundle = Bundle()
                        bundle.putSerializable("source", ReportLogUtil.CreateConferenceType.GROUP)
                        bundle.putString("group_name", groupName)
                        ServiceHubLazy.jdmtmeService?.startConference(topActivity, StartConferenceType.CREATE,
                                true,
                                true,
                                false, gid, bundle)
                    }
                } else {
                    val bundle = Bundle()
                    bundle.putSerializable("source", ReportLogUtil.CreateConferenceType.GROUP)
                    bundle.putString("group_name", groupName)
                    ServiceHubLazy.jdmtmeService?.startConference(topActivity, StartConferenceType.CREATE,
                            true,
                            true,
                            false, gid, bundle)
                }
            } else {
                var user: ConferenceUser? = null
                if (TextUtils.isEmpty(app) || TextUtils.isEmpty(pin)) {
                    user = ConferenceUser("", "", "")
                } else {
                    user = ConferenceUser(pin, app, "0")
                    val contactInfo = AppCache.getInstance().getContactInfo(pin, app, false)
                    if (contactInfo != null) {
                        user.nickname = contactInfo.name
                        user.avatar = contactInfo.avatar
                    }
                }
                if (TabletUtil.isEasyGoAndFoldOrTablet()) {
                    TabletUtil.startFullScreenActivity {
                        val bundle = Bundle()
                        bundle.putSerializable("source", ReportLogUtil.CreateConferenceType.SINGLE)
                        ServiceHubLazy.jdmtmeService?.call(topActivity, user, bundle)
                    }
                } else {
                    val bundle = Bundle()
                    bundle.putSerializable("source", ReportLogUtil.CreateConferenceType.SINGLE)
                    ServiceHubLazy.jdmtmeService?.call(topActivity, user, bundle)
                }
            }
        }
    }

    /**
     * 长链接收到会议邀请
     */
    override fun conferenceInvite(invite: ConferenceInvite?) {
        val inviteMsg = "conferenceInvite, meetingId: ${invite?.meetingId}, code: ${invite?.meetingCode}"
        MELogUtil.localI(TAG, inviteMsg)
        meetingLog {inviteMsg}
//        if (!forwardProcessConferenceId.isNullOrBlank() && forwardProcessConferenceId == conferenceInvite?.meetingId) {
//            //表示已经预先处理了，处理的tcp包先于邀请的包到达
//            forwardProcessConferenceId = null
//            return
//        }
        if (invite?.invitor == null || TextUtils.isEmpty(invite.meetingId)) {
            return
        }
        //邀请者是自己，不处理，服务器目前所有参与方都会发送消息---2021.02.25
        if (TextUtils.equals((invite.invitor?.pin ?: "").lowercase(Locale.getDefault()), PreferenceManager.UserInfo.getUserName().lowercase(Locale.getDefault())) &&
            TextUtils.equals(invite.invitor?.app ?: "", PreferenceManager.UserInfo.getTimlineAppID())) {
            meetingLog {"conferenceInvite, invitor is self"}
            return
        }

        val conference = Conference(
                invite.meetingId ?: "",
                invite.meetingCode ?: 0,
                null,
                null,
                null,
                null,
                invite.meetingName,
                null,
                Conference.TYPE_INSTANT,
                STATUS_CREATED,
                invite.totalNum ?: 0,
                null,
                null,
                null,
                null,
                false,
                0,
                null,
                null,
                Conference.RECORD_STATUS_IDLE,
                null,
                null
        )

        val inviter: ConferenceUser = ConferenceUser(
                invite.invitor?.pin ?: "", invite.invitor?.app ?: "",
                if (TextUtils.isEmpty(invite.invitor?.teamId)) "0" else invite.invitor?.teamId
                        ?: ""
        )
        var name = invite.invitor?.pin
        var avater: String? = null
        val info = AppCache.getInstance()
                .getContactInfo(invite.invitor?.pin, invite.invitor?.app, true)
        if (info != null) {
            if (!TextUtils.isEmpty(info.name)) {
                name = info.name
            }
            avater = info.avatar
        }

        inviter.nickname = name
        inviter.avatar = avater
        if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
            val injector = ConferenceUser(PreferenceManager.UserInfo.getUserName(),
                    PreferenceManager.UserInfo.getTimlineAppID(),
                    PreferenceManager.UserInfo.getTeamId())
            injector.nickname = PreferenceManager.UserInfo.getUserRealName()
            injector.avatar = PreferenceManager.UserInfo.getUserCover()
            jdmtmeService?.reject(conference, inviter, StopReason.BUSY)
            meetingLog {"conferenceInvite, invitor is self"}
            return
        }

        if (JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
            mCurrentMeetingId = invite.meetingId
        }

        if (appService.isForeground) {
            //APP在前台，直接打开邀请页面
            ReportLogUtil.conferenceAction("invite", "Tcp invite foreground", conference.code)
            startVideoConference(conference, inviter, invite.conferenceType ?: 0)
            meetingLog {"conferenceInvite, appService isForeground"}
        } else {
            //不在前台
            //判断后台启动权限
            if (PhoneUtil.isBgStartAllowed(AppBase.getAppContext())) {
                //有权限
                if (AppBase.getMainActivity() != null) {
                    //有UI，表示只是在后台
                    ReportLogUtil.conferenceAction("invite", "Tcp invite background, has Permission, hasMainUI", conference.code)
                    startVideoConference(conference, inviter, invite.conferenceType ?: 0)
                    meetingLog {"conferenceInvite, Tcp invite background, has Permission, hasMainUI, ${conference.code}"}
                } else {
                    //无UI，表示应用被杀了后重启进程
                    mConference = conference
                    mInviter = inviter
                    mConferenceType = invite.conferenceType

                    val intent = Intent()
                    val appInfo = BaseCoreApplication.getAppInfo(AppBase.getAppContext())
                    val mainActivityActionName = appInfo.metaData.getString("mainActivityActionName")
                    intent.action = mainActivityActionName
                    intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK) //进程不在top时
                    AppBase.getAppContext().startActivity(intent)
                    ReportLogUtil.conferenceAction("invite", "Tcp invite background, has Permission, noMainUI", conference.code)
                    meetingLog {"conferenceInvite, Tcp invite background, has Permission, noMainUI, ${conference.code}"}
                }
            } else {
                //无权限，开service
                invite.conferenceType?.let {
                    mConference = conference
                    mInviter = inviter
                    mConferenceType = it
                    // TODO:  VideoConferenceForegroundService 里面的一些功能是否需要接口调用
                    VideoConferenceForegroundService.startService(BaseCoreApplication.getApplication(), conference.id, conference.subject,
                            JsonUtils.getJsonString(conference), JsonUtils.getJsonString(inviter),
                            it, null)
                    ReportLogUtil.conferenceAction("invite", "Tcp invite background, no Permission, startService", conference.code)
                    meetingLog {"conferenceInvite, Tcp invite background, no Permission, startService, ${conference.code}"}
                }
            }
        }
    }

    private fun startVideoConference(conference: Conference, inviter: ConferenceUser, conferenceType: Int) {
        MELogUtil.localI(TAG, "call startVideoConference:{meetingId:${conference.id}, meetingCode: ${conference.code}, inviter: ${inviter.pin}".plus(",conferenceType:$conferenceType}"))
        if (TabletUtil.isEasyGoAndFoldOrTablet()) {
            TabletUtil.startFullScreenActivity {
                val bundle = Bundle()
                bundle.putSerializable("source", ReportLogUtil.CreateConferenceType.GROUP)
                jdmtmeService?.called(AppBase.getTopActivity(), conference, inviter,
                        if (conferenceType == 0) InviteType.SINGLE else InviteType.GROUP,
                        // TODO: HeadsetObserverAndHelper.getInstance().isPhoneRingMute 后期改咚咚接口调用
                        HeadsetObserverAndHelper.getInstance().isPhoneRingMute || !MeetingConfig.playRingSetting)
            }
        } else {
            jdmtmeService?.called(AppBase.getTopActivity(), conference, inviter,
                    if (conferenceType == 0) InviteType.SINGLE else InviteType.GROUP,
                    HeadsetObserverAndHelper.getInstance().isPhoneRingMute || !MeetingConfig.playRingSetting)
        }
    }

    /**
     * 长链接收到其他端拒绝会议
     */
    override fun conferenceReject(reject: ConferenceReject?) {
        MELogUtil.localI(TAG, "call conferenceReject, meetingId: ${reject?.meetingId}, meetingCode: ${reject?.meetingCode}")
        val rejector: User = reject?.rejector ?: return
        val conferenceRejector = ConferenceUser(rejector.pin, rejector.app, rejector.teamId)
        val isSelf = TextUtils.equals(rejector.app, MyInfo.mMy.appId) && (rejector.pin ?: "").equals(MyInfo.mMy.pin, ignoreCase = true)

        if (!TextUtils.equals(reject.deviceId, deviceService?.uuid) && isSelf) {
            ToastUtils.showToast(R.string.meeting_video_conference_rejected_in_other_device)
            jdmtmeService?.stopCalled(StopReason.REJECT, reject.meetingCode)
        } else {
            if (!isSelf) {
                var reason = StopReason.REJECT
                if (TextUtils.equals(reject.reason, "52")) {
                    reason = StopReason.BUSY
                } else if (TextUtils.equals(reject.reason, "54")) {
                    reason = StopReason.TIMEOUT
                }
                jdmtmeService?.stopCall(reason, conferenceRejector, reject.meetingCode)
            }
        }
        VideoConferenceForegroundService.stopService(AppBase.getAppContext())
        ReportLogUtil.conferenceAction("reject", "reject", reject.meetingCode ?: 0)
        clearInvite()
    }

    private fun clearInvite() {
        mConference = null
        mInviter = null
        mConferenceType = 0
    }

    /**
     * 长链接收到会议结束
     */
    override fun conferenceEnd(end: ConferenceData?) {
        MELogUtil.localI(TAG, "call conferenceEnd, meetingId: ${end?.meetingId}, meetingCode: ${end?.meetingCode}")
        if (TextUtils.equals(mCurrentMeetingId, end?.meetingId)) {
//            ToastUtils.showToast(R.string.meeting_video_conference_hang_up)
            jdmtmeService?.stopCalled(StopReason.END, end?.meetingCode)
        }

        VideoConferenceForegroundService.stopService(AppBase.getAppContext())
        ReportLogUtil.conferenceAction("end", "end", end?.meetingCode ?: 0)
        meetingLog {"conferenceEnd, end, ${end?.meetingCode}"}
        clearInvite()
    }

    /**
     * 长链接收到点击加入会议
     */
    override fun conferenceJoin(join: ConferenceClickJoinMeeting?) {
        MELogUtil.localI(TAG, "call conferenceJoin, meetingId: ${join?.meetingId}, meetingCode: ${join?.meetingCode} current device is ${deviceService?.uuid}")
        if (!TextUtils.equals(join?.deviceId, deviceService?.uuid)) {
            val calledSession = conferenceService?.processingCalled?.value?: wrapperService?.processingCalled?.value
            val conferenceSession = conferenceService?.processingConference?.value
            val pendingConference = conferenceService?.pendingConference?.value
            if (null != calledSession && TextUtils.equals(calledSession.conference.id, join?.meetingId)) {
                val conference = conferenceSession?.conference?.value
                if (pendingConference == null) {
                    //手机端未入会
                    if (conference == null || !TextUtils.equals(conference.id, join?.meetingId)) {
                        ToastUtils.showToast(R.string.meeting_video_conference_joined_in_other_device)
                    }
                } else {
                    if (!TextUtils.equals(pendingConference.id, join?.meetingId)) {
                        ToastUtils.showToast(R.string.meeting_video_conference_joined_in_other_device)
                    }
                }
                jdmtmeService?.stopCalled(StopReason.ANSWER, join?.meetingCode)
            }
        }
        VideoConferenceForegroundService.stopService(AppBase.getAppContext())
        ReportLogUtil.conferenceAction("join", "join", join?.meetingCode ?: 0)
        clearInvite()
    }

    private fun meetingLog(msg: () -> String) {
        runCatching {
            ErrorParse.errorParse(331001, errorMsg = msg())
        }
    }
}