package com.jd.me.dd.im;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelStoreOwner;

import com.jd.cdyjy.common.base.ui.fragment.BaseSearchFragment;
import com.jd.oa.unifiedsearch.UnifiedSearchTab;
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate;

public class UnifiedSearchFragment extends BaseSearchFragment implements UnifiedSearchTabDelegate.Host, UnifiedSearchTab {
    private static final String TAG = "UnifiedSearchFragment";
    public static final String ARG_TYPE = "arg.type";

    public String sessionId;
    public String searchId;

    private UnifiedSearchTabDelegate mDelegate;

    private String mType;

    public static UnifiedSearchFragment newInstance(String type) {
        Bundle args = new Bundle();
        UnifiedSearchFragment fragment = new UnifiedSearchFragment();
        args.putString(ARG_TYPE, type);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public ViewModelStoreOwner getViewModelStoreOwner() {
        return getActivity();
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);

        mType = getArguments().getString(ARG_TYPE);

        UnifiedSearchTabDelegate.Factory factory = new UnifiedSearchTabDelegate.DefaultFactory();
        mDelegate = factory.create(this, mType);

        if (mDelegate == null) {
            throw new IllegalStateException("Cant create delegate with type: " + mType);
        }

        mDelegate.onAttach(context);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mDelegate.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return mDelegate.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mDelegate.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mDelegate.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        mDelegate.onViewStateRestored(savedInstanceState);
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mDelegate.onSaveInstanceState(outState);
    }

    @Override
    public void onResume() {
        super.onResume();
        mDelegate.onResume();
    }

    @Override
    public void onStart() {
        super.onStart();
        mDelegate.onStart();
    }

    @Override
    public void onPause() {
        super.onPause();
        mDelegate.onPause();
    }

    @Override
    public void onStop() {
        super.onStop();
        mDelegate.onStop();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mDelegate.onDestroyView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mDelegate.onDestroy();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        mDelegate.onConfigurationChanged(newConfig);
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        mDelegate.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        mDelegate.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public String getTitle() {
        return getArguments().getString(ARG_TYPE);
    }

    @Override
    public void search(String s) {
        mDelegate.search(s, true);
    }

    @Override
    public void tabSelected(String s) {
        if (!isAdded()) return;
        Log.d(TAG, "tabSelected: " + s);
        mDelegate.tabSelected(s);
    }

    @Override
    public void editTextChanged(String s) {
        if (!isAdded()) return;
        Log.d(TAG, "editTextChanged: " + s);
        mDelegate.onTextChanged(s);
    }

    @Override
    public void keyboardSearchClicked(String s) {
        if (!isAdded()) return;
        Log.d(TAG, "keyboardSearchClicked: " + s);
        mDelegate.search(s, true);
    }

    @Override
    public FragmentManager getHostFragmentManager() {
        return getChildFragmentManager();
    }

    @Override
    public void close() {
        getActivity().finish();
    }

    @Override
    public String getSessionId() {
        return sessionId;
    }

    @Override
    public String getSearchId() {
        return searchId;
    }
}