package com.jd.me.dd.im;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.ViewGroup;

import com.alibaba.fastjson.JSON;
import com.chenenyu.router.IRouter;
import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.cdyjy.icsp.migratechatmsg.MigrateDataHelper;
import com.jd.cdyjy.migratechatdata.server.IMigrateEventListener;
import com.jd.me.datetime.picker.DatePickerDialog;
import com.jd.me.datetime.picker.DatePickerView;
import com.jd.me.dd.im.model.DeptInfo;
import com.jd.me.dd.im.model.RelativeTeamInfo;
import com.jd.me.dd.im.model.RelativeTeamList;
import com.jd.me.dd.im.model.SubNodeInfo;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.exception.UploadException;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.fragment.FileViewer;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.model.MessageBean;
import com.jd.oa.model.MineInfo;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.JoyWorkService;
import com.jd.oa.model.service.TabbarService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.JPushService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.JoySpaceFileService;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.ColorGatewayNetEnvironment;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.network.utils.Utils;
import com.jd.oa.notification.ChatNotificationInfo;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.notification.HalfScreenProxyActivity;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.JdSaasLoginPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.storage.UseType;
import com.jd.oa.tablet.FoldPlaceHolderActivity;
import com.jd.oa.timezone.HolidayHelperKt;
import com.jd.oa.translation.AutoTranslateManager;
import com.jd.oa.translation.Language;
import com.jd.oa.ui.groupicon.AvatarUtil;
import com.jd.oa.unifiedsearch.joyday.utils.DateUtil;
import com.jd.oa.utils.ActiveAnalyzeUtil;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.TaskNotificationSettingUtil;
import com.jd.oa.utils.Utils2App;
import com.jd.oa.utils.VerifyUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.Serializable;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import jd.cdyjy.jimcore.commoninterface.AudioPermissionEntity;
import jd.cdyjy.jimcore.commoninterface.AudioType;
import jd.cdyjy.jimcore.commoninterface.MsgClickTransferEntity;
import jd.cdyjy.jimcore.commoninterface.MsgClickTransferEntityResult;
import jd.cdyjy.jimcore.commoninterface.global.IMGlobal;
import jd.cdyjy.jimcore.commoninterface.global.service.IChooseResult;
import jd.cdyjy.jimcore.commoninterface.global.service.IDateChooserResult;
import jd.cdyjy.jimcore.commoninterface.global.service.IGroupAvatarListener;
import jd.cdyjy.jimcore.commoninterface.global.service.IMainFramHttpListener;
import jd.cdyjy.jimcore.commoninterface.global.service.IOrgOrAssociatedListCallback;
import jd.cdyjy.jimcore.commoninterface.global.service.IOrgAndAssociatedShowCallback;
import jd.cdyjy.jimcore.commoninterface.global.service.IRedKeyCallBack;
import jd.cdyjy.jimcore.commoninterface.global.service.ISaasContactResult;
import jd.cdyjy.jimcore.commoninterface.global.service.IStartFullActyCallBack;
import jd.cdyjy.jimcore.commoninterface.global.service.ISubscriptionCallback;

import static com.jd.me.dd.im.ImDdServiceImpl.syncNoticeUnread;
import static com.jd.oa.AppBase.loginTimline;
import static com.jd.oa.fragment.BaseFragment.ANIMATION_TYPE;
import static com.jd.oa.fragment.BaseFragment.ARG_HAS_MORE_APPROVE;
import static com.jd.oa.fragment.BaseFragment.SHOW_ANIMATION;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_DETAIL_URL;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_APPROVE;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_BIRTHDAY;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_COMPANY_AGE;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_DAKA;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_H5;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_JOY_MEETING;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_MEETING_ROOM_RESERVE;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_SYSTEM;
import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_USE_CAR;
import static com.jd.oa.preference.JDMETenantPreference.KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST;
import static com.jd.oa.utils.Utils.compatibleDeepLink;


/**
 * 接入方自己实现，可以根据需要实现自己的业务，只需继承IMGlobal即可
 */
@SuppressWarnings("WeakerAccess")
public class GlobalImpl extends IMGlobal {

    private static final String TAG = GlobalImpl.class.getSimpleName();

    public static final String LOGOUT = "logout";
    private static boolean mHasMoreApprove = false;

    Application application = Utils2App.getApp();

    AppService appService = AppJoint.service(AppService.class);
    JPushService jPushService = AppJoint.service(JPushService.class);

    JoyWorkService joyWorkService = AppJoint.service(JoyWorkService.class);

    GlobalImplKt globalImplKt = new GlobalImplKt();

    public GlobalImpl() {
        super();
    }

    @Override
    public void chooseDate(@NonNull Context context, long startTime, long endTime, long dayRange, @NonNull final IDateChooserResult result) {
        DatePickerDialog dialog = new DatePickerDialog(context);
        dialog.setSelectMode(DatePickerView.SelectMode.RANGE);
        HolidayHelperKt.holidayFetcher(dialog);
        dialog.setStartDateRequired(false);
        dialog.setEndDateRequired(false);
        dialog.setAllRequired(true);

        if (startTime >= 0) {
            dialog.setStartDate(new Date(startTime));
//            dialog.setMinDate(new Date(startTime));
        }
        if (endTime >= 0) {
            dialog.setEndDate(new Date(endTime));

//            dialog.setMaxDate(new Date(endTime));
        }
        if (dayRange >= 0) {
            int monthRange = (int) dayRange;
            if (monthRange == dayRange) {
                dialog.setMaxSelectedMonthRange(monthRange);
            }
        }
        dialog.setOnCalendarRangeSelectedListener(new DatePickerView.OnCalendarRangeSelectedListener() {
            @Override
            public void onRangeSelected(Date startDate, Date endDate) {
                Date newStartDate = DateUtil.startOfDay(startDate);
                Date newEndDate = DateUtil.endOfDay(endDate);
                result.onResult(newStartDate.getTime(), newEndDate.getTime());
            }
        });

        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                result.onResult(-1L, -1L);
            }
        });
        dialog.getWindow().setDimAmount(0.2f);
        dialog.show();
    }

    /**
     * 互踢回调
     */
    @Override
    public void kickOut(String msg) {
        try {
            MELogUtil.localV(LOGOUT, "kickOut  msg=" + msg);
            MELogUtil.onlineV(LOGOUT, "kickOut  msg=" + msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        long kickOutTime = System.currentTimeMillis();
        try {
            kickOutTime = new JSONObject(msg).optLong("time");
        } catch (Exception e) {
            e.printStackTrace();
        }
        String kickOutTimeStr = DateUtils.getFormatString(kickOutTime, DateUtils.DATE_FORMAT_LONG);
        logout(application.getString(R.string.me_has_alreay_logon_other_devices_timline, kickOutTimeStr));
    }

    private void logout(String tips) {
        appService.setForceKickOut(true);
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT, true);
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_OUT_TIPS, tips);
        IMigrateEventListener listener = MigrateDataHelper.getMigrateEventListener();
        if (listener != null) {
            listener.onKickout();
        }
        appService.userKickOut(tips, null);
    }

    @Override
    public void quitLogin(int code, String tips) {
        logout(tips);
        try {
            MELogUtil.localV(LOGOUT, "quitLogin  tips=" + tips + " code = " + code);
            MELogUtil.onlineV(LOGOUT, "quitLogin  tips=" + tips + " code = " + code);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onUserInvalid() {
        super.onUserInvalid();
        appService.userKickOut(application.getString(R.string.me_timline_invaild_user), null);
    }

    @Override
    public boolean canShowComplaint() {
        return VerifyUtils.isVerifyUser();
    }

    /**
     * 通知宿主获取token回调
     */
    @Override
    public void requestToken() {
        if (!PreferenceManager.UserInfo.getAgreedPrivacyPolicy()) {
            return;
        }
        LogRecorder.getDefault().record("GlobalImpl requestToken");
        PreferenceManager.UserInfo.clearTimlineToken();
        loginTimline();
        try {
            MELogUtil.localV(LOGOUT, "requestToken");
            MELogUtil.onlineV(LOGOUT, "requestToken");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void uploadLogFile() {
        appService.uploadLogFile();
    }

    @Override
    public int notifyIconSet() {
        return R.drawable.jdme_app_launcher;
    }

    @Override
    public MsgClickTransferEntityResult noticeClick(String noticeId, MsgClickTransferEntity msgClickTransferEntity) {
        if (msgClickTransferEntity != null && !TextUtils.isEmpty(msgClickTransferEntity.param)) {
            try {
                JSONObject jsonObject = new JSONObject(msgClickTransferEntity.param);
                long mid = jsonObject.optLong("mid");
                String toApp = jsonObject.optString("fromApp");
                String toPin = jsonObject.optString("fromPin");
                String infox = jsonObject.optString("infox");
                String mutiUrl = jsonObject.optString("mutiUrl");
                String finalUrl = "";
                if (!TextUtils.isEmpty(mutiUrl)) {
                    JSONObject urlObj = new JSONObject(mutiUrl);
                    if (TabletUtil.isTablet() && urlObj.has("apad")) {
                        finalUrl = urlObj.getString("apad");
                    } else if (!TabletUtil.isTablet() && urlObj.has("android")) {
                        finalUrl = urlObj.getString("android");
                    }
                }
//                long noticeTime = jsonObject.optLong("noticeTime");
//                if (noticeTime == 0) {
//                    noticeTime = System.currentTimeMillis();
//                }
                MsgClickTransferEntityResult result = new MsgClickTransferEntityResult();
                // 没有infox字段，并且是action_list，为咚咚推送
                if (TextUtils.isEmpty(infox)) {
                    String resultUrl = null;
                    String extend = jsonObject.optString("extend");
                    JSONObject extendObj = new JSONObject(extend);
                    String contentUrl = extendObj.optString("contentUrl");
                    String url = extendObj.optString("url");
                    if (msgClickTransferEntity.action == MsgClickTransferEntity.ACTION_LIST) {
                        resultUrl = url;
                        if (TextUtils.isEmpty(resultUrl)) {
                            resultUrl = contentUrl;
                        }
                    } else if (msgClickTransferEntity.action == MsgClickTransferEntity.ACTION_CONTENT) {
                        resultUrl = contentUrl;
                        if (TextUtils.isEmpty(resultUrl)) {
                            resultUrl = url;
                        }
                    }
                    if (!TextUtils.isEmpty(finalUrl)) {
                        resultUrl = finalUrl;
                    }
                    if (TextUtils.isEmpty(resultUrl)) {
                        result.consumed = false;
                    } else {
                        if (resultUrl != null && !resultUrl.startsWith("http")) {
                            resultUrl = "http://" + resultUrl;
                        }
                        OpennessApi.openUrl(resultUrl, false);
//                        Router.build(RouterConstant.ROUTER_SCHEME_JDME + "web?url=" + Uri.encode(resultUrl)).with(EXTRA_SHOW_SHARE, true)
//                                .go(Utils2Activity.getTopActivity());
                        result.consumed = true;
                    }

                } else {
                    JSONObject extendJsonObject = new JSONObject(infox);

                    MessageBean bean = MessageBean.toMessageBean(extendJsonObject);
                    if (!TextUtils.isEmpty(finalUrl)) {
                        bean.deepLink = finalUrl;
                    }
                    if (bean != null) {
                        result.consumed = openNoticeDetail(noticeId, bean);
                    } else {
                        result.consumed = false;
                    }
                }
                if (result.consumed) {
                    syncNoticeUnread(String.valueOf(mid), noticeId, toApp, toPin);
                }
                return result;

            } catch (JSONException e) {
                e.printStackTrace();
                return super.noticeClick(noticeId, msgClickTransferEntity);
            }
        } else {
            return super.noticeClick(noticeId, msgClickTransferEntity);
        }
    }

    public boolean openNoticeDetail(String noticeId, MessageBean bean) {
        if (!TextUtils.isEmpty(bean.deepLink)) {
            Activity activity = AppBase.getTopActivity();
            Context context = AppBase.getAppContext();
            // 判断是否需要验证token
            final Uri uri = Uri.parse(bean.deepLink);
            // deep link不为null，并且deep link需要获取token,auth,如：jdme://auth/XXX，都是需要授权的
            String host = uri.getHost();
            if (host == null) {
                return false;
            }
            boolean isNeedToken = host.equals("auth");
            if (isNeedToken) {
                if (APP_ID_JOY_MEETING.equals(noticeId)) {
                    //点击joy meeting通知进入列表页
//                    PageEventUtil.onEvent(activity, PageEventUtil.EVENT_JOYMEETING_MSG_JOIN);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_timline_joyMeeting_detail_click, JDMAConstants.mobile_timline_joyMeeting_detail_click);
                    return false;
                } else {
                    jPushService.gainTokenAndGoPlugin(bean.deepLink, "" + bean.appId);
                    return true;
                }
            }
            IRouter router;

            //新版日程推送不能区分客户端版本，所以需要兼容旧版本，让旧版本到提示升级的界面
            if (bean.deepLink.startsWith(DeepLink.SCHEDULE_DETAIL_V2)) {
                router = Router.build(Uri.parse(bean.deepLink).getQueryParameter("route"));
            } else {
                router = Router.build(bean.deepLink);
            }

            if (TextUtils.equals(APP_ID_APPROVE, noticeId)) {
                router.with(ARG_HAS_MORE_APPROVE, mHasMoreApprove);
            } else if (TextUtils.equals(APP_ID_BIRTHDAY, noticeId)) {
                router.with(SHOW_ANIMATION, true);
                router.with(ANIMATION_TYPE, MineInfo.BADGE_BIRTHDAY);
            } else if (TextUtils.equals(APP_ID_COMPANY_AGE, noticeId)) {
                router.with(SHOW_ANIMATION, true);
                router.with(ANIMATION_TYPE, MineInfo.BADGE_COMPANY_AGE);
            }
            router.go(activity == null ? context : activity, new RouteNotFoundCallback(activity == null ? context : activity));
            return true;
        } else if (!TextUtils.isEmpty(bean.appId) && !TextUtils.isEmpty(bean.target)) {
            Context activity = AppBase.getTopActivity();
            if (activity == null) {
                activity = AppBase.getAppContext();
                if (activity == null) {
                    return false;
                }
            }
            Intent intent = new Intent(activity, FunctionActivity.class);
            intent.putExtra(EXTRA_APP_ID, bean.appId);
            intent.putExtra(EXTRA_APP_DETAIL_URL, bean.target);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
            if (activity instanceof Application) {
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            }
            activity.startActivity(intent);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void onShowNoticeList(String noticeId, NoticeTipsShowCallback noticeTipsShowCallback) {
        if (TextUtils.isEmpty(noticeId)) return;
        if (TextUtils.equals(APP_ID_APPROVE, noticeId)) {
            getApprovalNumber(noticeTipsShowCallback);
        }
        switch (noticeId) {
            //TODO appid硬编码
            case "~me201912190669":
            case "~me202001160685":
                //mail
                JDMAUtils.onEventClick(JDMAConstants.mobile_mail_click, JDMAConstants.mobile_mail_click);
                break;
            case APP_ID_JOY_MEETING:
//                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_joyMeeting_detail_click,JDMAConstants.mobile_timline_joyMeeting_detail_click);
                break;
            case APP_ID_SYSTEM:
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_system_msg_click, JDMAConstants.mobile_timline_system_msg_click);
                break;
            case APP_ID_USE_CAR:
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_employeeTravel_click, JDMAConstants.mobile_timline_employeeTravel_click);
                break;
            case APP_ID_DAKA:
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_sign_click, JDMAConstants.mobile_timline_sign_click);
                break;
            case APP_ID_H5:
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_jumpH5_click, JDMAConstants.mobile_timline_jumpH5_click);
                break;
            case APP_ID_MEETING_ROOM_RESERVE:
                JDMAUtils.onEventClick(JDMAConstants.mobile_timline_notification_conferenceRoom_reserve_click, JDMAConstants.mobile_timline_notification_conferenceRoom_reserve_click);
                break;
        }
    }

    /**
     * 获取未审批流程个数信息
     */
    private void getApprovalNumber(final NoticeTipsShowCallback noticeTipsShowCallback) {
        NetWorkManager.request(this, NetworkConstant.API_MY_APPROVAL_INFO, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                String notify = (String) map.get("myApprovalInfo");
                SpannableString spannableString = new SpannableString(notify);
                final String deepLink = compatibleDeepLink(map);
                if (TextUtils.isEmpty(notify) || TextUtils.isEmpty(deepLink)) {
                    noticeTipsShowCallback.hideNoticeTips();
                    return;
                }
                mHasMoreApprove = true;
                final Activity activity = AppBase.getTopActivity();
                String myApproveString = activity.getString(R.string.me_approve_my_approve);
                if (notify == null) {
                    return;
                }
                int index = notify.indexOf(myApproveString);
                if (index >= 0) {
                    ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#f75c5c"));
                    spannableString.setSpan(colorSpan, index, index + myApproveString.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                }
                noticeTipsShowCallback.showNoticeTips(spannableString, new NoticeTipsClickCallback() {
                    @Override
                    public void onTipsClicked() {
                        if (activity.isFinishing()) {
                            return;
                        }
                        //进入我的审批列表
                        Router.build(deepLink).go(activity);
//                        PageEventUtil.onEvent(activity, PageEventUtil.EVENT_MSG_APPROVE_NOTIFICATION);
                        JDMAUtils.onEventClick(JDMAConstants.mobile_timline_approve_click, JDMAConstants.mobile_timline_approve_click);
                    }
                });
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                Logger.d(TAG, errorMsg);
                noticeTipsShowCallback.hideNoticeTips();
                mHasMoreApprove = false;
            }
        }), null);
    }

    @Override
    public boolean isApplicationForground() {
        return appService.isForeground();
    }

    @Override
    public boolean hasMainUI() {
        return AppBase.getMainActivity() != null;
    }

    @Override
    public String getNoticeBtnText(String btnCode) {
        if (TextUtils.equals("me_join_meeting", btnCode)) {
            return application.getString(R.string.me_join_meeting);
        } else {
            return application.getString(R.string.me_notice_detail);
        }
    }

    @Override
    public void onGetSessionKey(final IRedKeyCallBack listener) {
        MELogUtil.localI(MELogUtil.TAG_RPT, "getPin----onGetSessionKey");
        MELogUtil.onlineI(MELogUtil.TAG_RPT, "getPin----onGetSessionKey");
        appService.getJDAccountCookie(R.string.jdme_redpack_bind_pin, new Callback2<String>() {
            @Override
            public void onSuccess(String sessionKey, String pin) {
                listener.onSuccess(sessionKey, pin, "jdme");
            }

            @Override
            public void onFail(String msg) {
                listener.onFailure(msg);
            }
        }, true);
    }

    @Override
    public void onSessionInvalid() {
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_REDPACKET_SESSION_INVALID, true);
    }

    @Override
    public void refreshUnReadCount(int unreadCount) {
        // TODO TAB未读数显示
        appService.refreshUnReadCount(unreadCount);
        Log.d(TAG, "==========> unreadCount " + unreadCount);

    }

    @Override
    public boolean isTablet() {
        return TabletUtil.isTablet();
    }

    @Override
    public boolean isInSplit(Activity activity) {
        return TabletUtil.isSplitMode(activity);
    }

    @Override
    public boolean isFold() {
        return TabletUtil.isFold();
    }

    /**
     * 获取当前网络环境的名称
     * <p>
     * 此方法首先尝试从用户信息中获取当前网络环境名称如果用户信息中未设置网络环境名称，
     * 则从本地配置中获取网络环境配置，并使用该配置中的环境名称作为当前环境名称
     *
     * @return 当前网络环境的名称: prod(生产环境), prev(预发环境), test(测试环境), jdos(jdos), custom(other)
     */
    @Override
    public String getMeEnvName() {
        // 从用户信息中获取当前网络环境名称
        String netEnv = PreferenceManager.UserInfo.getNetEnvironment();
        // 获取本地配置中的网络环境配置
        final NetEnvironmentConfigModel netEnvironmentConfig = LocalConfigHelper.getInstance(AppBase.getAppContext()).getNetEnvironmentConfig();
        // 如果用户信息中的网络环境名称为空，则使用本地配置中的环境名称
        if (TextUtils.isEmpty(netEnv)) {
            netEnv = netEnvironmentConfig.getEnv();
        }
        // 返回当前网络环境名称
        return netEnv.toLowerCase();
    }

    @Override
    public boolean isEasyGoEnable() {
        return TabletUtil.isEasyGoEnable();
    }

    @Override
    public boolean startFullScreenActy(final IStartFullActyCallBack listener) {
        Activity avt = AppBase.getTopActivity();
        if (avt != null && listener != null) {
            FoldPlaceHolderActivity.start(avt);
        }
        if (listener != null) {
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    listener.startActy();
                }
            }, 50);
        }
        return true;
    }

    @NonNull
    @Override
    public AudioPermissionEntity setAudioPermission(AudioType audioType) {
        int category = convertAudioTypeTOCategory(audioType);
        JMAudioCategoryManager.JMEAudioCategorySet set = JMAudioCategoryManager.getInstance().setAudioCategory(category);
        return new AudioPermissionEntity(convertCategoryToAudioType(set.currentAudioCategory), set.available, set.secret);
    }

    @Override
    public void releaseAudioPermission(String secret) {
        JMAudioCategoryManager.getInstance().releaseAudio(secret);
    }

    @Override
    public boolean canSetAudioCategory(@NonNull AudioType audioType) {
        int category = convertAudioTypeTOCategory(audioType);
        return JMAudioCategoryManager.getInstance().canSetAudioCategory(category);
    }

    @NonNull
    @Override
    public AudioType getCurrentAudioCategory() {
        return convertCategoryToAudioType(JMAudioCategoryManager.getInstance().getCurrentAudioCategory());
    }

    private AudioType convertCategoryToAudioType(int category) {
        AudioType audioType = AudioType.JME_AUDIO_CATEGORY_IDLE;
        switch (category) {
            case JMAudioCategoryManager.JME_AUDIO_CATEGORY_IDLE:
                audioType = AudioType.JME_AUDIO_CATEGORY_IDLE;
                break;
            case JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING:
                audioType = AudioType.JME_AUDIO_CATEGORY_VIDEO_MEETING;
                break;
            case JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOIP:
                audioType = AudioType.JME_AUDIO_CATEGORY_VOIP;
                break;
            case JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_MEETING:
                audioType = AudioType.JME_AUDIO_CATEGORY_JOY_MEETING;
                break;
            case JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_PLAY:
                audioType = AudioType.JME_AUDIO_CATEGORY_VIDEO_MSG;
                break;
            case JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOICE_PLAY:
                audioType = AudioType.JME_AUDIO_CATEGORY_VOICE_MSG;
                break;
            case JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_TV:
                audioType = AudioType.JME_AUDIO_CATEGORY_ME_TV;
                break;
        }
        return audioType;
    }

    private int convertAudioTypeTOCategory(AudioType audioType) {
        int category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_IDLE;
        switch (audioType) {
            case JME_AUDIO_CATEGORY_IDLE:
                category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_IDLE;
                break;
            case JME_AUDIO_CATEGORY_VIDEO_MEETING:
                category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING;
                break;
            case JME_AUDIO_CATEGORY_VOIP:
                category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOIP;
                break;
            case JME_AUDIO_CATEGORY_JOY_MEETING:
                category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_MEETING;
                break;
            case JME_AUDIO_CATEGORY_VIDEO_MSG:
                category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_PLAY;
                break;
            case JME_AUDIO_CATEGORY_VOICE_MSG:
            case JME_AUDIO_CATEGORY_VOICE_RECORD_MSG:
                category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOICE_PLAY;
                break;
            case JME_AUDIO_CATEGORY_ME_TV:
                category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_TV;
                break;
        }
        return category;
    }

    private ISubscriptionCallback mSubscriptionListener;

    @Override
    public void setSubscriptionListener(@NonNull ISubscriptionCallback listener) {
        mSubscriptionListener = listener;
    }

    @Override
    public boolean isSubscriptionOpen(final String pin, final String app) {
        TaskNotificationSettingUtil.getRobotStatus(new TaskNotificationSettingUtil.TaskRobotStatusCallbackAdapter() {
            @Override
            public void onGetRobotStatus(boolean status) {
                super.onSetRobotStatusSuccess(status);
                if (null == mSubscriptionListener) {
                    return;
                }
                mSubscriptionListener.setSubscriptionState(pin, app, status);
            }
        });
        return super.isSubscriptionOpen(pin, app);
    }

    @Override
    public boolean isSubscriptionShow(String pin, String app) {
        if ("app.fe23xyoy".equals(pin)) {//任务助手(机器人)的pin
            return TenantConfigBiz.INSTANCE.isJoyWorkEnable();
        } else {
            return false;
        }
    }

    @Override
    public void setSubscription(final String pin, final String app, final boolean result) {
        super.setSubscription(pin, app, result);
        ////在消息列表中点击关闭/开启消息通知
        JDMAUtils.onEventClick(JDMAConstants.mobile__JoyWork_joyworkmessage_setting_turnoffreminder_click, "");
        TaskNotificationSettingUtil.setRobotStatus(result, new TaskNotificationSettingUtil.TaskRobotStatusCallbackAdapter() {
            @Override
            public void onSetRobotStatusSuccess(boolean status) {
                super.onSetRobotStatusSuccess(status);
                if (null == mSubscriptionListener) {
                    return;
                }
                mSubscriptionListener.setSubscriptionState(pin, app, status);
            }

            @Override
            public void onFailed() {
                super.onFailed();
                mSubscriptionListener.setSubscriptionState(pin, app, !result);
            }
        });
    }

    @Override
    public String getImageSavePath() {
        File file = FileCache.getInstance().getPicFile(UseType.TENANT);
        if (null != file) {
            return file.getAbsolutePath();
        }
        return "";
    }

    @Override
    public void logReportD(@NonNull String tag, @NonNull String msg, @NonNull Throwable throwable) {
        MELogUtil.localD(TAG, msg, throwable);
        MELogUtil.onlineD(TAG, msg, throwable);
    }

    @Override
    public void logReportE(@NonNull String tag, @NonNull String msg, @NonNull Throwable throwable) {
        MELogUtil.localE(TAG, msg, throwable);
        MELogUtil.onlineE(TAG, msg, throwable);
    }
//    @Override
//    public void initJDMeMeeting() {
//        JDMeetingManager.Companion.getInstance().initJBMeetingInNeed();
//    }
//
//    @Override
//    public void jumpJDMeCreateMeeting(@Nullable Object group) {
//        JDMeetingManager.Companion.getInstance().create(group);
//    }
//
//    @Override
//    public void jumpJDMeLoading(@NonNull String meetingCode, @NonNull String pwd) {
//        JDMeetingManager.Companion.getInstance().attendJDMeeting(meetingCode, pwd);
//    }
    /*    public boolean isDisableScreenCapture() {
        return SafetyControlManager.getInstance().getConfigByKey("disableScreenShot", "0").equals("1");
    }*/

    public void colorRequest(@NonNull String action, @NonNull HashMap<String, String> header, @NonNull HashMap<String, Object> params, @NonNull final IMainFramHttpListener listener) {
        List<String> apiModel = LocalConfigHelper.getInstance(AppBase.getAppContext()).getTimelineApiModel();
        if (apiModel != null && !apiModel.contains(action)) {
            return;
        }
        // imCommon.api接口请求量特别大，需要做特殊逻辑 处理在请求中的 loginType参数。
        // 因为imCommon.api只在 me-app中调用，所以这个逻辑只在 me-app上。
        if (Objects.equals(action, "imCommon.api")) {
             String offerLoginType = ABTestManager.getInstance().getConfigByKey("jdme.im.color.offerLoginType", "0");
             if (Objects.equals(offerLoginType, "1")) {
                 params.put("__temp_login_type__", true);
             }
        }

        HttpManager.color().post(params, header, action, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                int eCode = -1;
                try {
                    //先根据joyspace的协议返回值判断status
                    JSONObject result = new JSONObject(info.result);
                    String status = result.optString("status");
                    if (!TextUtils.isEmpty(status)) {
                        listener.onResult("success".equals(status) ? 0 : 1, info.result);
                    } else {
                        //没有status 按京ME协议返回
                        if (!TextUtils.isEmpty(info.getErrorCode())) {
                            listener.onResult(Integer.parseInt(info.getErrorCode()), info.result);
                        } else {
                            listener.onResult(eCode, info.result);
                        }
                    }
                } catch (JSONException e) {
                    listener.onResult(eCode, info.result);
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                listener.onHttpFailure(exception.getExceptionCode(), TextUtils.isEmpty(exception.getMessage()) ? info : exception.getMessage());
            }
        });
    }

    @Override
    public void request(@NonNull String action, @NonNull HashMap<String, Object> params, @NonNull final IMainFramHttpListener listener) {
        List<String> apiModel = LocalConfigHelper.getInstance(AppBase.getAppContext()).getTimelineApiModel();
        if (apiModel != null && !apiModel.contains(action)) {
            return;
        }
        HttpManager.v2().post(null, params, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                int eCode = -1;
                try {
                    //先根据joyspace的协议返回值判断status
                    JSONObject result = new JSONObject(info.result);
                    String status = result.optString("status");
                    if (!TextUtils.isEmpty(status)) {
                        listener.onResult("success".equals(status) ? 0 : 1, info.result);
                    } else {
                        //没有status 按京ME协议返回
                        if (!TextUtils.isEmpty(info.getErrorCode())) {
                            listener.onResult(Integer.parseInt(info.getErrorCode()), info.result);
                        } else {
                            listener.onResult(eCode, info.result);
                        }
                    }
                } catch (JSONException e) {
                    listener.onResult(eCode, info.result);
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                listener.onHttpFailure(exception.getExceptionCode(), TextUtils.isEmpty(exception.getMessage()) ? info : exception.getMessage());
            }
        }, action);
    }

    @Override
    public void chooseFolder(String gid, String title, @NonNull IChooseResult result) {
        openJoySpaceDir(AppBase.getTopActivity(), gid, title, result);
    }

    private static final List<BroadcastReceiver> receivers = new ArrayList<>();

    public void openJoySpaceDir(Context context, String gid, String title, final IChooseResult result) {
        final String action = "JS_SDK_SELECT_DIRECTORY";
        if (receivers.size() > 0) {
            for (BroadcastReceiver receiver : receivers) {
                LocalBroadcastManager.getInstance(context).unregisterReceiver(receiver);
            }
            receivers.clear();
        }
        BroadcastReceiver receiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent == null) {
                    return;
                }
                if (action.equals(intent.getAction())) {
                    Serializable data = intent.getSerializableExtra("data");
                    JSONObject wrap = (JSONObject) JSONObject.wrap(data);
                    if (wrap != null) {
                        result.onChoose(wrap.toString());
                        MELogUtil.localI(MELogUtil.TAG_IM, "onSelectDirSuccess data: " + wrap);
                        for (BroadcastReceiver receiver : receivers) {
                            LocalBroadcastManager.getInstance(context).unregisterReceiver(receiver);
                        }
                        receivers.clear();
                    }
                }
            }
        };
        receivers.add(receiver);
        LocalBroadcastManager.getInstance(context).registerReceiver(receiver, new IntentFilter(action));
        if (TextUtils.isEmpty(gid)) {
            joyWorkService.openSelDir(context, title, "");
        } else {
            joyWorkService.openSelDir(context, title, gid);
        }
    }


    @Override
    public void uploadJoySpace(@NonNull String path, @NonNull String bizId, @NonNull String channel, @NonNull IMainFramHttpListener listener) {
        uploadJoySpaceFile(path, bizId, channel, listener);
    }

    private FileUploadManager mUploadManager;

    public void uploadJoySpaceFile(String filePath, String bizId, String channel, final IMainFramHttpListener listener) {
        MELogUtil.localI(MELogUtil.TAG_IM, TAG + " uploadJoySpaceFile filePath: " + filePath);
        HashMap<String, Object> params = new HashMap<>();
        params.put("bizId", bizId);
        params.put("channel", channel);
        if (!TextUtils.isEmpty(filePath) && FileUtils.isFileExist(filePath)) {
            String joySpaceKey = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.JOYSPACE);
            if (mUploadManager == null) {
                mUploadManager = new FileUploadManager.Builder()
                        .setContext(AppBase.getAppContext())
                        .setFileService(new JoySpaceFileService())
                        .build();
            }
            Task.Callback<UploadResult> callback = new Task.SimpleCallback<UploadResult>() {

                @Override
                public void onProgressChange(Task.Progress progress) {
                    super.onProgressChange(progress);
                    MELogUtil.localI(MELogUtil.TAG_IM, TAG + " uploadJoySpaceFile onProgressChange: " + progress.getPercent());
                }

                @Override
                public void onComplete(UploadResult result) {
                    JSONObject resultJson = new JSONObject();
                    try {
                        resultJson.put("fileDownloadUrl", result.getFileDownloadUrl());
                        resultJson.put("uploadId", result.getUploadId());
                        resultJson.put("fileMd5", result.getFileMd5());
                        resultJson.put("errorCode", 0);
                        resultJson.put("errorMsg", "success");
                        listener.onResult(0, resultJson.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    MELogUtil.localI(MELogUtil.TAG_IM, TAG + " uploadJoySpaceFile onComplete: " + resultJson);
                }

                @Override
                public void onFailure(Exception exception) {
                    MELogUtil.localE(MELogUtil.TAG_IM, TAG + " uploadJoySpaceFile onFailure: ", exception);
                    int errorCode = 1;
                    if (exception instanceof UploadException) {
                        UploadException exception1 = (UploadException) exception;
                        errorCode = TextUtils.isEmpty(exception1.getErrorCode()) ? 1 : Integer.parseInt(exception1.getErrorCode());
                    }
                    listener.onHttpFailure(errorCode, exception.getMessage());
                }
            };
            mUploadManager.create(filePath)
                    .setAppKey(joySpaceKey)
                    .setRequestId(bizId)
                    .setExtra(params)
                    .setCallback(callback)
                    .start();
        } else {
            listener.onHttpFailure(-1, TextUtils.isEmpty(filePath) ? "Filepath is empty!" : "File not found!");
        }
    }

    @Override
    public void JDMAClick(String pageId, String eventId, Map<String, String> pageParams, Map<String, String> eventParams) {
        JDMAUtils.clickPageId(pageId, eventId, pageParams, eventParams);
    }

    @Override
    public void JDMAPV(String pageName, Map<String, String> params) {
        JDMAUtils.eventPV(pageName, params);
    }

    @Override
    public boolean getABTestInfoByKey(String key, boolean defaultValue) {
        String val = ABTestManager.getInstance().getConfigByKey(key, "");
        if (TextUtils.isEmpty(val)) {
            return defaultValue;
        }
        if ("1".equals(val)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isControlScreeShot() {
        return SafetyControlManager.getInstance().isControlScreeShot;
    }

    @Override
    public void getGroupAvatarByText(Context context, String text, String color, IGroupAvatarListener<String> callback) {
        getGroupIconByText(context, text, color, callback);
    }

    @Override
    public void showChatMessageNotify(String avatarUrl, String nickName, CharSequence content, long timestamp, String to, String toApp, boolean isGroup, int chatType) {
        ChatNotificationInfo data = new ChatNotificationInfo(avatarUrl, nickName, timestamp, to, toApp, isGroup, chatType);
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                if (!ChatNotificationManager.ChatNotificationDisable()) {
                    ChatNotificationManager chatNotificationManager = ChatNotificationManager.getInstance();
                    if (chatNotificationManager != null) {
                        chatNotificationManager.showNotificationBanner(data, content);
                    }
                }
            }
        });
    }

    @Override
    public void closeBottomChat() {
        if (AppBase.getTopActivity() instanceof HalfScreenProxyActivity) {
            HalfScreenProxyActivity hostActivity = (HalfScreenProxyActivity) AppBase.getTopActivity();
            hostActivity.closeDialog();
            ActiveAnalyzeUtil.getInstance().onUserInteraction();
        }
    }

    @Override
    public void bottomChatFullScreen() {
        if (AppBase.getTopActivity() instanceof HalfScreenProxyActivity) {
            HalfScreenProxyActivity hostActivity = (HalfScreenProxyActivity) AppBase.getTopActivity();
            hostActivity.fullscreenDialog();
            ActiveAnalyzeUtil.getInstance().onUserInteraction();
        }
    }

    @Override
    public void chatBottomChange(boolean big) {
        if (AppBase.getTopActivity() instanceof HalfScreenProxyActivity) {
            HalfScreenProxyActivity hostActivity = (HalfScreenProxyActivity) AppBase.getTopActivity();
            if (big) {
                hostActivity.expandHalfScreen();
                ActiveAnalyzeUtil.getInstance().onUserInteraction();
            }
        }
    }

    @Override
    public void openBottomChat(String to, String toApp, boolean isGroup, int chatType) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                if (!ChatNotificationManager.ChatNotificationDisable()) {
                    ChatNotificationInfo chatData = new ChatNotificationInfo("", "", 0, to, toApp, isGroup, chatType);
                    if (AppBase.getTopActivity() != null && !AppBase.getTopActivity().isDestroyed() && !AppBase.getTopActivity().isFinishing()) {
                        HalfScreenProxyActivity.startActivity(chatData);
                    }
                }
            }
        });
    }

    @Override
    public boolean isInChatList() {
        //202104251432 为消息列表tab appId
        return "202104251432".equals(AppBase.sCurrentTab);
    }

    @Override
    public boolean isBottomChatOpen() {
        Activity activity = AppBase.getTopActivity();
        if (activity instanceof HalfScreenProxyActivity) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isOpenFromApp() {
        return AppBase.isColdBoot;
    }

    @Override
    public void setMainTabView(ViewGroup view) {
        TabbarService tabbarService = AppJoint.service(TabbarService.class);
        tabbarService.getTabbarConfig(new TabbarService.ITabbarConfigCallback() {
            @Override
            public void onSucsess() {

            }

            @Override
            public void onSuccess(String info) {
                ImDdService imDdService = AppJoint.service(ImDdService.class);
                imDdService.refreshQuickApp(info);
            }

            @Override
            public void onFailed() {
                ImDdService imDdService = AppJoint.service(ImDdService.class);
                imDdService.refreshQuickApp(null);
            }
        }, false);
    }

    @Override
    public void openGroupAvatarEdit(FragmentActivity activity, String url, IGroupAvatarListener<String> callback) {
        goToGroupModifyPage(activity, url, callback);
    }

    @Override
    public void closeGroupAvatarEdit(FragmentActivity activity) {
        LocalBroadcastManager.getInstance(activity).sendBroadcast(new Intent("action.group.icon.modify.close"));
    }

    public void getGroupIconByText(Context context, String text, String color, IGroupAvatarListener<String> callback) {
        AvatarUtil.upLoadAvatar(context, text, color, new AvatarUtil.UploadAvatarListener() {
            @Override
            public void onSuccess(String avatarUrl, String text, String color) {
                if (callback == null) {
                    return;
                }
                try {
                    JSONObject result = new JSONObject();
                    result.put("avatarUrl", avatarUrl);
                    result.put("backgroundColor", color);
                    result.put("text", text);
                    callback.onSuccess(result.toString());
                } catch (Exception e) {
                    callback.onFail();
                }
            }

            @Override
            public void onFail() {
                callback.onFail();
            }
        },"");
    }


    public void goToGroupModifyPage(FragmentActivity activity, String url, IGroupAvatarListener<String> callback) {
        String deepLink = DeepLink.GROUP_ICON_MODIFY;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("url", url);
            deepLink = deepLink + "?mparam=" + URLEncoder.encode(jsonObject.toString(), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
//            callback.onFail();
        }
        Intent intent = Router.build(deepLink).getIntent(AppBase.getAppContext());
        final String key = "goToGroupModifyPage" + System.currentTimeMillis();
        AutoUnregisterResultCallback<ActivityResult> callback1 = new AutoUnregisterResultCallback<ActivityResult>() {

            @Override
            public void onActivityResult(ActivityResult o, boolean unused) {
                if (o.getData() != null && o.getResultCode() == Activity.RESULT_OK) {
                    callback.onSuccess(o.getData().getStringExtra("result"));
                } else {
                    callback.onFail();
                }
            }
        };
        ActivityResultLauncher<Intent> register = activity.getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), callback1);
        callback1.setLauncher(register);
        register.launch(intent);

    }

    @Override
    public void showUnreadSmartGuide() {
        // 2025-3-18 清除逻辑
    }

    @Override
    public void openFileByMain(FragmentActivity activity, String url, String path) {
        MELogUtil.localD(TAG, "url: " + url + ", path: " + path);
        FileViewer.openLocalFile(activity, url, path);
    }

    @Override
    public boolean isSourceValid(String source, String secret) {
        return globalImplKt.isSourceValid(source, secret);
    }

    // 返回“消息”自动翻译灰度信息
    private boolean isMessageAutoTranslateEnabled() {
        return TextUtils.equals(ABTestManager.getInstance()
                .getConfigByKey("me.translate.enable", "1"), "1");
    }

    @NonNull
    @Override
    public Map<String, Object> getAutoTranslateSwitchInfo() {
        return AutoTranslateManager.getAutoTranslateSwitchInfo(AutoTranslateManager.TRANSLATE_MESSAGE);
    }

    @Override
    public void gotoLanguageSetting(@NonNull Context context) {
        if (isMessageAutoTranslateEnabled()) {
            Router.build(DeepLink.FRAGMENT_AUTO_TRANSLATE_SETTING).go(context);
        } else {
            MELogUtil.localE(TAG, "gotoLanguageSetting: 不在灰度内");
        }
    }

    @Override
    public boolean autoTranslateEnable() {
        return isMessageAutoTranslateEnabled();
    }

    @Override
    public Map<String, Object> getDeviceInfo() {
        HashMap<String, Object> map = new HashMap<>();
        HttpManagerConfig.DeviceInfo mDeviceInfo = HttpManager.getConfig().getDeviceInfo();
        map.put("deviceId", DeviceUtil.getDeviceUniqueId());
        map.put("networkType", Utils.isWiFi(HttpManager.getContext()) ? "wifi" : "4G");
        map.put("User-Agent", "JDME" + File.separator + mDeviceInfo.getAppVersionName());
        map.put("deviceName", Build.MODEL);
        map.put("osVersion", Build.VERSION.RELEASE);
        map.put("fp", mDeviceInfo.getFp());
        return map;
    }

    @NonNull
    @Override
    public List<Map<String, String>> getLanguageList() {
        ArrayList<Map<String, String>> result = new ArrayList<>();
        //翻译语言列表缓存的是getCommonConfig里的数据
        try {
            String cache = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST);
            if (!TextUtils.isEmpty(cache)) {
                ArrayList<Language> languageListResponse = JsonUtils.getGson().fromJson(cache, new TypeToken<ArrayList<Language>>() {
                }.getType());
                if (languageListResponse != null && !languageListResponse.isEmpty()) {
                    //只取前10种语言
                    int maxSize = Math.min(languageListResponse.size(), 10);
                    for (int i = 0; i < maxSize; i++) {
                        Language response = languageListResponse.get(i);
                        HashMap<String, String> languageItem = new HashMap<>();
                        languageItem.put("displayLanguage", response.language);
                        languageItem.put("displayLanguageCode", response.languageCode);
                        languageItem.put("displayLanguageAbbreviation", response.abbreviation);
                        result.add(languageItem);
                    }
                }
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "getLanguageAbbreviation 从缓存解析从getTenantConfig获取的languageKindConfList json出错");
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public String getSaasContactTitle(ISaasContactResult result) {
        return getContactJsonString();
    }

    public String getContactJsonString(){
        String appId = LocalConfigHelper.getInstance(AppBase.getAppContext()).getAppID();//jdsaas/jdme
        String teamName = PreferenceManager.UserInfo.getTeamName();
        String avatar = JdSaasLoginPreference.INSTANCE.get(JdSaasLoginPreference.INSTANCE.getKV_ENTITY_AVATAR());
        boolean organizationEnable = TenantConfigBiz.INSTANCE.isOrganizationEnable();
        boolean relatedOrganizationEnable = TenantConfigBiz.INSTANCE.isRelatedOrganizationEnable();
        Map<String,String> map = new HashMap();
        map.put("target",(organizationEnable || relatedOrganizationEnable) ? "true" : "false");//true-显示整个结构
        map.put("avatar",avatar);
        map.put("teamName",teamName);
        if(organizationEnable){
            map.put("organizationStructure",DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam={\"pageType\":\"organizationStructure\"}");
        }
        if(relatedOrganizationEnable){
            map.put("relativeTeam",DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam={\"pageType\":\"relativeTeam\"}");
        }
        JSONObject jsonObject = new JSONObject(map);
        return jsonObject.toString();
    }

    @Override
    public String getProductID() {
        String appId = LocalConfigHelper.getInstance(AppBase.getAppContext()).getAppID();//jdsaas/jdme
        return appId;
    }

    @Override
    public void updateCurrentUserInfo() {
        // 更新当前登录用户信息
        new Handler(Looper.getMainLooper()).post(() -> {
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            imDdService.updateCurrentUserInfo();
        });
    }

    @Override
    public void getOrgAndAssociatedShow(IOrgAndAssociatedShowCallback listener) {
        //关联组织me和we都显示，组织架构只是we上显示
        listener.onOrgAndAssociatedShow(TenantConfigBiz.INSTANCE.isOrganizationEnable(),
                TenantConfigBiz.INSTANCE.isRelatedOrganizationEnable());
    }

    @Override
    public void getOrgOrAssociatedList(Map<String, Object> params, IOrgOrAssociatedListCallback listener) {
        String pageType = (String) params.get("pageType");
        if("relativeTeam".equals(pageType)){ //关联组织
            String relatedOrgId = (String) params.get("relatedOrgId");
            String deptId = (String) params.get("deptId");
            String deptType = String.valueOf(params.get("deptType"));
            getRelatedOrgList(relatedOrgId,deptId,deptType,listener);
        } else if ("organizationStructure".equals(pageType)) {
            String deptId = (String) params.get("deptId");//组织架构
            getEbookDeptInfo(deptId,listener);
        }
    }

    public void getRelatedOrgList(String relatedOrgId,String deptId,String deptType, IOrgOrAssociatedListCallback listener){
        if(TextUtils.isEmpty(relatedOrgId)){ //我的关联组织，第一级列表接口
            HttpManager.color().post(null,null, "ebook.getRelatedOrgList",new SimpleRequestCallback<String>(){
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    ApiResponse<RelativeTeamList> response = ApiResponse.parse(info.result,RelativeTeamList.class);
                    if(response.isSuccessful()){
                        Map<String,Object> result = new HashMap<>();
                        RelativeTeamList relativeTeamList = response.getData();
                        //将我的关联组织列表数据适配成SubNodeInfo
                        List<SubNodeInfo> subNodeList = new ArrayList<>();
                        for (RelativeTeamInfo relativeTeamInfo : relativeTeamList.relatedOrgList){
                            SubNodeInfo subNodeInfo = new SubNodeInfo();
                            subNodeInfo.relatedOrgId = relativeTeamInfo.relatedOrgId;
                            subNodeInfo.deptName = relativeTeamInfo.relatedOrgName;
                            subNodeInfo.avatar = relativeTeamInfo.avatar;
                            subNodeInfo.nodeType = 1;
                            subNodeList.add(subNodeInfo);
                        }
                        result.put("subNodeList",subNodeList);
                        listener.onResult(0,JSON.toJSONString(result));
                    }
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    listener.onResult(-1,null);
                }
            });
        }else{ //关联组织架构列表
            Map<String,Object> params = new HashMap<>();
            params.put("relatedOrgId",relatedOrgId);
            params.put("deptId",deptId);
            params.put("deptType",deptType);
            HttpManager.color().post(params,null,"ebook.getRelatedOrgInfo",new SimpleRequestCallback<String>(){
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    Map<String,Object> result = new HashMap<>();
                    ApiResponse<DeptInfo> response = ApiResponse.parse(info.result,DeptInfo.class);
                    if(response.isSuccessful()){
                        DeptInfo deptInfo = response.getData();
                        result.put("emplList",deptInfo.emplList);
                        for(SubNodeInfo subNodeInfo : deptInfo.parentList){
                            subNodeInfo.relatedOrgId = deptInfo.relatedOrgId;
                            subNodeInfo.deptType = deptInfo.deptType;
                        }
                        result.put("parentList",deptInfo.parentList);
                        for(SubNodeInfo subNodeInfo : deptInfo.subNodeList){
                            subNodeInfo.relatedOrgId = deptInfo.relatedOrgId;
                            subNodeInfo.deptType = deptInfo.deptType;
                        }
                        result.put("subNodeList",deptInfo.subNodeList);
                        listener.onResult(0,JSON.toJSONString(result));
                    }
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    listener.onResult(-1,null);
                }
            });
        }
    }

    public void getEbookDeptInfo(String deptId, IOrgOrAssociatedListCallback listener){
        Map<String, Object> params = new HashMap<>();
        params.put("deptId",deptId);
        HttpManager.color().post(params,null,"ebook.getEbookDeptInfoV2",new SimpleRequestCallback<String>(){
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<DeptInfo> response = ApiResponse.parse(info.result,DeptInfo.class);
                if(response.isSuccessful()){
                    Map<String,Object> result = new HashMap<>();
                    DeptInfo deptInfo = response.getData();
                    result.put("emplList",deptInfo.emplList);
                    result.put("parentList",deptInfo.parentList);
                    result.put("subNodeList",deptInfo.subNodeList);
                    listener.onResult(0,JSON.toJSONString(result));
                }
            }
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                listener.onResult(-1,null);
            }
        });
    }

    @Override
    public String getMainColorAppKey() {
        return ColorGatewayNetEnvironment.getCurrentEnv().getAppKey();
    }

    @Override
    public String getMainColorToken() {
        return TokenManager.getInstance().getAccessToken();
    }
}