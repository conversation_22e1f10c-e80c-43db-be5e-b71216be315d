package com.jd.me.dd.im.biz.quickapp;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.utils.LocaleUtils;

import java.io.Serializable;
import java.util.List;

/*
 * Time: 2024/7/18
 * Author: qudongshi
 * Description:
 */
class QuickAppsModel implements Serializable {


    public Content content;
    public String errorMessage;
    public String errorCode;

    public class Content {
        List<QuickAppItem> quickApp;
    }

    /**
     *
     */
    public class QuickAppItem {

        private String id;
        private String appName;

        public String deepLink;

        private String icon;

        private I18NAppName appNameI18;

        private IconTheme iconTheme;

        public String getAppName(Context context) {
            String localStr = LocaleUtils.getUserSetLocaleStr(context);
            if (!TextUtils.isEmpty(localStr) && appNameI18 != null) {
                if ("zh_CN".equals(localStr)) {
                    if (!TextUtils.isEmpty(appNameI18.zh_cn)) {
                        return appNameI18.zh_cn;
                    }
                } else if (!TextUtils.isEmpty(appNameI18.en_us)) {
                    return appNameI18.en_us;
                }
            }
            return appName;
        }

        public String getAppNameEn() {
            String val = appName;
            if (appNameI18 != null && !TextUtils.isEmpty(appNameI18.en_us)) {
                val = appNameI18.en_us;
            }
            return val;
        }

        public String getIcon() {
            String resIcon = "";
            if (iconTheme != null) {
                if (ThemeApi.isGlobal()) {
                    if (ThemeApi.isDarkTheme()) {
                        resIcon = iconTheme.dark;
                    } else {
                        resIcon = iconTheme.light;
                    }
                } else {
                    resIcon = iconTheme.light;
                }
            }
            if (TextUtils.isEmpty(resIcon)) {
                resIcon = this.icon;
            }
            return resIcon;
        }

        public String getId() {
            if (TextUtils.isEmpty(id)) {
                return "";
            }
            return id;
        }


        /**
         * 比较是否有比变化
         *
         * @param context
         * @param item
         * @return
         */
        public boolean equals(Context context, QuickAppItem item) {
            if (item == null) {
                return false;
            }
            return getId().equals(item.getId())
                    && getAppName(context).equals(item.getAppName(context))
                    && getIcon().equals(item.getIcon());
        }
    }

    public class I18NAppName {
        String en_us;
        String zh_cn;
    }

    public class IconTheme {
        String light;
        String dark;
    }
}
