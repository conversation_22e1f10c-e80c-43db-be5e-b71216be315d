package com.jd.me.dd.im.conference

import com.jd.cdyjy.icsp.entity.MemberEntity
import com.jd.cdyjy.jimui.ui.selector.SelectorCallback.SelectorExtraCallback
import com.jd.cdyjy.jimui.ui.selector.SelectorCallback.SelectorUICallback
import com.jd.me.dd.im.conference.model.ConferenceUser
import com.jingdong.conference.account.model.User
import com.jingdong.conference.integrate.ServiceHubLazy
import kotlinx.coroutines.CancellableContinuation
import kotlin.coroutines.resume

class SelectorCallbackConference(private val _continuation: CancellableContinuation<Pair<List<User>, Any?>>) : SelectorExtraCallback() {
    private var _callback: SelectorUICallback? = null

    init {
        _continuation.invokeOnCancellation {
            _callback?.closeSelector()
        }
    }

    override fun back() {
        if (_continuation.isActive) {
            (ServiceHubLazy.externalService as ExternalServiceImpl).selectStart = false
            val sendCard: Boolean? = extra as? Boolean
            _continuation.resume(Pair(listOf(), sendCard))
        }
    }

    override fun commit(items: ArrayList<MemberEntity>, callback: SelectorUICallback?) {
        _callback = callback
        if (_continuation.isActive) {
            (ServiceHubLazy.externalService as ExternalServiceImpl).selectStart = false
            val sendCard: Boolean? = extra as? Boolean
            _continuation.resume(items.map {
                ConferenceUser(it.mId, it.mApp, "0").apply {
                    nickname = it.mName
                    avatar = it.mAvatar
                }
            } to sendCard)
        }
        callback?.closeSelector()
    }

}