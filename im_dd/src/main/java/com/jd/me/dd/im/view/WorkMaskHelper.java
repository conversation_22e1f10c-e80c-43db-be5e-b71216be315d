package com.jd.me.dd.im.view;

import androidx.fragment.app.FragmentActivity;

import android.widget.FrameLayout;

import androidx.annotation.Keep;

import com.jd.oa.mask.MaskDecoration;


/**
 * create by huf<PERSON> on 2020-02-18
 */
@Keep
public class WorkMaskHelper implements MaskDecoration {
    @Override
    public void decoration(float left, float top, float right, float bottom, final FragmentActivity activity, FrameLayout parent) {
//        View view = LayoutInflater.from(activity).inflate(R.layout.jdme_im_work_mask, parent);
//        TextView tips = view.findViewById(R.id.tips);
//        TextView know = view.findViewById(R.id.know);
//        String tipsS = activity.getResources().getString(R.string.me_im_dd_work_mask_tips);
//        // 中文环境
//        if (tipsS.contains("我")) {
//            TextHelper.setTextViewColor(Color.BLACK, tips, tipsS, tipsS.length() - 4, tipsS.length());
//        } else {
//            tips.setText(tipsS);
//            know.setTextColor(Color.parseColor("#F0250F"));
//        }
//        know.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                String deeplink = DeepLink.APP_MARKET_OLD;
//                Router.build(deeplink).go(activity, new RouteNotFoundCallback(activity));
//                close(activity);
//            }
//        });
//        view.findViewById(R.id.close).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                close(activity);
//            }
//        });
    }

    private void close(FragmentActivity activity) {
//        PreferenceManager.UserInfo.setWorkMaskHadShow(true);
//        Intent intent = new Intent("chat.list.mask.know");
//        LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
    }

    @Override
    public void clickMask() {

    }

    @Override
    public void onBackPressed() {

    }
}
