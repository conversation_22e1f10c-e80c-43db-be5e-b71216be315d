package com.jd.me.dd.im.conference

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.lifecycle.lifecycleScope
import com.chenenyu.router.Router
import com.jd.me.dd.im.R
import com.jd.oa.AppBase
import com.jd.oa.JDMAConstants
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.TabletUtil
import com.jingdong.conference.conference.model.StartConferenceType
import com.jingdong.conference.core.extension.showToast
import com.jingdong.conference.integrate.ServiceHub
import com.jingdong.conference.integrate.ServiceHubLazy
import jd.cdyjy.jimcore.core.ipc_global.MyInfo
import jd.cdyjy.jimcore.http.reportLog.ReportLogUtil
import kotlinx.coroutines.launch

class FragmentVideoConference : BaseFragment(), View.OnClickListener,
        ServiceHub by ServiceHubLazy {

    var joinVideoConference: Button? = null
    var createVideoConference: Button? = null
    var scheduleConference: Button? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleScope.launchWhenResumed {
            kotlin.runCatching {
                val list = internalService?.conferenceList() ?: return@launchWhenResumed
                childFragmentManager.beginTransaction().add(R.id.list, list)
                        .commitAllowingStateLoss()
            }.onFailure {
                activity?.finish()
            }
        }
    }

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.meeting_fragment_video_conference_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
    }

    private fun initView(view: View) {
        joinVideoConference = view.findViewById(R.id.joinVideoConference)
        createVideoConference = view.findViewById(R.id.createVideoConference)
        scheduleConference = view.findViewById(R.id.scheduleConference)
        joinVideoConference?.setOnClickListener(this)
        createVideoConference?.setOnClickListener(this)
        scheduleConference?.setOnClickListener(this)
        scheduleConference?.visibility = if (MyInfo.hasVideoConferenceReversePermission()) View.VISIBLE else View.GONE
    }

    override fun onClick(v: View?) {
        when (v) {
            joinVideoConference -> {
                lifecycleScope.launch {
                    //判断Voip
                    when {
                        !JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING) -> {
                            showToast(R.string.meeting_start_conference_conference_busy)
                        }

                        jdmtmeService?.busy == true -> {
                            showToast(R.string.meeting_start_conference_conference_busy)
                        }

                        else -> {
                            if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
                                return@launch
                            }
                            if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                                val joinVideoConferenceId = joinVideoConference?.id
                                TabletUtil.startFullScreenActivity {
                                    jdmtmeService?.startConference(
                                            activity,
                                            if (v?.id == joinVideoConferenceId) StartConferenceType.JOIN else StartConferenceType.CREATE,
                                            false,
                                            false,
                                            true,
                                            null,
                                            Bundle().apply {
                                                putSerializable(
                                                        ExternalServiceImpl.EXTRA_SOURCE,
                                                        ReportLogUtil.CreateConferenceType.LIST
                                                )
                                            })
                                }
                            } else {
                                jdmtmeService?.startConference(
                                        activity,
                                        if (v?.id == joinVideoConference?.id) StartConferenceType.JOIN else StartConferenceType.CREATE,
                                        false,
                                        false,
                                        true,
                                        null,
                                        Bundle().apply {
                                            putSerializable(
                                                    ExternalServiceImpl.EXTRA_SOURCE,
                                                    ReportLogUtil.CreateConferenceType.LIST
                                            )
                                        })
                            }
                        }
                    }
                }
            }

            createVideoConference -> {
                lifecycleScope.launch {
                    //判断Voip
                    when {
                        !JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING) -> {
                            showToast(R.string.meeting_start_conference_conference_busy)
                        }

                        ServiceHubLazy.jdmtmeService?.busy == true -> {
                            showToast(R.string.meeting_start_conference_conference_busy)
                        }

                        else -> {
                            if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING)) {
                                return@launch
                            }
                            if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                                TabletUtil.startFullScreenActivity {
                                    ServiceHubLazy.jdmtmeService?.startConference(
                                            activity,
                                            StartConferenceType.CREATE,
                                            false,
                                            false,
                                            true,
                                            null,
                                            Bundle().apply {
                                                putSerializable(
                                                        ExternalServiceImpl.EXTRA_SOURCE,
                                                        ReportLogUtil.CreateConferenceType.LIST
                                                )
                                            })
                                }
                            } else {
                                ServiceHubLazy.jdmtmeService?.startConference(
                                        activity,
                                        StartConferenceType.CREATE,
                                        false,
                                        false,
                                        true,
                                        null,
                                        Bundle().apply {
                                            putSerializable(
                                                    ExternalServiceImpl.EXTRA_SOURCE,
                                                    ReportLogUtil.CreateConferenceType.LIST
                                            )
                                        })
                            }
                        }
                    }
                }
            }

            scheduleConference -> {
                val activity = AppBase.getTopActivity() ?: return
                val bundle = Bundle()
                bundle.putString("routeTag", "create")
                bundle.putBoolean("timlineMeeting", true)
                bundle.putString("from", "timlineMeeting")
                Router.build(DeepLink.CALENDER_SCHEDULE).with(bundle).go(activity)
                JDMAUtils.onEventClick(JDMAConstants.timline_video_create_schedule, JDMAConstants.timline_video_create_schedule)
            }

            //一期待删
//            voiceConference -> {
//                TimlineUIHelper.showConferenceStart(context)
//            }
        }
    }

}