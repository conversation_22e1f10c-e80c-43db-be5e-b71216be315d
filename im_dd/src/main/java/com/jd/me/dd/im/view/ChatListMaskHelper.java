package com.jd.me.dd.im.view;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import android.widget.FrameLayout;

import com.jd.oa.mask.MaskDecoration;

/**
 * create by huf<PERSON> on 2019-09-17
 */
@Keep
public class ChatListMaskHelper implements MaskDecoration {
    private static ChatListMaskHelper sHelper = new ChatListMaskHelper();

    public static ChatListMaskHelper getInstance() {
        return sHelper;
    }

    @Override
    public void decoration(final float left, final float top, float right, final float bottom, @NonNull final FragmentActivity activity, @NonNull FrameLayout parent) {
//        View view = LayoutInflater.from(activity).inflate(R.layout.jdme_im_chat_list_mask, parent);
//        final ArcDottedLine dottedLine = view.findViewById(R.id.top_line);
//        final View addView = view.findViewById(R.id.new_add);
//        addView.post(new Runnable() {
//            @Override
//            public void run() {
//                String tipsS = activity.getResources().getString(R.string.me_im_dd_work_mask_tips);
//                if (tipsS.contains("utilities")) { // 英文环境
//                    RectF rectF = new RectF(addView.getLeft() + CommonUtils.dp2px(28), bottom - (bottom - top) / 5, left - CommonUtils.dp2px(8), addView.getTop() - CommonUtils.dp2px(5));
//                    dottedLine.setOval(rectF);
//                } else { // 中文环境(非英文环境）
//                    RectF rectF = new RectF(addView.getLeft() - CommonUtils.dp2px(12), bottom - (bottom - top) / 5, left - CommonUtils.dp2px(8), addView.getBottom() - addView.getHeight() / 2);
//                    dottedLine.setOval(rectF);
//                }
//            }
//        });
//        view.findViewById(R.id.mask_know).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                PreferenceManager.UserInfo.setChatListMaskHadShow(true);
//                Intent intent = new Intent("chat.list.mask.know");
//                LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
//            }
//        });
    }

    @Override
    public void clickMask() {

    }

    @Override
    public void onBackPressed() {

    }
}
