package com.jd.me.dd.im;

import android.content.Context;

import com.jd.me.dd.im.tool.EntityTools;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import jd.cdyjy.jimcore.commoninterface.tab.IMTab;
import jd.cdyjy.jimcore.commoninterface.tab.TabEntity;

import static jd.cdyjy.jimcore.OpimCoreWrapper.showTab;

/**
 * Created by peidongbiao on 2019/3/21
 */
public class TabImpl extends IMTab {
    //    private static final String TAG = "TabImpl";
    private AppService appService = AppJoint.service(AppService.class);

    public TabImpl() {
        super();
    }

    @Override
    public void onTabClick(final Context context, TabEntity tabEntity) {
        if (null == tabEntity) {
            return;
        }
        super.onTabClick(context, tabEntity);
        appService.onTabClick(context, EntityTools.getTabEntityJd(tabEntity));
    }

    @Override
    public void isShowTab() {
        super.isShowTab();
        TabEntityJd tab = appService.getTab();
        if (null != tab) {
            showTab(EntityTools.getTabEntity(tab));
        }
    }
}
