package com.jd.me.dd.im.conference.video

import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Matrix
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.jd.cdyjy.icsp.utils.AudioPermissionMonitor
import com.jd.me.dd.im.R
import com.jd.oa.BaseActivity
import com.jd.oa.annotation.Navigation
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.StatusBarUtil

@Navigation(hidden = true)
class VideoPlayerActivity : BaseActivity() {

    companion object {
        fun start(context: Context, url: String, duration: Long) {
            val intent = Intent(context, VideoPlayerActivity::class.java).apply {
                if (context is Application) {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                putExtra("url", url)
                putExtra("duration", duration)
            }

            context.startActivity(intent)
        }
    }


    private val url: String by lazy {
        val s = intent.getStringExtra("url")
        if (s == null) {
            return@lazy ""
        } else {
            return@lazy s
        }
    }
    private val duration: Long by lazy { intent.getLongExtra("duration", 0) }

    private val videoView: PreviewVideoTextureView by lazy { findViewById(R.id.videoView) }
    private val playProgressView: PlayProgressView by lazy { findViewById(R.id.playProgressView) }
    private val close: ImageView by lazy { findViewById(R.id.close) }
    private val videoControl: ImageView by lazy { findViewById(R.id.videoControl) }
    private val control: ImageView by lazy { findViewById(R.id.control) }
    private val videoPlayTime: TextView by lazy { findViewById(R.id.videoPlayTime) }
    private val videoTime: TextView by lazy { findViewById(R.id.videoTime) }
//    private val secret = "video_player_secret"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.JDME_ActivityTransparent)
        setContentView(R.layout.jdme_im_activity_video_player)
        ActionBarHelper.init(this)
        StatusBarUtil.setColor(this, resources.getColor(R.color.color_333333))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) { // 5.0
            val window = window
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.statusBarColor = 0x01000000
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) { // 4.4
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val width = videoView.width
        val height = videoView.height
        if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏
            val sx: Float = height / videoView.videoWidth
            val sy: Float = width / videoView.videoHeight
            val matrix = Matrix().apply {
                postTranslate((height - videoView.videoWidth) / 2, (width - videoView.videoHeight) / 2)
                preScale(videoView.videoWidth / height, videoView.videoHeight / width)
                if (sx >= sy) {
                    postScale(sy, sy, height.toFloat() / 2, width.toFloat() / 2)
                } else {
                    postScale(sx, sx, height.toFloat() / 2, width.toFloat() / 2)
                }
            }
            videoView.setTransform(matrix)
            videoView.postInvalidate()
        } else {
            //横屏
            val sx: Float = width / videoView.videoHeight
            val sy: Float = height / videoView.videoWidth
            val matrix = Matrix().apply {
                postTranslate((height - videoView.videoWidth) / 2, (width - videoView.videoHeight) / 2)
                preScale(videoView.videoWidth / height, videoView.videoHeight / width)
                if (sx >= sy) {
                    postScale(sy, sy, height.toFloat() / 2, width.toFloat() / 2)
                } else {
                    postScale(sx, sx, height.toFloat() / 2, width.toFloat() / 2)
                }
            }
            videoView.setTransform(matrix)
            videoView.postInvalidate()
        }
    }

    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
        close.setOnClickListener {
            onBackPressed()
        }

        videoView.setOnVideoSizeChangedListener { mp, width, height ->
            val sx = videoView.width / mp.videoWidth.toFloat()
            val sy = videoView.height / mp.videoHeight.toFloat()
            val matrix = Matrix().apply {
                postTranslate((videoView.width - mp.videoWidth) / 2f, (videoView.height - mp.videoHeight) / 2f)
                preScale(mp.videoWidth / videoView.width.toFloat(), mp.videoHeight / videoView.height.toFloat())
                if (sx >= sy) {
                    postScale(sy, sy, videoView.width / 2f, videoView.height / 2f)
                } else {
                    postScale(sx, sx, videoView.width / 2f, videoView.height / 2f)
                }
            }

            videoView.setTransform(matrix)
            videoView.postInvalidate()
        }
        videoView.setVideoPlayProgressListener {
            runOnUiThread {
                val retStr = DateUtils.formatRecordTime1((it * 1000).toLong())
                val progress = it * 1000 * 100f / duration
                if (videoView.mState == PreviewVideoTextureView.STATE_PLAY) {
                    videoPlayTime.text = retStr.toString()
                    if (retStr == videoTime.text) {
                        playProgressView.setCurrentProgress(100)
                    } else {
                        playProgressView.setCurrentProgress(progress.toInt())
                    }

                } else {
                    videoView.cancelTimer()
                }
            }
        }
        videoView.setOnCompletionListener {
            videoView.cancelTimer()
            videoControl.setImageResource(R.drawable.jdme_im_video_play)
            playProgressView.reset()
            control.visibility = View.VISIBLE
            videoPlayTime.text = DateUtils.formatRecordTime1(0)
//            JMAudioCategoryManager.getInstance().releaseAudio(secret)
//            AudioPermissionMonitor.INSTANCE.releaseVideo()
        }

        control.setOnClickListener {
            when (videoView.mState) {
                PreviewVideoTextureView.STATE_PLAY -> {
                    videoControl.setImageResource(R.drawable.jdme_im_video_play)
                    control.visibility = View.VISIBLE
                    videoView.pause()
                }

                PreviewVideoTextureView.STATE_PAUSE,
                PreviewVideoTextureView.STATE_COMPLETE,
                PreviewVideoTextureView.STATE_INIT,
                PreviewVideoTextureView.STATE_STOP,
                -> {
                    videoControl.setImageResource(R.drawable.jdme_im_video_pause)
                    control.visibility = View.GONE
                    videoView.start()
                }
            }
        }

        playProgressView.setOnPlayListener(object : PlayProgressView.onPlayListener {
            override fun start() {
                videoView.pause()
            }

            override fun drag(progress: Int) {
                val current = (progress * duration).toFloat() / 100f
                videoPlayTime.text = DateUtils.formatRecordTime1(current.toLong())
            }

            override fun play(progress: Int) {
                val current = progress / 100f * duration
                videoView.seekTo((current / 1000).toInt())
                videoControl.setImageResource(R.drawable.jdme_im_video_pause)
                control.visibility = View.GONE
            }
        })

        videoControl.setOnClickListener {
            when (videoView.mState) {
                PreviewVideoTextureView.STATE_PLAY -> {
                    videoControl.setImageResource(R.drawable.jdme_im_video_play)
                    control.visibility = View.VISIBLE
                    videoView.pause()
                }

                PreviewVideoTextureView.STATE_PAUSE,
                PreviewVideoTextureView.STATE_COMPLETE,
                PreviewVideoTextureView.STATE_INIT,
                PreviewVideoTextureView.STATE_STOP,
                -> {
                    videoControl.setImageResource(R.drawable.jdme_im_video_pause)
                    control.visibility = View.GONE
                    videoView.start()
                }
            }
        }

        playProgressView.setMax(100)
        videoTime.text = DateUtils.formatRecordTime1(duration)

        if (!url.isNullOrBlank()) {
            kotlin.runCatching {
                videoView.setVideoUrl(url)
                videoView.postDelayed({
                    videoView.start()
                    videoControl.setImageResource(R.drawable.jdme_im_video_pause)
                }, 100)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if (videoView.mState == PreviewVideoTextureView.STATE_PLAY) {
            videoControl.setImageResource(R.drawable.jdme_im_video_play)
            control.visibility = View.VISIBLE
            videoView.pause()
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        videoView.release()
    }
}