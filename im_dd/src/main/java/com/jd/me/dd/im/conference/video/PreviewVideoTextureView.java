package com.jd.me.dd.im.conference.video;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.media.MediaPlayer;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.TextureView;

import java.io.IOException;
import java.util.Timer;
import java.util.TimerTask;

public class PreviewVideoTextureView extends TextureView implements
        TextureView.SurfaceTextureListener,
        MediaPlayer.OnPreparedListener,
        MediaPlayer.OnSeekCompleteListener {
    private static final String TAG = "PreviewVideoTextureView";

    private MediaPlayer mMediaPlayer;
    private String mVideoPath;
    private String mVideoUrl;
    private SurfaceTexture mSurfaceTexture;
    private SurfaceHolder mSurfaceHolder;
    private Surface mSurface;

    public static final int STATE_INIT = 0;
    public static final int STATE_PLAY = 1;
    public static final int STATE_PAUSE = 2;
    public static final int STATE_COMPLETE = 3;
    public static final int STATE_STOP = 4;

    public int mState = 0; // play = 1, stop = 2,

    public PreviewVideoTextureView(Context context) {
        this(context, null);
    }

    public PreviewVideoTextureView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PreviewVideoTextureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        this.setSurfaceTextureListener(this);
        Log.d(TAG, "init() called");
    }

    public void setVideoPath(String path) {
        Log.d(TAG, "setVideoPath() called with: path = [" + path + "]");
        mVideoPath = path;
    }

    public void setVideoUrl(String url) {
        mVideoUrl = url;
    }

    public void start() {
        Log.d(TAG, "start() called");
        if (mMediaPlayer == null) {
            mMediaPlayer = new MediaPlayer();
            mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    if (mCompletionListener != null) {
                        mCompletionListener.onCompletion(mp);
                    }
                    mState = STATE_COMPLETE;
                }
            });
            mMediaPlayer.setOnErrorListener(mOnVideoErrorListener);
            mMediaPlayer.setOnPreparedListener(this);
            mMediaPlayer.setOnSeekCompleteListener(this);
            try {
                if (!TextUtils.isEmpty(mVideoPath)) {
                    mMediaPlayer.setDataSource(mVideoPath);
                } else if (!TextUtils.isEmpty(mVideoUrl)) {
                    mMediaPlayer.setDataSource(getContext(), Uri.parse(mVideoUrl));
                } else {
                    return;
                }

                if (mSurfaceTexture == null) {
                    mSurfaceTexture = getSurfaceTexture();
                    if (mSurfaceTexture == null) {
                        return;
                    }
                }
                mSurface = new Surface(mSurfaceTexture);
                mMediaPlayer.setSurface(mSurface);
                mMediaPlayer.prepareAsync();
                mMediaPlayer.setOnVideoSizeChangedListener(mOnVideoSizeChangedListener);

            } catch (IllegalArgumentException e) {
                Log.e(TAG, "playVideo: ", e);
            } catch (SecurityException e) {
                Log.e(TAG, "playVideo: ", e);
            } catch (IOException e) {
                Log.e(TAG, "playVideo: ", e);
            } catch (IllegalStateException e) {
                Log.e(TAG, "playVideo: ", e);
            }
        } else {
            try {
                if (mState == STATE_STOP) {

                    mMediaPlayer.prepareAsync();
                } else if (mState == STATE_COMPLETE || mState == STATE_PAUSE) {
                    mMediaPlayer.start();

                    mState = STATE_PLAY;
                    addTimer();
                }
            } catch (IllegalStateException e) {
                Log.e(TAG, "playVideo: ", e);
            }

        }
        mState = STATE_PLAY;
    }

    Timer mTimer = new Timer();

    public void addTimer() {
        if (null == mTimer) {
            mTimer = new Timer();
        }
        try {//极限情况下刚执行就已经调用了cancel，https://bugly.qq.com/v2/crash-reporting/crashes/24df64704e/149601?pid=1
            mTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    if (mMediaPlayer != null) {
                        int currentPosition = mMediaPlayer.getCurrentPosition() / 1000;
                        if (mVideoPlayProgressListener != null) {
                            mVideoPlayProgressListener.updateProgress(currentPosition);
                        }
                    }
                }
            }, 5, 500);//开始计时任务后的5毫秒，第一次执行run方法；以后每500毫秒执行一次
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isPlaying() {
        if (mMediaPlayer != null) {
            return mMediaPlayer.isPlaying();
        }
        return false;
    }

    MediaPlayer.OnCompletionListener mCompletionListener;

    public void setOnCompletionListener(MediaPlayer.OnCompletionListener completionListener) {
        mCompletionListener = completionListener;
    }

    public void pause() {
        try {
            if (mMediaPlayer != null) {
                if (mMediaPlayer.isPlaying()) {
                    mMediaPlayer.pause();
                }
            }
        } catch (IllegalStateException e) {
            // *****LiaoQianChuan***** 2019/1/10 注意：此处如果还没有初始化成功，会报错，需要单独再在onPrepared的地方暂停
        }
        mState = STATE_PAUSE;
    }

    public void stop() {
        if (mMediaPlayer != null) {
            mMediaPlayer.stop();
        }
        if (mVideoStartListener != null) {
            mVideoStartListener.onStop();
        }
        mState = STATE_STOP;
    }


    public void seekTo(int time) {
        //seek会seek到当前帧的前一秒
        //例如，调用seekTo(1000)，实际上seek到了0，即上一秒
        if (mMediaPlayer != null) {
            mMediaPlayer.seekTo((time + 1) * 1000);
        }
    }

    public void cancelTimer() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
    }

    public void release() {
        if (mTimer != null) {
            mTimer.cancel();
        }

        if (mSurfaceTexture != null) {
            mSurfaceTexture.release();
        }

        if (mMediaPlayer != null) {
            mMediaPlayer.stop();

            mMediaPlayer.reset();  //防止内存泄漏

            mMediaPlayer.release();
            mMediaPlayer = null;
        }
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // TextureView.SurfaceTextureListener
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////

    @Override
    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
        Log.d(TAG, "onSurfaceTextureAvailable() called with: surface = [" + surface + "], width = [" + width + "], height = [" + height + "]");
        mSurfaceTexture = surface;
    }

    @Override
    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
        Log.d(TAG, "onSurfaceTextureSizeChanged() called with: surface = [" + surface + "], width = [" + width + "], height = [" + height + "]");
    }

    @Override
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
        Log.d(TAG, "onSurfaceTextureDestroyed() called with: surface = [" + surface + "]");
        release();
        return false;
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {
        Log.d(TAG, "onSurfaceTextureUpdated() called with: surface = [" + surface + "]");
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // MediaPlayer.OnPreparedListener
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////

    @Override
    public void onPrepared(MediaPlayer mp) {
        try {
            if (mp != null) {
                if (mState == STATE_PAUSE) {
                    return;
                }
                mp.start();
                mState = STATE_PLAY;
                addTimer();
                if (mVideoStartListener != null) {
                    mVideoStartListener.onStart();
                }
            }
        } catch (IllegalStateException e) {
            Log.d(TAG, "onPrepared: ");
        }
    }

    private MediaPlayer.OnVideoSizeChangedListener mOnVideoSizeChangedListener;

    public void setOnVideoSizeChangedListener(MediaPlayer.OnVideoSizeChangedListener onVideoSizeChangedListener) {
        mOnVideoSizeChangedListener = onVideoSizeChangedListener;
    }

    private MediaPlayer.OnErrorListener mOnVideoErrorListener;

    public void setOnVideoErrorListener(MediaPlayer.OnErrorListener onError) {
        mOnVideoErrorListener = onError;
    }

    public float getVideoWidth() {
        return mMediaPlayer.getVideoWidth();
    }

    public float getVideoHeight() {
        return mMediaPlayer.getVideoHeight();
    }

    @Override
    public void onSeekComplete(MediaPlayer mp) {
        try {
            if (mp != null) {
                mp.start();
                mState = STATE_PLAY;
                addTimer();
            }
        } catch (IllegalStateException e) {
            Log.d(TAG, "onPrepared: ");
        }
    }

    public interface VideoPlayProgressListener {
        void updateProgress(int time);
    }

    private VideoPlayProgressListener mVideoPlayProgressListener;

    public void setVideoPlayProgressListener(VideoPlayProgressListener videoPlayProgressListener) {
        mVideoPlayProgressListener = videoPlayProgressListener;
    }

    public interface VideoStartListener {
        void onStart();

        void onStop();
    }

    private VideoStartListener mVideoStartListener;

    public void setVideoStartListener(VideoStartListener listener) {
        mVideoStartListener = listener;
    }
}
