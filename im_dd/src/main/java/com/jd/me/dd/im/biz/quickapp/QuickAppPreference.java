package com.jd.me.dd.im.biz.quickapp;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

/*
 * Time: 2024/7/18
 * Author: qudongshi
 * Description:
 */
public class QuickAppPreference extends AbsKvEntities {
    private final String PREF_NAME = "IM_QUICK_APP";

    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    @Override
    public String getPrefrenceName() {
        return PREF_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    private static QuickAppPreference preference;

    private QuickAppPreference() {
    }

    public static synchronized QuickAppPreference getInstance() {
        if (preference == null) {
            preference = new QuickAppPreference();
        }
        return preference;
    }

    public static KvEntity<String> KV_ENTITY_DATA = new KvEntity("data", "[]");

    public static KvEntity<Boolean> KV_ENTITY_ENABLE = new KvEntity("enable", true);

}
