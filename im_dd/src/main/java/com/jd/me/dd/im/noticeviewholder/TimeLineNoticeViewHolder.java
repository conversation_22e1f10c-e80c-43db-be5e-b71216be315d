package com.jd.me.dd.im.noticeviewholder;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.jd.cdyjy.common.base.ui.custom.notice.NoticeEntity;
import com.jd.cdyjy.common.base.ui.custom.notice.OnNoticeItemListener;

/**
 * Created by peidongbiao on 2019/4/16
 */
@SuppressWarnings("WeakerAccess")
public abstract class TimeLineNoticeViewHolder extends RecyclerView.ViewHolder {

    public TimeLineNoticeViewHolder(View itemView) {
        super(itemView);
    }

    public abstract void bindItemView(RecyclerView.ViewHolder holder, NoticeEntity noticeEntity, int position, int totalCount, OnNoticeItemListener itemListener);
}
