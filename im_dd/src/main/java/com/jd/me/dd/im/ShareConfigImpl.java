package com.jd.me.dd.im;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;

import com.jd.cdyjy.common.base.ui.custom.share.IMShareConfig;

public class ShareConfigImpl extends IMShareConfig {

    @Override
    public int shareTips() {
        return R.string.me_timline_share_tip;
    }

    @Override
    public int hostName() {
        return R.string.me_app_name;
    }

    @Override
    public void requestLogin(Context context) {
        try {
            context.startActivity(new Intent()
                    .setComponent(new ComponentName(context.getPackageName(), "com.jd.oa.StartupActivity"))
                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
        } catch (Exception e) {
        }
    }
}
