package com.jd.me.dd.im;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.Nullable;

import jd.cdyjy.jimcore.commoninterface.conversationlist.IMThemeChangeConfig;
import com.jd.oa.theme.manager.ThemeApi;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

@SuppressWarnings("unused")
public class ChangThemeImpl extends IMThemeChangeConfig {

    public String getThemeRootDirPath() {
        return ThemeApi.getThemeRootDirPath();
    }

    public String getThemeConfigJsonStr() {
        return ThemeApi.getThemeConfigJsonStr();
    }

    public String getThemeId() {
        return ThemeApi.getThemeId();
    }

    public String getThemeMd5() {
        return ThemeApi.getThemeMd5();
    }

    public boolean isGlobal() {
        return ThemeApi.isGlobal();
    }

    public boolean isDarkTheme() {
        return ThemeApi.isDarkTheme();
    }

    @Override
    public void setStatusBarDarkMode(@Nullable Context context) {
        QMUIStatusBarHelper.setStatusBarDarkMode((Activity) context);
    }

    @Override
    public void setStatusBarLightMode(@Nullable Context context) {
        QMUIStatusBarHelper.setStatusBarLightMode((Activity) context);
    }
}
