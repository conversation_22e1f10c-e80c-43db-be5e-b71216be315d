package com.jd.me.dd.im;

import com.jd.cdyjy.common.gallery.gallery_interface.BaseGalleryUIConfig;

public class GalleryUIConfigImpl extends BaseGalleryUIConfig {

    @Override
    public int getGalleryBaseColor() {
        return R.color.red_warn;
    }

    @Override
    public int getOriginalCheckboxRes() {
        return R.drawable.jdme_im_gallery_check_box;
    }

    @Override
    public int getSelectCheckboxRes() {
        return R.drawable.jdme_selector_checkbox_red;
    }

    @Override
    public int getImageSelectIndicatorRes() {
        return R.drawable.jdme_im_gallery_pic_sel_indicator_bg;
    }
}
