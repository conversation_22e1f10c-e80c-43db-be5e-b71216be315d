package com.jd.me.dd.im.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.jd.me.dd.im.R;
import com.jd.oa.utils.CommonUtils;

/**
 * create by huf<PERSON> on 2020-02-17
 */
public class ArcDottedLine extends View {
    // 每一个虚线对应的度数
    private static final int STEP = 5;
    private static final int GAP = 3;
    // 三角形水平边的长度
    private static final int TRI_LEN = 30;
    private Paint mPaint, mFillPaint;
    private Path mPath;
    private float y = -1;
    private Drawable bitmap;
    private int bitmapW = TRI_LEN;


    private PointF start, end, controller;

    public ArcDottedLine(Context context) {
        super(context);
        init();
    }

    public ArcDottedLine(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ArcDottedLine(Context context, @Nullable  AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setColor(Color.WHITE);
        mPaint.setStrokeWidth(2);
        mPaint.setStyle(Paint.Style.STROKE);
        // 设置虚线
        PathEffect effects = new DashPathEffect(new float[]{CommonUtils.dp2px(getContext(), STEP), CommonUtils.dp2px(getContext(), GAP)}, 1);
        mPaint.setPathEffect(effects);

        mFillPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mFillPaint.setColor(Color.WHITE);
        mFillPaint.setStyle(Paint.Style.FILL);
        mPath = new Path();

        bitmap = getResources().getDrawable(R.drawable.jdme_im_chat_list_mask_tri_left);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (start == null) {
            start = new PointF(0, 0);
            end = new PointF(0, 0);
            controller = new PointF(0, 0);
        }
        mPath.reset();
        mPath.moveTo(start.x, start.y);
        mPath.quadTo(controller.x, controller.y, end.x, end.y);
        canvas.drawPath(mPath, mPaint);
        canvas.drawCircle(start.x, start.y, 10, mFillPaint);
        int save = canvas.save();
        bitmap.setBounds(new Rect(0, 0, bitmapW, bitmapW));
        canvas.translate(end.x - bitmapW / 2.0f, end.y - bitmapW / 2f);
        canvas.rotate(y, bitmapW / 2, bitmapW / 2);
        bitmap.draw(canvas);
        canvas.restoreToCount(save);
    }

    public void setPoints(PointF start, PointF end, PointF controller, float angle, int bitmapW) {
        this.start = start;
        this.end = end;
        this.controller = controller;
        this.y = angle;
        this.bitmapW = bitmapW;
        invalidate();
    }

    public void setOval(RectF oval) {
        start = new PointF(oval.left, oval.bottom);
        end = new PointF(oval.right, oval.top);
        controller = new PointF(oval.left + oval.width() / 4, oval.top + oval.height() / 4);
        this.y = (float) (-180 - Math.toDegrees(Math.atan(Math.abs(end.y - controller.y) / Math.abs(end.x - controller.x))));
        invalidate();
    }
}
