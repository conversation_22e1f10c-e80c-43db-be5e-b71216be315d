package com.jd.me.dd.im;


import android.net.Uri;

import com.chenenyu.router.Router;
import com.jd.cdyjy.common.base.ui.custom.user_info.IMUserInfoConfig;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.router.DeepLink;

import org.json.JSONException;
import org.json.JSONObject;

import com.jd.oa.utils.Utils2App;

/**
 * 个人名片UI定制化
 */
public class UserInfoUIImpl extends IMUserInfoConfig {
//    private static final String TAG = UserInfoUIImpl.class.getSimpleName();

    @Override
    public int getSendMsgSrc() {
        return R.drawable.jdme_im_contact_info_send_message;
    }

    @Override
    public int getAddFriendSrc() {
        return R.drawable.jdme_im_contact_info_add_contact;
    }

    @Override
    public int getVoipCallSrc() {
        return R.drawable.jdme_im_contact_info_voip_call;
    }

    @Override
    public int getVideoCallSrc() {
        return R.drawable.jdme_im_contact_info_video_call;
    }

    @Override
    public int getSecretChatSrc() {
        return R.drawable.jdme_im_contact_info_secret_chat;
    }

    @Override
    public int userInfoTopBgRes() {
        return R.drawable.jdme_im_userinfo_details_page_bg;
    }

    public void onDepartmentClick(String json) {
        try {
            JSONObject jsonObject = new JSONObject(json);
            String userName = jsonObject.optString("userpin");
            String appId = "";
            String url = "";
            switch (NetworkConstant.getCurrentServerName().index) {
                case 3:
                    url = "https://openorg.jd.com/mobile?userName=" + userName;
                    appId = "201912160656";
                    break;
                default:
                    url = "https://uopenorg.jd.com/mobile?userName=" + userName;
                    appId = "202108251088";
                    break;
            }
            Router.build(DeepLink.appCenter(appId, "url=" + Uri.encode(url))).go(Utils2App.getApp());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public int titleBarCalendarButton() {
        return 1;
    }

    public void onCalendarClick(String json) {
        try {
            JSONObject sourceJson = new JSONObject(json);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("appid", sourceJson.get("toApp"));
            jsonObject.put("pin", sourceJson.get("to"));
            String newUri = DeepLink.CALENDER_OTHER_PAGE + "?mparam=" + Uri.encode(jsonObject.toString());
            Router.build(newUri).go(Utils2App.getApp());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
