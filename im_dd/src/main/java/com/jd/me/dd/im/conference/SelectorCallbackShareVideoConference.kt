package com.jd.me.dd.im.conference

import android.content.Context
import android.text.TextUtils
import android.view.View
import com.jd.cdyjy.icsp.cache.AppCache
import com.jd.cdyjy.icsp.entity.MemberEntity
import com.jd.cdyjy.jimui.ui.selector.SelectorCallback.SelectorCallback
import com.jd.cdyjy.jimui.ui.selector.SelectorCallback.SelectorUICallback
import com.jd.me.dd.im.R
import com.jd.me.dd.im.conference.model.GroupShareTarget
import com.jd.me.dd.im.conference.model.MemberShareTarget
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.model.ShareMeetingBean
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.JsonUtils
import com.jingdong.conference.integrate.ServiceHubLazy
import com.jingdong.conference.integrate.ShareTarget
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume

class SelectorCallbackShareVideoConference(private val context: Context, private val data: ShareMeetingBean, private val con: Continuation<List<ShareTarget>>) : SelectorCallback {

    val imDdService by lazy{ AppJoint.service(ImDdService::class.java) }

    override fun back() {
        (ServiceHubLazy.externalService as ExternalServiceImpl).selectStart = false
        con.resume(emptyList())
    }

    override fun commit(items: ArrayList<MemberEntity>?, callback: SelectorUICallback?) {
        if (items.isNullOrEmpty()) {
            (ServiceHubLazy.externalService as ExternalServiceImpl).selectStart = false
            con.resume(emptyList())
            return
        }

        val dialogContent= getDialogContent(items)
        callback?.dialog3(context.getString(R.string.meeting_forward_msg_to), dialogContent,
                context.getString(R.string.me_cancel), null,
                context.getString(R.string.meeting_send_count, items.size), View.OnClickListener {
            process(items)
            (ServiceHubLazy.externalService as ExternalServiceImpl).selectStart = false
            con.resume(items.map { if (it.isGroup) GroupShareTarget(it.mId) else MemberShareTarget(it.mId, it.mApp) })
            callback?.closeSelector()
            kotlin.runCatching {
                MELogUtil.localI("ShareMeeting", "meeting info:".plus(JsonUtils.getJsonString(data).toString())
                        .plus(" send to:").plus(dialogContent))
            }
        })

    }

    private fun process(items: ArrayList<MemberEntity>?) {
        items?.forEach {
            val gid = if (it.isGroup) {
                it.mId
            } else {
                null
            }
//            sendInvite(it.mId, it.mApp, gid, data)
            imDdService.sendVideoConferenceMsg(gid, it.mId, it.mApp, data)
        }
    }

//    private fun sendInvite(pin: String, app: String?, gid: String?, data: ShareMeetingBean) {
//        var parameter = MsgParameter.createMeetingCardParameter(pin, app, gid, ChatMessageDao.ChatType.NCHAT, data)
//        var factory = MeetingCardMsgFactory()
//        var msg = factory.sendMsg(parameter) { msgId, mid, state ->
//            MsgEvent.eventUpdateMsgState(msgId, mid, state)
//        }
//
//        if (msg != null) {
//            MsgEvent.eventAddMsgToBottom(msg)
//        }
//    }

    private fun getDialogContent(items: ArrayList<MemberEntity>): String? {
        val buffer = StringBuffer()
        val size = items.size
        var i = 0
        for (member in items) {
            i++
            if (member.isGroup) {
                if (!TextUtils.isEmpty(member.mName)) {
                    buffer.append(member.mName).append(",")
                } else {
                    val group = AppCache.getInstance().getChatGroupInfo(member.mId, false)
                    if (null == group) {
                        buffer.append(member.mId).append(",")
                    } else {
                        if (TextUtils.isEmpty(group.name)) {
                            buffer.append(member.mId).append(",")
                        } else {
                            buffer.append(group.name).append(",")
                        }
                    }
                }
            } else {
                val contactInfo = AppCache.getInstance().getContactInfo(member.mId, member.mApp, false)
                if (null == contactInfo) {
                    buffer.append(member.mId).append(",")
                } else {
                    if (TextUtils.isEmpty(contactInfo.mShowName)) {
                        buffer.append(member.mId).append(",")
                    } else {
                        buffer.append(contactInfo.mShowName).append(",")
                    }
                }
            }
            if (i >= 2) {
                buffer.deleteCharAt(buffer.length - 1)
                if (size > 2) {
                    buffer.append(context.getString(R.string.me_im_deng))
                }
                break
            }
        }
        if (size == 1) {
            buffer.deleteCharAt(buffer.length - 1)
        }
        return buffer.toString()
    }
}