package com.jd.me.dd.im.conference.model

import android.os.Parcel
import android.os.Parcelable
import com.jingdong.conference.account.model.User

class ConferenceUser(override var pin: String?, override var appId: String?, override var teamId: String?) : User {
    private var _avatar: String? = null
    private var _nickname: String? = null

    constructor(parcel: Parcel) : this(parcel.readString(), parcel.readString(), parcel.readString()) {
        _avatar = parcel.readString()
        _nickname = parcel.readString()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(pin)
        parcel.writeString(appId)
        parcel.writeString(teamId)
        parcel.writeString(_avatar)
        parcel.writeString(_nickname)
    }

    override var avatar: String?
        get() = _avatar
        set(value) {
            _avatar = value
        }
    override var nickname: String?
        get() = _nickname
        set(value) {
            _nickname = value
        }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ConferenceUser> {
        override fun createFromParcel(parcel: Parcel): ConferenceUser {
            return ConferenceUser(parcel)
        }

        override fun newArray(size: Int): Array<ConferenceUser?> {
            return arrayOfNulls(size)
        }
    }
}