<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application>
        <!-- 咚咚 -->
        <meta-data
            android:name="ipc_preferences_provider_authority"
            android:value="${applicationId}.mixdd.icsp"
            tools:replace="android:value" />

        <!--core进程名配置,value等于空时为单进程-->
        <meta-data
            android:name="ipc_android_process_name"
            android:value=""
            tools:replace="android:value" />

        <!--ui进程名配置,value需配置为宿主进程名-->
        <meta-data
            android:name="ipc_ui_process_name"
            android:value="${applicationId}"
            tools:replace="android:value" />

        <meta-data
            android:name="ipc_cp_core_provider_authority"
            android:value="${applicationId}.im"
            tools:replace="android:value" />

        <!--app前缀，开发者可自己设置-->
        <!--TODO jdme硬编码-->
        <meta-data
            android:name="ipc_app_prefix"
            android:value="jdme"
            tools:replace="android:value" />

        <!--是否支持漫游，true为支持，开发者可自己设置-->
        <meta-data
            android:name="chat_roaming"
            android:value="false" />

        <!--tracker地址自定义，开发者可自己设置-->
        <meta-data
            android:name="tracker_host"
            android:value="chat.jd.com/locate" />

        <!-- 咚咚灰度环境 android:value="ap-ee2.jd.com"-->
        <!-- 咚咚预发环境 android:value="**************"-->
        <!-- 咚咚测试环境 android:value="************"-->
        <meta-data
            android:name="connect_host"
            android:value=""
            tools:replace="android:value" />

        <!--配置toolBar属于fragment还是activity  true为fragment，false为Activity，开发者可自己设置-->
        <meta-data
            android:name="toolbarModel"
            android:value="true" />

        <!--*value必须为宿主主activity-->
        <!--        <meta-data-->
        <!--            android:name="mainActivityClassName"-->
        <!--            android:value="com.jd.oa.business.index.MainActivity"-->
        <!--            tools:replace="android:value" />-->
        <meta-data
            android:name="mainActivityClassName"
            android:value="com.jd.oa.business.home.MainActivity"
            tools:replace="android:value" />

        <meta-data
            android:name="mainActivityActionName"
            android:value="com.jd.oa.ACTION.MAIN"
            tools:replace="android:value" />

        <!-- 缓存文件夹名字 -->
        <meta-data
            android:name="base_cache_dir"
            android:value="timline"
            tools:replace="android:value" />

        <meta-data
            android:name="log_path_name"
            android:value="log_Timline_JDME"
            tools:replace="android:value" />

        <!--当配置为多进程时，需要配置process=ipc_ui_process_name的值+ipc_android_process_name的值-->
        <service
            android:name="jd.cdyjy.jimcore.core.tcp.core.NotificationService"
            android:exported="false"
            tools:node="replace" />

        <!--当配置为多进程时，需要配置process=ipc_ui_process_name的值+ipc_android_process_name的值-->
        <!--<provider
            android:name="jd.cdyjy.jimcore.core.dblib.cp.CPCoreDatabase"
            android:authorities="${applicationId}.im"
            android:exported="false"
            tools:node="replace" />-->

        <!--当配置为多进程时，需要配置process=ipc_ui_process_name的值+ipc_android_process_name的值-->
        <!--<provider
            android:name="jd.cdyjy.jimcore.core.dblib.pref.provider.PreferencesProvider"
            android:authorities="${applicationId}.mixdd.icsp"
            android:exported="false"
            tools:node="replace" />-->

        <!--*provider前缀为宿主进程名,上层代码引用方式为：宿主进程名 +.fileProvider-->
        <provider
            android:name="com.jd.cdyjy.common.gallery.util.OpimFileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/im_file_paths"
                tools:replace="android:resource" />
        </provider>
        <!-- 监听咚咚启动关闭 -->
        <receiver
            android:name=".receiver.TimlineOnOffReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.jd.oa.timLine.RESUME" />
                <action android:name="com.jd.oa.timLine.PAUSE" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.jd.me.dd.im.ui.ContactSelectorActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".conference.ActivityConferenceList"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />

        <activity
            android:name=".conference.video.VideoPlayerActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="fullUser"
            android:theme="@style/JDME_ActivityTransparent" />
    </application>
</manifest>