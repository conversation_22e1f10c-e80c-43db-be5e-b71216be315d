<?xml version="1.0" encoding="utf-8"?><!-- 待办checkbox样式 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@drawable/jdme_icon_checkbox_normal" android:state_checked="false" android:state_enabled="true" android:state_pressed="false" />
    <item android:drawable="@drawable/jdme_icon_checkbox_normal" android:state_checked="false" android:state_enabled="true" android:state_pressed="true" />
    <item android:drawable="@drawable/jdme_icon_checkbox_checked" android:state_checked="true" android:state_enabled="true" android:state_pressed="false" />
    <item android:drawable="@drawable/jdme_icon_checkbox_checked" android:state_checked="true" android:state_enabled="true" android:state_pressed="true" />
    <item android:drawable="@drawable/jdme_icon_checkbox_gray_disable" android:state_enabled="false" />
</selector>