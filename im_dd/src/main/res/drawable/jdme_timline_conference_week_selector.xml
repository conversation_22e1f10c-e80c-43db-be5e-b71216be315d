<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="oval">
            <stroke android:width="1px" android:color="@color/dd_colorPrimary" />
            <solid android:color="@color/dd_colorPrimary" />
            <size android:width="25dp" android:height="25dp" />
            <padding android:bottom="3dp" android:left="3dp" android:right="3dp" android:top="3dp" />
        </shape>
    </item>

    <item>
        <shape android:shape="oval">
            <padding android:bottom="3dp" android:left="3dp" android:right="3dp" android:top="3dp" />
            <stroke android:width="1px" android:color="@color/opim_timline_colorInfoHint" />
            <size android:width="25dp" android:height="25dp" />
        </shape>
    </item>
</selector>