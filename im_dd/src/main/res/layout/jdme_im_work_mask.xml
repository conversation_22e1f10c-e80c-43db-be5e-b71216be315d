<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_im_work_mask_bg"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:src="@drawable/jme_icon_work_mask_schematic_diagram" />

            <TextView
                android:id="@+id/tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="22dp"
                android:layout_marginBottom="22dp"
                android:text="@string/me_im_dd_work_mask_tips"
                android:textColor="#848484"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="#D6DBE1" />

            <TextView
                android:textColor="#2B3138"
                android:id="@+id/know"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:padding="12dp"
                android:textSize="16sp"
                android:text="@string/me_im_dd_work_mask_add_now" />
        </LinearLayout>

        <ImageView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="40dp"
            android:src="@drawable/jme_icon_work_mask_close" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>