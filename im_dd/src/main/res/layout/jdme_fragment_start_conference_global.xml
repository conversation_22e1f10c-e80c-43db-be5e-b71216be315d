<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/horizontal_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="20dp"
        app:layout_constraintEnd_toStartOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/joinVideoConference"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/opim_button_join_video_conference" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/join_video_conference"
                    android:textColor="#333333"
                    android:textSize="14sp" />
            </LinearLayout>

        </FrameLayout>

        <FrameLayout
            android:id="@+id/createVideoConference"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/opim_button_start_conference" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/video_conference"
                    android:textColor="#333333"
                    android:textSize="14sp" />


            </LinearLayout>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/scheduleConference"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/opim_button_schedule_conference" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/video_conference_reserve"
                    android:textColor="#333333"
                    android:textSize="14sp" />

            </LinearLayout>

        </FrameLayout>

        <!--        <FrameLayout-->
        <!--            android:id="@+id/voiceConference"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="100dp"-->
        <!--            android:layout_weight="1">-->

        <!--            <LinearLayout-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:orientation="vertical"-->
        <!--                android:layout_gravity="center"-->
        <!--                android:gravity="center_horizontal">-->

        <!--                <ImageView-->
        <!--                    android:layout_width="50dp"-->
        <!--                    android:layout_height="50dp"-->
        <!--                    android:src="@drawable/opim_button_voice_conference"/>-->

        <!--                <TextView-->
        <!--                    android:layout_width="wrap_content"-->
        <!--                    android:layout_height="wrap_content"-->
        <!--                    android:textSize="14sp"-->
        <!--                    android:textColor="#333333"-->
        <!--                    android:layout_marginTop="10dp"-->
        <!--                    android:text="@string/voice_conference"/>-->


        <!--            </LinearLayout>-->

        <!--        </FrameLayout>-->
    </LinearLayout>

    <View
        android:id="@+id/space"
        android:layout_width="match_parent"
        android:layout_height="@dimen/conference_suggest_space"
        android:layout_marginTop="20dp"
        android:background="?android:listDivider"
        app:layout_constraintTop_toBottomOf="@id/horizontal_container" />

    <Button
        android:id="@+id/conferenceList"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginBottom="25dp"
        android:background="@null"
        android:drawableStart="@drawable/opim_ic_conference_list"
        android:text="@string/opim_start_conference_global_button_conference_list"
        android:textColor="?android:textColorPrimary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space" />
</androidx.constraintlayout.widget.ConstraintLayout>