<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/right_img_2"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="8dp"
        android:importantForAccessibility="no"
        android:visibility="gone"
        app:srcCompat="@drawable/jdme_icon_ai_im_title"
        tools:layout_height="22dp"
        tools:layout_width="22dp"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/right_img_1"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="8dp"
        android:importantForAccessibility="no"
        app:srcCompat="@drawable/jdme_sel_cmn_dd_mail" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="4dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/right_img_0"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            app:srcCompat="@drawable/jdme_sel_cmn_dd_address_book" />

        <TextView
            android:id="@+id/right_tip_0"
            android:layout_width="15sp"
            android:layout_height="15sp"
            android:layout_alignLeft="@id/right_img_0"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/jdme_friend_req_unread_count_bg"
            android:gravity="center"
            android:text="99+"
            android:textColor="@color/white"
            android:textSize="@dimen/dp_10"
            android:visibility="gone"
            tools:text="99" />
    </RelativeLayout>

</LinearLayout>