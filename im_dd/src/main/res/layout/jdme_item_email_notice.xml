<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="10dp"
    android:background="@drawable/opim_notice_item_bg_white_selector"
    android:orientation="vertical"
    android:padding="10dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/item_mail_addresser_profile"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_centerVertical="true"
            android:visibility="invisible"
            tools:src="@drawable/jdme_default_icon" />

        <TextView
            android:id="@+id/item_mail_addresser_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/item_mail_receiver_time"
            android:layout_toEndOf="@+id/item_mail_addresser_profile"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="@dimen/me_text_size_middle"
            tools:text="testtesttesttesttesttesttesttesttesttesttesttest" />

        <TextView
            android:id="@+id/item_mail_receiver_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:textColor="@color/jdme_color_myapply_line"
            android:textSize="@dimen/me_text_size_14"
            tools:text="2020/20/20" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="5dp"
        android:background="#EEF1F4" />

    <TextView
        android:id="@+id/item_mail_subject"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="@dimen/me_text_size_14"
        tools:text="Re:dddddddddldkdkdkdkdkkdkdkdkkdkd" />

    <TextView
        android:id="@+id/item_mail_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="#40000000"
        android:textSize="@dimen/me_text_size_14"
        tools:text="dsadsadasdasdsadasdasdasdasdsadadasdasdasdasdaddsadsadasdasdsadasdasdasdasdsadadasdasdasdasdaddsadsadasdasdsadasdasdasdasdsadadasdasdasdasdaddsadsadasdasdsadasdasdasdasdsadadasdasdasdasdaddsadsadasdasdsadasdasdasdasdsadadasdasdasdasdaddsadsadasdasdsadasdasdasdasdsadadasdasfffffdasdasdaddsadsadasdasdsadasdasdasdasdsadadasdasdasdasdad" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="5dp"
        android:background="#EEF1F4" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical|right">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:text="@string/me_notice_detail"
            android:textColor="#666666"
            android:textSize="@dimen/me_text_size_middle" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/jdme_icon_arrow_right_gray" />

    </LinearLayout>
</LinearLayout>