<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="27dp"
    android:layout_height="25dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_top_left_avatar"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/default_person_blue_avatar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.jd.oa.badge.RedDotView
        android:id="@+id/rdv_top_left_badge"
        android:layout_width="6dp"
        android:layout_height="6dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="1dp"/>

    <ImageView
        android:id="@+id/iv_top_left_feeling"
        android:layout_width="11dp"
        android:layout_height="11dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>



</androidx.constraintlayout.widget.ConstraintLayout>