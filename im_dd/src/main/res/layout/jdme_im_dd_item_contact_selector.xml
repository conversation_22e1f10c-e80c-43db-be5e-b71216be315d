<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="63dp"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/checkbox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:src="@drawable/jdme_icon_checkbox_normal" />

            <com.jd.oa.mae.bundles.widget.CircleImageView
                android:id="@+id/jdme_contact_avatar"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/checkbox"
                app:srcCompat="@drawable/ddtl_avatar_personal_normal_blue" />

            <LinearLayout
                android:id="@+id/layout_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/jdme_contact_avatar"
                android:layout_marginStart="10dp"
                android:layout_toEndOf="@+id/jdme_contact_avatar"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#D9000000"
                    android:textSize="16sp"
                    tools:text="陈宏宇" />

                <TextView
                    android:id="@+id/tv_external"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="0dp"
                    android:textColor="#FF1B96FE"
                    android:background="@drawable/jdme_im_chat_external_contact"
                    android:visibility="gone"
                    android:textSize="10sp"
                    android:text="@string/me_im_external_contact"
                    tools:visibility="visible" />
            </LinearLayout>
            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/layout_name"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/jdme_contact_avatar"
                android:textColor="#66000000"
                android:textSize="12sp"
                tools:text="软件工程师" />

        </RelativeLayout>

        <FrameLayout
            android:id="@+id/layout_optional"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:paddingHorizontal="16dp">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:background="@drawable/jdme_bg_contact_optional">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/tv_icon_optional"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:textSize="@dimen/JMEIcon_16"
                    android:textColor="#666666"
                    android:text="@string/icon_tabbar_joyme_de"/>

                <TextView
                    android:id="@+id/tv_optional"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="4dp"
                    android:textColor="#666666"
                    android:textSize="12sp"
                    tools:text="@string/me_im_optional"/>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="44dp"
        android:background="#EEF1F4" />
</LinearLayout>