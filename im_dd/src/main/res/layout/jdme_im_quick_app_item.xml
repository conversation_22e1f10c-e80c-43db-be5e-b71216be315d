<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/app_item"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:padding="10dp"
        android:textColor="#6A6A6A"
        android:textSize="13dp"
        tools:text="Test Test" />

    <ImageView
        android:id="@+id/app_split"
        android:layout_width="2dp"
        android:layout_height="12dp"
        android:layout_gravity="center"
        android:src="@drawable/custom_divider" />

</LinearLayout>
