<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/joinVideoConference"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:textAllCaps="false"
            android:drawableTop="@drawable/meeting_button_join_video_conference"
            android:drawablePadding="@dimen/dp_10"
            android:text="@string/meeting_join_video_conference1"
            android:textColor="#333333"
            android:textSize="13sp" />

        <Button
            android:id="@+id/createVideoConference"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:drawableTop="@drawable/meeting_button_start_conference"
            android:drawablePadding="@dimen/dp_10"
            android:text="@string/meeting_video_conference1"
            android:textAllCaps="false"
            android:textColor="#333333"
            android:textSize="13sp" />

        <Button
            android:id="@+id/scheduleConference"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:drawableTop="@drawable/meeting_button_schedule_conference"
            android:drawablePadding="@dimen/dp_10"
            android:text="@string/meeting_video_conference_reserve1"
            android:textAllCaps="false"
            android:textColor="#333333"
            android:textSize="13sp" />

    </LinearLayout>
</LinearLayout>