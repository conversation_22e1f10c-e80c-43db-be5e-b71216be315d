<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <TextView
        android:id="@+id/notice_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_14"
        android:background="@drawable/opim_datetime_background"
        android:paddingBottom="@dimen/dp_1"
        android:paddingLeft="@dimen/dp_6"
        android:paddingRight="@dimen/dp_6"
        android:paddingTop="@dimen/dp_1"
        android:textColor="@android:color/white"
        android:textSize="@dimen/bodyTextAppearanceSize12" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <LinearLayout
            android:id="@+id/layout_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:paddingBottom="4dp"
            android:background="@drawable/opim_notice_item_bg_white_selector"
            android:orientation="vertical">

            <TextView
                android:id="@+id/notice_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="6dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="#D9000000"
                android:textSize="@dimen/bodyTextAppearanceSize16"
                tools:text="Titletitle"/>
            <com.jd.cdyjy.jimui.ui.widget.NoticeImageView
                android:id="@+id/notice_pic"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:scaleType="centerCrop"
                android:background="#d8d8d8"
                android:layout_marginBottom="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10" />

            <com.jd.cdyjy.jimui.ui.widget.ExpandableTextView
                android:id="@+id/notice_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="10dp"
                android:textColor="#B3000000"
                android:lineSpacingMultiplier="1.5"
                android:textColorLink="#ff1B96FE"
                android:textSize="@dimen/bodyTextAppearanceSize12"
                tools:text="ContentContent"/>

            <LinearLayout
                android:id="@+id/notice_extend"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    style="@style/opim_separatorVerticalStyle"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"/>
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp">
                    <LinearLayout
                        android:id="@+id/layout_action"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_reject"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp"
                            android:gravity="center"
                            android:text="@string/me_joy_meeting_reject"
                            android:textColor="#2B3138"
                            android:background="@drawable/jdme_ripple_white"
                            android:focusable="true"
                            android:clickable="true"/>
                        <View
                            android:layout_width="1px"
                            android:layout_height="match_parent"
                            android:background="@color/opimColorWhiteGray"/>
                        <TextView
                            android:id="@+id/tv_accept"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp"
                            android:gravity="center"
                            android:text="@string/me_joy_meeting_accept"
                            android:textColor="#F0250F"
                            android:background="@drawable/jdme_ripple_white"
                            android:focusable="true"
                            android:clickable="true"/>
                    </LinearLayout>
                    <TextView
                        android:id="@+id/tv_join"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:gravity="center"
                        android:text="@string/me_join_meeting"
                        android:textColor="#F0250F"
                        android:background="@drawable/jdme_ripple_white"
                        android:focusable="true"
                        android:clickable="true"
                        android:visibility="gone"/>
                </FrameLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/notice_last_gap"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:visibility="gone"
        tools:visibility="visible"/>
</LinearLayout>