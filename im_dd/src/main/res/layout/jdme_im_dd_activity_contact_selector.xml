<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_contact"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="#FFFFFF"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/cb_all"
            android:layout_width="wrap_content"
            android:layout_height="28dip"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/comm_spacing_horizontal"
            android:layout_weight="1"
            android:button="@drawable/jdme_selector_checkbox"
            android:paddingLeft="8dp"
            android:text="@string/me_check_all"
            android:textColor="#363636"
            android:textSize="15sp" />

        <Button
            android:id="@+id/bt_finish"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="12dp"
            android:background="@drawable/jdme_bg_btn_red_radius_selector"
            android:text="@string/me_finish"
            android:textColor="#ffffff"
            android:textSize="15sp" />
    </LinearLayout>
</LinearLayout>