<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.jd.me.dd.im.conference.video.PreviewVideoTextureView
        android:id="@+id/videoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/control"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        app:srcCompat="@drawable/jdme_im_video_control" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="left"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="40dp"
        app:srcCompat="@drawable/jdme_im_preview_close" />

    <LinearLayout
        android:id="@+id/videoController"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/jdme_im_gradient_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/videoControl"
            android:layout_width="55dp"
            android:layout_height="30dp"
            android:clickable="true"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            app:srcCompat="@drawable/jdme_im_video_play" />

        <TextView
            android:id="@+id/videoPlayTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <com.jd.me.dd.im.conference.video.PlayProgressView
            android:id="@+id/playProgressView"
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/videoTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="20dp"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

    </LinearLayout>

</FrameLayout>