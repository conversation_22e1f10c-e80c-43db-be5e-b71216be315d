<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/back_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/jdme_icon_back_black"
        android:layout_gravity="center_vertical" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/video_conference_fl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="10dp">

            <TextView
                android:id="@+id/video_conference"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/meeting_video_conference_title"
                android:textSize="16sp" />

            <View
                android:id="@+id/video_line"
                android:layout_width="50dp"
                android:layout_height="4dp"
                android:layout_gravity="bottom|center"
                android:background="@drawable/meeting_line_262626" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/voice_conference_fl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp">

            <TextView
                android:id="@+id/voice_conference"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/meeting_voice_conference_title"
                android:textSize="16sp" />

            <View
                android:id="@+id/voice_line"
                android:layout_width="50dp"
                android:layout_height="4dp"
                android:layout_gravity="bottom|center"
                android:background="@drawable/meeting_line_262626" />

        </FrameLayout>

    </LinearLayout>


</FrameLayout>