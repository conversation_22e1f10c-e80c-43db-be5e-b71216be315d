<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <FrameLayout
        android:id="@+id/old_view_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:layout_marginRight="7dp" />

    <TextView
        android:id="@+id/opim_unread_count"
        android:layout_width="15sp"
        android:layout_height="15sp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:background="@drawable/jdme_friend_req_unread_count_bg"
        android:gravity="center"
        android:text="111"
        android:textColor="#ffffff"
        android:textSize="12sp" />
</RelativeLayout>