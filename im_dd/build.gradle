apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

//    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        vectorDrawables.useSupportLibrary = true
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION


        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name, AROUTER_MODULE_NAME: project.name]
//                arguments = [AROUTER_MODULE_NAME: "me_" + project.name]
            }
        }

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    print("timlineDebug = " + timlineDebug.toBoolean())
    namespace 'com.jd.me.dd.im'
    lint {
        abortOnError false
    }
    if (timlineDebug.toBoolean()) {
        flavorDimensions "opim"
        productFlavors {
            timline {
                dimension "opim"
                matchingFallbacks = ["timline"]
            }
        }
    } else {
        flavorDimensions.addAll(flavor_dimensions)
        productFlavors {
            me {
                dimension 'app'
                matchingFallbacks = ['me', 'timline']
            }
            saas {
                dimension 'app'
                matchingFallbacks = ['saas', 'timline']
            }
            official {
                dimension 'channel'
                matchingFallbacks = ['official', 'timline']
            }
            store {
                dimension 'channel'
                matchingFallbacks = ['store', 'timline']
            }
        }
    }

//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['libs']
//        }
//    }
//
//    // ========= 咚咚无界版 start =====
//    compile(name: 'DatePicker-release', ext: 'aar')
//    compile(name: 'speechcodec-debug', ext: 'aar')
//    compile(name: 'redpackteslib-release', ext: 'aar')
//    compile(name: 'videoRecorder', ext: 'aar')
//    compile(name: 'migratechatdata', ext: 'aar')
//    compile(name: 'signalvariant', ext: 'aar')
//    if (timlineDebug.toBoolean()) {
//        implementation project(':JIMUi')
//        implementation project(':JIMAudio')
//        implementation project(':JIMBase')
//        implementation project(':JIMCore')
//        implementation project(':JIMGallery')
//        implementation project(':JIMGlide')
//        implementation project(':JIMOkhttp')
//        implementation project(':JIMMap')
//        implementation project(':JIMSdk')
//        implementation project(':JIMSmiley')
//        implementation project(':JIMWidget')
//        implementation project(':jimutils')
//        implementation project(':JIMDownloadUpload')
//    } else {
//        compile(name: 'jimui-v1.0.0', ext: 'aar')
//        compile(name: 'jimaudio-v1.0.0', ext: 'aar')
//        compile(name: 'jimbase-v1.0.0', ext: 'aar')
//        compile(name: 'jimcore-v1.0.0', ext: 'aar')
//        compile(name: 'jimgallery-v1.0.0', ext: 'aar')
//        compile(name: 'jimglide-v1.0.0', ext: 'aar')
//        compile(name: 'jimmap-v1.0.0', ext: 'aar')
//        compile(name: 'jimokhttp-v1.0.0', ext: 'aar')
//        compile(name: 'jimsdk-v1.0.0', ext: 'aar')
//        compile(name: 'jimsmiley-v1.0.0', ext: 'aar')
//        compile(name: 'jimwidget-v1.0.0', ext: 'aar')
//        compile(name: 'jimutils-v1.0.0', ext: 'aar')
//        compile(name: 'JIMDownloadUpload-v1.0.0', ext: 'aar')
//    }
}

dependencies {
    def isExclude = timlineDebug.toBoolean();
    def excludes
    if (isExclude) {
        excludes = ['jim*.aar', 'JIM*.aar']
        api project(':JIMUi')
        api project(':JIMAudio')
        api project(':JIMBase')
        api project(':JIMCore')
        api project(':JIMGallery')
        api project(':JIMGlide')
        api project(':JIMOkhttp')
        api project(':JIMMap')
        api project(':JIMSdk')
        api project(':JIMSmiley')
        api project(':JIMWidget')
        api project(':jimutils')
        api project(':JIMDownloadUpload')
    } else {
        excludes = []
        api project(':aar_jimcore')
        api project(':aar_jimui')
        api project(':aar_jimaudio')
        api project(':aar_jimbase')
        api project(':aar_JIMDownloadUpload')
        api project(':aar_jimmap')
        api project(':aar_jimokhttp')
        api project(':aar_jimsdk')
        api project(':aar_jimutils')
        api project(':aar_jimwidget')
        api project(':aar_jimgallery')
        api project(':aar_jimsmiley')
        api project(':aar_jimglide')
        api project(':aar_jimsignalvariant')
    }
    api fileTree(dir: 'libs', include: ['*.jar'], excludes: excludes)

    implementation 'com.jd.oa:lib_date_picker:1.0.0-SNAPSHOT'
    api project(':aar_chatsdk')
    api project(':aar_speechcodec')
    api project(':aar_migratechatdata')
    api project(':aar_videoRecorder')

//    testImplementation 'junit:junit:4.12'
//    androidTestImplementation 'com.android.support.test:runner:1.0.2'
//    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'

    implementation COMPILE_COMMON.gson
    implementation "androidx.versionedparcelable:versionedparcelable:1.1.0"
    implementation "androidx.coordinatorlayout:coordinatorlayout:1.1.0"
//    implementation "com.android.support.constraint:constraint-layout:${constraintVersion}"

//    implementation 'com.jingdong.wireless.libs:jdmasdk:4.0.6'

//    implementation 'com.tencent.bugly:crashreport:2.8.6.0'
//    implementation 'com.tencent.bugly:nativecrashreport:3.6.0.0'

//    implementation 'com.chenenyu.router:router:1.5.2'
//    kapt 'com.chenenyu.router:compiler:1.5.1'

    implementation "com.tencent.wcdb:wcdb-android:$wcdbVersion"
    implementation 'com.github.Dimezis:BlurView:version-2.0.3'
    implementation "com.commit451:PhotoView:$photoViewVersion"
    implementation "androidx.recyclerview:recyclerview:1.2.0"
    implementation "androidx.exifinterface:exifinterface:1.0.0"
    implementation("cn.qqtheme.framework:WheelPicker:$wheelPickerVersion") {
        exclude group: 'com.android.support'
    }
    implementation "com.google.code.gson:gson:$gsonVersion"
    implementation "com.github.bumptech.glide:glide:$glideVersion"
    implementation "jp.wasabeef:glide-transformations:$glideTransformationsVersion"
    implementation "com.davemorrissey.labs:subsampling-scale-image-view:$subsamplingScaleImageViewVersion"
    implementation("androidx.lifecycle:lifecycle-extensions:2.2.0") {
        exclude group: "com.android.support"
    }
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.2.0"
    implementation "androidx.activity:activity-ktx:1.6.0"
    implementation "androidx.fragment:fragment-ktx:1.2.5"
//    implementation "com.android.support:multidex:$multidexVersion"
    implementation "androidx.palette:palette:1.0.0"
    implementation 'org.whispersystems:curve25519-java:0.4.1'
    implementation 'com.google.guava:guava:27.0.1-android'
    implementation "com.google.protobuf:protobuf-java:$protobufVersion"
    implementation 'com.getkeepsafe.relinker:relinker:1.4.4'
//    implementation 'com.tencent.bugly:crashreport:2.8.6.0'

    // ========= 咚咚无界版 end ====

    implementation 'com.jd.oa:mae-bundles-monitorfragment:1.0.2-SNAPSHOT'

    implementation project(":common")
//    implementation project(":im_dd_redpacket")

    //红包依赖
    implementation('com.jd.jrapp.jdpay:redpackets:3.06.01-202505281103') {
        exclude group: 'com.tencent', module: 'mmkv'
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'com.squareup.okio', module: 'okio'
        exclude group: 'com.jd.jrapp.bm.lib', module: 'mmkv-sp'
    }

//    api('com.jd.jrapp.jdpay:jdpaysdk:1.0.0.30.2-202101111411-6.0.60-SNAPSHOT') {
//        exclude group: 'androidx.appcompat', module: 'appcompat'
//        exclude group: 'androidx.recyclerview', module: 'recyclerview'
//        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
//        exclude group: 'com.alibaba', module: 'fastjson'
//        exclude group: 'com.google.code.gson', module: 'gson'
//        exclude group: 'com.jd.jrapp.library', module: 'tbs-x5'
////        exclude group: 'com.jdjr.security.library', module: 'jdjr_aks_mobile_core'
//        exclude group: 'com.tencent.tbs.tbssdk', module: 'sdk'
//    }

    implementation "io.reactivex.rxjava3:rxandroid:3.0.0"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion"
    kapt "ke.tang:context-injector-compiler:$contextInjector"
    implementation "ke.tang:context-injector:$contextInjector"
    implementation "io.reactivex.rxjava3:rxandroid:3.0.0"
    implementation "commons-io:commons-io:2.7"

    implementation 'com.google.android.material:material:1.2.0-alpha03'
    implementation 'org.apmem.tools:layouts:1.10@aar'
    implementation 'com.tencent.mars:mars-xlog:1.2.5'
    implementation "ke.tang:refresh:1.1.1"

    //QUIC库
    implementation 'com.jingdong.wireless.bundle-lib:mediaplayer:2.5.4-me'
    //会议
//    releaseImplementation('com.jingdong.conference:debug:1.4.16-release') {
//        exclude group: "org.apache.httpcomponents"
//    }
//    debugImplementation('com.jingdong.conference:debug:1.4.16-debug') {
//        exclude group: "org.apache.httpcomponents"
//    }
//
//    implementation('com.jingdong.conference:conference:1.4.16') {
//        exclude group: "org.apache.httpcomponents"
//    }
//
//    implementation('com.jingdong.conference:integrate:1.4.16') {
//        exclude group: "org.apache.httpcomponents"
//    }
//    implementation('com.jingdong.conference:device:1.4.16') {
//        exclude group: "org.apache.httpcomponents"
//    }

    implementation("com.jdcloud.sdk:core:1.2.3-SNAPSHOT") {
//        force true
        exclude group: "com.google.code.gson"
        exclude group: "org.apache.commons"
        exclude group: "org.apache.httpcomponents"
    }

//    implementation 'com.jdcloud.jrtc:jrtc-android:3.7.3-SNAPSHOT'

    api "com.alibaba:arouter-api:1.5.1"
    kapt "com.alibaba:arouter-compiler:1.5.1"

    implementation project(":unifiedsearch")
    implementation project(":libjdmeeting")
    implementation project(':libjoymeeting')
//    implementation 'com.jd.oa:joymeeting:1.0.0-SNAPSHOT'
    implementation project(':libphotoeditor')

    final def markwon_version = '4.5.1'
    api "io.noties.markwon:core:$markwon_version"
    api "io.noties.markwon:ext-strikethrough:$markwon_version"
    api "io.noties.markwon:image-glide:$markwon_version"
    api "io.noties.markwon:ext-tables:$markwon_version"
    api "io.noties.markwon:html:$markwon_version"
    api "io.noties.markwon:image:$markwon_version"
    api "io.noties.markwon:inline-parser:$markwon_version"

//    def version = rootProject.ext.saasMeeting//京we 编译时依赖 implementation 在app中
//    compileOnly("com.jingdong.conference:mesdk:$version")

    //京东会议sdk-----
    def meetingSdkExcludes = {
        exclude group: "org.apache.httpcomponents"
        exclude group: 'com.tencent', module: 'mmkv-static'
        exclude group: "org.apache.commons"
        exclude group: 'commons-logging', module: 'commons-logging'
        exclude group: 'com.alibaba', module: 'fastjson'
        exclude group: 'com.jdcloud.sdk', module: 'core'
        exclude group: 'com.tencent', module: 'mmkv'
        exclude group: 'com.github.open-android', module: 'pinyin4j'
        exclude group: 'com.github.bumptech.glide', module: 'glide'
        exclude group: 'androidx.constraintlayout', module: 'constraintlayout'
        exclude group: 'com.chibatching.kotpref', module: 'enum-support'
        exclude group: 'com.jingdong.wireless.jdsdk', module: 'android-sdk-gatewaysign'
        exclude group: 'com.jingdong.wireless.jdsdk', module: 'okuuid'
    }
    //saas会议sdk版本
    def localSaasMeetingVersion = rootProject.ext.saasMeeting
    def finalSaasMeetingVersion = VIDEO_MEETING_VERSION.toString().isEmpty() ? localSaasMeetingVersion : VIDEO_MEETING_VERSION
    saasImplementation("com.jingdong.conference:mesdk:$finalSaasMeetingVersion", meetingSdkExcludes)
    //me会议sdk版本
    def localMeMeetingVersion = rootProject.ext.meMeeting
    def finalMeMeetingVersion = VIDEO_MEETING_VERSION.toString().isEmpty() ? localMeMeetingVersion : VIDEO_MEETING_VERSION
    meImplementation("com.jingdong.conference:mesdk:$finalMeMeetingVersion", meetingSdkExcludes)
    //京东会议sdk-----
}
