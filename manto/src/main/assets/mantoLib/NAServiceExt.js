if(typeof ext !== 'undefined' && typeof ext.addPluginAPIs === 'function' ){
    ext.addPluginAPIs({
        requestMe : function (e) {
            jd.__invokeMethod__('requestMe', e)
        },
        requestGateway : function (e) {
            jd.__invokeMethod__('requestGateway', e)
        },
        getAuthorizationCode : function (e) {
            jd.__invokeMethod__('getAuthorizationCode', e)
        },
        setWifi : function (e) {
            jd.__invokeMethod__('setWifi', e)
        },
        getMeUserInfo : function (e) {
            var t = {};
            jd.__invokeMethod__(
                "getMeUserInfo",
                {},
                {
                    beforeSuccess: function (e) {
                        t = e;
                    },
                }
            );
            return t;
        },
        setMeStorage : function (e) {
            var t = {};
            jd.__invokeMethod__(
                "setMeStorage",
                e,
                {
                    beforeSuccess: function (e) {
                        t = e;
                    },
                }
            );
            return t;
        },
        getMeStorage : function (e) {
            var t = {};
            jd.__invokeMethod__(
                "getMeStorage",
                e,
                {
                    beforeSuccess: function (e) {
                        t = e;
                    },
                }
            );
            return t;
        },
        removeMeStorage : function (e) {
            var t = {};
            jd.__invokeMethod__(
                "removeMeStorage",
                e,
                {
                    beforeSuccess: function (e) {
                        t = e;
                    },
                }
            );
            return t;
        },
        getFilePath : function (e) {
            var t = {};
            jd.__invokeMethod__(
                "getFilePath",
                e,
                {
                    beforeSuccess: function (e) {
                        t = e;
                    },
                }
            );
            return t;
        },

        postMessageToNative : function (params) {
            jd.__invokeMethod__("postMessageToNative", params);
        },
        //新增
        requestAuthCode : function (e) {
            jd.__invokeMethod__('requestAuthCode', e)
        },

        openProfile : function (e) {
            jd.__invokeMethod__('openProfile', e)
        },

        openChat : function (e) {
            jd.__invokeMethod__('openChat', e)
        },

        openGroupChat : function (e) {
            jd.__invokeMethod__('openGroupChat', e)
        },

        chooseMEContact : function (e) {
            jd.__invokeMethod__('chooseMEContact', e)
        },

        openSchema : function (e) {
            jd.__invokeMethod__('openSchema', e)
        },

        chooseChat : function (e) {
            jd.__invokeMethod__('chooseChat', e)
        },

        sendMessageCard : function (e) {
            jd.__invokeMethod__('sendMessageCard', e)
        },

        checkAppUpdate : function (e) {
            jd.__invokeMethod__('checkAppUpdate', e)
        },

        showMoreMenu : function (e) {
            jd.__invokeMethod__('showMoreMenu', e)
        },

        sendShareCard : function (e) {
            jd.__invokeMethod__('sendShareCard', e)
        },

        sendMessageCardToGroup : function (e) {
            jd.__invokeMethod__('sendMessageCardToGroup', e)
        },

        requestAccess : function (e) {
            jd.__invokeMethod__('requestAccess', e)
        },
        sendEvent : function (e) {
            jd.__invokeMethod__('sendEvent', e)
        },
        registerEvent : function (e) {
            jd.__invokeMethod__('registerEvent', e)
        },
        unRegisterEvent : function (e) {
            jd.__invokeMethod__('unRegisterEvent', e)
        }
    })
}

jd.requestMe = function (e) {
    jd.__invokeMethod__('requestMe', e)
}
jd.requestGateway = function (e) {
    jd.__invokeMethod__('requestGateway', e)
}
jd.getAuthorizationCode = function (e) {
    jd.__invokeMethod__('getAuthorizationCode', e)
}

jd.setWifi = function (e) {
    jd.__invokeMethod__('setWifi', e)
}
jd.getMeUserInfo = function (e) {
    var t = {};
    jd.__invokeMethod__(
        "getMeUserInfo",
        {},
        {
            beforeSuccess: function (e) {
                t = e;
            },
        }
    );
    return t;
};
jd.setMeStorage = function (e) {
    var t = {};
    jd.__invokeMethod__(
        "setMeStorage",
        e,
        {
            beforeSuccess: function (e) {
                t = e;
            },
        }
    );
    return t;
};
jd.getMeStorage = function (e) {
    var t = {};
    jd.__invokeMethod__(
        "getMeStorage",
        e,
        {
            beforeSuccess: function (e) {
                t = e;
            },
        }
    );
    return t;
};
jd.removeMeStorage = function (e) {
    var t = {};
    jd.__invokeMethod__(
        "removeMeStorage",
        e,
        {
            beforeSuccess: function (e) {
                t = e;
            },
        }
    );
    return t;
};
jd.getFilePath = function (e) {
    var t = {};
    jd.__invokeMethod__(
        "getFilePath",
        e,
        {
            beforeSuccess: function (e) {
                t = e;
            },
        }
    );
    return t;
};

jd.postMessageToNative = function (params) {
    jd.__invokeMethod__("postMessageToNative", params);
};

//新增
jd.requestAuthCode = function (e) {
    jd.__invokeMethod__('requestAuthCode', e)
}

jd.openProfile = function (e) {
    jd.__invokeMethod__('openProfile', e)
}

jd.openChat = function (e) {
    jd.__invokeMethod__('openChat', e)
}

jd.openGroupChat = function (e) {
    jd.__invokeMethod__('openGroupChat', e)
}

jd.chooseMEContact = function (e) {
    jd.__invokeMethod__('chooseMEContact', e)
}

jd.openSchema = function (e) {
    jd.__invokeMethod__('openSchema', e)
}

jd.chooseChat = function (e) {
    jd.__invokeMethod__('chooseChat', e)
}

jd.sendMessageCard = function (e) {
    jd.__invokeMethod__('sendMessageCard', e)
}

jd.checkAppUpdate = function (e) {
    jd.__invokeMethod__('checkAppUpdate', e)
}

jd.showMoreMenu = function (e) {
    jd.__invokeMethod__('showMoreMenu', e)
}

jd.sendShareCard = function (e) {
    jd.__invokeMethod__('sendShareCard', e)
}

jd.sendMessageCardToGroup = function (e) {
    jd.__invokeMethod__('sendMessageCardToGroup', e)
}

jd.requestAccess = function (e) {
    jd.__invokeMethod__('requestAccess', e)
}

// ======2025.02。15
// 设置容器背景颜色，调用示例如下，color为空的话，清空本地已经设置的颜色，恢复容器背景默认颜色
// jd.setBackgroundColorContent({ color:"#RRGGBBAA" })
jd.setBackgroundColorContent = function (e) {
    jd.__invokeMethod__('setBackgroundColorContent', e);
};

//add 7.9.10
jd.sendEvent = function (e) {
    jd.__invokeMethod__('sendEvent', e)
};
jd.registerEvent = function (e) {
    jd.__invokeMethod__('registerEvent', e)
};
jd.unRegisterEvent = function (e) {
    jd.__invokeMethod__('unRegisterEvent', e)
};

jd.postMessageToNative = function (params) {
  jd.__invokeMethod__("postMessageToNative", params); //nativeAPIname为原生方法的名称
};

jd.onCustomEvent = function (eventName, callback) {
  jd.__onMethod__(eventName, function (e) {
    "function" == typeof callback && callback(e);
  });
};