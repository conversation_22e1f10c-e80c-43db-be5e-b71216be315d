package com.jd.manto.open;

import android.graphics.Bitmap;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.BitmapFactory;

import com.jd.oa.AppBase;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

class BitmapCache {
    private final static String MNI_APP_SHARE_PIC = "MNI_APP_SHARE_PIC";

    /**
     * 保存Bitmap为文件;可能报空指针是因为没有配置权限
     */
    static void saveBitmap2file(Bitmap bmp) {
        CompressFormat format = Bitmap.CompressFormat.JPEG;
        int quality = 100;
        OutputStream stream = null;
        try {
            stream = new FileOutputStream(AppBase.getAppContext().getCacheDir().getPath()
                    + "/"
                    + MNI_APP_SHARE_PIC
                    + ".jpg");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        bmp.compress(format, quality, stream);
        try {
            if (stream != null) {
                stream.flush();
                stream.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 读取文件为Bitmap
     */
    static Bitmap getBitmapFromFile() {
        try {
            return BitmapFactory.decodeStream(new FileInputStream(AppBase.getAppContext().getCacheDir().getPath()
                    + "/"
                    + MNI_APP_SHARE_PIC
                    + ".jpg"));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 读取文件为Bitmap
     */
    static boolean reset() {
        File f = new File(AppBase.getAppContext().getCacheDir().getPath()
                + "/"
                + MNI_APP_SHARE_PIC
                + ".jpg");
        if (f.exists()) {
            return f.delete();
        }
        return false;
    }
}
