package com.jd.manto.open;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.utils.ToastUtils;
import com.jingdong.Manto;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;
import com.jingdong.manto.sdk.api.ILogin;
import com.jingdong.manto.utils.MantoProcessUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.jd.oa.router.DeepLink.ACTIVITY_URI_Capture;

/**
 * the example for add your custom js api to manto engine, these apis will be called for js to engine
 * 1. manto running different process from host, if must call some function in host, should use across type api, otherwise inprocess type.
 * 2. if start a page to get data , not api , please use LAUNCH_FOR_RESULT_TYPE type
 * 3. if you want return data async, using ACROSS_PROCESS_TYPE、IN_PROCESS_TYPE， otherwise IN_PROCESS_SYNC_TYPE、ACROSS_PROCESS_SYNC_TYPE
 */

public class JSAPIDemoModule extends AbstractMantoModule {
    public static final String REGEX = "openapp\\.jdmobile://virtual\\?params=([\\s\\S]+)";

    @Override
    //the group name for these apis
    public String getModuleName() {
        return "openmoudle";
    }

    @Override
    //here regist all the jsapi, with method name/index/type
    //you can regist all the similar api here with different type
    //1. ACROSS_PROCESS_TYPE, native method type for calling the host api cross process asynchronous, and handle the method in handleMethod.
    //2. IN_PROCESS_TYPE, native method type for calling api in manto asynchronous, and handle the method in handleMethod.
    //3. LAUNCH_FOR_RESULT_TYPE, native method type for picker , and handle the method in handleMethod, then format the data in handleResult.
    //4. IN_PROCESS_SYNC_TYPE, native method type for calling api in manto synchronous , and handle the method in handleMethodSync, return a bundle data.
    //5. ACROSS_PROCESS_SYNC_TYPE, native method type for calling the host api cross process synchronous, and handle the method in handleMethodSync, just return invoke status
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("lanch_scan", 10001, LAUNCH_FOR_RESULT_TYPE));//launch scan activity to get scan code
        list.add(new JsApiMethod("lanch_scan_async", 10002, LAUNCH_FOR_RESULT_TYPE));//launch scan activity to get scan code, return data async
        list.add(new JsApiMethod("o_openToast", 10003, IN_PROCESS_SYNC_TYPE));//show toast in manto process
        list.add(new JsApiMethod("get_user", 10004, ACROSS_PROCESS_SYNC_TYPE));//get the user name from host process
        list.add(new JsApiMethod("get_androidinfo", 10005, IN_PROCESS_TYPE));//get android os version async in manto process
        list.add(new JsApiMethod("get_appversion", 10006, ACROSS_PROCESS_TYPE));//get a2 in host process async
    }

    @Override
    // 1.all the js data(params) will pass to this function to parse to bundle data
    // 2.then the bundle will pass to handleMethod/handleMethodSync
    // 3.this function will be invoked in process of manto, not app process
    public Bundle initData(String method, MantoCore ac, JSONObject params) {
        // 1.前端参数：params
        if ("lanch_scan".equals(method)) {
            final Bundle req = new Bundle();
            req.putString("scanType", "QRcode");
            req.putInt("requestCode", 65535);
            Log.e("better", "parseParams:" + MantoProcessUtil.getProcessName());
            return req;
        } else if ("lanch_scan_async".equals(method)) {
            final Bundle req = new Bundle();
            req.putString("scanType", "QRcode");
            req.putBoolean(IMantoBaseModule.HANDLERESULT_WITHCALLBACK, true);//return data async
            req.putInt("requestCode", 65535);
            Log.e("better", "parseParams:" + MantoProcessUtil.getProcessName());
            return req;
        } else if ("o_openToast".equals(method)) {
            final String msg = params.optString("title");
            final Bundle req = new Bundle();
            req.putString("title", msg);
            return req;
        } else if ("get_user".equals(method)) {
            final String msg = params.optString("title");
            final Bundle req = new Bundle();
            req.putString("title", msg);
            return req;
        } else if ("get_androidinfo".equals(method)) {
            final String msg = params.optString("title");
            final Bundle req = new Bundle();
            req.putString("title", msg);
            return req;
        } else if ("get_appversion".equals(method)) {
            final String msg = params.optString("title");
            final Bundle req = new Bundle();
            req.putString("title", msg);
            return req;
        }
        return new Bundle();
    }

    @Override
    //the method with type ACROSS_PROCESS_TYPE、IN_PROCESS_TYPE、LAUNCH_FOR_RESULT_TYPE will be called here to implement the really function with method name
    //1.data , the bundle format int initData
    //2.callback, after all the function invoked, you can pass the bundle data to js with this callback(onFailed/onSuccess) asynchronous
    //3.activity, the method regist as ACROSS_PROCESS_TYPE, activity will be null, type IN_PROCESS_TYPE, activity will be manto base activity
    public void handleMethod(String method, MantoCore activity, Bundle data, MantoResultCallBack callback) {
        if ("lanch_scan".equals(method)) {
            Intent intent = Router.build(ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
            String scanTypes = data.getString("scantype");
            if (null != scanTypes && scanTypes.length() > 0) {
                intent.putExtra("scanType", scanTypes);
            }
            activity.getActivity().startActivityForResult(intent, 65535);
        } else if ("lanch_scan_async".equals(method)) {
            Intent intent = Router.build(ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
            String scanTypes = data.getString("scantype");
            if (null != scanTypes && scanTypes.length() > 0) {
                intent.putExtra("scanType", scanTypes);
            }
            activity.getActivity().startActivityForResult(intent, 65535);
        } else if ("get_androidinfo".equals(method)) {
            Bundle bundle = new Bundle();
            String version = android.os.Build.VERSION.RELEASE;
            String model = android.os.Build.MODEL;
            bundle.putString("version", version);
            bundle.putString("model", model);
            Toast.makeText(activity.getActivity().getApplicationContext(), "version:" + version + ",model" + model, Toast.LENGTH_LONG).show();
            callback.onSuccess(bundle);
        } else if ("get_appversion".equals(method)) {
            Bundle bundle = new Bundle();
            final String msg = data.getString("title");
            int versioncode = getVersionCode(); //here activity will be null
            bundle.putString("versionCode", "" + msg + ": " + versioncode);
            callback.onSuccess(bundle);
        }
    }

    public static int getVersionCode() {
        Context context = AppBase.getAppContext();
        int versionCode = 0;
        try {
            versionCode = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0).versionCode;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return versionCode;
    }

    @Override
    //the method with type IN_PROCESS_SYNC_TYPE、ACROSS_PROCESS_SYNC_TYPE will be called here to implement the really function with method name
    //1.data , the bundle format int initData
    //2.activity, the method regist as ACROSS_PROCESS_SYNC_TYPE, activity will be null, type IN_PROCESS_TYPE, activity will be manto base activity
    public Bundle handleMethodSync(String method, MantoCore activity, Bundle data) {
        if ("o_openToast".equals(method)) {
            final String msg = data.getString("title");
            ToastUtils.showToast(msg);
            final Bundle resp = new Bundle();
            resp.putString(IMantoBaseModule.ERROR_CODE, IMantoBaseModule.ERROR_CODE_SUCCESS); // should contain error code
            resp.putAll(data);
            return resp;
        } else if ("get_user".equals(method)) {
            final String msg = data.getString("title");
            ILogin iLogin = Manto.instanceOf(ILogin.class);
            String user = "none";
            if (null != iLogin) {
                user = iLogin.getPin(activity.getActivity());
            }
            ToastUtils.showToast("" + msg + ": " + user);
            final Bundle resp = new Bundle();
            resp.putString(IMantoBaseModule.ERROR_CODE, IMantoBaseModule.ERROR_CODE_SUCCESS); // should contain error code
            resp.putAll(data);
            return resp;
        }
        return new Bundle();
    }

    @Override
    //the method with LAUNCH_FOR_RESULT_TYPE will be called here, when onActivityResult is call from picker
    //1.activity, the manto base activity
    //2.intent/resultCode/requestCode, the base data from onActivityResult
    //3.return the bundle data to js
    public Bundle handleResult(String method, MantoCore activity, Intent intent, int resultCode, int requestCode) {
        Bundle map = new Bundle();
        switch (resultCode) {
            case Activity.RESULT_OK:
                String scanResult = intent.getStringExtra("result");
                String scanType = intent.getStringExtra("type");
                map.putString("charSet", "utf-8");
                if (!TextUtils.isEmpty(scanResult)) {
                    Pattern pattern = Pattern.compile(REGEX);
                    Matcher matcher = pattern.matcher(scanResult);
                    if (matcher.matches()) {
                        try {
                            JSONObject params = new JSONObject(matcher.group(1));
//                            String appId = params.optString("appId").trim();
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    scanResult = "";
                }
                map.putString(IMantoBaseModule.RESULT, scanResult);
                map.putString(IMantoBaseModule.ERROR_CODE, IMantoBaseModule.ERROR_CODE_SUCCESS);//the error code of success, must contained
                map.putString("rawData", Base64.encodeToString(scanResult.getBytes(), Base64.DEFAULT));
                map.putString("scanType", scanType);
                return map;
            case Activity.RESULT_CANCELED:
                map.putString(IMantoBaseModule.ERROR_CODE, IMantoBaseModule.ERROR_CODE_FAILED);//the error code of failed, must contained
                return map;
            default:
                map.putString(IMantoBaseModule.ERROR_CODE, "2");
                return map;
        }
    }

    @Override
    //the method with LAUNCH_FOR_RESULT_TYPE, and the bundle data returned in function initData with IMantoBaseModule.HANDLERESULT_WITHCALLBACK=true
    // will be called here, when onActivityResult is call from picker
    //1.activity, the manto base activity
    //2.intent/resultCode/requestCode, the base data from onActivityResult
    //3. callback to pass bundle data to js
    public void handleResultWithCallback(String method, MantoCore activity, Intent intent, int resultCode, int requestCode, MantoResultCallBack callback) {
        Bundle map = new Bundle();
        switch (resultCode) {
            case Activity.RESULT_OK:
                String scanResult = intent.getStringExtra("result");
                String scanType = intent.getStringExtra("type");
                map.putString("charSet", "utf-8");
                if (!TextUtils.isEmpty(scanResult)) {
                    Pattern pattern = Pattern.compile(REGEX);
                    Matcher matcher = pattern.matcher(scanResult);
                    if (matcher.matches()) {
                        try {
                            JSONObject params = new JSONObject(matcher.group(1));
                            String appId = params.optString("appId").trim();
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    scanResult = "";
                }
                map.putString(IMantoBaseModule.RESULT, scanResult);
                map.putString("rawData", Base64.encodeToString(scanResult.getBytes(), Base64.DEFAULT));
                map.putString("scanType", scanType);
                callback.onSuccess(map);
            case Activity.RESULT_CANCELED:
                callback.onFailed(new Bundle());
            default:
                callback.onFailed(new Bundle());
        }
    }
}
