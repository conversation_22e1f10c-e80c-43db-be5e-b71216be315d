package com.jd.manto.open;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jingdong.manto.MantoActivityResult;
import com.jingdong.manto.jsapi.refact.media.JsApiPreviewImage;

import java.util.ArrayList;

/**
 * Created by kris on 2020/3/10.
 */

public class JsApiPreviewImageNew extends JsApiPreviewImage {
    @Override
    public void startPreview(final @NonNull MantoActivityResult activity,
                             ArrayList<String> urls, int currPos) {
//        UnAlbumStartUtils.startPicturePreview(activity, urls, currPos);
        try {
            GalleryProvider mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
            mGalleryProvider.preview(activity.getActivity(), urls, currPos);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
