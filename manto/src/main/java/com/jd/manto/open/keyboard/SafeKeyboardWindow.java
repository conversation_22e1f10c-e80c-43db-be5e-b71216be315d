package com.jd.manto.open.keyboard;


import android.app.Activity;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

public class SafeKeyboardWindow {
    private ViewGroup window;
    private Builder builder;
    boolean isShowing = false;
    private SafeKeyboardWindow(){

    }

    /**
     * 通过builder 创建容器，使用PopupWindow
     * @param builder
     */
    private SafeKeyboardWindow(Builder builder){
        if (builder == null) {
            return;
        }
        if (builder.view == null) {
            return;
        }
        if (builder.activity == null) {
            return;
        }
        this.builder = builder;
        FrameLayout view = builder.activity.findViewById(android.R.id.content);
        if (view == null) {
            return;
        }
        window = (ViewGroup)view.getChildAt(0);
    }

    /**
     * 安全键盘容器弹出展示，默认靠底居中
     */
    public void show(){
        if (window != null && !isShowing) {
            window.post(new Runnable() {
                @Override
                public void run() {
                    int width = ViewGroup.LayoutParams.MATCH_PARENT;
                    if (builder.width > 0) {
                        width = builder.width;
                    }
                    int height = ViewGroup.LayoutParams.WRAP_CONTENT;
                    if (builder.width > 0) {
                        height = builder.height;
                    }

                    FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                            width, height);
                    params.gravity = Gravity.BOTTOM;
                    params.bottomMargin = builder.y;
                    params.leftMargin = builder.x;
                    window.addView(builder.view, params);
                    isShowing = true;
                }
            });
        }
    }


    /**
     * 关闭安全键盘
     */
    public void close(){
        if (window != null) {
            window.post(new Runnable() {
                @Override
                public void run() {
                    isShowing = false;
                    window.removeView(builder.view);
                }
            });
        }
    }


    /**
     * 安全键盘容器builder
     */
    public static class Builder {
        Activity activity;
        int x;
        int y;
        int width;
        int height;
        View view;

        public Builder setActivity(Activity activity) {
            this.activity = activity;
            return this;
        }

        public Builder setX(int x) {
            this.x = x;
            return this;
        }

        public Builder setY(int y) {
            this.y = y;
            return this;
        }

        public Builder setWidth(int width) {
            this.width = width;
            return this;
        }

        public Builder setHeight(int height) {
            this.height = height;
            return this;
        }

        public Builder registerCustomView(View view){
            this.view = view;
            return this;
        }

        public SafeKeyboardWindow build() {
            return new SafeKeyboardWindow(this);
        }
    }

}
