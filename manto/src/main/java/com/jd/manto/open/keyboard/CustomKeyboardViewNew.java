package com.jd.manto.open.keyboard;

import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.text.InputType;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.jd.manto.R;
import com.jingdong.manto.utils.MantoLog;

/**
 * 自定义的一个数字键盘，支持取消，确定，输入框
 */
public class CustomKeyboardViewNew extends LinearLayout implements ICustemKeyboardView{

    public static final int X_MODE_CHARACTER = 1;
    public static final int X_MODE_DOT = 2;
    public static final int X_MODE_NONE = 0;
    private Context mContext;
    private Bundle mData;
    private EditText mInputEditText;
    private ICustomKeyboardInterface mListener;
    private Button mKey0;
    private Button mKey1;
    private Button mKey2;
    private Button mKey3;
    private Button mKey4;
    private Button mKey5;
    private Button mKey6;
    private Button mKey7;
    private Button mKey8;
    private Button mKey9;
    private View mKeyD;
    private Button mKeyX;
    private int mXMode = 0;

    public CustomKeyboardViewNew(Context context, Bundle data) {
        super(context);
        init(context);
        MantoLog.d("CustomKeyboardView", data);
    }

    public CustomKeyboardViewNew(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        init(context);
    }

    private void init(Context context) {
        this.mContext = context.getApplicationContext();
        View inflate = LayoutInflater.from(context).inflate(R.layout.manto_custom_keyboard_new, this, true);
        this.mKey1 =  inflate.findViewById(R.id.custom_keyboard_1);
        this.mKey2 =  inflate.findViewById(R.id.custom_keyboard_2);
        this.mKey3 =  inflate.findViewById(R.id.custom_keyboard_3);
        this.mKey4 =  inflate.findViewById(R.id.custom_keyboard_4);
        this.mKey5 =  inflate.findViewById(R.id.custom_keyboard_5);
        this.mKey6 =  inflate.findViewById(R.id.custom_keyboard_6);
        this.mKey7 =  inflate.findViewById(R.id.custom_keyboard_7);
        this.mKey8 =  inflate.findViewById(R.id.custom_keyboard_8);
        this.mKey9 =  inflate.findViewById(R.id.custom_keyboard_9);
        this.mKeyX =  inflate.findViewById(R.id.custom_keyboard_x);
        this.mKey0 =  inflate.findViewById(R.id.custom_keyboard_0);
        this.mKeyD =  inflate.findViewById(R.id.custom_keyboard_d);
        this.mInputEditText = inflate.findViewById(R.id.manto_input_editText);
        mInputEditText.setInputType(InputType.TYPE_NULL);
        OnClickListener clickListener = new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (CustomKeyboardViewNew.this.mInputEditText != null) {
                    int i;
                    if (view.getId() == R.id.custom_keyboard_1) {
                        i = 8;
                    } else if (view.getId() == R.id.custom_keyboard_2) {
                        i = 9;
                    } else if (view.getId() == R.id.custom_keyboard_3) {
                        i = 10;
                    } else if (view.getId() == R.id.custom_keyboard_4) {
                        i = 11;
                    } else if (view.getId() == R.id.custom_keyboard_5) {
                        i = 12;
                    } else if (view.getId() == R.id.custom_keyboard_6) {
                        i = 13;
                    } else if (view.getId() == R.id.custom_keyboard_7) {
                        i = 14;
                    } else if (view.getId() == R.id.custom_keyboard_8) {
                        i = 15;
                    } else if (view.getId() == R.id.custom_keyboard_9) {
                        i = 16;
                    } else if (view.getId() == R.id.custom_keyboard_0) {
                        i = 7;
                    } else if (view.getId() == R.id.custom_keyboard_d) {
                        i = 67;
                    } else {
                        i = 0;
                    }
                    CustomKeyboardViewNew.this.mInputEditText.dispatchKeyEvent(new KeyEvent(0, i));
                    CustomKeyboardViewNew.this.mInputEditText.dispatchKeyEvent(new KeyEvent(1, i));
                }
            }
        };
        this.mKey1.setOnClickListener(clickListener);
        this.mKey2.setOnClickListener(clickListener);
        this.mKey3.setOnClickListener(clickListener);
        this.mKey4.setOnClickListener(clickListener);
        this.mKey5.setOnClickListener(clickListener);
        this.mKey6.setOnClickListener(clickListener);
        this.mKey7.setOnClickListener(clickListener);
        this.mKey8.setOnClickListener(clickListener);
        this.mKey9.setOnClickListener(clickListener);
        this.mKeyX.setOnClickListener(clickListener);
        this.mKey0.setOnClickListener(clickListener);
        this.mKeyD.setOnClickListener(clickListener);

        if (Build.VERSION.SDK_INT >= 14) {
            this.mKey1.setContentDescription("1");
            this.mKey2.setContentDescription("2");
            this.mKey3.setContentDescription("3");
            this.mKey4.setContentDescription("4");
            this.mKey5.setContentDescription("5");
            this.mKey6.setContentDescription("6");
            this.mKey7.setContentDescription("7");
            this.mKey8.setContentDescription("8");
            this.mKey9.setContentDescription("9");
            this.mKey0.setContentDescription("0");
            this.mKeyX.setContentDescription("字母X");
            this.mKeyD.setContentDescription("删除");
        }

        setInputEditText(mInputEditText);

        findViewById(R.id.manto_intput_keyboard_close).setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    Bundle bundle = new Bundle();
                    bundle.putString("value", mInputEditText.getText().toString());
                    mListener.onComplete(bundle);
                }
            }
        });

        findViewById(R.id.manto_intput_keyboard_cancel).setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onCancel();
                }
            }
        });
    }

    @Override
    protected final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        setInputEditText(null);
    }

    @Override
    public void registerListener(ICustomKeyboardInterface listener) {
        mListener = listener;
    }

    public void setInputEditText(EditText editText) {
        if (editText != null) {
            this.mInputEditText = editText;
            int imeOptions = this.mInputEditText.getImeOptions();
            CharSequence imeActionLabel = this.mInputEditText.getImeActionLabel();
            if (!TextUtils.isEmpty(imeActionLabel)) {
                this.mKeyX.setText(imeActionLabel);
            }
            switch (imeOptions) {
                case 1:
                    this.mXMode = 0;
                    if (TextUtils.isEmpty(imeActionLabel)) {
                        this.mKeyX.setText("");
                        return;
                    }
                    return;
                default:
                    return;
            }
        }
    }
}
