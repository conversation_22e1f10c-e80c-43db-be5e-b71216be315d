package com.jd.manto.open;

import android.graphics.Bitmap;
import android.os.Bundle;

import com.jd.manto.R;
import com.jd.oa.fragment.utils.CaptureWebViewUtil;
import com.jd.oa.utils.ToastUtils;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;
import com.jingdong.manto.jsapi.refact.JSApiShareAppMessage;
import com.jingdong.manto.sdk.api.IShareManager;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by kris on 2020/2/13.
 * <p>
 * 注意区分type(小程序类型)和shareType（分享类型）
 */

public class JSApiMessage extends JSApiShareAppMessage {

    @Override
    public void shareMantoApp(final MantoCore core, final Bundle bundle, final MantoResultCallBack callback) {
        String linkInfo = bundle.getString("url");
        String title = bundle.getString("title");
        String iconUrl = bundle.getString("imageUrl");
//        String defaultLink = bundle.getString("defaultLink");
        String desc = bundle.getString("desc");
////        String flag = bundle.getString("flag");
//        String path = bundle.getString("path");
//        String channel = bundle.getString("channel");
//        String mpId = bundle.getString("mpId");
//        String mpPath = bundle.getString("mpPath");
        String onlineImageUrl = bundle.getString("onlineImageUrl");

        final JSONObject jsonObject = new JSONObject();
        try {
            if (desc != null) {
                jsonObject.put("summary", desc);
                jsonObject.put("content", desc);
            }
            if (title != null) {
                jsonObject.put("title", title);
            }
            if (iconUrl != null) {
                jsonObject.put("icon", iconUrl);
            }
            if (linkInfo != null) {
                JSONObject linkJson = null;
                try {
                    linkJson = new JSONObject(linkInfo);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    if (linkJson == null) {
                        linkJson = new JSONObject();
                    }
                    linkJson.put("defaultUrl", linkInfo);
                    jsonObject.put("linkInfo", linkJson);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            Bitmap bitmap;
            if (onlineImageUrl != null) {
                jsonObject.put("image", onlineImageUrl);
                BitmapCache.reset();
            } else {
                bitmap = CaptureWebViewUtil.getViewBitmapNoBg(core.getActivity().getWindow().getDecorView());
                BitmapCache.saveBitmap2file(bitmap);
            }
            ShareProxyActivity.startActivity(core.getActivity(), jsonObject.toString(), new IShareManager.ShareCallback() {
                @Override
                public void onShareSuccess(Bundle bundle) {
                    callback.onSuccess(null);
                    ToastUtils.showToast(R.string.libshare_share_success);
                }

                @Override
                public void onShareFailed(Bundle bundle) {
                    callback.onFailed(null);
                    ToastUtils.showToast(R.string.libshare_share_fail);
                }

                @Override
                public void onShareCancel() {
                    callback.onCancel(null);
                    ToastUtils.showToast(R.string.libshare_cancel_share);
                }

                @Override
                public void onShareClickChannel(Bundle bundle) {
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
