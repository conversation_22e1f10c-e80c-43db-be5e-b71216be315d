package com.jd.manto.open;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;

import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;
import com.jingdong.manto.jsapi.refact.JSApiShareAppMessage;

public class JSApiShareMessage extends JSApiShareAppMessage {
    @Override
    public void shareMantoApp(final MantoCore activity, final Bundle bundle, final MantoResultCallBack callback) {
        String url = bundle.getString("url");
        String title = bundle.getString("title");
        String imageUrl = bundle.getString("imageUrl");
        String flag = bundle.getString("flag");
        String desc = bundle.getString("desc");
        String path = bundle.getString("path");
        String channel = bundle.getString("channel");
        String mpId = bundle.getString("mpId");
        String mpPath = bundle.getString("mpPath");
        String defaultLink = bundle.getString("defaultLink");

        int type = bundle.getInt("shareType", -1);
        String linkUrl = defaultLink;
        boolean wsShare = false;

        if (imageUrl == null || flag == null) {
            Bundle backBundle = new Bundle();
            backBundle.putString("errMessage", "imageUrl is null");
            callback.onFailed(backBundle);
            return;
        }

        if (TextUtils.isEmpty(desc)) {
            desc = "";
        }

        if (TextUtils.isEmpty(title)) {
            title = "";
        }

        if (TextUtils.isEmpty(channel)) {
            channel = "Wxfriends,Wxmoments";
        }

        if (!TextUtils.isEmpty(url)) {
            linkUrl = url;
        }

        if ((type == 0 || type == 1 || type == 2) && !TextUtils.isEmpty(mpId) && !TextUtils.isEmpty(mpPath)) {
            wsShare = true;
        }

        final String finalChannel = channel;
        OpennessApi.shareSystem(activity.getActivity(), desc, title, linkUrl, imageUrl, null, new AbsOpennessCallback() {
            @Override
            public void done(int code, String msg) {
                Bundle bundle = new Bundle();
                bundle.putString("shareChannel", finalChannel);
                callback.onSuccess(bundle);
            }

            @Override
            public void fail(int code, String msg) {
                Bundle bundle = new Bundle();
                bundle.putString("shareErrMsg", msg);
                callback.onFailed(bundle);
            }

            @Override
            public void cancel(int code, String msg) {
                callback.onCancel(new Bundle());
                super.cancel(code, msg);
            }
        });
    }
}