package com.jd.manto.open;

import android.app.Activity;

import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;
import com.jingdong.manto.jsapi.refact.JsApiOpenDocument;

/**
 * Created by kris on 2020/2/11.
 */

public class JsApiOpenDocumentNew extends JsApiOpenDocument {
    @Override
    public boolean openInner() {
        return true;
    }

    @Override
    public int openDocument(Activity ac, String filePath, String ext, String token, MantoResultCallBack callback) {
        return -1;
    }

    @Override
    public int openWithThirdApp(Activity activity, String filePath, String extension, MantoResultCallBack callback) {
        return super.openWithThirdApp(activity, filePath, extension, callback);
    }
}
