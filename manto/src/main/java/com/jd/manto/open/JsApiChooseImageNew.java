package com.jd.manto.open;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.melib.utils.PermissionUtils;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.CollectionUtil;
import com.jingdong.manto.MantoActivityResult;
import com.jingdong.manto.jsapi.refact.media.JsApiChooseImage;
import com.jingdong.manto.ui.MantoActivity;
import com.jingdong.manto.ui.MantoTransportActivity;

import java.util.ArrayList;

/**
 * Created by kris on 2020/3/10.
 */

public class JsApiChooseImageNew extends JsApiChooseImage {
    private static final String EXTRA_RESULT_SELECTION_PATH = "extra_result_selection_path";
    private static final String SELECT_MEDIA_LIST = "select_media_list";

    /**
     * @param activity    activity
     * @param intent      参数如下：
     *                    1.manto_compressed   boolean，是否压缩
     *                    2.manto_media_type   int，    固定1
     *                    3.manto_count        int，    可选择的图片数量\
     *                    4.manto_show_camera  boolean, 相册第一格是否选择拍照
     * @param requestCode requestCode
     */
    @Override
    public void startGallery(MantoActivityResult activity, Intent intent, int requestCode) {
        int selectCount = intent.getIntExtra("manto_count", 1);
        int sourceType = intent.getIntExtra("manto_media_type", 1);
        boolean withCamera = intent.getBooleanExtra("manto_show_camera", true);
        chooseImage(activity.getActivity(), requestCode, selectCount, sourceType, withCamera);
    }

    public static void chooseImage(@NonNull final Activity activity, final int requestCode, final int maxNum, final int type, final boolean withCamera) {
        MantoTransportActivity.start(activity, new MantoTransportActivity.OnCreateActivityListener() {
            @Override
            public void onCreate(final Activity activity) {
                PermissionUtils.checkOnePermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE, activity.getString(com.jme.common.R.string.me_storage_permission), new Runnable() {
                    @Override
                    public void run() {
                        try {
                            GalleryProvider mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                            mGalleryProvider.openGallery(activity, maxNum, requestCode);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        }, new MantoActivityResult.ResultCallback() {
            @Override
            public void onActivityResult(int reqCode, int resultCode, Intent data) {
                if (data == null) {
                    return;
                }
                final ArrayList<String> pList = data.getStringArrayListExtra(EXTRA_RESULT_SELECTION_PATH);
                if (CollectionUtil.notNullOrEmpty(pList)) {
                    if (activity instanceof FragmentActivity) {
                        DialogUtils.showLoadDialog((FragmentActivity) activity, activity.getResources().getString(com.jme.common.R.string.me_rn_compress_image));
                    }
                    if (activity instanceof FragmentActivity) {
                        DialogUtils.removeLoadDialog((FragmentActivity) activity);
                    }
                    if (activity instanceof MantoActivity && !activity.isFinishing() && ((MantoActivity) activity).resultCallback != null) {
                        data = new Intent();
                        data.putStringArrayListExtra(SELECT_MEDIA_LIST, pList);
                        ((MantoActivity) activity).resultCallback.onActivityResult(requestCode, Activity.RESULT_OK, data);
                    }
                }
            }
        });
    }

}
