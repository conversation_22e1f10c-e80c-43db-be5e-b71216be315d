package com.jd.manto.open;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.ResultReceiver;

import com.jd.oa.BaseActivity;
import com.jd.oa.basic.ShareBasic;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.module.MEShare;
import com.jingdong.manto.sdk.api.IShareManager;
import com.jingdong.manto.sdk.thread.MantoHandler;

import org.json.JSONObject;

public class ShareProxyActivity extends BaseActivity {
    private static final String KEY_EXTRA_SHARE_INFO = "man_to_extra_share_info";
    private static final String KEY_EXTRA_SHARE_RESULT_RECEIVER = "man_to_extra_share_result_receiver";

    private static final int SHARE_SUCCESS = 1;
    private static final int SHARE_FAIL = 2;
    private static final int SHARE_CANCEL = 3;
    private static final int SHARE_CLICK_CHANNEL = 4;

    private ResultReceiver resultReceiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(0, 0);
        if (getIntent() == null || getIntent().getExtras() == null) {
            finishActivity();
            return;
        }
        String shareInfo = getIntent().getStringExtra(KEY_EXTRA_SHARE_INFO);
        if (shareInfo == null) {
            finishActivity();
            return;
        }
        resultReceiver = getIntent().getParcelableExtra(KEY_EXTRA_SHARE_RESULT_RECEIVER);
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(shareInfo);
            Bitmap bitmap = BitmapCache.getBitmapFromFile();
            ShareBasic.shareToCard(jsonObject, bitmap, this, new DynamicCallback() {
                @Override
                public void call(Intent data, int resultCode) {
                    if (MEShare.SUCCESS == resultCode) {
                        Bundle bundle = new Bundle();
                        sendResult(SHARE_SUCCESS, bundle);
                        finishActivity();
                    } else if (MEShare.CANCEL == resultCode) {
                        sendResult(SHARE_CANCEL, new Bundle());
                        finishActivity();
                    } else {
                        Bundle bundle = new Bundle();
                        sendResult(SHARE_FAIL, bundle);
                        finishActivity();
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        //结果会走callback Listener，onActivityResult不再需要处理结果
        if (requestCode != 1215) {
            super.onActivityResult(requestCode, resultCode, data);
            finishActivity();
            return;
        }
        finishActivity();
    }

    private void sendResult(int statusCode, Bundle bundle) {
        if (resultReceiver != null) {
            resultReceiver.send(statusCode, bundle);
        }
    }

    private static class ShareResultReceiver extends ResultReceiver {
        final IShareManager.ShareCallback shareCallback;

        ShareResultReceiver(Handler handler, IShareManager.ShareCallback callBack) {
            super(handler);
            shareCallback = callBack;
        }

        @Override
        protected final void onReceiveResult(int resultCode, final Bundle bundle) {
            if (shareCallback != null) {
                if (resultCode == SHARE_SUCCESS) {
                    shareCallback.onShareSuccess(bundle);
                } else if (resultCode == SHARE_CLICK_CHANNEL) {
                    shareCallback.onShareClickChannel(bundle);
                } else if (resultCode == SHARE_FAIL) {
                    shareCallback.onShareFailed(bundle);
                } else if (resultCode == SHARE_CANCEL) {
                    shareCallback.onShareCancel();
                } else {
                    shareCallback.onShareCancel();
                }
            }
        }
    }

    private void finishActivity() {
        finish();
    }

    public static void startActivity(Context context, String info, final IShareManager.ShareCallback shareCallback) {
        if (info == null) {
            return;
        }
        Intent intent = new Intent(context, ShareProxyActivity.class);
        intent.putExtra(KEY_EXTRA_SHARE_RESULT_RECEIVER,
                new ShareResultReceiver(MantoHandler.fetchFreeHandler(Looper.getMainLooper()), shareCallback));
        intent.putExtra(KEY_EXTRA_SHARE_INFO, info);
        context.startActivity(intent);
    }
}
