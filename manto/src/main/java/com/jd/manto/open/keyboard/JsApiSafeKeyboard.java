package com.jd.manto.open.keyboard;

import android.app.Activity;
import android.os.Bundle;

import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;
import com.jingdong.manto.jsapi.openmodule.MantoLifecycleLisener;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;

import org.json.JSONObject;

import java.util.List;


/**
 * 自定义安全键盘jsapi
 */
public class JsApiSafeKeyboard extends AbstractMantoModule {
    private static final String METHOD = "requestSafeKeyboard";
    private static final String MODULE = "safeKeyboard";
    private SafeKeyboardWindow window;
    private MantoResultCallBack callBack;

    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod(METHOD,10009, IN_PROCESS_TYPE));
    }

    @Override
    public Bundle initData(String s, MantoCore activity, JSONObject jsonObject) {
        Bundle bundle = new Bundle();
        boolean show = jsonObject.optBoolean("show");
        String data = jsonObject.optString("data", "");
        bundle.putString("data", data);
        bundle.putBoolean("show",show);
        return bundle;
    }

    @Override
    public void handleMethod(String method, MantoCore activity, Bundle data, final MantoResultCallBack callback) {
        this.callBack = callBack;

        if (window != null) {
            window.close();
        }

        //初始化自定义view，支持传参
        final ICustemKeyboardView view = new CustomKeyboardViewNew(activity.getActivity(), data);
        //初始化安全键盘容器，设置参数x、y、width、height，自定义view
        window = new SafeKeyboardWindow.Builder().setActivity(activity.getActivity())
                .setX(0)   //容器水平相对偏移量 默认底部左下
                .setY(0)   //容器垂直相对偏移量 默认底部左下
                .setWidth(0) //容器宽度默认MATCH_PARENT，可自设置
                .setHeight(0) //容器高度默认WRAP_CONTENT，可自设置
                .registerCustomView((CustomKeyboardViewNew)view)
                .build();

        //构造接口，设置回调
        ICustomKeyboardInterface iCustomKeyboardInterface = new ICustomKeyboardInterface() {
            @Override
            public void onComplete(Bundle bundle) {
                callback.onSuccess(bundle);
                if (window != null) {
                    window.close();
                }
            }

            @Override
            public void onCancel() {
                callback.onFailed(null);
                if (window != null) {
                    window.close();
                }
            }
        };
        //view 注入回调接口
        view.registerListener(iCustomKeyboardInterface);

        //弹出容器展示
        window.show();
    }

    @Override
    public String getModuleName() {
        return MODULE;
    }

    @Override
    public MantoLifecycleLisener addLifecycleLisener(String method, Bundle bundle) {
        return new MantoLifecycleLisener() {
            @Override
            public void onReady() {

            }

            @Override
            public void onDestroy() {
                if (window != null) {
                    window.close();
                }
            }

            @Override
            public boolean onRemove() {
                if (window != null) {
                    if (callBack != null) {
                        callBack.onFailed(null);
                    }
                    window.close();
                    window = null;
                    return true;
                }
                return false;
            }

            @Override
            public void onPause() {

            }

            @Override
            public void onBackground() {

            }

            @Override
            public void onForeground() {

            }
        };
    }
}
