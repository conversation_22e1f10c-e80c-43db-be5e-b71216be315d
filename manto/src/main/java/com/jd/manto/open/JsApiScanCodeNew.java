package com.jd.manto.open;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.scanner.ScanActivity;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;
import com.jingdong.manto.jsapi.refact.JsApiScanCode;

import static com.jd.oa.router.DeepLink.ACTIVITY_URI_Capture;


/**
 * Created by kris on 2020/1/17.
 */

public class JsApiScanCodeNew extends JsApiScanCode {

    @Override
    public void scan(Activity activity, Bundle data, MantoResultCallBack callback) {
        Intent intent = Router.build(ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
        if (intent == null) {
            intent = new Intent(AppBase.getAppContext(), ScanActivity.class);
        }
        intent.putExtras(data);
        activity.startActivityForResult(intent, JsApiScanCode.REQUEST_CODE);
    }
}
