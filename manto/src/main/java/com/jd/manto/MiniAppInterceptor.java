package com.jd.manto;

import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;

public class MiniAppInterceptor implements RouteInterceptor {
    private static final String TAG = "MiniAppInterceptor";

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            boolean dispose = MantoInitializer.parseMiniUri(chain.getRequest().getUri());
            if (dispose) {
                return chain.intercept();
            }
            return chain.process();
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }
}
