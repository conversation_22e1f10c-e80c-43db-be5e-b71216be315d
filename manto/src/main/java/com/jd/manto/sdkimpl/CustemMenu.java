package com.jd.manto.sdkimpl;

import android.content.Context;

import com.jingdong.manto.sdk.api.ICustomMenuInterface;

import java.util.ArrayList;

public class CustemMenu implements ICustomMenuInterface {
    @Override
    public ArrayList<CustomMenuData> getCustomMenus(Context context) {
        return null;
    }

    @Override
    public void onMenuClicked(Context context, CustomData customData, int i, String s) {

    }

    @Override
    public boolean disableShare() {
        return true;
    }

    @Override
    public boolean disableShortCut() {
        return true;
    }

    @Override
    public boolean disableAbout() {
        return false;
    }

    @Override
    public boolean disableDebugSwitch() {
        return false;
    }

    @Override
    public boolean disableToggleFavor() {
        return true;
    }

    @Override
    public boolean disableFeedBack() {
        return true;
    }

    @Override
    public boolean disablePerformanceSwitch() {
        return false;
    }

    @Override
    public boolean disableAboutShare() {
        return true;
    }

    @Override
    public boolean disableMPStore() {
        return false;
    }

    @Override
    public boolean disableSetting() {
        return false;
    }
}
