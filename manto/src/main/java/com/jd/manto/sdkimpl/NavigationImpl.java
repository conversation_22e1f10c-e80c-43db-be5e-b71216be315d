package com.jd.manto.sdkimpl;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.jd.manto.NavigateProxyActivity;
import com.jd.manto.R;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ToastUtils;
import com.jingdong.manto.sdk.api.INavigate;

import org.json.JSONObject;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by kris on 2020/3/11.
 */

public class NavigationImpl implements INavigate {
    public static final String REGEX = "openapp\\.jdmobile://virtual\\?params=([\\s\\S]+)";

    @Override
    public void navigateTo(Context context, String s) {
        // json 字符串 {"url":"openapp\\.jdmobil*****"}
        try {
            JSONObject params = new JSONObject(s);
            String url = params.optString("url").trim();
            if (TextUtils.isEmpty(url)) {
                return;
            }
            if (url.startsWith(DeepLink.JDME) || url.startsWith(DeepLink.OPEN + DeepLink.JDME)) {
                NavigateProxyActivity.startActivity(context, url);
                return;
            }
            Pattern pattern = Pattern.compile(REGEX);
            Matcher matcher = pattern.matcher(url);
            if (matcher.matches()) {
                String deepLink = matcher.group(0);
                Uri uri = Uri.parse(deepLink);
                String json = uri.getQueryParameter("params");
                params = new JSONObject(json);
                url = params.optString("url").trim();
                if (TextUtils.isEmpty(url)) {
                    return;
                }
                NavigateProxyActivity.startActivity(context, DeepLink.webUrl(url, 1));
            } else {
                ToastUtils.showToast(R.string.me_not_support);
            }
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showToast(R.string.me_not_support);
        }
    }
}