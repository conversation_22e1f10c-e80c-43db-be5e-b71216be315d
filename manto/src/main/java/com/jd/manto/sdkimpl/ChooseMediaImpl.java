package com.jd.manto.sdkimpl;

import android.app.Activity;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jingdong.manto.MantoActivityResult;
import com.jingdong.manto.sdk.api.AbsChooseMedia;
import com.yu.bundles.album.AlbumListener;
import com.yu.bundles.album.ConfigBuilder;
import com.yu.bundles.album.MaeAlbum;
import com.yu.bundles.album.utils.MimeType;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public final class ChooseMediaImpl extends AbsChooseMedia {

    public void onTakePhoto(MantoActivityResult activityResult, String fullFileName, final int requestCode) {
        MELogUtil.localD(MELogUtil.TAG_MINI, "ChooseMediaImpl onTakePhoto");
        // 使用默认实现
        super.onTakePhoto(activityResult, fullFileName, requestCode);
    }

    public void onRecordVideo(final MantoActivityResult activityResult, final Intent intent, final int requestCode) {
        MELogUtil.localD(MELogUtil.TAG_MINI, "ChooseMediaImpl onRecordVideo");
    }

    @Override
    public void onChooseImage(MantoActivityResult activityResult, Intent intent, int i) {
        MELogUtil.localD(MELogUtil.TAG_MINI, "ChooseMediaImpl onChooseImage");
        //     1.manto_compressed   boolean，是否压缩
//     2.manto_media_type   int，    固定1，表示图片
//     3.manto_count        int，    可选择的图片数量
//     4.manto_show_camera  boolean, 相册第一格是否选择拍照
//     5.manto_isClip       boolean, 是否裁剪图片
        onChooseMedia(activityResult, intent, i);
    }

    @Override
    public void onChooseVideo(MantoActivityResult activityResult, Intent intent, int i) {
        MELogUtil.localD(MELogUtil.TAG_MINI, "ChooseMediaImpl onChooseVideo");
    /*
                  1. manto_count:              int类型，       数量；选择视频，目前只有1个
*                 2. manto_show_camera:        boolean类型，   相册中第一格是否显示拍摄图标，默认true
*                 3. manto_video_time_max:     int类型，       视频长度，单位秒
*                 4. manto_record_path:        String类型，    录制视频文件名，可以忽然
         */
        onChooseMedia(activityResult, intent, i);
    }

    @Override
    public void onPreviewImages(@NonNull MantoActivityResult activityResult, ArrayList<String> urls, int currPos) {
        MELogUtil.localD(MELogUtil.TAG_MINI, "ChooseMediaImpl onPreviewImages");
    }

    @Override
    public void onChooseMedia(final MantoActivityResult activityResult, final Intent intent, final int i) {
        MELogUtil.localD(MELogUtil.TAG_MINI, "ChooseMediaImpl onChooseMedia");
        if (intent != null) {
            // Step1 : process params from intent
            // 1:选择图片，2：选择视频
            int chooseMediaType = intent.getIntExtra("manto_media_type", 1);
            // 2. 媒体选择个数
            int chooseMediaCount = intent.getIntExtra("manto_count", 1);

            boolean imageEnable = true;
            boolean videoEnable = false;
            if (chooseMediaType == 2) {
                imageEnable = false;
                videoEnable = true;
                chooseMediaCount = 1;
            }

            MELogUtil.localD(MELogUtil.TAG_MINI, "ChooseMediaImpl onChooseMedia params imageEnable = " + imageEnable + " videoEnable = " + videoEnable + " chooseMediaCount = " + chooseMediaCount);

            Set<MimeType> typeSet;
            ConfigBuilder.FILE_TYPE type;
            if (imageEnable && videoEnable) {
                typeSet = MimeType.ofImageAndVideo();
                type = ConfigBuilder.FILE_TYPE.IMAGE_AND_VIDEO;
            } else if (videoEnable) {
                typeSet = MimeType.ofVideo();
                type = ConfigBuilder.FILE_TYPE.VIDEO;
            } else {
                typeSet = MimeType.ofImageWithOutGif();
                type = ConfigBuilder.FILE_TYPE.IMAGE;
            }
            if (!imageEnable && !videoEnable) {
                type = ConfigBuilder.FILE_TYPE.IMAGE_AND_VIDEO;
            }

            MaeAlbum.from(activityResult.getActivity())
                    .setIsShowCapture(false)
                    .maxSize(chooseMediaCount)
                    .fileType(type)
                    .column(3)
                    .choose(typeSet)
                    .forResult(new AlbumListener() {
                        @Override
                        public void onSelected(List<String> ps) {
                            Intent resultIntent = new Intent();
                            resultIntent.putStringArrayListExtra("select_media_list", (ArrayList<String>) ps);  // ps 可以传空，表示未选择
                            activityResult.getResultCallback().onActivityResult(i, Activity.RESULT_OK, resultIntent);
                        }

                        @Override
                        public void onFull(List<String> ps, String p) {
                            Intent resultIntent = new Intent();
                            resultIntent.putStringArrayListExtra("select_media_list", (ArrayList<String>) ps);  // ps 可以传空，表示未选择
                            activityResult.getResultCallback().onActivityResult(i, Activity.RESULT_CANCELED, resultIntent);
                        }
                    });
        }
    }
}
