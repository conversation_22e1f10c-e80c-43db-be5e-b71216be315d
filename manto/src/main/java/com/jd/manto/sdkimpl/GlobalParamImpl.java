package com.jd.manto.sdkimpl;

import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;

import androidx.annotation.NonNull;

import com.jd.oa.network.NetInfoUtils;
import com.jd.oa.preference.PreferenceManager;
import com.jingdong.manto.sdk.api.IGlobalParam;

import java.math.BigInteger;
import java.net.InetAddress;
import java.util.Map;

/**
 * <AUTHOR>  2019-08-08
 **/
public class GlobalParamImpl implements IGlobalParam {
    @NonNull
    @Override
    public String getUUID(Context context) {
        return PreferenceManager.UserInfo.getUserName();
    }

    @Override
    public String getCartUUID(Context context) {
        return "cart-" + PreferenceManager.UserInfo.getUserName();
    }

    @NonNull
    @Override
    public String getIp(Context context) {
        try {
            final WifiManager mWifiManager;
            mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = mWifiManager.getConnectionInfo();
            byte[] ipAddressByteArray =
                    BigInteger.valueOf(wifiInfo.getIpAddress()).toByteArray();
            NetInfoUtils.reverseByteArray(ipAddressByteArray);
            InetAddress inetAddress = InetAddress.getByAddress(ipAddressByteArray);
            return inetAddress.getHostAddress();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "**************";
    }

    @NonNull
    @Override
    public String getRandomCartUUID(Context context) {
        return null;
    }

    @NonNull
    @Override
    public Map getEncryptUUID(Context context) {
        return null;
    }
}
