package com.jd.manto.sdkimpl;

import android.util.Log;

import com.jingdong.manto.sdk.api.IMantoLog;

public class LogImpl implements IMantoLog {

    @Override
    public void v(String tag, String msg) {
        try {
            Log.v(tag, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void v(String tag, Throwable throwable) {
        try {
            Log.v(tag, Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void v(String tag, String msg, Throwable throwable) {
        try {
            Log.v(tag, msg + "   " + Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void v(String tag, Object... objects) {
        try {
            String msg = "";
            for (Object o : objects) {
                msg = String.valueOf(o) + "  ";
            }
            Log.v(tag, msg.trim());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void d(String tag, String msg) {
        try {
            Log.d(tag, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void d(String tag, Throwable throwable) {
        try {
            Log.d(tag, Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void d(String tag, String msg, Throwable throwable) {
        try {
            Log.d(tag, msg + "   " + Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void d(String tag, Object... objects) {
        try {
            String msg = "";
            for (Object o : objects) {
                msg = String.valueOf(o) + "  ";
            }
            Log.d(tag, msg.trim());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void w(String tag, String msg) {
        try {
            Log.w(tag, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void w(String tag, Throwable throwable) {
        try {
            Log.w(tag, Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void w(String tag, String msg, Throwable throwable) {
        try {
            Log.d(tag, msg + "   " + Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void w(String tag, Object... objects) {
        try {
            String msg = "";
            for (Object o : objects) {
                msg = String.valueOf(o) + "  ";
            }
            Log.w(tag, msg.trim());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void e(String tag, String msg) {
        try {
            Log.e(tag, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void e(String tag, Throwable throwable) {
        try {
            Log.e(tag, Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void e(String tag, String msg, Throwable throwable) {
        try {
            Log.e(tag, msg + "   " + Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void e(String tag, Object... objects) {
        try {
            String msg = "";
            for (Object o : objects) {
                msg = String.valueOf(o) + "  ";
            }
            Log.e(tag, msg.trim());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void i(String tag, String msg) {
        try {
            Log.i(tag, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void i(String tag, Throwable throwable) {
        try {
            Log.i(tag, Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void i(String tag, String msg, Throwable throwable) {
        try {
            Log.i(tag, msg + "   " + Log.getStackTraceString(throwable));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void i(String tag, Object... objects) {
        try {
            String msg = "";
            for (Object o : objects) {
                msg = String.valueOf(o) + "  ";
            }
            Log.i(tag, msg.trim());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void json(String tag, String msg) {
        try {
            Log.d(tag, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
