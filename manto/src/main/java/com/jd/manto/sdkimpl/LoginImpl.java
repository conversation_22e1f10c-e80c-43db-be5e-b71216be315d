package com.jd.manto.sdkimpl;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.wjloginclient.ClientUtils;
import com.jingdong.manto.sdk.api.ILogin;

import jd.wjlogin_sdk.common.WJLoginHelper;
import jd.wjlogin_sdk.common.listener.OnDataCallback;
import jd.wjlogin_sdk.model.A4LoginInfo;
import jd.wjlogin_sdk.model.ErrorResult;
import jd.wjlogin_sdk.model.FailResult;

//登录接入参考：
//  1.安卓sdk指引文档https://cf.jd.com/pages/viewpage.action?pageId=150235936；
//  2.安卓sdk下载：https://cf.jd.com/pages/viewpage.action?pageId=164109531
//  3.登录及验证码接入流程：https://cf.jd.com/pages/viewpage.action?pageId=456125555

/**
 * 登录接口实现
 */
public class LoginImpl implements ILogin {

    private final WJLoginHelper helper = ClientUtils.getWJLoginHelper();

    /**
     * 通过统一登录接口判断
     */
    @Override
    public boolean hasLogin() {
//        System.out.println("LoginImpl------- helper.hasLogin()：" + helper.hasLogin()+"   helper.getPin()="+helper.getPin());
        return AppBase.iAppBase.isLogin() && !TextUtils.isEmpty(helper.getA2());
    }

    @Override
    public void doLogin(String processName, Bundle extra, final CallBack callBack) {
//        System.out.println("LoginImpl-------doLogin---1");
        login(callBack);
    }

    @Override
    public void doLogin(Activity activity, Bundle extra, CallBack callBack) {
//        System.out.println("LoginImpl-------doLogin---2");
        login(callBack);
    }

    @NonNull
    @Override
    public String getPin(Context context) {
//        return helper.getPin();
        return "";
    }

    @Override
    public String getA2(Context context) {
//        System.out.println("LoginImpl-------getA2：" + helper.getA2());
        return helper.getA2();
//        return a2;
//        return "";
    }

    // cookie 格式来自：http://color.jd.com/help/faq/pin
    @Override
    public String getCookie(Context context) {
//        System.out.println("LoginImpl-------getCookie：");
        String cookie = "";
//        if (!TextUtils.isEmpty(getPin(context))) {
//            try {
//                cookie = "pin=" + URLEncoder.encode(getPin(context), "UTF-8") + ";";
//            } catch (UnsupportedEncodingException e) {
//                e.printStackTrace();
//            }
//        }
//
//        if (!TextUtils.isEmpty(getA2(context))) {
//            cookie = cookie + "wskey=" + getA2(context);
//        }
        String meToken = TokenManager.getInstance().getAccessToken();
        if (!TextUtils.isEmpty(meToken)) {
            return "me_token=" + meToken;
        }
        return cookie;
    }

    @Override
    public void getWebCookie(final LoginInfoCallBack callBack) {
//        System.out.println("LoginImpl-------getWebCookie");
//        helper.sendGetA4(new OnDataCallback<A4LoginInfo>() {
//            @Override
//            public void onSuccess(A4LoginInfo a4LoginInfo) {
//                callBack.onSuccess(a4LoginInfo.getA4(), getPin(AppBase.getAppContext()));
//            }
//
//            @Override
//            public void onError(ErrorResult errorResult) {
//                callBack.onError(errorResult.getErrorCode(), errorResult.getErrorMsg());
//
//            }
//
//            @Override
//            public void onFail(FailResult failResult) {
//                callBack.onFailure(failResult.getReplyCode(), failResult.getMessage());
//            }
//        });
    }

    @Override
    public void clearWebCookie() {
//        System.out.println("LoginImpl-------clearWebCookie");
//        helper.clearA4();
    }


    /**
     * 注意如果needSyncWebCookies返回true,此方法则不用实现
     */
    @Override
    public void asyncWebCookies() {
        MantoWebLoginHelper.gentokenAsync();
    }

    /**
     * 注意如果您这里返回true,请务必完成syncWebCookies的实现
     */
    @Override
    public boolean needSyncWebCookies() {
        return true;
    }

    /**
     * 这里实现同步web cookie, 请注意调用webCookieCallBack的onSuccess或者onFail，否则会阻断webview组件的加载
     *
     * @param url               url
     * @param webCookieCallBack onSuccess回调里务必是正确的url，否则无法加载html页面
     */
    @Override
    public void syncWebCookies(String url, WebCookieCallBack webCookieCallBack) {
        MantoWebLoginHelper.syncWebCookie(url, webCookieCallBack);
    }


    public static final String PIN_TAG = "JdPinUtils";
    private void login(final CallBack callBack) {
        if (hasLogin()) {
            return;
        }
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                MELogUtil.localI(PIN_TAG, "getPin----login");
                MELogUtil.onlineI(PIN_TAG, "getPin----login");
                AppService appService = AppJoint.service(AppService.class);
                appService.getJDAccountCookie(com.jme.common.R.string.xcx_bind_pin, new Callback2<String>() {
                    @Override
                    public void onSuccess(String bean, String bean1) {
//                        ToastUtils.showToast("更新a2=" + bean + "  pin=" + bean1);
                        callBack.onSuccess();
                    }

                    @Override
                    public void onFail(String msg) {
//                        ToastUtils.showToast("更新A2失败");
                        callBack.onFailure();
                    }
                }, false);
            }
        }, 0);
    }
}
