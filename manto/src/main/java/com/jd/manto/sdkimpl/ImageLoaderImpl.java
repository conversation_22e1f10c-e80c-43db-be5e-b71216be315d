package com.jd.manto.sdkimpl;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.ImageView;

import com.jingdong.manto.sdk.api.IImageLoader;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.FailReason;
import com.nostra13.universalimageloader.core.listener.ImageLoadingListener;

/**
 * Created by zlj on 18-7-24.
 */

public class ImageLoaderImpl implements IImageLoader {

    @Override
    public void loadImage(final ImageView imageView, String url) {
        ImageLoader.getInstance().displayImage(url, imageView);
    }

    @Override
    public void loadImage(Context context, String url, final ImageLoaderCallback callback) {
        ImageLoader.getInstance().loadImage(url, new ImageLoadingListener() {
            @Override
            public void onLoadingStarted(String imageUri, View view) {

            }

            @Override
            public void onLoadingFailed(String imageUri, View view, FailReason failReason) {
                callback.onFail();
            }

            @Override
            public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage) {
                callback.onSuccess(loadedImage);
            }

            @Override
            public void onLoadingCancelled(String imageUri, View view) {
                callback.onFail();
            }
        });
    }

}
