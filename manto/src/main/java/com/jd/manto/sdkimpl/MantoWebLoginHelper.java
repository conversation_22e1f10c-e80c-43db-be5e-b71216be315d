package com.jd.manto.sdkimpl;

import com.jingdong.manto.sdk.api.ILogin;
import com.tencent.smtt.sdk.WebViewClient;

/**
 * 京东主站 webview登录态打通实现参考
 *
 * 参考文档 https://cf.jd.com/pages/viewpage.action?pageId=100096608， 主要思想是将加载目标jd h5 url通过客户端原生网络库访问genToken接口 得到token，然后用webview加载拼接token后的url。
 *
 * 由于主站是依赖于主站的基础库，mantodemo这里展示主站示例代码，您需按照您应用的h5登录态打通逻辑做此实现。
 *
 */
public class MantoWebLoginHelper {


//    private static final String DEFAULT_URL = "https%3a%2f%2fplogin.m.jd.com%2fjd-mlogin%2fstatic%2fhtml%2fappjmp_blank.html";

//    private static X5WebView x5WebView;

//    private static long gentokenTime;

//    private static boolean isGentokenFinished = false;

    public static void gentokenAsync(){
//        if (isGentokenFinished) {
//            MantoLog.d(TAG, "gentokenAsync has done");
//            return;
//        }
//
//        try {
//            queryBrowserUrl(new QueryUrlListener() {
//                @Override
//                public void onComplete(String url) {
//                    isGentokenFinished = true;
//                    loadUrl(url);
//                }
//
//                @Override
//                public void onError(HttpError error) {
//                    WebLoginHelper.onGentokenFail();
//                }
//            });
//        } catch (Exception e) {
//            WebLoginHelper.onGentokenFail();
//        }
    }

    private static void loadUrl(final String url){
//        Runnable r = new Runnable() {
//            @Override
//            public void run() {
//                synchronized(MantoWebLoginHelper.class) {
//                    try {
//                        if (x5WebView != null) {
//                            onDestroy();
//                        }
//                        x5WebView = new X5WebView(JdSdk.getInstance().getApplication());
//                        x5WebView.setWebViewClient(new DefaultWebViewClient());
//                        MantoLog.d(TAG, "loadUrl: " + url);
//                        if (x5WebView != null) {
//                            x5WebView.loadUrl(url);
//                        } else {
//                            WebLoginHelper.onGentokenFail();
//                        }
//                    } catch (Exception e) {
//                        MantoLog.d(TAG, e.getMessage());
//                        String expandLog = "";
//                        try {
//                            expandLog = " | Process: " + ProcessUtil.getProcessName(JdSdk.getInstance().getApplicationContext());
//                        } catch (Exception e1) {
//                            MantoLog.d(TAG, e1.getMessage());
//                        }
//                        expandLog += " | " + WebUtils.getWebDirList(JdSdk.getInstance().getApplication());
//                        WebLoginHelper.onGentokenFail();
//                    }
//                }
//            }
//        };
//        //加载url必须在主线程
//        if (Looper.getMainLooper() == Looper.myLooper()) {
//            r.run();
//        } else {
//            new Handler(Looper.getMainLooper()).post(r);
//        }
    }

//    private static void queryBrowserUrl(final QueryUrlListener listener) {

//        HttpGroupSetting httpGroupSetting = HttpGroupUtils.createNewSettings();
//        httpGroupSetting.setType(1000);
//        httpGroupSetting.setMyActivity((Activity) BaseFrameUtil.getInstance().getCurrentMyActivity());
//        HttpGroup httpGroup = HttpGroup.getHttpGroup(httpGroupSetting);
//        HttpSetting httpSetting = new HttpSetting();
//        httpSetting.setEffect(0);
//        httpSetting.setNotifyUser(false);
//        httpSetting.setHost(Configuration.getPortalHost());
//        httpSetting.setUseFastJsonParser(true);
//        httpSetting.setFunctionId("genToken");
//        httpSetting.setListener(new HttpGroup.OnCommonListener() {
//            double gentokenStart = 0;
//
//            @Override
//            public void onReady(HttpGroup.HttpSettingParams httpSettingParams) {
//                httpSettingParams.putJsonParam("to", DEFAULT_URL);
//                gentokenStart = System.currentTimeMillis() / 1000.0;
//            }
//
//            @Override
//            public void onError(HttpError error) {
//                gentokenTime = System.currentTimeMillis() - Math.round(gentokenStart * 1000);
//                if (null != listener){
//                    listener.onError(error);
//                }
//            }
//
//            @Override
//            public void onEnd(HttpResponse httpResponse) {
//                JDJSONObject jsonObject = httpResponse != null ? httpResponse.getFastJsonObject() : new JDJSONObject();
//                String token = jsonObject.getString("tokenKey");
//                if (null == token) {
//                    this.onError(null);
//                } else {
//                    String url = jsonObject.getString("url");
//                    if (null == url) {
//                        this.onError(null);
//                    } else {
//                        MantoLog.d(TAG, "fun:genToken onEnd() -->> token = " + token);
//                        MantoLog.d(TAG, "fun:genToken onEnd() -->> url = " + url);
//                        try {
//                            Map<String, String> params = new HashMap<>();
//                            params.put("to", DEFAULT_URL);
//                            params.put("tokenKey", token);
//                            String mergerUrl = HttpGroup.mergerUrlAndParams(url, params);
//                            MantoLog.d(TAG, "queryBrowserUrl() mergerUrl -->> " + mergerUrl);
//                            gentokenTime = System.currentTimeMillis() - Math.round(gentokenStart * 1000);
//                            if (null != listener) {
//                                listener.onComplete(mergerUrl);
//                            }
//                        } catch (Exception e) {
//                            MantoLog.e(TAG, e.getMessage(), e);
//                            this.onError(null);
//                        }
//                    }
//                }
//            }
//        });
//        httpGroup.add(httpSetting);
//    }

    private static synchronized void onDestroy() {
//        if (x5WebView != null) {
//            try {
//                x5WebView.stopLoading();
//                x5WebView.removeAllViews();
//                x5WebView.destroy();
//                x5WebView = null;
//            } catch (Exception e) {
//                MantoLog.d(TAG, e.getMessage());
//            }
//        }
    }

    static class DefaultWebViewClient extends WebViewClient {
//        boolean isError;
//        long startTime;
//        DefaultWebViewClient() {
//        }
//
//        @Override
//        public void onPageFinished(WebView webView, String s) {
//            super.onPageFinished(webView, s);
//            MantoLog.d(TAG,"onPageFinished:" + s);
//            if (isError){
//                return;
//            }
//            if (webView.getProgress() >= 100){
//                WebLoginHelper.onGentokenSuccess();
//                onDestroy();
//            }
//        }
//
//        @Override
//        public boolean shouldOverrideUrlLoading(WebView webView, String s) {
//            return false;
//        }
//
//        @Override
//        public void onReceivedError(WebView webView, int i, String s, String s1) {
//            super.onReceivedError(webView, i, s, s1);
//            MantoLog.d(TAG,"onReceiveonReceivedErrordError:" + s);
//            isError = true;
//            WebLoginHelper.onGentokenFail();
//            onDestroy();
//        }
//
//        @Override
//        public void onPageStarted(WebView webView, String url, Bitmap bitmap) {
//            super.onPageStarted(webView, url, bitmap);
//            MantoLog.d(TAG,"onPageStarted:" + url);
//            isError = false;
//            startTime = System.currentTimeMillis();
//            if ("about:blank".equals(url)) {
//                onDestroy();
//                return;
//            }
//        }
    }

//    private interface QueryUrlListener{
//        void onComplete(String url);
//
//        void onError(HttpError error);
//    }

    public static void refresh() {
//        if (MantoProcessUtil.isMantoProcess()) {
//            isGentokenFinished = false;
//            UserUtil.getWJLoginHelper().refreshLoginStatus();
//            gentokenAsync();
//        }
    }

    public static void syncWebCookie(final String url, final ILogin.WebCookieCallBack webCookieCallBack) {
        //TODO 此为示例代码
        webCookieCallBack.onSuccess(url);
//        HttpGroupSetting httpGroupSetting = HttpGroupUtils.createNewSettings();
//        httpGroupSetting.setType(1000);
//        httpGroupSetting.setMyActivity((Activity) BaseFrameUtil.getInstance().getCurrentMyActivity());
//        HttpGroup httpGroup = HttpGroup.getHttpGroup(httpGroupSetting);
//        HttpSetting httpSetting = new HttpSetting();
//        httpSetting.setEffect(0);
//        httpSetting.setNotifyUser(false);
//        httpSetting.setHost(Configuration.getPortalHost());
//        httpSetting.setUseFastJsonParser(true);
//        httpSetting.setFunctionId("genToken");
//        httpSetting.setListener(new HttpGroup.OnCommonListener() {
//
//            @Override
//            public void onReady(HttpGroup.HttpSettingParams httpSettingParams) {
//                httpSettingParams.putJsonParam("to", url);
//            }
//
//            @Override
//            public void onError(HttpError error) {
//                if (null != webCookieCallBack){
//                    webCookieCallBack.onFailure();
//                }
//            }
//
//            @Override
//            public void onEnd(HttpResponse httpResponse) {
//                JDJSONObject jsonObject = httpResponse != null ? httpResponse.getFastJsonObject() : new JDJSONObject();
//                String token = jsonObject.getString("tokenKey");
//                if (null == token) {
//                    webCookieCallBack.onFailure();
//                } else {
//                    String baseUrl = jsonObject.getString("url");
//                    if (null == baseUrl || jsonObject.containsKey("error_msg") || baseUrl.startsWith("https://h5.360buyimg.com/login/html/gentokenWarning.html")) {
//                        webCookieCallBack.onFailure();
//                    } else {
//                        MantoLog.d(TAG, "fun:genToken onEnd() -->> token = " + token);
//                        MantoLog.d(TAG, "fun:genToken onEnd() -->> url = " + baseUrl);
//                        try {
//                            Map<String, String> params = new HashMap<>();
//                            params.put("to", URLEncoder.encode(url));
//                            params.put("tokenKey", token);
//                            String mergerUrl = HttpGroup.mergerUrlAndParams(baseUrl, params);
//                            MantoLog.d(TAG, "queryBrowserUrl() mergerUrl -->> " + mergerUrl);
//                            if (null != webCookieCallBack) {
//                                webCookieCallBack.onSuccess(mergerUrl);
//                            }
//                        } catch (Exception e) {
//                            MantoLog.e(TAG, e.getMessage(), e);
//                            webCookieCallBack.onFailure();
//                        }
//                    }
//                }
//            }
//        });
//        httpGroup.add(httpSetting);
    }
}
