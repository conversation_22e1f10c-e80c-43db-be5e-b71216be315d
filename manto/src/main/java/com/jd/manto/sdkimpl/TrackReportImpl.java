package com.jd.manto.sdkimpl;

import android.content.Context;
import android.util.Log;

import com.jingdong.manto.sdk.api.ITrackReport;

import java.util.HashMap;
import java.util.Map;

public class TrackReportImpl implements ITrackReport {
//    @Override
//    public void sendCommonDataWithExt(Context context, String eventName, String eventId, String eventParam, String pageName, String pageId, HashMap<String, String> ext) {
//        Log.e("TrackReportImpl", String.format("eventName: %s, eventId: %s, eventParam: %s, pageName: %s, pageId: %s, ext: %s", eventName, eventId, eventParam, pageName, pageId, ext));
//    }
//
//    @Override
//    public void sendPagePv(Context context, Object page, String page_param, String page_id, HashMap<String, String> ext) {
//        Log.e("TrackReportImpl", String.format("page: %s, page_param: %s, page_id: %s, ext: %s", page, page_param, page_id, ext));
//
//    }

    @Override
    public void sendSysData(Context context, Map<String, String> map, Map<String, String> map1) {

    }

    @Override
    public void sendPagePv(Context context, Map<String, String> map, Map<String, String> map1) {

    }

    @Override
    public void sendClickData(Context context, Map<String, String> map, Map<String, String> map1) {

    }

    @Override
    public void sendExposureData(Context context, Map<String, String> map, Map<String, String> map1) {

    }

    @Override
    public void sendJDOrderInfo(Context context, Map<String, String> map, Map<String, String> map1) {

    }
}
