package com.jd.manto.sdkimpl;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;

import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jingdong.manto.sdk.api.IShareManager;

import java.util.HashMap;

public class ShareImpl implements IShareManager {
    @Override
    public void shareMantoApp(Activity activity, HashMap<String, Object> hashMap,
                              final ShareCallback shareCallback) {
        String url = (String) hashMap.get("url");
        String title = (String) hashMap.get("title");
        String imageUrl = (String) hashMap.get("imageUrl");
        String flag = (String) hashMap.get("flag");
        String desc = (String) hashMap.get("desc");
        String path = (String) hashMap.get("path");
        String channel = (String) hashMap.get("channel");
        String mpId = (String) hashMap.get("mpId");
        String mpPath = (String) hashMap.get("mpPath");
        String defaultLink = (String) hashMap.get("defaultLink");

        String linkUrl = defaultLink;

        if (imageUrl == null || flag == null) {
            Bundle backBundle = new Bundle();
            backBundle.putString("errMessage", "imageUrl is null");
            shareCallback.onShareFailed(backBundle);
            return;
        }

        if (TextUtils.isEmpty(desc)) {
            desc = "";
        }

        if (TextUtils.isEmpty(title)) {
            title = "";
        }

        if (TextUtils.isEmpty(channel)) {
            channel = "Wxfriends,Wxmoments";
        }

        if (!TextUtils.isEmpty(url)) {
            linkUrl = url;
        }
        final String finalChannel = channel;
        OpennessApi.shareSystem(activity, desc, title, linkUrl, imageUrl, null, new AbsOpennessCallback() {
            @Override
            public void done(int code, String msg) {
                Bundle bundle = new Bundle();
                bundle.putString("shareChannel", finalChannel);
                shareCallback.onShareSuccess(bundle);
            }

            @Override
            public void fail(int code, String msg) {
                Bundle bundle = new Bundle();
                bundle.putString("shareErrMsg", msg);
                shareCallback.onShareFailed(bundle);
            }

            @Override
            public void cancel(int code, String msg) {
                shareCallback.onShareCancel();
                super.cancel(code, msg);
            }
        });
    }
}
