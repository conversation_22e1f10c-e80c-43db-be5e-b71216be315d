package com.jd.manto;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.chenenyu.router.Router;
import com.jd.oa.BaseActivity;

public class NavigateProxyActivity extends BaseActivity {
    private static final String KEY_EXTRA_URL = "manto_extra_navigate_url";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(0, 0);
        if (getIntent() == null || getIntent().getExtras() == null) {
            finish();
            return;
        }
        String url = getIntent().getStringExtra(KEY_EXTRA_URL);
        if (url == null) {
            finish();
            return;
        }
        // 传递协议内容，调用宿主跳转方法，直达目标页
        Router.build(url).go(this);
        finish();
    }

    public static void startActivity(Context context, String url) {
        if (url == null) {
            return;
        }
        Intent intent = new Intent(context, NavigateProxyActivity.class);
        intent.putExtra(KEY_EXTRA_URL, url);
        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(intent);
    }
}