package com.jd.manto;

import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.jd.manto.hd.udp.JsApiUDPSocketNew;
import com.jd.manto.hd.wifi.JsApiWifiNew;
import com.jd.manto.jdext.code.JsApiGetJosAuthCode;
import com.jd.manto.jdext.pay.JsApiRequestPayment;
import com.jd.manto.jdext.phone.JsApiGetPhoneNumber;
import com.jd.manto.jdext.plus.JsApiGetPlusAuth;
import com.jd.manto.jdext.router.JsApiRouterToNative;
import com.jd.manto.jdext.uuid.JsApiGetUUIdSync;
import com.jd.manto.lbs.JsApiLocationNew;
import com.jd.manto.map.JsApiMapView;
import com.jd.manto.me.JsMeApp;
import com.jd.manto.me.JsMeChat;
import com.jd.manto.me.JsMeEvent;
import com.jd.manto.me.JsMeLogin;
import com.jd.manto.me.JsMeNet;
import com.jd.manto.me.JsMeSpecial;
import com.jd.manto.me.JsMeStorage;
import com.jd.manto.me.JsMeUser;
import com.jd.manto.open.JSAPIDemoModule;
import com.jd.manto.open.JSApiMessage;
import com.jd.manto.open.JSApiShareMessage;
import com.jd.manto.open.JsApiChooseImageNew;
import com.jd.manto.open.JsApiMakeVoIPCallNew;
import com.jd.manto.open.JsApiOpenDocumentNew;
import com.jd.manto.open.JsApiPreviewImageNew;
import com.jd.manto.open.JsApiPrivateAddContactNew;
import com.jd.manto.open.JsApiScanCodeNew;
import com.jd.manto.open.keyboard.JsApiSafeKeyboard;
import com.jd.manto.sdkimpl.ChooseMediaImpl;
import com.jd.manto.sdkimpl.CustemMenu;
import com.jd.manto.sdkimpl.GlobalParamImpl;
import com.jd.manto.sdkimpl.ImageLoaderImpl;
import com.jd.manto.sdkimpl.LogImpl;
import com.jd.manto.sdkimpl.LoginImpl;
import com.jd.manto.sdkimpl.MantoActionBarImpl;
import com.jd.manto.sdkimpl.NavigationImpl;
import com.jd.manto.sdkimpl.ShareImpl;
import com.jd.manto.sdkimpl.TrackReportImpl;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.MiniAppTmpPreference;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.WebViewUtils;
import com.jingdong.Manto;
import com.jingdong.manto.jsapi.refact.JSApiFetch;
import com.jingdong.manto.launch.LaunchParam;
import com.jingdong.manto.mainproc.IMainProcChannel;
import com.jingdong.manto.mainproc.MainProcMessage;
import com.jingdong.manto.sdk.api.AbsChooseMedia;
import com.jingdong.manto.sdk.api.IActionBar;
import com.jingdong.manto.sdk.api.ICustomMenuInterface;
import com.jingdong.manto.sdk.api.IGlobalParam;
import com.jingdong.manto.sdk.api.IImageLoader;
import com.jingdong.manto.sdk.api.ILogin;
import com.jingdong.manto.sdk.api.IMantoLog;
import com.jingdong.manto.sdk.api.INavigate;
import com.jingdong.manto.sdk.api.IPermission;
import com.jingdong.manto.sdk.api.IShareManager;
import com.jingdong.manto.sdk.api.ITrackReport;
import com.jingdong.manto.utils.MantoLog;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class MantoInitializer {
    /**
     * 初始方法，主进程与小程序进程都要调用此方法
     * 参数1：applicationContext
     * 参数2：debug 是否 debug
     */
    public static void init(final Context applicationContext, final Boolean debug) {
        // 注入小程序开放接口（以I开头）与实现
        Manto.register(IGlobalParam.class, GlobalParamImpl.class);
        Manto.register(ILogin.class, LoginImpl.class);
        Manto.register(IImageLoader.class, ImageLoaderImpl.class);
        Manto.register(ITrackReport.class, TrackReportImpl.class);
        Manto.register(IMantoLog.class, LogImpl.class);
        Manto.register(IPermission.class, PermissionImpl.class);
        Manto.register(INavigate.class, NavigationImpl.class);
        Manto.register(IShareManager.class, ShareImpl.class);
        Manto.register(IActionBar.class, MantoActionBarImpl.class);
        // 选择视频
        Manto.register(AbsChooseMedia.class, ChooseMediaImpl.class);

        // === 以下接口实现，后面文档会详细说明
//      Manto.register(IRequestPayment.class, MantoJDPayImpl.class);
        Manto.register(ICustomMenuInterface.class, CustemMenu.class);

        //读取配置文件
        ThirdPartyConfigModel.MiniAppConfigModel miniAppConfigModel
                = ThirdPartyConfigHelper.getInstance((Application) applicationContext).getMiniAppConfigModel();
        String partner = null,gatewayLoginType = null,gatewayClient = null,signAppId = null,signSecret = null,appKey = null;
        boolean jdEnv = true;
        if(miniAppConfigModel != null){
            partner = miniAppConfigModel.partner;
            gatewayLoginType = miniAppConfigModel.gatewayLoginType;
            gatewayClient = miniAppConfigModel.gatewayClient;
            signAppId = miniAppConfigModel.signAppId;
            signSecret = miniAppConfigModel.signSecret;
            appKey = miniAppConfigModel.appKey;
            jdEnv = miniAppConfigModel.jdEnv;
        }

        final Map<String, String> paramMap = new HashMap<>();
        //【宿主相关配置，必须】
        paramMap.put(Manto.Config.PARTNER, partner);                           // 合作伙伴标识，宿主英文名
        paramMap.put(Manto.Config.VERSION_NAME, DeviceUtil.getVersionName(applicationContext.getApplicationContext())); //App版本
        paramMap.put(Manto.Config.VERSION_CODE, "" + DeviceUtil.getLocalVersionCode(applicationContext.getApplicationContext()));//App版本号

        // 【登录相关配置，必须】
        // 京东内部，loginType 与 client 参数具体参考：http://color.jd.com/help/faq/pin
        paramMap.put(Manto.Config.GATEWAY_LOGIN_TYPE, gatewayLoginType);   // 金融为6，其他为4
        paramMap.put(Manto.Config.GATEWAY_CLIENT, gatewayClient); // 网关登录态标识，与宿主的登录态实现相关，请咨询网关进行配置
        paramMap.put(Manto.Config.SIGN_APP_ID, signAppId);
        paramMap.put(Manto.Config.SIGN_SECRET, signSecret);
        // 设置X5 License
        paramMap.put(Manto.Config.X5_AUTH_KEY, WebViewUtils.getX5License(applicationContext));

        // 默认热启动并且不重定向页面
        paramMap.put(Manto.Config.SWITCH_HOT_RELAUNCH, "1");

        // 【小程序域名相关配置，非必须】非京东环境时需配置参数HOST 与 BETA_HOST, 使用该地址进行小程序后端请求；
        //                          京东内App不需要配置此参数
      /*
      paramMap.put(Manto.Config.HOST, "https://beta-api.m.jd.com");			 // 正式环境
      paramMap.put(Manto.Config.BETA_HOST, "https://beta-api.m.jd.com"); // 测试环境
      */

        //【小程序引擎配置，非必须】SWITCH_SHOW_HISTORY (0: 关闭, 1: 开启) 是否可长按关闭按钮，展示最近浏览小程序记录，默认开
//        paramMap.put(Manto.Config.SWITCH_SHOW_HISTORY, "0");

        //【执行小程序初始化逻辑】
        /*
         * 参数1: MantoCallBack，用于获取上述配置的参数值
         * 参数2: appKey 宿主的 appKey，【具体值由产品提供】jd00e7c795ae0f4648(商城),jd9316b5fb0014c150，hostId2f65fbe0d450（预发布）
         * 参数3: debug 是否 debug 模式，宿主工程的 BuildConfig.Debug    debug为true是预发环境   debug为false是正式环境
         * 参数4: jdEnv 是否 jd 环境，京东内App，设置为true，其他设置 false
         */
        Manto.setRuntimeLevel(Manto.RUNTIME_TYPE.j2v8);
        Locale locale = new Locale("zh", "CN");
        ;
        if (!LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).toLowerCase().startsWith("zh")) {
            locale = new Locale("en", "US");
        }
        Manto.init(new Manto.MantoCallback() {
            @Override
            public Context getContext() {
//                System.out.println("LoginImpl-------applicationContext---" + applicationContext);
                return applicationContext;
            }

            @Override
            public String getValue(String key) {
                return paramMap.get(key);
            }
        }, appKey, debug, true, locale);

        //【注入定制jsApi】
        initOpenJsApi();

        //【登录登出广播】用于监听用户登录登出操作
        initLoginBroadcast(applicationContext);

        Manto.getMainProcChannel().registerMantoListener(new IMainProcChannel.MainProcListener() {
            @Override
            public void onMantoMessage(MainProcMessage message) {
                if (message.data != null) {
                    MantoLog.d("getMainProcChannel", "onMantoMessage " + message.messageName +
                            ", appId " + message.appId + ", data " + message.data.toString());
                    return;
                }
                MantoLog.d("getMainProcChannel", "onMantoMessage " + message.messageName +
                        ", appId " + message.appId + ", data as null");
            }

            @Override
            public void onMessage(Parcelable parcelable) {
                MantoLog.d("getMainProcChannel", "parcelable " + parcelable.toString());
            }
        });
    }

    /**
     * 注入定制的jsApi，具体如何定制，文档有详细说明
     */
    private static void initOpenJsApi() {
        Manto.addServiceJsApi(new JSApiFetch());
        Manto.addServiceJsApi(new JsApiScanCodeNew());
        Manto.addServiceJsApi(new JsApiOpenDocumentNew());
        Manto.addServiceJsApi(new JsApiMakeVoIPCallNew());
        Manto.addServiceJsApi(new JsApiPrivateAddContactNew());
        Manto.addServiceJsApi(new JSAPIDemoModule());

        Manto.addServiceJsApi(new JsApiChooseImageNew());
        Manto.addPageJsApi(new JsApiChooseImageNew());
        Manto.addWebViewJsApi(new JsApiChooseImageNew());

        Manto.addServiceJsApi(new JsApiPreviewImageNew());
        Manto.addWebViewJsApi(new JsApiPreviewImageNew());

        Manto.addServiceJsApi(new JsApiRequestPayment());   // 支付
        Manto.addServiceJsApi(new JsApiGetJosAuthCode());   // jos 授权码
        Manto.addServiceJsApi(new JsApiGetPhoneNumber());   // 手机号
        Manto.addPageJsApi(new JsApiGetPhoneNumber());
        Manto.addServiceJsApi(new JsApiGetPlusAuth());      // plus 会员信息
        Manto.addServiceJsApi(new JsApiRouterToNative());   // router 路由
        Manto.addServiceJsApi(new JsApiGetUUIdSync());  // uuid

        // 地图组件，使用腾讯地图
        Manto.addPageJsApi(new JsApiMapView());
        Manto.addServiceJsApi(new JsApiMapView());
        Manto.addServiceJsApi(new JsApiLocationNew());

        Manto.addServiceJsApi(new JSApiShareMessage());

        Manto.addServiceJsApi(new JsApiSafeKeyboard());
        Manto.addServiceJsApi(new JsApiWifiNew());
        Manto.addServiceJsApi(new JsApiUDPSocketNew());
        Manto.addServiceJsApi(new JsMeLogin());
        Manto.addServiceJsApi(new JsMeNet());
        Manto.addServiceJsApi(new JsMeSpecial());
        Manto.addServiceJsApi(new JsMeUser());
        Manto.addServiceJsApi(new JsMeStorage());
        Manto.addServiceJsApi(new JSApiMessage());
        Manto.addServiceJsApi(new JsMeChat());
        Manto.addServiceJsApi(new JsMeApp());
        Manto.addServiceJsApi(new JsMeEvent());
    }

    private static void initLoginBroadcast(Context ctx) {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(MantoRipper.ACTION_LOGIN);
        intentFilter.addAction(MantoRipper.ACTION_LOGOUT);
        ctx.registerReceiver(new MantoRipper(), intentFilter);
    }

    /**
     * @param appId
     * @param debugType
     * @param launchPath
     * @param extrasJson
     * @param pageAlias
     * @param scene
     * @param jmScene    为了不影响scene原来业务 add 7.9.10版本，小程序感知来源
     */
    public static void doStartMiniApp(String appId, String jmAppId, String debugType, String launchPath, String extrasJson, String pageAlias, String scene, String jmScene) {
        final LaunchParam launchParam = new LaunchParam();
//        launchParam.extrasJson = extrasJson;
        launchParam.debugType = debugType;
        launchParam.appId = appId;
        launchParam.launchPath = launchPath;
        launchParam.pageAlias = pageAlias;
        launchParam.scene = scene;
        try {
            if (TextUtils.isEmpty(jmScene)) {
                launchParam.extrasJson = extrasJson;
            } else {
                JSONObject extrasJsonObj = null;
                if (TextUtils.isEmpty(extrasJson)) {
                    extrasJsonObj = new JSONObject();
                } else {
                    extrasJsonObj = new JSONObject(extrasJson);
                }
                extrasJsonObj.put("jm_scene", jmScene);
                launchParam.extrasJson = extrasJsonObj.toString();
            }
        } catch (Exception e) {
            launchParam.extrasJson = extrasJson;
        }
        if (launchParam.debugType.length() == 0) {
            launchParam.debugType = "1";
        }
        if (launchParam.launchPath.length() == 0) {
            launchParam.launchPath = null;
        }
        if (launchParam.extrasJson.length() == 0) {
            launchParam.extrasJson = null;
        }
        if (launchParam.pageAlias.length() == 0) {
            launchParam.pageAlias = null;
        }
        if (launchParam.scene.length() == 0) {
            launchParam.scene = null;
        }
        String val = ConfigurationManager.get().getEntry("android.mini.usecache", "1");
        if("1".equals(val)){
            launchParam.useCache = true;
            // strictParamCompareWhenUseCache 通过参数控制是否严格校验启动参数，默认false。
            //（严格校验，比较appId、type、path、启动参数、pageAlias参数； 不严格校验，比较appId、type）
            launchParam.strictParamCompareWhenUseCache = true;
            if (MultiTaskManager.getInstance().hasItem(jmAppId)) {
                launchParam.cacheTime = JsMeSpecial.FLOAT_CACHE_TIME_LONG;
            } else {
                //默认给5分钟，添加悬浮窗时去更新这个时间
                launchParam.cacheTime = JsMeSpecial.FLOAT_CACHE_TIME_SHORT;
            }
        }
        Manto.launchMiniProgram(launchParam);
        HashMap<String, String> paramMap = new HashMap<>();
        if (!TextUtils.isEmpty(scene)) {
            paramMap.put("scene", scene);
        }
        if (!TextUtils.isEmpty(jmScene)) {
            paramMap.put("jm_scene", jmScene);
        }
        JDMAUtils.clickEvent("", JDMAConstants.Mobile_Event_Platform_Mini_Start, paramMap);
    }

    public static boolean parseMiniUri(@NonNull String uriStr) {
        try {
            final Uri uri = Uri.parse(uriStr);
            return parseMiniUri(uri);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static final String HOST = "jm";
    private static final String PATH1 = "/miniapp";

    public static boolean parseMiniUri(@NonNull Uri uri) {
        try {
            if (!uri.getScheme().contains(DeepLink.JDME_SCHEME)) {
                if (uri.getScheme().contains("jdmobile")) {
                    //埋点
                    scanJdma(ScanResultDispatcher.ScanHandleType.OPEN_JD_APP.getType(), uri.toString());

                    Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                    Activity activity = AppBase.getTopActivity();
                    if (activity != null) {
                        activity.startActivity(intent);
                    }
                    return true;
                }
                return false;
            }
            if (!HOST.equals(uri.getHost()) || !PATH1.startsWith(uri.getPath())) {
                return false;
            }
            //埋点
            scanJdma(ScanResultDispatcher.ScanHandleType.MINI_APP.getType(), uri.toString());

            final String params = uri.getQueryParameter("mparam");
            final JSONObject json = new JSONObject(params);
//            final String des = json.optString("des");
//            if ("jdmp".equals(des) || "vapp".equals(des)) {
            final String vappType = json.optString("vapptype", "1");
            final String appId = json.optString("appId");
            final String path = json.optString("path");
            final String extraJson = json.optString("param");
            final String pageAlias = json.optString("pageAlias");
            final String scene = json.optString("scene");
            final String jmScene = TextUtils.isEmpty(scene) ? "deeplink" : scene;
            NetWorkManagerAppCenter.getAppIdByMINI(null, new SimpleRequestCallback<String>() {
                @Override
                public void onSuccess(ResponseInfo<String> response) {
                    super.onSuccess(response);
                    try {
                        JSONObject jsonObject = new JSONObject(response.result);
                        String content = jsonObject.optString("content");
                        JSONObject jsonContent = new JSONObject(content);
                        JSONObject menuInfo = jsonContent.optJSONObject("menuInfo");
                        jsonContent.put("callbackInfo", jsonContent.optString("callBackInfo"));
                        MiniAppTmpPreference.getInstance().put(MiniAppTmpPreference.getMenuInfoKey(appId), menuInfo == null ? "{}" : menuInfo.toString());
                        MiniAppTmpPreference.getInstance().put(MiniAppTmpPreference.getAppInfoKey(appId), jsonContent.toString());
                        MiniAppTmpPreference.getInstance().putBool(MiniAppTmpPreference.getHasMultiTaskKey(), AppBase.isMultiTask());
                    } catch (Exception e) {
                        MELogUtil.localE(MELogUtil.TAG_MINI, "getAppIdByMINI Exception ", e);
                    }
                    doStartMiniApp(appId, null, vappType, path, extraJson.toString(), pageAlias, scene, jmScene);
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    MELogUtil.localE(MELogUtil.TAG_MINI, "getAppIdByMINI error " + info, exception);
                    doStartMiniApp(appId, null, vappType, path, extraJson.toString(), pageAlias, scene, jmScene);
                }
            }, appId);
            return true;
//            } else {
//                ToastUtils.showToast(R.string.mini_app_no_jd_app);
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

}
