package com.jd.manto.me;

import android.app.Activity;
import android.os.Bundle;

import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;

import org.json.JSONObject;

import java.util.List;

public class JsMeUser extends AbstractMantoModule {
    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("getMeUserInfo", ACROSS_PROCESS_SYNC_TYPE));
    }

    @Override
    public Bundle initData(String s, MantoCore activity, JSONObject jsonObject) {
        return new Bundle();
    }

    @Override
    public String getModuleName() {
        return "User";
    }

    @Override
    public Bundle handleMethodSync(String method, MantoCore activity, Bundle data) {
        String appId = data.getString(IMantoBaseModule.APP_ID_KEY);
        OpenEventTrackingUtil.trackEventManto(appId, method); //开放能力埋点
        //noinspection SwitchStatementWithTooFewBranches
        switch (method) {
            case "getMeUserInfo":
                Bundle result = new Bundle();
                result.putString("userName", PreferenceManager.UserInfo.getUserName());
                result.putString("userLDAP", PreferenceManager.UserInfo.getEmailAccount());
                data.putString(ERROR_CODE, ERROR_CODE_SUCCESS); // should contain error code
                data.putBundle(BUNDLE_REAL_RESULT, result);
                return data;
        }
        return super.handleMethodSync(method, activity, data);
    }

}
