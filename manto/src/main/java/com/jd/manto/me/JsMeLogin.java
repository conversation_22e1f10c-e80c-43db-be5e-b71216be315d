package com.jd.manto.me;

import static com.jd.manto.MiniAppCommon.*;

import android.app.ProgressDialog;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.jd.manto.R;
import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class JsMeLogin extends AbstractMantoModule {

    public static final String METHOD_REQUEST_ACCESS = "requestAccess";

    private final JsMeLoginKt jsMeLoginKt = new JsMeLoginKt();

    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("getAuthorizationCode", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("requestAuthCode", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod(METHOD_REQUEST_ACCESS, ACROSS_PROCESS_TYPE));
    }

    @Override
    public Bundle initData(String s, MantoCore activity, JSONObject jsonObject) {
        String meAppId = jsonObject.optString("meAppId");
        String meAppKey = jsonObject.optString("meAppKey");
        String appKey = jsonObject.optString("appKey");
        Bundle bundle = new Bundle();
        bundle.putString("meAppId", meAppId);
        bundle.putString("meAppKey", meAppKey);
        bundle.putString("appKey", appKey);
        if (METHOD_REQUEST_ACCESS.equals(s)) {
            JSONArray scopeList = jsonObject.optJSONArray("scopeList");
            if (scopeList != null && scopeList.length() > 0) {
                ArrayList<String> list = new ArrayList<>();
                for (int i = 0; i < scopeList.length(); i++) {
                    try {
                        list.add(scopeList.getString(i));
                    } catch (Exception e) {
                    }
                }
                bundle.putStringArrayList("scopeList", list);
            }
        }
        return bundle;
    }


    @Override
    public String getModuleName() {
        return "Login";
    }

    @Override
    public final void handleMethod(String method, MantoCore activity, Bundle data, final MantoResultCallBack callback) {
        final Bundle bundle = new Bundle();
        if (data == null) {
            reportMiniAppError(CODE_UNKNOWN_EXCEPTION, callback, bundle);
            return;
        }
        final String meAppId = data.getString("meAppId");
        final String appKey = data.getString("meAppKey");
        final String appKeyNew = data.getString("appKey");
        String appId = data.getString(IMantoBaseModule.APP_ID_KEY);
        OpenEventTrackingUtil.trackEventManto(appId, method); //开放能力埋点
        //noinspection SwitchStatementWithTooFewBranches
        switch (method) {
            case "getAuthorizationCode":
                if (appKey != null && appKey.length() > 0) {
                    NetWorkManager.getAuthorizationCode(null, new SimpleRequestCallback<String>(AppBase.getTopActivity(), false) {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            if (info == null) {
                                callback.onFailed(bundle);
                                return;
                            }
                            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram getAuthorizationCode success");
                            try {
                                JSONObject result = new JSONObject(info.result);
                                JSONObject jsonObject = result.optJSONObject("data");
                                if (jsonObject != null) {
                                    String openId = jsonObject.optString("code");
                                    if (openId.length() > 0) {
                                        bundle.putString("openId", openId);
                                        callback.onSuccess(bundle);
                                        return;
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            callback.onFailed(bundle);
                        }

                        @Override
                        public void onFailure(HttpException exception, String info) {
                            super.onFailure(exception, info);
                            callback.onFailed(bundle);
                        }
                    }, appKey);
                    return;
                }

                if (meAppId != null && meAppId.length() > 0) {
                    final ProgressDialog progressDialog = new ProgressDialog(AppBase.getTopActivity());
                    progressDialog.setMessage(AppBase.getAppContext().getString(R.string.me_loading));
                    AppInfoHelper.getAskInfo(AppBase.getTopActivity(), meAppId, "0", MyPlatform.sIsInner, AppInfoHelper.USE_FOR_APP_AUTHORIZE, new AskInfoResultListener() {
                        @Override
                        public void onResult(@NonNull AskInfoResult askInfoResult) {
                            progressDialog.dismiss();
                            if(askInfoResult.getSuccess()){
                                String json = askInfoResult.getSource();
                                try {
                                    JSONObject jsonObject = new JSONObject(json);
                                    String errorCode = jsonObject.optString("errorCode");
                                    if (!"0".equals(errorCode)) {
                                        callback.onFailed(bundle);
                                    }
                                    jsonObject = jsonObject.getJSONObject("content");
                                    String openId = jsonObject.optString("openId");
                                    bundle.putString("openId", openId);
                                    callback.onSuccess(bundle);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    callback.onFailed(bundle);
                                } finally {
                                    progressDialog.dismiss();
                                }
                            } else {
                                callback.onFailed(bundle);
                            }
                        }
                    });
                    return;
                }
                callback.onFailed(bundle);
                break;
            case "requestAuthCode":
                if (appKeyNew != null && !appKeyNew.isEmpty()) {
                    NetWorkManager.getAuthorizationCode(null, new SimpleRequestCallback<String>(AppBase.getTopActivity(), true) {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            if (info == null) {
                                reportMiniAppError(CODE_UNKNOWN_ERROR, callback, bundle);
                                return;
                            }
                            try {
                                JSONObject result = new JSONObject(info.result);
                                JSONObject jsonObject = result.optJSONObject("data");
                                if (jsonObject != null) {
                                    //服务器调用成功，调用结果成功
                                    String openId = jsonObject.optString("code", "");
                                    String expireIn = jsonObject.optString("expireIn", "");
                                    if (!openId.isEmpty()) {
                                        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram requestAuthCode success");
                                        bundle.putString("code", openId);
                                        bundle.putString("expireIn", expireIn);
                                        reportMiniAppSuccess(callback, bundle);
                                        return;
                                    } else {
                                        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram requestAuthCode success, but openId empty");
                                        reportMiniAppError(CODE_UNKNOWN_ERROR, callback, bundle);
                                    }
                                } else {
                                    //服务器调用成功，但是调用结果失败
                                    if (result.has("code")) {
                                        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram requestAuthCode completed, but api returned fail result with code: " + result.optInt("code"));
                                        reportMiniAppError(result.optInt("code"), callback, bundle);
                                        return;
                                    }
                                }
                            } catch (Exception e) {
                                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram requestAuthCode json parsing failed: " + e.getMessage());
                                e.printStackTrace();
                            }
                            reportMiniAppError(CODE_UNKNOWN_ERROR, callback, bundle);
                        }

                        @Override
                        public void onFailure(HttpException exception, String info) {
                            super.onFailure(exception, info);
                            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram requestAuthCode failed, message: " + info);
                            reportMiniAppError(exception.getExceptionCode(), callback, bundle);
                        }
                    }, appKeyNew);
                    return;
                }
                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram requestAuthCode invalid parameter");
                reportMiniAppError(CODE_INVALID_PARAMETER, callback, bundle);
                break;
            case METHOD_REQUEST_ACCESS:
                jsMeLoginKt.requestAccess(activity, data, callback);
                break;
        }
    }
}
