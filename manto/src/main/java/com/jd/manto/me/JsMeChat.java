package com.jd.manto.me;

import static com.jd.manto.MiniAppCommon.CODE_EMPTY_GROUP_ID;
import static com.jd.manto.MiniAppCommon.CODE_EMPTY_USER_ID;
import static com.jd.manto.MiniAppCommon.CODE_INTERNAL_ERROR;
import static com.jd.manto.MiniAppCommon.CODE_INVALID_PARAMETER;
import static com.jd.manto.MiniAppCommon.CODE_NONE_EXISTS_GROUP;
import static com.jd.manto.MiniAppCommon.CODE_NOT_GROUP_MEMBER;
import static com.jd.manto.MiniAppCommon.CODE_NO_NETWORK_CONNECTION;
import static com.jd.manto.MiniAppCommon.CODE_OPERATION_CANCELED;
import static com.jd.manto.MiniAppCommon.CODE_UNKNOWN_ERROR;
import static com.jd.manto.MiniAppCommon.CODE_UNKNOWN_EXCEPTION;
import static com.jd.manto.MiniAppCommon.reportMiniAppError;
import static com.jd.manto.MiniAppCommon.reportMiniAppSuccess;
import static com.jd.oa.provider.MiniMoreMenuProvider.APP_INFO_DATA;
import static com.jd.oa.router.DeepLink.CONTACTS;
import static com.jd.oa.router.DeepLink.MINI_APP;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.chenenyu.router.Router;
import com.jd.manto.tools.MantoTools;
import com.jd.oa.AppBase;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.abilities.dialog.MoreHelper;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd;
import com.jd.oa.provider.MiniMoreMenuProvider;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jingdong.manto.MantoActivityResult;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;
import com.jingdong.manto.ui.MantoTransportActivity;
import com.jingdong.manto.utils.MantoProcessUtil;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class JsMeChat extends AbstractMantoModule {

    public final int REQUEST_CODE_CHOOSE_CONTACT = 333;
    public final int REQUEST_CODE_CHOOSE_CHAT = 334;

    public static final String SEND_MSG_TO_GROUP = "sendMessageCardToGroup";

    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("openProfile", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("openChat", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("openGroupChat", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("chooseMEContact", IN_PROCESS_TYPE));
        list.add(new JsApiMethod("chooseMEContactInner", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("chooseChat", IN_PROCESS_TYPE));
        list.add(new JsApiMethod("chooseChatInner", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("sendMessageCard", IN_PROCESS_TYPE));
        list.add(new JsApiMethod("sendMessageCardInner", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("sendShareCard", IN_PROCESS_TYPE));
        list.add(new JsApiMethod("sendShareCardInner", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod(SEND_MSG_TO_GROUP, ACROSS_PROCESS_TYPE));

    }

    @Override
    public Bundle initData(String s, MantoCore mantoCore, JSONObject jsonObject) {
        Log.e("tag", "执行方法" + s + " 参数:" + jsonObject.toString());
        //获取参数
        final String userId = jsonObject.optString("userId");
        final String appId = jsonObject.optString("appId");
        final boolean secret = jsonObject.optBoolean("secret");
        final String groupId = jsonObject.optString("groupId");
        //chooseContact 参数
        //selected保留原始JSON格式到使用时再解析
        final String selected = jsonObject.optString("selected");
        final String title = jsonObject.optString("title");
        final int max = jsonObject.optInt("max");
        //chooseChat 参数
        final boolean multiSelect = jsonObject.optBoolean("multiSelect");
        final boolean showConfirm = jsonObject.optBoolean("showConfirm");
        final String confirmTitle = jsonObject.optString("confirmTitle");
        final String confirmDesc = jsonObject.optString("confirmTitle");
        final String confirmText = jsonObject.optString("confirmTitle");
        final String confirmCancelText = jsonObject.optString("confirmTitle");
        //sendMessageCard 参数
        final boolean multiple = jsonObject.optBoolean("multiple");
        final String appIdListRaw = jsonObject.optString("appId"); //和上面appId区分开
        final String okText = jsonObject.optString("okText", "");
        final String messageDataRaw = jsonObject.optString("messageData", "");
        //通过bundle传参到handleMethod
        Bundle bundle = new Bundle();
        if (s.equals("chooseMEContact")) {
            bundle.putString("params", jsonObject.toString());
        } else if (s.equals("chooseMEContactInner")) {
            bundle.putString("params", jsonObject.toString());
        } else if (s.equals("chooseChat")) {
            bundle.putString("params", jsonObject.toString());
        } else if (s.equals("chooseChatInner")) {
            bundle.putString("params", jsonObject.toString());

            bundle.putBoolean("multiSelect", multiSelect);
            bundle.putBoolean("showConfirm", showConfirm);
            bundle.putString("confirmTitle", confirmTitle);
            bundle.putString("confirmDesc", confirmDesc);
            bundle.putString("confirmText", confirmText);
            bundle.putString("confirmCancelText", confirmCancelText);
        } else if (s.equals("sendMessageCard")) {
            bundle.putString("params", jsonObject.toString());
        } else if (s.equals("sendMessageCardInner")) {
            bundle.putString("params", jsonObject.toString());

            bundle.putBoolean("multiple", multiple);
            bundle.putString("appIdListRaw", appIdListRaw);
            bundle.putString("okText", okText);
            bundle.putString("messageDataRaw", messageDataRaw);
        } else if (s.equals("sendShareCard")) {
            bundle.putString("params", jsonObject.toString());
        } else if (s.equals("sendShareCardInner")) {
            bundle.putString("params", jsonObject.toString());
        } else if (SEND_MSG_TO_GROUP.equals(s)) {
            bundle.putString("data", jsonObject.toString());
        }
        {
            bundle.putString("userId", userId);
            bundle.putString("appId", appId);
            bundle.putBoolean("secret", secret);
            bundle.putString("groupId", groupId);
            //chooseContact 参数
            bundle.putInt("max", max);
            bundle.putString("title", title);
            bundle.putString("selected", selected);
            //chooseChat 参数
            bundle.putBoolean("multiSelect", multiSelect);
            bundle.putBoolean("showConfirm", showConfirm);
            bundle.putString("confirmTitle", confirmTitle);
            bundle.putString("confirmDesc", confirmDesc);
            bundle.putString("confirmText", confirmText);
            bundle.putString("confirmCancelText", confirmCancelText);
            //sendMessageCard 参数
            bundle.putBoolean("multiple", multiple);
            bundle.putString("appIdListRaw", appIdListRaw);
            bundle.putString("okText", okText);
            bundle.putString("messageDataRaw", messageDataRaw);
        }

        return bundle;
    }

    @Override
    public String getModuleName() {
        return "IM";
    }

    @Override
    public void handleMethod(String method, MantoCore core, Bundle data, MantoResultCallBack callback) {
        final Bundle bundle = new Bundle();
        if (data == null) {
            reportMiniAppError(CODE_UNKNOWN_EXCEPTION, callback, bundle);
            return;
        }
        Context context = AppBase.getTopActivity() != null ? AppBase.getTopActivity() : AppBase.getAppContext();
        final ImDdService imDdService = AppJoint.service(ImDdService.class);
        String appId = data.getString(IMantoBaseModule.APP_ID_KEY);
        OpenEventTrackingUtil.trackEventManto(appId, method); //开放能力埋点

        switch (method) {
            case "openProfile":
                openProfile(data, context, callback, imDdService);
                break;
            case "openChat":
                openChat(data, context, callback, imDdService);
                break;
            case "openGroupChat":
                openGroupChat(data, context, callback, imDdService);
                break;
            case "chooseMEContact":
                chooseMEContact(data, callback, core.getActivity());
                break;
            case "chooseMEContactInner":
                chooseMEContactInner(data, context, callback, imDdService);
                break;
            case "chooseChat":
                Log.e("tag", "chooseChat");
                MantoTools.callMethod(core.getActivity(), data, "chooseChatInner", callback);
                break;
            case "chooseChatInner":
                Log.e("tag", "chooseChatInner");
                chooseChatInner(data, context, callback, imDdService);
                break;
            case "sendMessageCard":
                Log.e("tag", "sendMessageCard");
                MantoTools.callMethod(core.getActivity(), data, "sendMessageCardInner", callback);
                break;
            case "sendMessageCardInner":
                Log.e("tag", "sendMessageCardInner");
                sendMessageCardInner(data, context, callback, imDdService);
                break;
            case "sendShareCard":
                Log.e("tag", "sendShareCard");
                MantoTools.callMethod(core.getActivity(), data, "sendShareCardInner", callback);
                break;
            case "sendShareCardInner":
                Log.e("tag", "sendShareCardInner");
                sendShareCardInner(data, core, callback);
                break;
            case SEND_MSG_TO_GROUP:
                sendMessageCardToGroup(context, imDdService, data, callback);
                break;
        }
    }

    private void openProfile(Bundle data, Context context, MantoResultCallBack callback, ImDdService imDdService) {
        Bundle bundle = new Bundle();
        final String userId = data.getString("userId", "");
        //appId默认值为ee
        String appId = data.getString("appId", MultiAppConstant.APPID);
        if (appId.isEmpty()) {
            appId = MultiAppConstant.APPID;
        }
        if (!userId.isEmpty()) {
            try {
                imDdService.showContactDetailInfo(context, appId, userId);
                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openProfile succeed");
                reportMiniAppSuccess(callback, bundle);
                return;
            } catch (Exception e) {
                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openProfile error, msg: " + e.getMessage());
                e.printStackTrace();
            }
            reportMiniAppError(CODE_INTERNAL_ERROR, callback, bundle);
            return;
        }
        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openProfile invalid parameter");
        reportMiniAppError(CODE_INVALID_PARAMETER, callback, bundle);
    }

    private void openChat(Bundle data, Context context, MantoResultCallBack callBack, ImDdService imDdService) {
        Bundle bundle = new Bundle();
        final String userId = data.getString("userId", "");
        //appId默认值为ee
        String appId = data.getString("appId", MultiAppConstant.APPID);
        if (appId.isEmpty()) {
            appId = MultiAppConstant.APPID;
        }

        if (!userId.isEmpty()) {
            //openChat做鉴权
            imDdService.openChat(context, appId, userId, null, new LoadDataCallback<Void>() {
                @Override
                public void onDataLoaded(Void unused) {
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openChat succeed");
                    reportMiniAppSuccess(callBack, bundle);
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openChat error, msg: " + s);
                    reportMiniAppError(CODE_INTERNAL_ERROR, callBack, bundle);
                }
            });
            reportMiniAppSuccess(callBack, bundle);
            return;
        }
        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openChat invalid parameter");
        reportMiniAppError(CODE_EMPTY_USER_ID, callBack, bundle);
    }

    private void openGroupChat(Bundle data, Context context, MantoResultCallBack callBack, ImDdService imDdService) {
        Bundle bundle = new Bundle();
        final String groupId = data.getString("groupId", "");

        if (!groupId.isEmpty()) {
            //openChat做鉴权
            imDdService.openChat(context, null, null, groupId, new LoadDataCallback<Void>() {
                @Override
                public void onDataLoaded(Void unused) {
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openGroupChat succeed");
                    reportMiniAppSuccess(callBack, bundle);
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    int errCode = 0;
                    if (i == 1) {
                        errCode = CODE_NONE_EXISTS_GROUP;
                    } else if (i == 2) {
                        errCode = CODE_NOT_GROUP_MEMBER;
                    } else {
                        errCode = CODE_INTERNAL_ERROR;
                    }
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openGroupChat error, msg: " + s);
                    reportMiniAppError(errCode, callBack, bundle);
                }
            });
            return;
        }
        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openGroupChat invalid parameter");
        reportMiniAppError(CODE_EMPTY_GROUP_ID, callBack, bundle);
    }


    private void chooseMEContact(Bundle data, MantoResultCallBack callBack, Activity activity) {

        String processName = MantoProcessUtil.getProcessName();
        String actionName = "";
        if (!TextUtils.isEmpty(processName)) {
            int index = processName.lastIndexOf(":");
            if (index >= 0 && index < processName.length() - 1) {
                String shortName = processName.substring(processName.lastIndexOf(":") + 1);
                actionName = activity.getPackageName() + ".ACTION_ASSIST_" + shortName.toUpperCase();
            }
        }

        Bundle bundle = new Bundle();
        bundle.putString("params", data.getString("params"));
        bundle.putString("actionName", actionName);
        bundle.putString(IMantoBaseModule.REQUEST_JSAPI_KEY, "chooseMEContactInner");

        callBack.onSuccess(bundle);
    }

    private void chooseMEContactInner(Bundle data, Context context, MantoResultCallBack callBack, ImDdService imDdService) {
        Bundle result = new Bundle();
        String title;
        int max;
        String selectedRaw;
        String actionName;
        ArrayList<MemberEntityJd> memberList = new ArrayList<>();
        try {
            //chooseContact参数
            String param = data.getString("params");
            JSONObject jsonParamObject = new JSONObject(param);
            actionName = jsonParamObject.optString("actionName", "");
            String realParam = jsonParamObject.optString("params");
            JSONObject jsonObject = new JSONObject(realParam);
            title = jsonObject.optString("title", "");
            max = jsonObject.optInt("max", 0);

            selectedRaw = jsonObject.optString("selected", "");
            JSONArray selectedJson = null;

            //解析selected
            selectedJson = new JSONArray(selectedRaw);
            if (selectedJson.length() != 0) {
                for (int i = 0; i < selectedJson.length(); i++) {
                    JSONObject object = selectedJson.getJSONObject(i);
                    MemberEntityJd member = new MemberEntityJd();
                    member.mId = object.optString("userId");
                    member.mApp = object.optString("appId", AppBase.iAppBase.getTimlineAppId());
                    memberList.add(member);
                }
            }
        } catch (Throwable e) {
            //json解析失败
            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseMEContact json parsing failed, msg: " + e.getMessage());
            e.printStackTrace();
            reportMiniAppError(CODE_UNKNOWN_ERROR, callBack, result);
            return;
        }

        if (!(context instanceof Activity)) {
            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseMEContact failed, get topActivity failed");
            reportMiniAppError(CODE_INTERNAL_ERROR, callBack, result);
            return;
        }

        if (max != 0) {
            MantoTransportActivity.start((Activity) context, new MantoTransportActivity.OnCreateActivityListener() {
                @Override
                public void onCreate(Activity activity) {
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseMEContact opens ContactSelector");
                    Intent intent = Router.build(CONTACTS).getIntent(activity);
                    intent.putExtra("extra_contact", memberList);
                    intent.putExtra("title", title);
                    intent.putExtra("max", max);
                    intent.putExtra("extra_specify_appId", new ArrayList<>(Collections.singletonList(imDdService.getAppID())));
                    activity.startActivityForResult(intent, REQUEST_CODE_CHOOSE_CONTACT);
                }
            }, new MantoActivityResult.ResultCallback() {
                @Override
                public void onActivityResult(int reqCode, int resultCode, Intent intent) {
                    if (intent == null) {
                        return;
                    }
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseMEContact opened ContactSelector, result code: " + resultCode);
                    if (reqCode == REQUEST_CODE_CHOOSE_CONTACT) {
                        if (resultCode == Activity.RESULT_OK) {
                            try {
                                Serializable extra = intent.getSerializableExtra("extra_contact");
                                if (extra instanceof ArrayList) {
                                    ArrayList<?> selected = (ArrayList<?>) extra;
                                    if (!selected.isEmpty() && selected.get(0) instanceof MemberEntityJd) {
                                        @SuppressWarnings("unchecked") ArrayList<MemberEntityJd> contacts = (ArrayList<MemberEntityJd>) selected;
                                        JSONArray jsonArray = new JSONArray();
                                        for (int i = 0; i < contacts.size(); i++) {
                                            JSONObject member = new JSONObject();
                                            member.put("userId", contacts.get(i).mId);
                                            member.put("appId", contacts.get(i).mApp);
                                            member.put("userName", contacts.get(i).mName);
                                            member.put("avatar", contacts.get(i).mAvatar);
                                            member.put("email", contacts.get(i).mEmail);
                                            jsonArray.put(member);
                                        }
                                        result.putString("contacts", jsonArray.toString());
                                        reportMiniAppSuccess(callBack, result);
                                        if (!TextUtils.isEmpty(actionName)) {
                                            MantoTools.startIntent(context, actionName);
                                        }
                                        return;
                                    }
                                }
                            } catch (Throwable e) {
                                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseMEContact opened ContactSelector, result code: " + resultCode + " json parsing failed: " + e.getMessage());
                                e.printStackTrace();
                            }
                        }
                        reportMiniAppError(CODE_INTERNAL_ERROR, callBack, result);
                    }
                    if (!TextUtils.isEmpty(actionName)) {
                        MantoTools.startIntent(context, actionName);
                    }
                }
            });
            return;
        }
        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseMEContact invalid parameter");
        reportMiniAppError(CODE_INVALID_PARAMETER, callBack, result);
    }

    private void chooseChatInner(Bundle params, Context context, MantoResultCallBack callBack, ImDdService imDdService) {
        Bundle result = new Bundle();

        String actionName = "";
        int max = 0;
        boolean multiSelect = false;
        String title = "";
        boolean showConfirm = false;
        String confirmTitle = "";
        String confirmDesc = "";
        String confirmText = "";
        String confirmCancelText = "";
        try {
            //chooseContact参数
            String param = params.getString("params");
            JSONObject jsonParamObject = new JSONObject(param);
            actionName = jsonParamObject.optString("actionName", "");
            String realParam = jsonParamObject.optString("params");
            JSONObject data = new JSONObject(realParam);
            //chooseChat参数
            max = data.optInt("max", 0);
            multiSelect = data.optBoolean("multiSelect", false);
            //页面自定义部分
            title = data.optString("title", "");
            showConfirm = data.optBoolean("showConfirm", false);
            confirmTitle = data.optString("confirmTitle", "");
            confirmDesc = data.optString("confirmDesc", "");
            confirmText = data.optString("confirmText", "");
            confirmCancelText = data.optString("confirmCancelText", "");
        } catch (Exception e) {
            //json解析失败
            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseChat json parsing failed, msg: " + e.getMessage());
            e.printStackTrace();
            reportMiniAppError(CODE_UNKNOWN_ERROR, callBack, result);
        }
        Bundle bundle = new Bundle();

        if (!(context instanceof Activity)) {
            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseChat failed, get topActivity failed");
            reportMiniAppError(CODE_INTERNAL_ERROR, callBack, bundle);
            return;
        }

        //检查必填项
        if (max != 0) {
            MemberListEntityJd entity = new MemberListEntityJd();
            if (multiSelect) {
                entity.setSelectMode(MemberListEntityJd.SELECT_MODE_MULTI);
            } else {
                entity.setSelectMode(MemberListEntityJd.SELECT_MODE_SINGLE);
            }
            entity.setFrom(UIHelperConstantJd.TYPE_SHARE).setShowConstantFilter(false).setShowSelf(true).setShowOptionalFilter(false).setMaxNum(max);
            String finalActionName = actionName;
            imDdService.gotoMemberList((Activity) context, REQUEST_CODE_CHOOSE_CHAT, entity, new Callback<ArrayList<MemberEntityJd>>() {
                @Override
                public void onSuccess(ArrayList<MemberEntityJd> bean) {
                    try {
                        JSONArray jsonArray = new JSONArray();
                        if (bean != null && !bean.isEmpty()) {
                            for (MemberEntityJd entityJd : bean) {
                                JSONObject jsonObject = new JSONObject();
                                if (!entityJd.isGroup()) {
                                    jsonObject.put("userId", entityJd.mId);
                                    jsonObject.put("sessionType", 0);
                                    jsonObject.put("appId", entityJd.mApp);
                                    jsonObject.put("name", entityJd.mName);
                                    jsonObject.put("avatar", entityJd.mAvatar);
                                } else {
                                    jsonObject.put("groupId", entityJd.mId);
                                    jsonObject.put("sessionType", 1);
                                    jsonObject.put("name", entityJd.mName);
                                    jsonObject.put("avatar", entityJd.mAvatar);
                                }
                                jsonArray.put(jsonObject);
                            }
                            bundle.putString("data", jsonArray.toString());
                            reportMiniAppSuccess(callBack, bundle);
                            MantoTools.startIntent(context, finalActionName);
                            return;
                        }
                    } catch (Throwable e) {
                        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseChat" + " json parsing failed: " + e.getMessage());
                        e.printStackTrace();
                    }
                    reportMiniAppError(CODE_INTERNAL_ERROR, callBack, bundle);
                    MantoTools.startIntent(context, finalActionName);
                }

                @Override
                public void onFail() {
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseChat opens chat selector failed");
                    reportMiniAppError(CODE_INTERNAL_ERROR, callBack, bundle);
                }
            });
            return;
        }
        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram chooseChat invalid parameter");
        reportMiniAppError(CODE_INVALID_PARAMETER, callBack, bundle);
    }

    private void sendMessageCardInner(Bundle params, Context context, MantoResultCallBack callBack, ImDdService imDdService) {
        String actionName = "";
        //处理json
        int messageType;
        JSONObject payload;
        //sendMessageCard 参数
        int maxCount = 0;
        boolean multiple = false;
        String appIdListRaw = "";
        String messageDataRaw = "";
        String customPageAppId = "";
        String customPageUrl = "";
        //页面自定义部分
        String pageTitle = "";
        String okText = "";
        Bundle bundle = new Bundle();

        try {
            //chooseContact参数
            String param = params.getString("params");
            JSONObject jsonParamObject = new JSONObject(param);
            actionName = jsonParamObject.optString("actionName", "");
            String realParam = jsonParamObject.optString("params");
            JSONObject jsonObject = new JSONObject(realParam);
            //sendMessageCard 参数
            maxCount = jsonObject.optInt("max", 1000);
            multiple = jsonObject.optBoolean("multiple", false);
            appIdListRaw = jsonObject.optString("appIdListRaw");
            messageDataRaw = jsonObject.optString("messageDataRaw");
            JSONObject customPage = jsonObject.optJSONObject("customPage");
            customPageUrl = customPage == null ? "" : customPage.optString("url", "");
            customPageAppId = customPage == null ? "" : customPage.optString("appId", "");
            if ("".equals(appIdListRaw)) {
                appIdListRaw = jsonObject.optString("appIdList");
            }
            if ("".equals(messageDataRaw)) {
                messageDataRaw = jsonObject.optString("messageData");
            }
            //页面自定义部分
            pageTitle = jsonObject.optString("title", "");
            okText = jsonObject.optString("okText");
        } catch (Throwable e) {
            //json解析失败
            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard json parsing failed, msg: " + e.getMessage());
            e.printStackTrace();
            reportMiniAppError(CODE_UNKNOWN_ERROR, callBack, bundle);
            return;
        }

        try {
            JSONObject jsonObject = new JSONObject(messageDataRaw);
            if (!jsonObject.has("type") || !jsonObject.has("data")) {
                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard json parsing failed, input does not contain type or data");
                reportMiniAppError(CODE_INVALID_PARAMETER, callBack, bundle);
                return;
            }
            messageType = jsonObject.optInt("type", 0);
            if (messageType == 0 || messageType > 3) {
                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard json parsing failed, input - invalid type");
                reportMiniAppError(CODE_INVALID_PARAMETER, callBack, bundle);
                return;
            }
            payload = jsonObject.optJSONObject("data");
            if (payload == null) {
                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard json parsing failed, input - invalid data");
                reportMiniAppError(CODE_INVALID_PARAMETER, callBack, bundle);
                return;
            }
            if (!TextUtils.isEmpty(customPageAppId)) {
                payload.put("customPageUrl", customPageUrl);
                payload.put("customPageAppId", customPageAppId);
            }
            payload.put("maxCount", maxCount);
            payload.put("appIdList", appIdListRaw);
        } catch (Throwable e) {
            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard json parsing failed, msg: " + e.getMessage());
            e.printStackTrace();
            reportMiniAppError(CODE_INTERNAL_ERROR, callBack, bundle);
            return;
        }
        //=======》需要传输的数据检测完成
        //检查必填项
        if (maxCount != 0) {
            //=======》查看回调结果
            String finalActionName = actionName;
//            LoadDataCallback loadDataCallback = new LoadDataCallback<Void>() {
//                @Override
//                public void onDataLoaded(Void aVoid) {
//                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard chat selector opened");
//                    reportMiniAppSuccess(callBack, bundle);
//                    MantoTools.startIntent(context, finalActionName);
//                }
//
//                @Override
//                public void onDataNotAvailable(String s, int i) {
//                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard chat selector opening failed");
//                    reportMiniAppError(CODE_INTERNAL_ERROR, callBack, bundle);
//                    MantoTools.startIntent(context, finalActionName);
//                }
//            };
//            //=======》根据消息类型拉起聊天选择器
//            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard sending message, type: " + messageType);
//            switch (messageType) {
//                case 1: // 文本类型
//                    imDdService.sendTextCard(payload.toString(), loadDataCallback);
//                    break;
//                case 2: // 链接类型
//                    imDdService.sendShareLink(payload.toString(), loadDataCallback);
//                    break;
//                default: // jue卡片
//                    imDdService.sendJueCard(payload.toString(), loadDataCallback);
//                    break;
//            }
//            return;

            //            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard sending message, type: " + messageType);
            MemberListEntityJd entity = new MemberListEntityJd();
            entity.setFrom(UIHelperConstantJd.TYPE_SENDTO_OTHER);
            entity.setSelectMode(multiple ? MemberListEntityJd.SELECT_MODE_MULTI : MemberListEntityJd.SELECT_MODE_SINGLE);
            entity.setTitle(pageTitle);
            entity.setMaxNum(maxCount);
            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.openSelectorAndSendJueCard(payload.toString(), entity, new LoadDataCallback<ArrayList<MemberEntityJd>>() {
                @Override
                public void onDataLoaded(ArrayList<MemberEntityJd> members) {
                    //回调前自动关闭选择器 并返回已发送消息的人员列表
                    //1.选了1个会话 先跳转至首页清栈->切换首页到会话列表页->打开对应会话->弹出分享结果弹框
                    //2.选了多个会话 直接弹出分享结果弹框
                    Log.e("tag", "onDataLoaded");
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard chat selector opened");
                    reportMiniAppSuccess(callBack, bundle);
                    MantoTools.startIntent(context, finalActionName);
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    Log.e("tag", "onDataNotAvailable");
                    MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard chat selector opening failed");
                    switch (i) {
                        case -1:
                            reportMiniAppError(CODE_INTERNAL_ERROR, callBack, bundle);
                            break;
                        case -2:
                            reportMiniAppError(CODE_OPERATION_CANCELED, callBack, bundle);
                            break;
                    }
                    MantoTools.startIntent(context, finalActionName);
                }
            });
            return;
        }
        MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram sendMessageCard invalid parameter");
        reportMiniAppError(CODE_INVALID_PARAMETER, callBack, bundle);
    }

    private void sendShareCardInner(Bundle params, MantoCore core, MantoResultCallBack callBack) {
        try {
            JSONObject jsonObject = new JSONObject(params.getString("params"));
            JSONObject val = new JSONObject(jsonObject.optString("params"));
            String appid = params.getString("appid");
            String deepLink = MINI_APP + "?mparam=%7B%22category%22%3A%22jump%22%2C%22des%22%3A%22jdmp%22%2C%22appId%22%3A%22APPID%22%2C%22vapptype%22%3A%221%22%7D";
            deepLink = deepLink.replace("APPID", appid);
            // appInfo
            String appInfo = "{}";
            Uri uriAppInfo = Uri.parse("content://" + MiniMoreMenuProvider.AUTHORITY() + "/" + APP_INFO_DATA);
            ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
            Cursor queryAppInfo = contentResolver.query(uriAppInfo, null, appid, null, null);
            if (null != queryAppInfo) {
                if (queryAppInfo.moveToNext()) {
                    appInfo = queryAppInfo.getString(0);
                }
                queryAppInfo.close();
            }
            AppInfo info = JsonUtils.getGson().fromJson(appInfo, AppInfo.class);
            OptionEntity.OptionShareInfo shareInfo = MoreHelper.parseShareInfo(val, info, deepLink);
            AppService appService = AppJoint.service(AppService.class);
            String finalDeepLink = deepLink;
            appService.shareToChart(AppBase.getTopActivity(), shareInfo, new LoadDataCallback() {
                @Override
                public void onDataLoaded(Object o) {
                    MELogUtil.localI(MINI_APP, "sendShareCardInner TYPE_SHARE onDataLoaded");
                    if (!TextUtils.isEmpty(finalDeepLink)) {
                        MELogUtil.localE(MELogUtil.TAG_MINI, "MiniProgram sendShareCardInner onDataLoaded failed");
                        Router.build(finalDeepLink).go(AppBase.getTopActivity());
                        reportMiniAppSuccess(callBack, params);
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    MELogUtil.localD(MINI_APP, "sendShareCardInner TYPE_SHARE onDataNotAvailable");
                    if (!TextUtils.isEmpty(finalDeepLink)) {
                        Router.build(finalDeepLink).go(AppBase.getTopActivity());
                    }
                    reportMiniAppError(CODE_INTERNAL_ERROR, callBack, params);
                }
            });
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_MINI, "MiniProgram sendShareCardInner failed");
            reportMiniAppError(CODE_INTERNAL_ERROR, callBack, params);
        }
    }

    private void sendMessageCardToGroup(Context context, ImDdService ddService, Bundle bundle, MantoResultCallBack callback) {
        if (context == null || ddService == null || bundle == null) {
            reportMiniAppError(CODE_INVALID_PARAMETER, callback, bundle);
            return;
        }
        String data = bundle.getString("data");
        if (TextUtils.isEmpty(data)) {
            reportMiniAppError(CODE_INVALID_PARAMETER, callback, bundle);
            return;
        }
        try {
            String source = bundle.getString("appid");
            if (TextUtils.isEmpty(source)) {
                source = "MiniApp";
            }
            JSONObject dataObj = new JSONObject(data);
            String groupId = dataObj.optString("groupId");
            String pin;
            boolean isGroup;
            if (TextUtils.isEmpty(groupId)) {
                isGroup = false;
                pin = dataObj.optString("robotId");
            } else {
                isGroup = true;
                pin = groupId;
            }
            if (TextUtils.isEmpty(pin)) {
                reportMiniAppError(CODE_INVALID_PARAMETER, callback, bundle);
                return;
            }
            String sendData = "{}";
            JSONObject messageData = dataObj.optJSONObject("messageData");
            if (messageData != null) {
                sendData = messageData.toString();
            }
            if (isGroup) {
                final String finalSource = source;
                final String finalSendData = sendData;
                ddService.getGroupInfo(pin, new LoadDataCallback<GroupInfoEntity>() {
                    @Override
                    public void onDataLoaded(GroupInfoEntity groupInfoEntity) {
                        if (groupInfoEntity != null) {
                            int flag = groupInfoEntity.getFlag();
                            if (flag == 1) {
                                reportMiniAppError(CODE_NONE_EXISTS_GROUP, callback, bundle);
                            } else if (flag == 2) {
                                reportMiniAppError(CODE_NOT_GROUP_MEMBER, callback, bundle);
                            } else {
                                ddService.sendJueCardToChat(pin, "robot.dd", isGroup, finalSource,
                                        finalSendData, new LoadDataCallback<Void>() {
                                            @Override
                                            public void onDataLoaded(Void unused) {
                                                reportMiniAppSuccess(callback, bundle);
                                            }

                                            @Override
                                            public void onDataNotAvailable(String s, int i) {
                                                reportMiniAppError(CODE_NO_NETWORK_CONNECTION, callback, bundle);
                                            }
                                        });
                            }
                        } else {
                            reportMiniAppError(CODE_INTERNAL_ERROR, callback, bundle);
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        reportMiniAppError(CODE_NO_NETWORK_CONNECTION, callback, bundle);
                    }
                });
            } else {
                ddService.sendJueCardToChat(pin, "robot.dd", isGroup, source,
                        sendData, new LoadDataCallback<Void>() {
                            @Override
                            public void onDataLoaded(Void unused) {
                                reportMiniAppSuccess(callback, bundle);
                            }

                            @Override
                            public void onDataNotAvailable(String s, int i) {
                                reportMiniAppError(CODE_NO_NETWORK_CONNECTION, callback, bundle);
                            }
                        });
            }

        } catch (Exception e) {
            reportMiniAppError(CODE_INVALID_PARAMETER, callback, bundle);
        }
    }
}
