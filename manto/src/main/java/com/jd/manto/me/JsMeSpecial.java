package com.jd.manto.me;

import static com.jd.manto.MiniAppCommon.CODE_INTERNAL_ERROR;
import static com.jd.manto.MiniAppCommon.CODE_INVALID_PARAMETER;
import static com.jd.manto.MiniAppCommon.CODE_UNKNOWN_ERROR;
import static com.jd.manto.MiniAppCommon.reportMiniAppError;
import static com.jd.manto.MiniAppCommon.reportMiniAppSuccess;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;

import com.jd.manto.tools.MantoTools;
import com.jd.me.activitystarter.ActivityResult;
import com.jd.me.activitystarter.ActivityResultParser;
import com.jd.me.activitystarter.ActivityStarter;
import com.jd.me.activitystarter.ResultCallback;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.abilities.dialog.Constants;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abilities.utils.MoreMenuUtils;
import com.jd.oa.ext.UriExtensionsKt;
import com.jd.oa.fragment.utils.MiniAppUtil;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.wifiauth.ShieldSdkPresenter;
import com.jd.oa.wifiauth.WifiAuthContract;
import com.jd.oa.wifiauth.WifiAuthListener;
import com.jingdong.Manto;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

import androidx.fragment.app.FragmentActivity;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class JsMeSpecial extends AbstractMantoModule {
    private static final String TAG = "JsMeSpecial";
    private static final String INTENT_FROM_MONTO_JUMP_KEY = "fromMontoOpenSchema";

    private Activity topActivity;

    public static final long FLOAT_CACHE_TIME_LONG = 12 * 60 * 60 * 1000;
    public static final long FLOAT_CACHE_TIME_SHORT = 2 * 60 * 1000;


    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("setWifi", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("openSchema", IN_PROCESS_TYPE));//在小程序进程
        list.add(new JsApiMethod("openSchemaInner", ACROSS_PROCESS_TYPE));//在主进程
        list.add(new JsApiMethod("checkAppUpdate", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("showMoreMenu", IN_PROCESS_TYPE));
    }

    @Override
    public Bundle initData(String s, MantoCore activity, JSONObject jsonObject) {
        final String url = jsonObject.optString("url");
        String type = jsonObject.optString("type");
        Bundle bundle = new Bundle();
        bundle.putString("url", url);
        bundle.putString("UrlType", type);
        if (jsonObject != null) {
            bundle.putString("params", jsonObject.toString());
        }

        String actionName = jsonObject.optString("actionName", "");
        if (StringUtils.isNotEmptyWithTrim(actionName)) {
            bundle.putString("actionName", actionName);
        }
        Log.e("tag", "methodName:" + s + "actionName:" + actionName);
        return bundle;
    }

    @Override
    public String getModuleName() {
        return "Special";
    }

    @Override
    public final void handleMethod(String method, MantoCore core, Bundle data, final MantoResultCallBack callback) {
        final Bundle bundle = new Bundle();
        final String url = data.getString("url", "");
        String type = data.getString("UrlType", "");
        if (type == null || type.isEmpty()) {
            type = "1";
        }
        Context context = AppBase.getTopActivity() != null ? AppBase.getTopActivity() : AppBase.getAppContext();
        String appId = data.getString(IMantoBaseModule.APP_ID_KEY);
        OpenEventTrackingUtil.trackEventManto(appId, method); //开放能力埋点
        switch (method) {
            case "setWifi":
                WifiAuthContract.Presenter mPresenter;
                mPresenter = new ShieldSdkPresenter(context, new WifiAuthListener() {
                    @Override
                    public void onProgressChange(int progress) {
                    }

                    @Override
                    public void onSuccess(String msg) {
                        callback.onSuccess(bundle);
                    }

                    @Override
                    public void onFailure(String msg) {
                        callback.onFailed(bundle);
                    }
                });
//                if (mPresenter.isWifiCertified()) {
//                }
                if (mPresenter != null) {
                    mPresenter.auth(AppBase.getTopActivity());
                }
                break;
            case "openSchema":
                MELogUtil.localI(TAG, "openSchema core的值" + core.getActivity());
                MantoTools.callMethod(core.getActivity(), data, "openSchemaInner", callback);
                break;
            case "openSchemaInner":
                //小程序跳转页面
                topActivity = AppBase.getMainActivity();
                String actionName = data.getString("actionName", "");
                MELogUtil.localI(TAG, "openSchemaInner actionName的值:" + actionName + " topActivity:" + topActivity);

                if (url != null && !url.isEmpty() && type != null) {
                    String CODE_OPEN_URL = "1";
                    String CODE_OPEN_LOCAL_URL = "2";
                    String CODE_OPEN_DEEP_LINK = "3";
                    String CODE_OPEN_SAFARI_URL = "4";
                    if (type.equals(CODE_OPEN_URL) || type.equals(CODE_OPEN_LOCAL_URL) || type.equals(CODE_OPEN_DEEP_LINK)) {
                        try {
                            boolean success = OpennessApi.openUrlOrDeepLink(url, null, false, false, new OpennessApi.OnGetIntentCallback() {
                                @Override
                                public void onIntent(Intent intent) {
                                    if (intent == null) return;
                                    intent.putExtra(INTENT_FROM_MONTO_JUMP_KEY, true);
                                    intent.putExtra("actionName", actionName);
                                    ActivityStarter.<ActivityResult>from((FragmentActivity) topActivity)
                                            .setIntent(intent)
                                            .setResultParser(new ActivityResultParser())
                                            .start(new ResultCallback<ActivityResult>() {
                                                @Override
                                                public void onResult(ActivityResult result) {
                                                    Log.d(TAG, "onResult: " + result);
                                                    if (result.getResultCode() == Activity.RESULT_OK) {
                                                        MantoTools.startIntent(topActivity, actionName);
                                                    }
                                                }
                                            });
                                }
                            });
                            if (success) {
                                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openSchema succeed");
                                reportMiniAppSuccess(callback, bundle);
                                return;
                            }
                        } catch (Throwable e) {
                            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openSchema failed, message: " + e.getMessage());
                            e.printStackTrace();
                            reportMiniAppError(CODE_INTERNAL_ERROR, callback, bundle);
                            return;
                        }
                    } else if (type.equals(CODE_OPEN_SAFARI_URL)) {
                        try {
                            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openSchema succeed");
                            Uri uri = Uri.parse(url);
                            UriExtensionsKt.openWithExternalApp(uri, context, true, false, null, new Function1<Uri, Unit>() {
                                @Override
                                public Unit invoke(Uri uri) {
                                    reportMiniAppSuccess(callback, bundle);
                                    return null;
                                }
                            });
                            return;
                        } catch (Throwable e) {
                            MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openSchema failed, message: " + e.getMessage());
                            e.printStackTrace();
                            reportMiniAppError(CODE_INTERNAL_ERROR, callback, bundle);
                            return;
                        }
                    }
                }

                MELogUtil.localI(MELogUtil.TAG_MINI, "MiniProgram openSchema invalid parameter");
                reportMiniAppError(CODE_INVALID_PARAMETER, callback, bundle);
                break;
            case "checkAppUpdate":
                OptionEntity updateEntity = new OptionEntity(-1, -1, url, "", Constants.TYPE_UPDATE, MoreMenuUtils.MENU_TYPE_MINI);
                MiniAppUtil.transferEvent(updateEntity);
                reportMiniAppSuccess(callback, bundle);
                break;
            case "showMoreMenu":
                core.getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        String appId = data.getString("appid");
                        String params = data.getString("params");
                        JSONObject jsonParam;
                        try {
                            jsonParam = new JSONObject(params);
                        } catch (JSONException e) {
                            throw new RuntimeException(e);
                        }
                        boolean result = MoreMenuUtils.show(appId, jsonParam, core.getActivity(), MoreMenuUtils.MENU_TYPE_MINI, new AbsOpennessCallback() {
                            @Override
                            public void done(int code, String msg) {
                                if (code == MoreMenuUtils.ADD_FLOAT) {
                                    //默认6个
//                                  Manto.setAppCacheCount(6);
                                    Manto.setAppCacheTime(appId, FLOAT_CACHE_TIME_LONG);
                                } else {
                                    Manto.setAppCacheTime(appId, FLOAT_CACHE_TIME_SHORT);
                                }
                            }
                        });
                        if (result) {
                            reportMiniAppSuccess(callback, bundle);
                        } else {
                            reportMiniAppError(CODE_UNKNOWN_ERROR, callback, bundle);
                        }
                    }
                });
                break;
        }
    }
}
