package com.jd.manto.me;

import android.annotation.SuppressLint;
import android.app.Service;
import android.os.Bundle;
import android.os.Vibrator;

import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;

import org.json.JSONObject;

import java.util.List;

public class JsMeApp extends AbstractMantoModule {
    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("restartApp", ACROSS_PROCESS_TYPE));
    }

    @Override
    public Bundle initData(String methodName, MantoCore mantoCore, JSONObject params) {
        Bundle bundle = new Bundle();
        return bundle;
    }

    @SuppressLint("MissingPermission")
    @Override
    public void handleMethod(String method, MantoCore core, Bundle data, MantoResultCallBack callback) {
        super.handleMethod(method, core, data, callback);
        String appId = data.getString(IMantoBaseModule.APP_ID_KEY);
        OpenEventTrackingUtil.trackEventManto(appId, method); //开放能力埋点
        switch (method) {
            case "restartApp":
                CommonUtils.restartApp(core.getActivity());
                break;
        }
    }

    @Override
    public String getModuleName() {
        return "App";
    }
}
