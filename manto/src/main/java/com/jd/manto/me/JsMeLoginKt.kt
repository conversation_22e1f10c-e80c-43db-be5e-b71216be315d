package com.jd.manto.me

import android.os.Bundle
import com.jd.manto.MiniAppCommon
import com.jd.manto.tools.readAppInfo
import com.jd.oa.abilities.api.ApiAuth
import com.jd.oa.abilities.model.AuthApp
import com.jd.oa.abilities.model.CHECK_AUTHORIZE_RESULT_ACCEPT
import com.jd.oa.abilities.model.CHECK_AUTHORIZE_RESULT_CANCELED
import com.jd.oa.abilities.model.CheckAuthorizeResult
import com.jd.oa.fragment.js.JSErrCode
import com.jd.oa.fragment.js.JSTools
import com.jd.oa.model.service.IServiceCallback
import com.jingdong.manto.MantoCore
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/20 13:56
 */
class JsMeLoginKt {

    fun requestAccess(
        core: MantoCore?,
        bundle: Bundle,
        callback: MantoResultCallBack
    ) {
        val reportError: (Int) -> Unit = {
            MiniAppCommon.reportMiniAppError(it, callback, bundle)
        }
        val appKey = bundle.getString("appKey")
        if (appKey.isNullOrEmpty()) {
            reportError(MiniAppCommon.CODE_INVALID_PARAMETER)
            return
        }
        val scopeList = bundle.getStringArrayList("scopeList")
        if (scopeList.isNullOrEmpty()) {
            reportError(MiniAppCommon.CODE_INVALID_PARAMETER)
            return
        }
        val appInfo = readAppInfo(core?.activity, bundle.getString("appid"))
        val authApp = if (appInfo != null) {
            AuthApp().apply {
                applicationId = appInfo.appID
                applicationIcon = appInfo.photoKey
                applicationName = appInfo.appName
            }
        } else null
        ApiAuth.requestAccess(core?.activity, appKey, scopeList, authApp, object :
            IServiceCallback<CheckAuthorizeResult> {
            //false 网络失败一类
            //true 校验result
            override fun onResult(success: Boolean, t: CheckAuthorizeResult?, error: String?) {
                if (!success || t == null) {
                    reportError(MiniAppCommon.CODE_NETWORK_FAILURE)
                } else {
                    when (t.status) {
                        CHECK_AUTHORIZE_RESULT_ACCEPT -> {//成功
                            bundle.putString("code", t.code)
                            bundle.putLong("expireIn", t.expireIn ?: 0L)
                            MiniAppCommon.reportMiniAppSuccess(callback, bundle)
                        }

                        CHECK_AUTHORIZE_RESULT_CANCELED -> {
                            reportError(MiniAppCommon.CODE_COMMON_OPERATION_CANCELED)
                        }

                        else -> {
                            reportError(MiniAppCommon.CODE_USER_PERMISSION_DENIED)
                        }
                    }
                }
            }
        })
    }


}