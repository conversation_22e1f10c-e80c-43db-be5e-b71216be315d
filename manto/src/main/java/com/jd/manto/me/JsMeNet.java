package com.jd.manto.me;

import android.os.Bundle;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;

import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

public class JsMeNet extends AbstractMantoModule {
    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("requestMe", ACROSS_PROCESS_TYPE));
        list.add(new JsApiMethod("requestGateway", ACROSS_PROCESS_TYPE));
    }

    @Override
    public Bundle initData(String s, MantoCore activity, JSONObject jsonObject) {
        Bundle bundle = new Bundle();
        bundle.putString("action", jsonObject.optString("action"));
        bundle.putString("postData", jsonObject.optString("postData"));
        return bundle;
    }

    @Override
    public String getModuleName() {
        return "Net";
    }

    @Override
    public final void handleMethod(String method, MantoCore activity, Bundle data, final MantoResultCallBack callback) {
        final Bundle bundle = new Bundle();
        final String action = data.getString("action");
        final String jsonData = data.getString("postData");
//                System.out.println("jsonData=" + jsonData);
        if (action == null || action.length() == 0) {
            callback.onFailed(bundle);
            return;
        }
        Gson gson = new Gson();
        Type type = new TypeToken<Map<String, Object>>() {
        }.getType();
        Map<String, Object> param = gson.fromJson(jsonData, type);
        String appId = data.getString(IMantoBaseModule.APP_ID_KEY);
        OpenEventTrackingUtil.trackEventManto(appId, method); //开放能力埋点
        switch (method) {
            case "requestMe":
                HttpManager.legacy().post(null, param, new SimpleRequestCallback<String>() {
                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        callback.onFailed(bundle);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        bundle.putString("data", info.result);
                        callback.onSuccess(bundle);
                    }
                }, action);
                break;
            case "requestGateway":
                HttpManager.post(null, param, new SimpleRequestCallback<String>() {
                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        callback.onFailed(bundle);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        bundle.putString("data", info.result);
                        callback.onSuccess(bundle);
                    }
                }, action);
                break;
        }
    }

}
