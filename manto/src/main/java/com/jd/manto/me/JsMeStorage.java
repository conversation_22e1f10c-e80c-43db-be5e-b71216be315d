package com.jd.manto.me;

import android.os.Bundle;
import android.text.TextUtils;

import com.jd.manto.MiniAppPreference;
import com.jd.oa.cache.FileCache;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jd.oa.preference.CrossPlatformPreference;
import com.jd.oa.storage.UseType;
import com.jingdong.manto.MantoCore;
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.JsApiMethod;

import org.json.JSONObject;

import java.util.List;

public class JsMeStorage extends AbstractMantoModule {
    @Override
    protected void injectJsApiMethod(List<JsApiMethod> list) {
        list.add(new JsApiMethod("setMeStorage", ACROSS_PROCESS_SYNC_TYPE));
        list.add(new JsApiMethod("getMeStorage", ACROSS_PROCESS_SYNC_TYPE));
        list.add(new JsApiMethod("removeMeStorage", ACROSS_PROCESS_SYNC_TYPE));
        list.add(new JsApiMethod("getFilePath", ACROSS_PROCESS_SYNC_TYPE));
        list.add(new JsApiMethod("setContextOptions", ACROSS_PROCESS_SYNC_TYPE));
        list.add(new JsApiMethod("getContextOptions", ACROSS_PROCESS_SYNC_TYPE));
    }

    @Override
    public Bundle initData(String s, MantoCore activity, JSONObject jsonObject) {
        Bundle bundle = new Bundle();
        bundle.putString("key", jsonObject.optString("key"));
        bundle.putString("value", jsonObject.optString("value"));
        String sceneId = jsonObject.optString(CrossPlatformPreference.KEY_SCENE_ID);
        if (!TextUtils.isEmpty(sceneId)) {
            bundle.putString(CrossPlatformPreference.KEY_SCENE_ID, sceneId);
            bundle.putInt(CrossPlatformPreference.KEY_TYPE, jsonObject.optInt(CrossPlatformPreference.KEY_TYPE));
            JSONObject options = jsonObject.optJSONObject(CrossPlatformPreference.KEY_OPTIONS);
            if (options != null) {
                bundle.putString(CrossPlatformPreference.KEY_OPTIONS, options.toString());
            }
        }
        return bundle;
    }

    @Override
    public String getModuleName() {
        return "Storage";
    }

    @Override
    public Bundle handleMethodSync(String method, MantoCore activity, Bundle data) {
        String key = data.getString("key");
        String value = data.getString("value");
        String appId = data.getString(IMantoBaseModule.APP_ID_KEY);
        OpenEventTrackingUtil.trackEventManto(appId, method); //开放能力埋点
        switch (method) {
            case "setMeStorage":
                if (key != null && key.length() > 0) {
                    MiniAppPreference.getInstance().put(key, value);
                    data.putString(ERROR_CODE, ERROR_CODE_SUCCESS);
                } else {
                    data.putString(ERROR_CODE, ERROR_CODE_FAILED);
                }
                return data;
            case "getMeStorage":
                if (key != null && key.length() > 0) {
                    Bundle result = new Bundle();
                    result.putString("value", MiniAppPreference.getInstance().get(key));
                    data.putString(ERROR_CODE, ERROR_CODE_SUCCESS);
                    data.putBundle(BUNDLE_REAL_RESULT, result);
                } else {
                    data.putString(ERROR_CODE, ERROR_CODE_FAILED);
                }
                return data;
            case "removeMeStorage":
                if (key != null && key.length() > 0) {
                    MiniAppPreference.getInstance().remove(key);
                    data.putString(ERROR_CODE, ERROR_CODE_SUCCESS);
                } else {
                    data.putString(ERROR_CODE, ERROR_CODE_FAILED);
                }
                return data;
            case "getFilePath":
                Bundle result = new Bundle();
                result.putString("path", FileCache.getInstance().getCacheFile(UseType.TENANT).getAbsolutePath());
                data.putString(ERROR_CODE, ERROR_CODE_SUCCESS);
                data.putBundle(BUNDLE_REAL_RESULT, result);
                return data;
            case "setContextOptions": {
                String sceneId = data.getString(CrossPlatformPreference.KEY_SCENE_ID);
                int type = data.getInt(CrossPlatformPreference.KEY_TYPE);
                String options = data.getString(CrossPlatformPreference.KEY_OPTIONS);
                if (TextUtils.isEmpty(sceneId) || TextUtils.isEmpty(options)) {
                    data.putString(ERROR_CODE, ERROR_CODE_FAILED);
                    return data;
                }
                data.putString(ERROR_CODE, ERROR_CODE_SUCCESS);
                CrossPlatformPreference.getDefault().setContextOptions(sceneId, type, options);
            }
            return data;
            case "getContextOptions": {
                String sceneId = data.getString(CrossPlatformPreference.KEY_SCENE_ID);
                int type = data.getInt(CrossPlatformPreference.KEY_TYPE);
                if (TextUtils.isEmpty(sceneId)) {
                    data.putString(ERROR_CODE, ERROR_CODE_FAILED);
                    return data;
                }
                String options = CrossPlatformPreference.getDefault().getContextOptions(sceneId, type);
                Bundle realResult = new Bundle();
                realResult.putString(CrossPlatformPreference.KEY_OPTIONS, options);
                data.putString(ERROR_CODE, ERROR_CODE_SUCCESS);
                data.putBundle(BUNDLE_REAL_RESULT, realResult);
            }
            return data;
        }
        return super.handleMethodSync(method, activity, data);
    }

}
