package com.jd.manto.me

import android.content.Context
import android.os.Bundle
import com.jd.manto.MiniAppCommon
import com.jd.manto.tools.EmptyMantoLifecycleListener
import com.jd.oa.AppBase
import com.jd.oa.eventbus.JmEventDispatcher
import com.jd.oa.eventbus.JmEventVoidReturnProcessor
import com.jd.oa.utils.logE
import com.jingdong.Manto
import com.jingdong.manto.MantoCore
import com.jingdong.manto.jsapi.openmodule.AbstractMantoModule
import com.jingdong.manto.jsapi.openmodule.JsApiMethod
import com.jingdong.manto.jsapi.openmodule.MantoLifecycleLisener
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack
import com.jingdong.manto.mainproc.MainProcMessage
import com.jingdong.manto.utils.MantoProcessUtil
import org.json.JSONObject


/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2025/4/1 11:57
 */
class JsMeEvent : AbstractMantoModule() {

    companion object {
        const val JS_EVENT_ON_NATIVE_EVENT = "registerEvent"
        const val JS_EVENT_OFF_NATIVE_EVENT = "unRegisterEvent"
        const val JS_EVENT_SEND_EVENT = "sendEvent"

        private const val KEY_EVENT_NAME = "eventName"
        private const val KEY_EVENT_PARAMS = "eventParams"
    }

    override fun getModuleName(): String = "Event"

    override fun injectJsApiMethod(list: MutableList<JsApiMethod>?) {
        list?.add(JsApiMethod(JS_EVENT_ON_NATIVE_EVENT, ACROSS_PROCESS_TYPE))
        list?.add(JsApiMethod(JS_EVENT_OFF_NATIVE_EVENT, ACROSS_PROCESS_TYPE))
        list?.add(JsApiMethod(JS_EVENT_SEND_EVENT, ACROSS_PROCESS_TYPE))
    }

    override fun initData(method: String?, mantoCore: MantoCore?, jsonObject: JSONObject?): Bundle {
        val bundle = Bundle()
        if (jsonObject == null) return bundle
        val eventName = jsonObject.optString(KEY_EVENT_NAME)
        bundle.putString(KEY_EVENT_NAME, eventName)
        if (method == JS_EVENT_SEND_EVENT) {
            val eventParams = jsonObject.optJSONObject(KEY_EVENT_PARAMS)
            if (eventParams != null) {
                bundle.putString(KEY_EVENT_PARAMS, eventParams.toString())
            }
        }
        return bundle
    }

    override fun handleMethodSync(method: String?, core: MantoCore?, data: Bundle?): Bundle {
        logE {
            "JsMeEvent handleMethodSync processName = ${MantoProcessUtil.getProcessName()}"
        }
        return super.handleMethodSync(method, core, data)
    }

    override fun handleMethod(
        method: String?,
        core: MantoCore?,
        data: Bundle?,
        callback: MantoResultCallBack?
    ) {
        super.handleMethod(method, core, data, callback)
        logE {
            "JsMeEvent handleMethod processName = ${MantoProcessUtil.getProcessName()}"
        }
        val context: Context? = core?.activity
            ?: if (AppBase.getTopActivity() != null) AppBase.getTopActivity() else AppBase.getAppContext()
        if (method.isNullOrEmpty() || data == null) return
        val eventName = data.getString(KEY_EVENT_NAME)
        if (eventName.isNullOrEmpty()) return
        when (method) {
            JS_EVENT_ON_NATIVE_EVENT -> {
                JmEventDispatcher.registerProcessor(JsMantotProcessor(eventName))
                MiniAppCommon.reportMiniAppSuccess(callback, Bundle())
            }

            JS_EVENT_OFF_NATIVE_EVENT -> {
                JmEventDispatcher.unregisterAll { processor ->
                    processor is JsMantotProcessor && processor.hasEvent(eventName)
                }
                MiniAppCommon.reportMiniAppSuccess(callback, Bundle())
            }

            JS_EVENT_SEND_EVENT -> {
                val eventParams = data.getString(KEY_EVENT_PARAMS)
                val obj = eventParams?.runCatching {
                    JSONObject(eventParams)
                }?.getOrNull()
                JmEventDispatcher.dispatchEvent(context, eventName, obj)
                MiniAppCommon.reportMiniAppSuccess(callback, Bundle())
            }
        }

    }

    override fun addLifecycleLisener(method: String?, bundle: Bundle?): MantoLifecycleLisener {
        return object : EmptyMantoLifecycleListener() {
            override fun onDestroy() {
                JmEventDispatcher.unregisterAll { processor ->
                    processor is JsMantotProcessor
                }
            }
        }
    }


    private class JsMantotProcessor(
        vararg events: String
    ) : JmEventVoidReturnProcessor<Any>(*events) {

        override fun processEvent(
            context: Context,
            event: String,
            args: Any?,
        ) {
            val message = MainProcMessage()
            message.messageName = "onNativeNotification"
            message.data = Bundle()
            message.data.putString(KEY_EVENT_NAME, event)
            message.data.putString(KEY_EVENT_PARAMS, args?.toString() ?: "{}")
            Manto.getMainProcChannel().sendMessageToManto(message)
        }
    }

}