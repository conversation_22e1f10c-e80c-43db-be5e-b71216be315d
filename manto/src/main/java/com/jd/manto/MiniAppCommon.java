package com.jd.manto;

import android.os.Bundle;

import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;

public class MiniAppCommon {
    //调用成功时返回
    public static final int CODE_OK = 0;
    public static final String MSG_OK = "调用成功时返回";

    public static final int CODE_UNKNOWN_ERROR = 100;
    public static final String MSG_UNKNOWN_ERROR = "未知错误，API 内部非预期的错误";

    public static final int CODE_UNKNOWN_EXCEPTION = 101;
    public static final String MSG_UNKNOWN_EXCEPTION = "框架未知错误";

    public static final int CODE_INTERNAL_ERROR = 102;
    public static final String MSG_INTERNAL_ERROR = "内部错误";

    public static final int CODE_FEATURE_NOT_SUPPORT = 103;
    public static final String MSG_FEATURE_NOT_SUPPORT = "API 不可用";

    public static final int CODE_INVALID_PARAMETER = 104;
    public static final String MSG_INVALID_PARAMETER = "参数错误";

    public static final int CODE_AUTHENTICATION_FAIL = 105;
    public static final String MSG_AUTHENTICATION_FAIL = "鉴权失败";

    public static final int CODE_SYSTEM_PERMISSION_DENIED = 106;
    public static final String MSG_SYSTEM_PERMISSION_DENIED = "系统拒绝授权";

    public static final int CODE_USER_PERMISSION_DENIED = 107;
    public static final String MSG_USER_PERMISSION_DENIED = "用户拒绝授权";

    public static final int CODE_ORGANIZATION_PERMISSION_DENIED = 108;
    public static final String MSG_ORGANIZATION_PERMISSION_DENIED = "无组织权限";

    public static final int CODE_COMMON_OPERATION_CANCELED = 109;
    public static final String MSG_OPERATION_CANCELED = "操作取消";

    public static final int CODE_NO_READ_PERMISSION = 201;
    public static final String MSG_NO_READ_PERMISSION = "文件无读权限";

    public static final int CODE_NO_WRITE_PERMISSION = 202;
    public static final String MSG_NO_WRITE_PERMISSION = "文件无写权限";

    public static final int CODE_FILE_DOES_NOT_EXIST = 203;
    public static final String MSG_FILE_DOES_NOT_EXIST = "文件不存在";

    public static final int CODE_FILE_ALREADY_EXISTS = 204;
    public static final String MSG_FILE_ALREADY_EXISTS = "文件已存在";

    public static final int CODE_DIRECTORY_NOT_EMPTY = 205;
    public static final String MSG_DIRECTORY_NOT_EMPTY = "文件夹非空";

    public static final int CODE_IS_NOT_DIRECTORY = 206;
    public static final String MSG_IS_NOT_DIRECTORY = "不是文件夹";

    public static final int CODE_IS_NOT_FILE = 207;
    public static final String MSG_IS_NOT_FILE = "不是文件";

    public static final int CODE_SIZE_LIMIT_EXCEEEDED = 208;
    public static final String MSG_SIZE_LIMIT_EXCEEEDED = "超出总写入大小限制";

    //不能同时操作路径和它的子路径
    public static final int CODE_PATH_OPERATION_FAILED = 209;
    public static final String MSG_PATH_OPERATION_FAILED = "不能同时操作路径和它的子路径";

    //读取的文件内容大小超过阈值
    public static final int CODE_DATA_READ_OVERFLOW = 210;
    public static final String MSG_DATA_READ_OVERFLOW = "读取的文件内容大小超过阈值";

    //加解密禁用操作
    public static final int CODE_ENCRYPTION_DECRYPTION_DENIED = 211;
    public static final String MSG_ENCRYPTION_DECRYPTION_DENIED = "加解密禁用操作";

    //写入的文件内容大小超过阈值
    public static final int CODE_WRITE_DATA_OVERFLOW = 212;
    public static final String MSG_WRITE_DATA_OVERFLOW = "写入的文件内容大小超过阈值";

    public static final int CODE_IS_INVALID_FILE_PATH = 213;
    public static final String MSG_IS_INVALID_FILE_PATH = "不合法的文件路径";

    public static final int CODE_FILE_NAME_TOO_LONG = 214;
    public static final String MSG_FILE_NAME_TOO_LONG = "文件名过长";

    public static final int CODE_NETWORK_REQUEST_CANCELLED = 301;
    public static final String MSG_NETWORK_REQUEST_CANCELLED = "网络请求取消";

    public static final int CODE_CONNECTION_TIMED_OUT = 302;
    public static final String MSG_CONNECTION_TIMED_OUT = "网络超时";

    public static final int CODE_NO_NETWORK_CONNECTION = 303;
    public static final String MSG_NO_NETWORK_CONNECTION = "网络离线";

    //网络 SDK 内部错误
    public static final int CODE_NETWORK_SDK_INTERNAL_ERROR = 304;
    public static final String MSG_NETWORK_SDK_INTERNAL_ERROR = "网络 SDK 内部错误";

    public static final int CODE_NETWORK_FAILURE = 305;
    public static final String MSG_NETWORK_FAILURE = "网络失败";

    public static final int CODE_UNABLE_TO_DOWNLOAD = 306;
    public static final String MSG_UNABLE_TO_DOWNLOAD = "文件下载失败";

    public static final int CODE_NETWORK_SDK_PARAMETER_ERROR = 307;
    public static final String MSG_NETWORK_SDK_PARAMETER_ERROR = "网络 SDK 参数错误";

    public static final int CODE_EMPTY_USER_ID = 1002012;

    public static final int CODE_EMPTY_GROUP_ID = 1002013;

    public static final int CODE_NONE_EXISTS_GROUP = 1002014;

    public static final int CODE_NOT_GROUP_MEMBER = 1002015;

    public static final int CODE_OPERATION_CANCELED = 1003003;

    //未获取到机器人信息
    public static final int CODE_NO_ROBOT_INFO= 1006001;



    public static void reportMiniAppError(int errCode, MantoResultCallBack callBack, Bundle bundle) {
        bundle.putInt("errCode", errCode);
        callBack.onFailed(bundle);
    }

    public static void reportMiniAppSuccess(MantoResultCallBack callBack, Bundle bundle) {
        bundle.putInt("errCode", 0);
        callBack.onSuccess(bundle);
    }
}
