package com.jd.manto.tools;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

//import com.jingdong.manto.d1.i;
import com.jingdong.manto.jsapi.openmodule.IMantoBaseModule;
import com.jingdong.manto.jsapi.openmodule.MantoResultCallBack;
import com.jingdong.manto.jsapi.openmodule.OpenJsApiManager;
import com.jingdong.manto.utils.MantoProcessUtil;

import java.util.HashMap;
import java.util.Map;

public class MantoTools {
    public static void callMethod(Activity activity, Bundle data, String requestJSApi, MantoResultCallBack callBack) {
        String processName = MantoProcessUtil.getProcessName();
        String actionName = "";
        if (!TextUtils.isEmpty(processName)) {
            int index = processName.lastIndexOf(":");
            if (index >= 0 && index < processName.length() - 1) {
                String shortName = processName.substring(processName.lastIndexOf(":") + 1);
                actionName = activity.getPackageName() + ".ACTION_ASSIST_" + shortName.toUpperCase();
            }
        }
        data.putString("actionName", actionName);
        data.putString("params", data.getString("params"));
        data.putString(IMantoBaseModule.REQUEST_JSAPI_KEY, requestJSApi);
        callBack.onSuccess(data);
    }

    public static String getMontoActionName(Activity activity) {
        String processName = MantoProcessUtil.getProcessName();
        String actionName = "";
        if (!TextUtils.isEmpty(processName)) {
            int index = processName.lastIndexOf(":");
            if (index >= 0 && index < processName.length() - 1) {
                String shortName = processName.substring(processName.lastIndexOf(":") + 1);
                actionName = activity.getPackageName() + ".ACTION_ASSIST_" + shortName.toUpperCase();
            }
        }
        return actionName;
    }

    /**
     * 返回小程序页面   由于跳转小程序太快 导致主APP页面卡在关闭前状态  所以延时100ms跳转
     *
     * @param context
     * @param actionName
     */
    public static void startIntent(Context context, String actionName) {
        if (context != null && !TextUtils.isEmpty(actionName)) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Intent i = new Intent(actionName);
                    context.startActivity(i);
                }
            }, 100);
        }
    }

//    public static void injectAllMantoMethod() {
//        Map<String, IMantoBaseModule> keyMaps = OpenJsApiManager.getSApiMap();
//        for (Map.Entry<String, IMantoBaseModule> item : keyMaps.entrySet()) {
//            String key = item.getKey();//service名字
//            IMantoBaseModule mantoApiModule = item.getValue();//service下挂的所有方法
//
//            String moduleName = mantoApiModule.getModuleName();
//            HashMap<String, i> methods = mantoApiModule.getNativeMethod();
//            Log.e("tag", "           ");
//            Log.e("tag", "           ");
//            Log.e("tag", "------------------------------------------------------------------------------------------------");
//            Log.e("tag", "serviceName:" + key + " moduleName:" + moduleName);
//            Log.e("tag", "------------------------------------------------------------------------------------------------");
//
//            for (Map.Entry<String, i> method : methods.entrySet()) {
//                String methodName = method.getKey();
//                i data = method.getValue();
//                Log.e("tag", "methodName:" + methodName + " v:" + data.c() + " b:" + data.b());
//            }
//        }
//    }
}
