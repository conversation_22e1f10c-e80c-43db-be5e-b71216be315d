package com.jd.manto.tools

import com.jingdong.manto.jsapi.openmodule.MantoLifecycleLisener

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/4/1 14:03
 */
open class EmptyMantoLifecycleListener : MantoLifecycleLisener {

    override fun onReady() {

    }

    override fun onDestroy() {

    }

    override fun onRemove(): Boolean = false

    override fun onPause() {

    }

    override fun onBackground() {

    }

    override fun onForeground() {

    }
}