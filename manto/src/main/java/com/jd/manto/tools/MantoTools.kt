package com.jd.manto.tools

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.jd.oa.AppBase
import com.jd.oa.business.app.model.AppInfo
import com.jd.oa.provider.MiniMoreMenuProvider
import com.jd.oa.utils.JsonUtils
import org.json.JSONArray
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON>unguang
 * @create 2024/12/20 14:51
 */

fun readAppInfo(context: Context?, appId: String?): AppInfo? = runCatching {
    if (appId.isNullOrEmpty()) return@runCatching null
    val finalContext = context ?: AppBase.getAppContext()
    var appInfo: String? = null
    val uriAppInfo =
        Uri.parse("content://" + MiniMoreMenuProvider.AUTHORITY() + "/" + MiniMoreMenuProvider.APP_INFO_DATA)
    finalContext.contentResolver?.query(uriAppInfo, null, appId, null, null)?.use { cursor ->
        if (cursor.moveToNext()) {
            appInfo = cursor.getString(0)
        }
    }
    if (appInfo == null) null else JsonUtils.getGson().fromJson(appInfo, AppInfo::class.java)
}.getOrNull()


fun jsonObjectToBundle(jsonObject: JSONObject): Bundle {
    val bundle = Bundle()
    jsonObject.keys().forEach { key ->
        when (val value = jsonObject.get(key)) {
            is Int -> bundle.putInt(key, value)
            is Long -> bundle.putLong(key, value)
            is Double -> bundle.putDouble(key, value)
            is Boolean -> bundle.putBoolean(key, value)
            is String -> bundle.putString(key, value)
            is JSONObject -> bundle.putBundle(key, jsonObjectToBundle(value))
            is JSONArray -> bundle.putSerializable(key, jsonArrayToArrayList(value)) // 正确转换 JSONArray
            else -> throw IllegalArgumentException("Unsupported type for key: $key")
        }
    }
    return bundle
}

fun jsonArrayToArrayList(jsonArray: JSONArray): ArrayList<Any> {
    val list = ArrayList<Any>()
    for (i in 0 until jsonArray.length()) {
        when (val value = jsonArray.get(i)) {
            is Int, is Long, is Double, is Boolean, is String -> list.add(value)
            is JSONObject -> list.add(jsonObjectToBundle(value))
            is JSONArray -> list.add(jsonArrayToArrayList(value)) // 递归处理嵌套 JSONArray
            else -> throw IllegalArgumentException("Unsupported type in JSONArray at index: $i")
        }
    }
    return list
}