package com.jd.manto;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.jingdong.Manto;

/**
 * Created by kris on 2020/3/11.
 */

public class MantoRipper extends BroadcastReceiver {
     public final static String ACTION_LOGOUT = "action_fake_manto_logout";
    public final static String ACTION_LOGIN = "action_fake_manto_login";
    @Override
    public void onReceive(Context context, Intent intent) {
         String action = intent.getAction();
        if (ACTION_LOGIN.equals(action)) {
            Manto.updateSandBox("1111");
        } else if (ACTION_LOGOUT.equals(action)) {
            Manto.logout();
        }
    }
}
