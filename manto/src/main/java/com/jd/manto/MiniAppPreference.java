package com.jd.manto;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class MiniAppPreference extends AbsKvEntities {

    private final String PREF_NAME = "mini_app";

    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    @Override
    public String getPrefrenceName() {
        return PREF_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    private static MiniAppPreference preference;

    private MiniAppPreference() {
    }

    public static synchronized MiniAppPreference getInstance() {
        if (preference == null) {
            preference = new MiniAppPreference();
        }
        return preference;
    }

    public void put(String key, String val) {
        KvEntity<String> k = new KvEntity(key, "");
        put(k, val);
    }

    public String get(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        return get(k);
    }

    public void remove(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        remove(k);
    }
}
