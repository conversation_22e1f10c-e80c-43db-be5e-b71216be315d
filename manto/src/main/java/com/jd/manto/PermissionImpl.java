package com.jd.manto;

import android.app.Activity;
import android.content.pm.PackageManager;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.jd.oa.AppBase;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEMonitorFragment;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionCallback;
import com.jingdong.manto.sdk.api.IPermission;

import java.util.List;

public class PermissionImpl implements IPermission {
    @Override
    public boolean hasPermission(String permission) {
        return ContextCompat.checkSelfPermission(AppBase.getAppContext(), permission) == PackageManager.PERMISSION_GRANTED;
    }

    @Override
    public boolean hasPermissions(String[] permissions) {
        boolean result = true;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(AppBase.getAppContext(), permission) == PackageManager.PERMISSION_DENIED) {
                result = false;
                break;
            }
        }

        return result;
    }

    @Override
    public boolean hasLocationPermissionWithScene(String s, String s1) {
        return false;
    }

    @Override
    public void requestLocationPermissionWithScene(Activity activity, PermissionCallBack permissionCallBack, String s, String s1, String s2) {

    }

    @Override
    public void requestPermission(Activity activity, String s, String s1, String s2, final PermissionCallBack permissionCallBack) {
        MAEMonitorFragment.getInstance(activity).maeRequestPermission(new String[]{s}, new MAEPermissionCallback() {
            @Override
            public void onPermissionApplySuccess() {
                permissionCallBack.onGranted();
            }

            @Override
            public void onPermissionApplyFailure(List<String> list, List<Boolean> list1) {
                permissionCallBack.onDenied();
            }
        });
    }

    @Override
    public void requestPermissions(Activity activity, List<String> list, List<String> list1, List<String> list2, final PermissionCallBack permissionCallBack) {
        String[] strArray = new String[list.size()];
        MAEMonitorFragment.getInstance(activity).maeRequestPermission(list.toArray(strArray), new MAEPermissionCallback() {
            @Override
            public void onPermissionApplySuccess() {
                permissionCallBack.onGranted();
            }

            @Override
            public void onPermissionApplyFailure(List<String> list, List<Boolean> list1) {
                permissionCallBack.onDenied();
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(Activity activity, int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {

    }

    @Override
    public String getAppNameAsPrefix() {
        return null;
    }
}
