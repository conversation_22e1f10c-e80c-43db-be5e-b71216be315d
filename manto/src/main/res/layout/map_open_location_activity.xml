<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/map_white"
        >

        <LinearLayout
            android:id="@+id/map_back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            >

            <ImageView
                android:layout_width="10.5dp"
                android:layout_height="18.5dp"
                android:layout_marginRight="10dp"
                android:src="@drawable/map_actionbar_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="位置信息"
                android:textColor="#000000"
                android:textSize="16sp" />

        </LinearLayout>

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.tencent.tencentmap.mapsdk.map.MapView
            android:id="@+id/mapviewOverlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/map_goto_my_position"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:paddingBottom="15dp"
            android:paddingRight="15dp"
            android:src="@drawable/map_other_position_normal" />

    </RelativeLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:background="@color/map_white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="15dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/map_location_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#2e2d2d"
                android:lines="1"
                android:ellipsize="end"
                android:text="地矿局"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/map_location_address"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="#848484"
                android:lines="1"
                android:ellipsize="end"
                android:text="北京市111000号"
                android:textSize="12sp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/map_navigate_btn"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:src="@drawable/map_navigate_normal" />

    </LinearLayout>

</LinearLayout>