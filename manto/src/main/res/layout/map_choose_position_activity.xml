<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/map_white"
        >

        <LinearLayout
            android:id="@+id/map_choose_position_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            >

            <ImageView
                android:layout_width="10.5dp"
                android:layout_height="18.5dp"
                android:layout_marginRight="10dp"
                android:src="@drawable/map_actionbar_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="位置信息"
                android:textColor="#000000"
                android:textSize="16sp" />

        </LinearLayout>

        <TextView
            android:id="@+id/map_choose_position_sure"
            android:text="确定"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="15dp"
            android:layout_alignParentRight="true"
            android:textSize="12sp"
            android:textColor="#f0250f"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/map_choose_position_search"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/map_choose_position_sure"
            android:layout_marginRight="15dp"
            android:src="@drawable/map_search_icon_big" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/map_choose_position_map_container"
        android:layout_width="match_parent"
        android:layout_height="280dp">

        <com.tencent.tencentmap.mapsdk.map.MapView
            android:id="@+id/mapviewOverlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/map_location_center"
            android:layout_width="21dp"
            android:layout_height="33dp"
            android:layout_centerInParent="true"
            android:src="@drawable/map_location_anchor"
            />

        <ImageView
            android:id="@+id/map_goto_my_position"
            android:paddingRight="15dp"
            android:paddingBottom="15dp"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:src="@drawable/map_other_position_normal"
            />

    </RelativeLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ListView
            android:id="@+id/map_choose_location_listview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:dividerHeight="0.1dp"
            android:background="@android:color/white"
            android:divider="#b0b0b0"
            android:fadingEdge="none"
            android:choiceMode="singleChoice"
            />

        <ProgressBar
            android:id="@+id/map_choose_position_loading"
            style="@android:style/Widget.Holo.ProgressBar.Large"
            android:progressDrawable="@drawable/map_toast_loading"
            android:visibility="gone"
            android:layout_gravity="center"
            android:layout_width="50dp"
            android:layout_height="50dp" />

    </FrameLayout>

</LinearLayout>