<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="16dp"
    android:orientation="vertical"
    tools:context=".main.SettingActivity">

    <EditText
        android:id="@+id/et_appkey"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="App_Key" />

    <EditText
        android:id="@+id/et_host"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Host" />

    <EditText
        android:id="@+id/et_beta_host"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Beta_host" />


    <View
        android:layout_width="match_parent"
        android:background="@color/manto_light_gray"
        android:layout_height="1dp"/>

    <EditText
        android:id="@+id/et_app_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="小程序ID" />

    <EditText
        android:id="@+id/et_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="小程序类型" />

    <View
        android:layout_width="match_parent"
        android:background="@color/manto_light_gray"
        android:layout_height="1dp"/>

    <EditText
        android:id="@+id/et_login_url"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="登录地址" />

    <EditText
        android:id="@+id/et_login_host"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="登录域名" />

    <EditText
        android:id="@+id/et_login_sso"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="sso key值" />

    <View
        android:layout_width="match_parent"
        android:background="@color/manto_light_gray"
        android:layout_height="1dp"/>
    
    <Button
        android:id="@+id/btn_ok"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="确定（确定需杀死重启）" />

</LinearLayout>