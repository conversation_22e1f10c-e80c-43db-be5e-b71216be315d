<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:background="#000000">

    <LinearLayout
        android:id="@+id/ijk_title_layout"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ijk_title_back_btn"
            android:layout_width="49dp"
            android:layout_height="49dp"
            android:scaleType="centerInside"
            android:src="@drawable/title_back_white_arrow"/>

        <TextView
            android:id="@+id/ijk_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="49dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#ccffffff"
            android:textSize="16dp"/>
    </LinearLayout>

</FrameLayout>