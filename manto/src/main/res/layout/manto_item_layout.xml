<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/manto_item_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/manto_pressed_selector"
    android:minHeight="64dp"
    android:paddingLeft="15dp"
    android:paddingRight="10dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:clickable="false"
        android:orientation="vertical">

        <TextView
            android:id="@+id/app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="name"
            android:textColor="#333333"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/app_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/app_name"
            android:layout_marginTop="3dp"
            android:text="name"
            android:textColor="#999999"
            android:textSize="12sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/right_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:clickable="false" />
</RelativeLayout>
