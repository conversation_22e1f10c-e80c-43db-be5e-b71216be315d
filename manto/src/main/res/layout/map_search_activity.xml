<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/map_white"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/map_search_back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingRight="10dp">

            <ImageView
                android:layout_width="10.5dp"
                android:layout_height="18.5dp"
                android:src="@drawable/map_actionbar_back" />
        </LinearLayout>

        <com.jingdong.manto.lbs.MantoSearchEditText
            android:id="@+id/map_search_et"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginBottom="10dp"
            android:layout_marginLeft="6dp"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:hint="搜索地点"
            android:background="@drawable/map_search_edittext_bg_shape"
            android:focusable="true"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:lines="1"
            android:maxLines="1"
            android:textColor="#2e2d2d"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/map_search_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:layout_marginRight="9dp"
            android:padding="6dp"
            android:text="搜索"
            android:textColor="#848484"
            android:textSize="12sp" />

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ListView
            android:id="@+id/map_search_listview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            />

        <LinearLayout
            android:id="@+id/map_search_loading_ll"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:gravity="center"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="40dp">

            <ProgressBar
                style="@android:style/Widget.Holo.ProgressBar.Large"
                android:progressDrawable="@drawable/map_toast_loading"
                android:layout_marginRight="6dp"
                android:layout_width="20dp"
                android:layout_height="20dp" />

            <TextView
                android:text="正在加载..."
                android:textColor="#848484"
                android:textSize="12sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </FrameLayout>

</LinearLayout>