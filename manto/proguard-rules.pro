# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# manto 混淆配置
#-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
#-dontpreverify
-dontnote
-verbose
-useuniqueclassmembernames
#-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
-dontoptimize
-ignorewarnings

-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class com.android.vending.licensing.ILicensingService
-keep public class * extends android.view.View
-keep public class * extends android.widget.ImageView
-keep public class * extends android.view.ViewGroup
-keep public class * extends android.widget.LinearLayout
-keep public class * extends android.widget.TextView
-keep public class * extends android.widget.Gallery
-keep public class * extends android.widget.ListView
-keep public class * extends android.widget.ScrollView
-keep public class * extends android.widget.RelativeLayout
-keep public class * extends android.widget.FrameLayout
-keep public class * extends android.widget.ImageSwitcher
-keep public class * extends android.widget.AppWidgetProvider

-keep class com.jd.manto.jdext.**{*;}
-keep class com.jd.manto.lbs.**{*;}
-keep class com.jd.manto.map.**{*;}
-keep class com.jd.manto.hd.**{*;}

-keepclasseswithmembers class * {
    native <methods>;
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

-keepclassmembers class fqcn.of.javascript.interface.for.webview {
   public *;
}

-keep public class * extends android.widget.LinearLayout{ *; }

-dontwarn okio.**
-keep class okio.** {*;}
-keep class okhttp3.**{*;}
-dontwarn okhttp3.**

-keep public class * extends android.support.**
-keep class android.support.v4.** {*;}
-keep class android.support.v7.** {*;}
-keep class android.support.constraint.** {*;}
-dontwarn android.support.**

-dontwarn sun.security.**
-dontwarn sun.misc.**
-keep class sun.security.pkcs.PKCS7{
public java.security.cert.X509Certificate[] getCertificates();
public PKCS7(byte[]);
}

-keep class com.tencent.**{*;}
-dontwarn com.tencent.**

-keep public class * extends android.app.Fragment

-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keepattributes Signature,Exceptions
-keep class **.R
-keep class **.R$* {
	*;
}

#start 腾讯X5内核
-keep class com.tencent.smtt.export.external.**{*;}
-keep class com.tencent.tbs.video.interfaces.IUserStateChangedListener {*;}
-keep class com.tencent.smtt.sdk.CacheManager {public *;}
-keep class com.tencent.smtt.sdk.CookieManager {public *;}
-keep class com.tencent.smtt.sdk.WebHistoryItem {public *;}
-keep class com.tencent.smtt.sdk.WebViewDatabase {public *;}
-keep class com.tencent.smtt.sdk.WebBackForwardList {public *;}
-keep public class com.tencent.smtt.sdk.WebView {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.WebView$HitTestResult {
	public static final <fields>;
	public java.lang.String getExtra();
	public int getType();
}
-keep public class com.tencent.smtt.sdk.WebView$PictureListener {
	public <fields>;
	public <methods>;
}
-keepattributes InnerClasses
-keep public enum com.tencent.smtt.sdk.WebSettings$** {*;}
-keep public class com.tencent.smtt.sdk.WebSettings {public *;}
-keepattributes Signature
-keep public class com.tencent.smtt.sdk.ValueCallback {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.WebViewClient {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.DownloadListener {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.WebChromeClient {
	public <fields>;
	public <methods>;
}
-keep class com.tencent.smtt.sdk.SystemWebChromeClient{	public *;}
# 1. extension interfaces should be apparent
-keep public class com.tencent.smtt.export.external.extension.interfaces.* {
	public protected *;
}
# 2. interfaces should be apparent
-keep public class com.tencent.smtt.export.external.interfaces.* {
	public protected *;
}
-keep public class com.tencent.smtt.sdk.WebViewCallbackClient {
	public protected *;
}
-keep public class com.tencent.smtt.sdk.WebStorage$QuotaUpdater {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.DownloadListener {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.QbSdk {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.CookieSyncManager {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.Tbs* {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.utils.LogFileUtils {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.utils.TbsLog {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.utils.TbsLogClient {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.CookieSyncManager {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.export.external.extension.proxy.ProxyWebViewClientExtension {
	public <fields>;
	public <methods>;}
-keep class MTT.ThirdAppInfoNew {*;}
-keepclasseswithmembers class * {
    ... *JNI*(...);
}
-keepclasseswithmembernames class * {
	... *JRI*(...);
}
-keep class **JNI* {*;}
#end 腾讯X5内核

#start 腾讯地图
-keep class com.tencent.mapsdk.**{*;}
-keep class com.tencent.tencentmap.**{*;}
#end 腾讯地图

-keep class com.handmark.pulltorefresh.library.**{*;}
-keep class com.google.zxing.**{*;}

#jdmp manto
-keep class com.jingdong.manto.**{*;}
-keep class com.jingdong.launch.**{*;}
-keep class com.jingdong.Manto.**{*;}
-keep class com.jingdong.sdk.jweb.**{*;}

#jdmp mapView
-keep class com.jd.manto.lbs.**{*;}
-keep class com.jd.manto.map.**{*;}
