plugins {
    id 'com.android.library'
    id 'com.chenenyu.router'
    id 'kotlin-android'
}

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION


        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.jd.manto'
//    compileOptions {
//        sourceCompatibility JavaVersion.VERSION_1_8
//        targetCompatibility JavaVersion.VERSION_1_8
//    }
}

dependencies {
// ===== 小程序接入配置依赖 =====
    api fileTree(dir: 'libs', include: ['*.jar'])
// ====== manto 配置如下 ======
    // 核心sdk主包
    api('com.jingdong.manto:manto:jm-4.5.0-10-SNAPSHOT') {
        exclude(group: 'com.tencent', module: 'smtt')
        exclude(group: 'com.jingdong.manto', module: 'mantoV8')
    }
    // 地图组件
    implementation('com.jingdong.manto:mantomap:2dmap-4.0.8')
    // 组件包：jd 内部业务扩展组件（plus、jos、支付、手机号）
    implementation('com.jingdong.manto:mantoJdExt:4.0.7')
    //硬件操作
    implementation('com.jingdong.manto:mantoHardware:4.0.7')

    // x5
//    implementation 'com.tencent:smtt:1.0.6-SNAPSHOT'
    // 线程池包
    implementation 'com.jingdong.wireless.jdsdk:jdthreadmanager:1.0.2'
    // 加解密包
    implementation 'com.jingdong.wireless.jdsdk.sdk:security:1.0.1'
    //腾讯地图jar
//    implementation 'com.jingdong.wireless.android-cp:TencentMapSDK_Raster:1.3.4'
    //腾讯定位jar
//    implementation 'com.jingdong.wireless.android-cp:TencentLocationSDK:v6.1.2.1_r6693480a_170627_1603'

    //相册
//    implementation('com.jingdong.wireless.android-cp:android-album-widget:mpaas-1.0.1')
//    implementation('com.jingdong.wireless.android-cp:android-base-widget:mpaas-1.0.0')


// 适配了 Android X （兼容 target 29）
    implementation('androidx.recyclerview:recyclerview:1.2.0') {
        exclude group: 'android.arch.lifecycle', module: 'runtime'
    }
    implementation("androidx.constraintlayout:constraintlayout:$constraintlayoutVersion") {
        exclude group: 'android.arch.lifecycle', module: 'runtime'
    }
    implementation('androidx.core:core:1.5.0') {
        exclude group: 'android.arch.lifecycle', module: 'runtime'
    }

// ===== 第三方开源组件库 （如果 app 已有，可忽略） =====
//    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'
    implementation "com.squareup.okhttp3:okhttp:$okhttpVersion"
    implementation project(":common")
    implementation project(":wifiauth")
    implementation project(':libscanner')
//    testImplementation 'junit:junit:4.+'
//    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
}